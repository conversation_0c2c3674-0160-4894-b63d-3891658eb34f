# EditorConfig helps developers define and maintain consistent 
# coding styles between different editors and IDEs 
# editorconfig.org 
# end_of_line定义成lf，兼容不同操作系统中的换行字符，vscode可使用EditorConfig for VS Code 插件配合使用，在保存文件时自动处理好换行符 
root = true 
[*] 
charset = utf-8 
indent_style = space 
indent_size = 2 
end_of_line = lf 
insert_final_newline = true 
trim_trailing_whitespace = true 
[*.md] 
insert_final_newline = false
trim_trailing_whitespace = false