module.exports = {
  root: true,
  env: {
    browser: true,
    node: true,
    es6: true
  },
  extends: ['plugin:prettier/recommended', 'eslint:recommended'],
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'vue/require-default-prop': 'error',
    'vue/no-v-html': 'off'
  },
  parserOptions: {
    parser: '@babel/eslint-parser',
    ecmaVersion: 12,
    sourceType: 'module'
  },
  globals: {
    _hvueToast: 'writable',
    _hvueAlert: 'writable',
    _hvueLoading: 'writable',
    TKFlowEngine: 'writable',
    TChatRTC: 'writable',
    ActiveXObject: 'writable',
    createScriptElement: 'writable',
    PACK_ENV: 'writable',
    $h: 'writable',
    $hvue: 'writable',
    serviceOptions: 'writable',
    MODULE_NAME: 'writable',
  },
  plugins: ['prettier'],
  overrides: [
    {
      files: ['**/__tests__/*.{j,t}s?(x)'],
      env: {
        mocha: true
      }
    }
  ]
};
