.build-bc-h5-view:
  stage: build
  only: 
    - sit
    - develop
    - /^release[-_].*$/
    - master
  tags:
    - win205
  script:
    - $ENV:path='C:\nodedic\node-v16.13.1-win-x64;'+$ENV:path
    - npm install --registry=https://npm.thinkive.com/repository/npm-group --legacy-peer-deps
    - npm run build --$CI_COMMIT_BRANCH
    - push_to_git "dist\bc-h5-view" "account/bc-h5-view" "$CI_PROJECT_NAMESPACE $CI_PROJECT_NAME  $CI_COMMIT_SHA $CI_COMMIT_BRANCH $CI_COMMIT_AUTHOR"
  when: manual


