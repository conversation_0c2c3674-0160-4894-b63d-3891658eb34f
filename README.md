# bc_h5_view

业务受理h5前端

## 浏览器支持

> 现代浏览器和 IE9 及以上

## 环境安装

需要先安装 node 环境
cnpm install 

```bash
# formily表单解析器在npm私有库(下载代理地址: http://*************:8081/repository/npm-group/)
npm install -S @common/formily-parser-h5

# 安装依赖
npm install

```

## 工程安装启动

```bash
# install dependencies
npm i

# serve with hot reload at localhost:8080（本地启动服务）
npm run serve

# build for dev with minification （编译服务器端开发环境包=>dist目录）
npm run build --dev

# build for test with minification （编译服务器测试包=>dist目录）
npm run build --test

# build for uat with minification （编译服务器UAT包=>dist目录）
npm run build --uat

# build for production with minification（编译服务器生产包=>dist目录）
npm run build

# build for production and view the bundle analyzer report （生产包优化分析）
npm run build --report

```