module.exports = {
  presets: [
    [
      '@babel/preset-env',
      {
        // 启用模块转换以确保兼容性
        modules: false, // 保持ES模块以便tree shaking
        // 改为usage模式，按需引入polyfill
        useBuiltIns: 'usage', // 根据代码使用情况自动引入polyfill
        corejs: {
          version: 3, // 使用core-js@3
          proposals: true
        },
        // 明确指定目标浏览器，确保ES6+语法被转译
        targets: {
          ios: '9',
          android: '4.4',
          chrome: '30',
          safari: '9'
        },
        // 强制转译所有ES6+语法，确保兼容性
        forceAllTransforms: true, // 强制转译所有语法
        // 调试模式，显示转换信息
        debug: false
      }
    ]
  ],
  plugins: [
    '@babel/plugin-proposal-optional-chaining',
    [
      '@babel/plugin-transform-runtime',
      {
        corejs: false, // 解决 helper 函数重复引入
        helpers: true,
        regenerator: true,
        useESModules: false // 确保兼容性
      }
    ]
  ]
};
