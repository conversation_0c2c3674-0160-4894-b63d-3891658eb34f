/*
 * @Author: chenjm
 * @Date: 2025-05-26 14:45:21
 * @LastEditors: chenjm
 * @LastEditTime: 2025-05-29 16:29:38
 * @Description:
 */
module.exports = {
  presets: [
    [
      '@babel/preset-env',
      {
        modules: false,
        useBuiltIns: 'usage',
        corejs: {
          version: 3,
          proposals: true
        },
        // 设置非常低的版本以强制转译所有ES6+语法
        targets: {
          ie: '9',
          ios: '8',
          android: '4.1',
          chrome: '25',
          safari: '8',
          firefox: '20'
        },
        // 强制转译所有ES6+语法
        forceAllTransforms: true,
        debug: false
      }
    ]
  ],
  plugins: [
    '@babel/plugin-proposal-optional-chaining',
    [
      '@babel/plugin-transform-runtime',
      {
        corejs: false,
        helpers: true,
        regenerator: true,
        useESModules: false
      }
    ]
  ]
};
