module.exports = {
  presets: [
    [
      '@babel/preset-env',
      {
        //modules: false, // 对ES6的模块文件不做转化，以便使用tree shaking、sideEffects等
        useBuiltIns: 'entry', // browserslist环境不支持的所有垫片都导入
        corejs: {
          version: 3, // 使用core-js@3
          proposals: true
        }
      }
    ]
  ],
  plugins: [
    '@babel/plugin-proposal-optional-chaining',
    [
      '@babel/plugin-transform-runtime',
      {
        corejs: false // 解决 helper 函数重复引入
      }
    ]
  ]
};
