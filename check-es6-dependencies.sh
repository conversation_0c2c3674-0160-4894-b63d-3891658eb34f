#!/bin/bash

# ES6+语法依赖检查脚本
# 用于识别node_modules中包含ES6+语法的第三方依赖包

echo "🔍 检查第三方依赖中的ES6+语法"
echo "================================"

# 从package.json中提取主要依赖
DEPENDENCIES=(
    "axios"
    "better-scroll" 
    "bignumber.js"
    "clipboard"
    "crypto-js"
    "element-ui"
    "exif-js"
    "lodash"
    "moment"
    "smooth-scroll-into-view-if-needed"
    "thinkive-hui"
    "thinkive-hvue"
    "urijs"
    "vant"
    "vee-validate"
    "video.js"
    "vue-fragment"
    "vue-pdf"
    "vue-router"
    "vuex"
    "@common/formily-parser-h5"
    "@thinkive/axios"
)

echo "📋 检查的依赖包数量: ${#DEPENDENCIES[@]}"
echo ""

# 检查函数
check_es6_syntax() {
    local package_name=$1
    local package_path="node_modules/$package_name"
    
    if [ ! -d "$package_path" ]; then
        echo "⚠️  $package_name: 目录不存在"
        return
    fi
    
    echo "🔍 检查 $package_name..."
    
    # 检查箭头函数
    local arrow_count=$(find "$package_path" -name "*.js" -not -path "*/test/*" -not -path "*/tests/*" -not -path "*/spec/*" 2>/dev/null | xargs grep -l "=>" 2>/dev/null | wc -l | tr -d ' ')
    
    # 检查const/let
    local const_count=$(find "$package_path" -name "*.js" -not -path "*/test/*" -not -path "*/tests/*" -not -path "*/spec/*" 2>/dev/null | xargs grep -l "const " 2>/dev/null | wc -l | tr -d ' ')
    local let_count=$(find "$package_path" -name "*.js" -not -path "*/test/*" -not -path "*/tests/*" -not -path "*/spec/*" 2>/dev/null | xargs grep -l "let " 2>/dev/null | wc -l | tr -d ' ')
    
    # 检查模板字符串
    local template_count=$(find "$package_path" -name "*.js" -not -path "*/test/*" -not -path "*/tests/*" -not -path "*/spec/*" 2>/dev/null | xargs grep -l "\`" 2>/dev/null | wc -l | tr -d ' ')
    
    # 检查class语法
    local class_count=$(find "$package_path" -name "*.js" -not -path "*/test/*" -not -path "*/tests/*" -not -path "*/spec/*" 2>/dev/null | xargs grep -l "class " 2>/dev/null | wc -l | tr -d ' ')
    
    local total_es6=$((arrow_count + const_count + let_count + template_count + class_count))
    
    if [ $total_es6 -gt 0 ]; then
        echo "❌ $package_name: 发现ES6+语法"
        echo "   - 箭头函数文件: $arrow_count"
        echo "   - const声明文件: $const_count" 
        echo "   - let声明文件: $let_count"
        echo "   - 模板字符串文件: $template_count"
        echo "   - class语法文件: $class_count"
        echo "   - 总计ES6+文件: $total_es6"
        
        # 添加到需要转译的列表
        echo "$package_name" >> /tmp/transpile_list.txt
    else
        echo "✅ $package_name: 无ES6+语法"
    fi
    echo ""
}

# 清空临时文件
> /tmp/transpile_list.txt

# 检查所有依赖
for dep in "${DEPENDENCIES[@]}"; do
    check_es6_syntax "$dep"
done

echo "📊 检查结果汇总"
echo "================"

if [ -s /tmp/transpile_list.txt ]; then
    echo "❌ 需要转译的依赖包:"
    cat /tmp/transpile_list.txt | sort | uniq | while read package; do
        echo "   - $package"
    done
    
    echo ""
    echo "📝 建议的transpileDependencies配置:"
    echo "transpileDependencies: ["
    echo "  // 自定义库"
    echo "  'thinkive-hvue',"
    echo "  '_thinkive-hvue',"
    echo "  'thinkive-hui',"
    echo "  '@common/formily-parser-h5',"
    echo "  '@thinkive/axios',"
    echo "  // 包含ES6+语法的第三方库"
    cat /tmp/transpile_list.txt | sort | uniq | while read package; do
        if [[ "$package" != "thinkive-"* && "$package" != "@"* ]]; then
            echo "  '$package',"
        fi
    done
    echo "]"
else
    echo "✅ 所有依赖包都不包含ES6+语法"
fi

# 清理临时文件
rm -f /tmp/transpile_list.txt

echo ""
echo "🎯 检查完成!"
