{"name": "bc-h5-view", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "test:unit": "vue-cli-service test:unit", "clean": "rimraf node_modules"}, "dependencies": {"@common/formily-parser-h5": "0.3.17", "@thinkive/axios": "^2.1.2-beta.3", "axios": "1.7.9", "better-scroll": "^1.15.2", "bignumber.js": "^9.0.2", "clipboard": "^2.0.11", "core-js": "^3.21.1", "crypto-js": "^4.0.0", "element-ui": "^2.15.6", "exif-js": "^2.3.0", "lodash": "^4.17.21", "moment": "^2.29.4", "regenerator-runtime": "^0.13.9", "smooth-scroll-into-view-if-needed": "^2.0.2", "thinkive-hui": "^1.0.26", "thinkive-hvue": "1.2.8", "urijs": "^1.19.1", "vant": "^2.12.47", "vee-validate": "^2.0.0", "video.js": "^8.10.0", "vue": "~2.6.12", "vue-fragment": "^1.6.0", "vue-pdf": "4.3.0", "vue-router": "^3.4.8", "vuex": "^3.5.1"}, "devDependencies": {"@babel/core": "^7.18.9", "@babel/eslint-parser": "^7.18.9", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-transform-runtime": "^7.18.9", "@babel/preset-env": "^7.18.9", "@vue/cli-plugin-babel": "^3.12.0", "@vue/cli-plugin-eslint": "^3.12.0", "@vue/cli-plugin-unit-mocha": "^3.12.0", "@vue/cli-service": "^3.12.0", "@vue/test-utils": "1.0.0-beta.29", "babel-loader": "^8.2.5", "chai": "^4.1.2", "compression-webpack-plugin": "^3.1.0", "dayjs": "^1.9.4", "eslint": "^5.16.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-vue": "^5.0.0", "filemanager-webpack-plugin": "^2.0.5", "less": "^3.12.2", "less-loader": "^7.1.0", "sass": "^1.18.0", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "^2.1.5", "style-resources-loader": "^1.5.0", "vconsole": "^3.11.2", "vue-cli-plugin-style-resources-loader": "^0.1.5", "vue-template-compiler": "~2.6.12", "webpack-bundle-analyzer": "^3.9.0", "xss": "^1.0.13"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}}