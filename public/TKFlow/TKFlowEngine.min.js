"use strict";var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},TKFlowEngine={};+function(t){function e(t,e,r){null!=t&&("number"==typeof t?this.fromNumber(t,e,r):null==e&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,e))}function r(){return new e(null)}function n(t,e,r,n,o,i){for(;--i>=0;){var s=e*this[t++]+r[n]+o;o=Math.floor(s/67108864),r[n++]=67108863&s}return o}function o(t,e,r,n,o,i){for(var s=32767&e,a=e>>15;--i>=0;){var h=32767&this[t],u=this[t++]>>15,f=a*h+u*s;h=s*h+((32767&f)<<15)+r[n]+(1073741823&o),o=(h>>>30)+(f>>>15)+a*u+(o>>>30),r[n++]=1073741823&h}return o}function i(t,e,r,n,o,i){for(var s=16383&e,a=e>>14;--i>=0;){var h=16383&this[t],u=this[t++]>>14,f=a*h+u*s;h=s*h+((16383&f)<<14)+r[n]+o,o=(h>>28)+(f>>14)+a*u,r[n++]=268435455&h}return o}function s(t){return re.charAt(t)}function a(t,e){var r=ne[t.charCodeAt(e)];return null==r?-1:r}function h(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s}function u(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0}function f(t){var e=r();return e.fromInt(t),e}function c(t,r){var n;if(16==r)n=4;else if(8==r)n=3;else if(256==r)n=8;else if(2==r)n=1;else if(32==r)n=5;else{if(4!=r)return void this.fromRadix(t,r);n=2}this.t=0,this.s=0;for(var o=t.length,i=!1,s=0;--o>=0;){var h=8==n?255&t[o]:a(t,o);h<0?"-"==t.charAt(o)&&(i=!0):(i=!1,0==s?this[this.t++]=h:s+n>this.DB?(this[this.t-1]|=(h&(1<<this.DB-s)-1)<<s,this[this.t++]=h>>this.DB-s):this[this.t-1]|=h<<s,(s+=n)>=this.DB&&(s-=this.DB))}8==n&&0!=(128&t[0])&&(this.s=-1,s>0&&(this[this.t-1]|=(1<<this.DB-s)-1<<s)),this.clamp(),i&&e.ZERO.subTo(this,this)}function l(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t}function p(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var r,n=(1<<e)-1,o=!1,i="",a=this.t,h=this.DB-a*this.DB%e;if(a-- >0)for(h<this.DB&&(r=this[a]>>h)>0&&(o=!0,i=s(r));a>=0;)h<e?(r=(this[a]&(1<<h)-1)<<e-h,r|=this[--a]>>(h+=this.DB-e)):(r=this[a]>>(h-=e)&n,h<=0&&(h+=this.DB,--a)),r>0&&(o=!0),o&&(i+=s(r));return o?i:"0"}function g(){var t=r();return e.ZERO.subTo(this,t),t}function d(){return this.s<0?this.negate():this}function v(t){var e=this.s-t.s;if(0!=e)return e;var r=this.t;if(0!=(e=r-t.t))return this.s<0?-e:e;for(;--r>=0;)if(0!=(e=this[r]-t[r]))return e;return 0}function m(t){var e,r=1;return 0!=(e=t>>>16)&&(t=e,r+=16),0!=(e=t>>8)&&(t=e,r+=8),0!=(e=t>>4)&&(t=e,r+=4),0!=(e=t>>2)&&(t=e,r+=2),0!=(e=t>>1)&&(t=e,r+=1),r}function y(){return this.t<=0?0:this.DB*(this.t-1)+m(this[this.t-1]^this.s&this.DM)}function w(t,e){var r;for(r=this.t-1;r>=0;--r)e[r+t]=this[r];for(r=t-1;r>=0;--r)e[r]=0;e.t=this.t+t,e.s=this.s}function S(t,e){for(var r=t;r<this.t;++r)e[r-t]=this[r];e.t=Math.max(this.t-t,0),e.s=this.s}function T(t,e){var r,n=t%this.DB,o=this.DB-n,i=(1<<o)-1,s=Math.floor(t/this.DB),a=this.s<<n&this.DM;for(r=this.t-1;r>=0;--r)e[r+s+1]=this[r]>>o|a,a=(this[r]&i)<<n;for(r=s-1;r>=0;--r)e[r]=0;e[s]=a,e.t=this.t+s+1,e.s=this.s,e.clamp()}function b(t,e){e.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)return void(e.t=0);var n=t%this.DB,o=this.DB-n,i=(1<<n)-1;e[0]=this[r]>>n;for(var s=r+1;s<this.t;++s)e[s-r-1]|=(this[s]&i)<<o,e[s-r]=this[s]>>n;n>0&&(e[this.t-r-1]|=(this.s&i)<<o),e.t=this.t-r,e.clamp()}function O(t,e){for(var r=0,n=0,o=Math.min(t.t,this.t);r<o;)n+=this[r]-t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n-=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n-=t[r],e[r++]=n&this.DM,n>>=this.DB;n-=t.s}e.s=n<0?-1:0,n<-1?e[r++]=this.DV+n:n>0&&(e[r++]=n),e.t=r,e.clamp()}function x(t,r){var n=this.abs(),o=t.abs(),i=n.t;for(r.t=i+o.t;--i>=0;)r[i]=0;for(i=0;i<o.t;++i)r[i+n.t]=n.am(0,o[i],r,i,0,n.t);r.s=0,r.clamp(),this.s!=t.s&&e.ZERO.subTo(r,r)}function B(t){for(var e=this.abs(),r=t.t=2*e.t;--r>=0;)t[r]=0;for(r=0;r<e.t-1;++r){var n=e.am(r,e[r],t,2*r,0,1);(t[r+e.t]+=e.am(r+1,2*e[r],t,2*r+1,n,e.t-r-1))>=e.DV&&(t[r+e.t]-=e.DV,t[r+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(r,e[r],t,2*r,0,1)),t.s=0,t.clamp()}function N(t,n,o){var i=t.abs();if(!(i.t<=0)){var s=this.abs();if(s.t<i.t)return null!=n&&n.fromInt(0),void(null!=o&&this.copyTo(o));null==o&&(o=r());var a=r(),h=this.s,u=t.s,f=this.DB-m(i[i.t-1]);f>0?(i.lShiftTo(f,a),s.lShiftTo(f,o)):(i.copyTo(a),s.copyTo(o));var c=a.t,l=a[c-1];if(0!=l){var p=l*(1<<this.F1)+(c>1?a[c-2]>>this.F2:0),g=this.FV/p,d=(1<<this.F1)/p,v=1<<this.F2,y=o.t,w=y-c,S=null==n?r():n;for(a.dlShiftTo(w,S),o.compareTo(S)>=0&&(o[o.t++]=1,o.subTo(S,o)),e.ONE.dlShiftTo(c,S),S.subTo(a,a);a.t<c;)a[a.t++]=0;for(;--w>=0;){var T=o[--y]==l?this.DM:Math.floor(o[y]*g+(o[y-1]+v)*d);if((o[y]+=a.am(0,T,o,w,0,c))<T)for(a.dlShiftTo(w,S),o.subTo(S,o);o[y]<--T;)o.subTo(S,o)}null!=n&&(o.drShiftTo(c,n),h!=u&&e.ZERO.subTo(n,n)),o.t=c,o.clamp(),f>0&&o.rShiftTo(f,o),h<0&&e.ZERO.subTo(o,o)}}}function D(t){var n=r();return this.abs().divRemTo(t,null,n),this.s<0&&n.compareTo(e.ZERO)>0&&t.subTo(n,n),n}function E(t){this.m=t}function I(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t}function A(t){return t}function F(t){t.divRemTo(this.m,null,t)}function _(t,e,r){t.multiplyTo(e,r),this.reduce(r)}function M(t,e){t.squareTo(e),this.reduce(e)}function R(){if(this.t<1)return 0;var t=this[0];if(0==(1&t))return 0;var e=3&t;return e=e*(2-(15&t)*e)&15,e=e*(2-(255&t)*e)&255,e=e*(2-((65535&t)*e&65535))&65535,e=e*(2-t*e%this.DV)%this.DV,e>0?this.DV-e:-e}function C(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}function U(t){var n=r();return t.abs().dlShiftTo(this.m.t,n),n.divRemTo(this.m,null,n),t.s<0&&n.compareTo(e.ZERO)>0&&this.m.subTo(n,n),n}function k(t){var e=r();return t.copyTo(e),this.reduce(e),e}function K(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var r=32767&t[e],n=r*this.mpl+((r*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(r=e+this.m.t,t[r]+=this.m.am(0,n,t,e,0,this.m.t);t[r]>=t.DV;)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)}function P(t,e){t.squareTo(e),this.reduce(e)}function L(t,e,r){t.multiplyTo(e,r),this.reduce(r)}function q(){return 0==(this.t>0?1&this[0]:this.s)}function J(t,n){if(t>4294967295||t<1)return e.ONE;var o=r(),i=r(),s=n.convert(this),a=m(t)-1;for(s.copyTo(o);--a>=0;)if(n.sqrTo(o,i),(t&1<<a)>0)n.mulTo(i,s,o);else{var h=o;o=i,i=h}return n.revert(o)}function j(t,e){var r;return r=t<256||e.isEven()?new E(e):new C(e),this.exp(t,r)}function H(){var t=r();return this.copyTo(t),t}function G(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]}function V(){return 0==this.t?this.s:this[0]<<24>>24}function W(){return 0==this.t?this.s:this[0]<<16>>16}function z(t){return Math.floor(Math.LN2*this.DB/Math.log(t))}function X(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1}function $(t){if(null==t&&(t=10),0==this.signum()||t<2||t>36)return"0";var e=this.chunkSize(t),n=Math.pow(t,e),o=f(n),i=r(),s=r(),a="";for(this.divRemTo(o,i,s);i.signum()>0;)a=(n+s.intValue()).toString(t).substr(1)+a,i.divRemTo(o,i,s);return s.intValue().toString(t)+a}function Z(t,r){this.fromInt(0),null==r&&(r=10);for(var n=this.chunkSize(r),o=Math.pow(r,n),i=!1,s=0,h=0,u=0;u<t.length;++u){var f=a(t,u);f<0?"-"==t.charAt(u)&&0==this.signum()&&(i=!0):(h=r*h+f,++s>=n&&(this.dMultiply(o),this.dAddOffset(h,0),s=0,h=0))}s>0&&(this.dMultiply(Math.pow(r,s)),this.dAddOffset(h,0)),i&&e.ZERO.subTo(this,this)}function Y(t,r,n){if("number"==typeof r)if(t<2)this.fromInt(1);else for(this.fromNumber(t,n),this.testBit(t-1)||this.bitwiseTo(e.ONE.shiftLeft(t-1),st,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(r);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(e.ONE.shiftLeft(t-1),this);else{var o=new Array,i=7&t;o.length=1+(t>>3),r.nextBytes(o),i>0?o[0]&=(1<<i)-1:o[0]=0,this.fromString(o,256)}}function Q(){var t=this.t,e=new Array;e[0]=this.s;var r,n=this.DB-t*this.DB%8,o=0;if(t-- >0)for(n<this.DB&&(r=this[t]>>n)!=(this.s&this.DM)>>n&&(e[o++]=r|this.s<<this.DB-n);t>=0;)n<8?(r=(this[t]&(1<<n)-1)<<8-n,r|=this[--t]>>(n+=this.DB-8)):(r=this[t]>>(n-=8)&255,n<=0&&(n+=this.DB,--t)),0!=(128&r)&&(r|=-256),0==o&&(128&this.s)!=(128&r)&&++o,(o>0||r!=this.s)&&(e[o++]=r);return e}function tt(t){return 0==this.compareTo(t)}function et(t){return this.compareTo(t)<0?this:t}function rt(t){return this.compareTo(t)>0?this:t}function nt(t,e,r){var n,o,i=Math.min(t.t,this.t);for(n=0;n<i;++n)r[n]=e(this[n],t[n]);if(t.t<this.t){for(o=t.s&this.DM,n=i;n<this.t;++n)r[n]=e(this[n],o);r.t=this.t}else{for(o=this.s&this.DM,n=i;n<t.t;++n)r[n]=e(o,t[n]);r.t=t.t}r.s=e(this.s,t.s),r.clamp()}function ot(t,e){return t&e}function it(t){var e=r();return this.bitwiseTo(t,ot,e),e}function st(t,e){return t|e}function at(t){var e=r();return this.bitwiseTo(t,st,e),e}function ht(t,e){return t^e}function ut(t){var e=r();return this.bitwiseTo(t,ht,e),e}function ft(t,e){return t&~e}function ct(t){var e=r();return this.bitwiseTo(t,ft,e),e}function lt(){for(var t=r(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t}function pt(t){var e=r();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e}function gt(t){var e=r();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e}function dt(t){if(0==t)return-1;var e=0;return 0==(65535&t)&&(t>>=16,e+=16),0==(255&t)&&(t>>=8,e+=8),0==(15&t)&&(t>>=4,e+=4),0==(3&t)&&(t>>=2,e+=2),0==(1&t)&&++e,e}function vt(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+dt(this[t]);return this.s<0?this.t*this.DB:-1}function mt(t){for(var e=0;0!=t;)t&=t-1,++e;return e}function yt(){for(var t=0,e=this.s&this.DM,r=0;r<this.t;++r)t+=mt(this[r]^e);return t}function wt(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:0!=(this[e]&1<<t%this.DB)}function St(t,r){var n=e.ONE.shiftLeft(t);return this.bitwiseTo(n,r,n),n}function Tt(t){return this.changeBit(t,st)}function bt(t){return this.changeBit(t,ft)}function Ot(t){return this.changeBit(t,ht)}function xt(t,e){for(var r=0,n=0,o=Math.min(t.t,this.t);r<o;)n+=this[r]+t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n+=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n+=t[r],e[r++]=n&this.DM,n>>=this.DB;n+=t.s}e.s=n<0?-1:0,n>0?e[r++]=n:n<-1&&(e[r++]=this.DV+n),e.t=r,e.clamp()}function Bt(t){var e=r();return this.addTo(t,e),e}function Nt(t){var e=r();return this.subTo(t,e),e}function Dt(t){var e=r();return this.multiplyTo(t,e),e}function Et(){var t=r();return this.squareTo(t),t}function It(t){var e=r();return this.divRemTo(t,e,null),e}function At(t){var e=r();return this.divRemTo(t,null,e),e}function Ft(t){var e=r(),n=r();return this.divRemTo(t,e,n),new Array(e,n)}function _t(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()}function Mt(t,e){if(0!=t){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}}function Rt(){}function Ct(t){return t}function Ut(t,e,r){t.multiplyTo(e,r)}function kt(t,e){t.squareTo(e)}function Kt(t){return this.exp(t,new Rt)}function Pt(t,e,r){var n=Math.min(this.t+t.t,e);for(r.s=0,r.t=n;n>0;)r[--n]=0;var o;for(o=r.t-this.t;n<o;++n)r[n+this.t]=this.am(0,t[n],r,n,0,this.t);for(o=Math.min(t.t,e);n<o;++n)this.am(0,t[n],r,n,0,e-n);r.clamp()}function Lt(t,e,r){--e;var n=r.t=this.t+t.t-e;for(r.s=0;--n>=0;)r[n]=0;for(n=Math.max(e-this.t,0);n<t.t;++n)r[this.t+n-e]=this.am(e-n,t[n],r,0,0,this.t+n-e);r.clamp(),r.drShiftTo(1,r)}function qt(t){this.r2=r(),this.q3=r(),e.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t),this.m=t}function Jt(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=r();return t.copyTo(e),this.reduce(e),e}function jt(t){return t}function Ht(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)}function Gt(t,e){t.squareTo(e),this.reduce(e)}function Vt(t,e,r){t.multiplyTo(e,r),this.reduce(r)}function Wt(t,e){var n,o,i=t.bitLength(),s=f(1);if(i<=0)return s;n=i<18?1:i<48?3:i<144?4:i<768?5:6,o=i<8?new E(e):e.isEven()?new qt(e):new C(e);var a=new Array,h=3,u=n-1,c=(1<<n)-1;if(a[1]=o.convert(this),n>1){var l=r();for(o.sqrTo(a[1],l);h<=c;)a[h]=r(),o.mulTo(l,a[h-2],a[h]),h+=2}var p,g,d=t.t-1,v=!0,y=r();for(i=m(t[d])-1;d>=0;){for(i>=u?p=t[d]>>i-u&c:(p=(t[d]&(1<<i+1)-1)<<u-i,d>0&&(p|=t[d-1]>>this.DB+i-u)),h=n;0==(1&p);)p>>=1,--h;if((i-=h)<0&&(i+=this.DB,--d),v)a[p].copyTo(s),v=!1;else{for(;h>1;)o.sqrTo(s,y),o.sqrTo(y,s),h-=2;h>0?o.sqrTo(s,y):(g=s,s=y,y=g),o.mulTo(y,a[p],s)}for(;d>=0&&0==(t[d]&1<<i);)o.sqrTo(s,y),g=s,s=y,y=g,--i<0&&(i=this.DB-1,--d)}return o.revert(s)}function zt(t){var e=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(e.compareTo(r)<0){var n=e;e=r,r=n}var o=e.getLowestSetBit(),i=r.getLowestSetBit();if(i<0)return e;for(o<i&&(i=o),i>0&&(e.rShiftTo(i,e),r.rShiftTo(i,r));e.signum()>0;)(o=e.getLowestSetBit())>0&&e.rShiftTo(o,e),(o=r.getLowestSetBit())>0&&r.rShiftTo(o,r),e.compareTo(r)>=0?(e.subTo(r,e),e.rShiftTo(1,e)):(r.subTo(e,r),r.rShiftTo(1,r));return i>0&&r.lShiftTo(i,r),r}function Xt(t){if(t<=0)return 0;var e=this.DV%t,r=this.s<0?t-1:0;if(this.t>0)if(0==e)r=this[0]%t;else for(var n=this.t-1;n>=0;--n)r=(e*r+this[n])%t;return r}function $t(t){var r=t.isEven();if(this.isEven()&&r||0==t.signum())return e.ZERO;for(var n=t.clone(),o=this.clone(),i=f(1),s=f(0),a=f(0),h=f(1);0!=n.signum();){for(;n.isEven();)n.rShiftTo(1,n),r?(i.isEven()&&s.isEven()||(i.addTo(this,i),s.subTo(t,s)),i.rShiftTo(1,i)):s.isEven()||s.subTo(t,s),s.rShiftTo(1,s);for(;o.isEven();)o.rShiftTo(1,o),r?(a.isEven()&&h.isEven()||(a.addTo(this,a),h.subTo(t,h)),a.rShiftTo(1,a)):h.isEven()||h.subTo(t,h),h.rShiftTo(1,h);n.compareTo(o)>=0?(n.subTo(o,n),r&&i.subTo(a,i),s.subTo(h,s)):(o.subTo(n,o),r&&a.subTo(i,a),h.subTo(s,h))}return 0!=o.compareTo(e.ONE)?e.ZERO:h.compareTo(t)>=0?h.subtract(t):h.signum()<0?(h.addTo(t,h),h.signum()<0?h.add(t):h):h}function Zt(t){var e,r=this.abs();if(1==r.t&&r[0]<=oe[oe.length-1]){for(e=0;e<oe.length;++e)if(r[0]==oe[e])return!0;return!1}if(r.isEven())return!1;for(e=1;e<oe.length;){for(var n=oe[e],o=e+1;o<oe.length&&n<ie;)n*=oe[o++];for(n=r.modInt(n);e<o;)if(n%oe[e++]==0)return!1}return r.millerRabin(t)}function Yt(t){var n=this.subtract(e.ONE),o=n.getLowestSetBit();if(o<=0)return!1;var i=n.shiftRight(o);(t=t+1>>1)>oe.length&&(t=oe.length);for(var s=r(),a=0;a<t;++a){s.fromInt(oe[Math.floor(Math.random()*oe.length)]);var h=s.modPow(i,this);if(0!=h.compareTo(e.ONE)&&0!=h.compareTo(n)){for(var u=1;u++<o&&0!=h.compareTo(n);)if(h=h.modPowInt(2,this),0==h.compareTo(e.ONE))return!1;if(0!=h.compareTo(n))return!1}}return!0}var Qt;"Microsoft Internet Explorer"==navigator.appName?(e.prototype.am=o,Qt=30):"Netscape"!=navigator.appName?(e.prototype.am=n,Qt=26):(e.prototype.am=i,Qt=28),e.prototype.DB=Qt,e.prototype.DM=(1<<Qt)-1,e.prototype.DV=1<<Qt;e.prototype.FV=Math.pow(2,52),e.prototype.F1=52-Qt,e.prototype.F2=2*Qt-52;var te,ee,re="0123456789abcdefghijklmnopqrstuvwxyz",ne=new Array;for(te="0".charCodeAt(0),ee=0;ee<=9;++ee)ne[te++]=ee;for(te="a".charCodeAt(0),ee=10;ee<36;++ee)ne[te++]=ee;for(te="A".charCodeAt(0),ee=10;ee<36;++ee)ne[te++]=ee;E.prototype.convert=I,E.prototype.revert=A,E.prototype.reduce=F,E.prototype.mulTo=_,E.prototype.sqrTo=M,C.prototype.convert=U,C.prototype.revert=k,C.prototype.reduce=K,C.prototype.mulTo=L,C.prototype.sqrTo=P,e.prototype.copyTo=h,e.prototype.fromInt=u,e.prototype.fromString=c,e.prototype.clamp=l,e.prototype.dlShiftTo=w,e.prototype.drShiftTo=S,e.prototype.lShiftTo=T,e.prototype.rShiftTo=b,e.prototype.subTo=O,e.prototype.multiplyTo=x,e.prototype.squareTo=B,e.prototype.divRemTo=N,e.prototype.invDigit=R,e.prototype.isEven=q,e.prototype.exp=J,e.prototype.toString=p,e.prototype.negate=g,e.prototype.abs=d,e.prototype.compareTo=v,e.prototype.bitLength=y,e.prototype.mod=D,e.prototype.modPowInt=j,e.ZERO=f(0),e.ONE=f(1),Rt.prototype.convert=Ct,Rt.prototype.revert=Ct,Rt.prototype.mulTo=Ut,Rt.prototype.sqrTo=kt,qt.prototype.convert=Jt,qt.prototype.revert=jt,qt.prototype.reduce=Ht,qt.prototype.mulTo=Vt,qt.prototype.sqrTo=Gt;var oe=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],ie=(1<<26)/oe[oe.length-1];e.prototype.chunkSize=z,e.prototype.toRadix=$,e.prototype.fromRadix=Z,e.prototype.fromNumber=Y,e.prototype.bitwiseTo=nt,e.prototype.changeBit=St,e.prototype.addTo=xt,e.prototype.dMultiply=_t,e.prototype.dAddOffset=Mt,e.prototype.multiplyLowerTo=Pt,e.prototype.multiplyUpperTo=Lt,e.prototype.modInt=Xt,e.prototype.millerRabin=Yt,e.prototype.clone=H,e.prototype.intValue=G,e.prototype.byteValue=V,e.prototype.shortValue=W,e.prototype.signum=X,e.prototype.toByteArray=Q,e.prototype.equals=tt,e.prototype.min=et,e.prototype.max=rt,e.prototype.and=it,e.prototype.or=at,e.prototype.xor=ut,e.prototype.andNot=ct,e.prototype.not=lt,e.prototype.shiftLeft=pt,e.prototype.shiftRight=gt,e.prototype.getLowestSetBit=vt,e.prototype.bitCount=yt,e.prototype.testBit=wt,e.prototype.setBit=Tt,e.prototype.clearBit=bt,e.prototype.flipBit=Ot,e.prototype.add=Bt,e.prototype.subtract=Nt,e.prototype.multiply=Dt,e.prototype.divide=It,e.prototype.remainder=At,e.prototype.divideAndRemainder=Ft,e.prototype.modPow=Wt,e.prototype.modInverse=$t,e.prototype.pow=Kt,e.prototype.gcd=zt,e.prototype.isProbablePrime=Zt,e.prototype.square=Et,window.BigInteger=e}(),function(t){function e(){this.BYTE_LENGTH=64,this.xBuf=new Array,this.xBufOff=0,this.byteCount=0,this.DIGEST_LENGTH=32,this.v0=[1937774191,1226093241,388252375,-628488704,-1452330820,372324522,-477237683,-1325724082],this.v=new Array(8),this.v_=new Array(8),this.X0=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.X=new Array(68),this.xOff=0,this.T_00_15=2043430169,this.T_16_63=2055708042,arguments.length>0?this.InitDigest(arguments[0]):this.Init()}function r(t,e){return t^e}function n(t,e,r){return r=r||32,e%=r,t<<e|t>>>r-e}function o(t){return E[(4026531840&t)>>>28][(251658240&t)>>>24]<<24|E[(15728640&t)>>>20][(983040&t)>>>16]<<16|E[(61440&t)>>>12][(3840&t)>>>8]<<8|E[(240&t)>>>4][(15&t)>>>0]<<0}function i(t){return t=o(t),r(r(r(t,n(t,2)),r(n(t,10),n(t,18))),n(t,24))}function s(t){return t=o(t),r(t,r(n(t,13),n(t,23)))}function a(t){var e=new Array,n=new Array;e[0]=r(t[0],A[0]),e[1]=r(t[1],A[1]),e[2]=r(t[2],A[2]),e[3]=r(t[3],A[3]);for(var o=0;o<32;o++)e[o+4]=r(e[o],s(r(r(e[o+1],e[o+2]),r(e[o+3],I[o])))),n[o]=e[o+4].toString(16);return n}function h(t,e,n){n=n||"cbc";for(var o=e,s=t,h=a(o),u=0;u<32;u++)s[u+4]=r(s[u],i(r(r(s[u+1],s[u+2]),r(s[u+3],parseInt(h[u],16)))));return new Array(s[35].toString(16),s[34].toString(16),s[33].toString(16),s[32].toString(16))}function u(t,e,n){n=n||"cbc";for(var o=e,s=t,h=a(o),u=new Array,f=h.length-1;f>=0;f--)u[h.length-1-f]=h[f];for(var f=0;f<32;f++)s[f+4]=r(s[f],i(r(r(s[f+1],s[f+2]),r(s[f+3],parseInt(u[f],16)))));return new Array(s[35].toString(16),s[34].toString(16),s[33].toString(16),s[32].toString(16))}function f(t,e){if(!t)return"";for(var r=[],n=0;n<t.length;n++){var o=t[n].toString(16);o.length<2&&(o="0"+o),r.push(o)}return e?r.join(""):"0x"+r.join("")}function c(t){var e=t.trim(),r="0x"===e.substr(0,2).toLowerCase()?e.substr(2):e,n=r.length;if(n%2!=0)return"";for(var o,i=[],s=0;s<n;s+=2)o=parseInt(r.substr(s,2),16),i.push(o);return i}function l(t){for(var e=[],r=t.length,n=0;n<r;n+=2)e[e.length]=parseInt(t.substr(n,2),16);return e}function p(t,e){for(;t.length%16!=0||t.length<16;)t=t.concat(c(e));return t}function g(t,e){e||(e=!1);for(var r=[],n=0;n<4;n++)r.push(f(t.slice(4*n,4*(n+1)),e).toString(16));return r}function d(t){for(var e=[],r=0;r<t.length;r++)if("-"==t[r].substring(0,1)){var n="0x"+t[r].substring(1),o=(4294967295-n+1).toString(16);e.push(v(o))}else e.push(v(t[r]));return e}function v(t){for(;t.length<8;)t="0"+t;return t}function m(t,e){var r=B.enc.Utf8.parse(e);r=l(r.toString()),r=p(r,"00"),r.length>16&&(r=r.slice(0,16));var n=g(r),o=B.enc.Utf8.parse(t);o=l(o.toString());var i=16-o.length%16;i=i.toString(16),i.length<2&&(i="0"+i),o="10"==i?o.concat(p([],i)):p(o,i);for(var s=Math.floor(o.length/16),a=[],u=0;u<s;u++){var f=o.slice(16*u,16*(u+1)),c=g(f);a=a.concat(h(c,n))}return b(d(a).join(""))}function y(t){for(var e=[],r=0,n=0;n<2*t.length;n+=2)e[n>>>3]|=parseInt(t[r])<<24-n%8*4,r++;return new B.lib.WordArray.init(e,t.length)}function w(t,e){var r=B.enc.Utf8.parse(e);r=l(r.toString()),r=p(r,"00"),r.length>16&&(r=r.slice(0,16));for(var n=g(r),o=t.length/16,i=[],s=0;s<o;s++){var a=t.slice(16*s,16*(s+1)),h=g(a);i=i.concat(u(h,n))}var f=d(i),c=f.join(""),v=b(c);if(v.length>0){var m=v[v.length-1];m>0&&(v=v.splice(0,v.length-m))}var w=y(v);return B.enc.Utf8.stringify(w)}function S(t,e,n){n=n||e;var o=B.enc.Utf8.parse(e);o=l(o.toString()),o=p(o,"00"),o.length>16&&(o=o.slice(0,16));var i=g(o),s=B.enc.Utf8.parse(n);s=l(s.toString()),s=p(s,"00"),s.length>16&&(s=s.slice(0,16));var a=B.enc.Utf8.parse(t);a=l(a.toString());var u=16-a.length%16;u=u.toString(16),u.length<2&&(u="0"+u),a="10"==u?a.concat(p([],u)):p(a,u);for(var f=Math.floor(a.length/16),c=[],v=0;v<f;v++){for(var m=[],y=a.slice(16*v,16*(v+1)),w=0;w<16;w++)m.push(r(s[w],y[w]));var S=g(m),T=h(S,i);T=d(T);s=b(T.join("")),c=c.concat(s)}return c}function T(t,e,n){n=n||e;var o=B.enc.Utf8.parse(e);o=l(o.toString()),o=p(o,"00"),o.length>16&&(o=o.slice(0,16));var i=g(o),s=B.enc.Utf8.parse(n);s=l(s.toString()),s=p(s,"00"),s.length>16&&(s=s.slice(0,16));for(var a=t.length/16,h=[],f=a-1;f>=0;f--){var c=s;f>0&&(c=t.slice(16*(f-1),16*f));for(var v=t.slice(16*f,16*(f+1)),m=g(v),w=u(m,i),S=d(w),T=S.join(""),O=b(T),x=O.length-1;x>=0;x--)h.push(r(O[x],c[x]))}if(h.reverse(),h.length>0){var N=h[h.length-1];N>0&&(h=h.splice(0,h.length-N))}var D=y(h);return B.enc.Utf8.stringify(D)}function b(t){for(var e=new Array,r=0;r<t.length/2;r++){var n=t.substr(2*r,2);e[r]=parseInt(n,16)}return e}function O(t){for(var e=new Array,r=0,n=0,o=new Array(3),i=new Array(4),s=t.length,a=0;s--;)if(o[r++]=t[a++],3==r){for(i[0]=(252&o[0])>>2,i[1]=((3&o[0])<<4)+((240&o[1])>>4),i[2]=((15&o[1])<<2)+((192&o[2])>>6),i[3]=63&o[2],r=0;r<4;r++)e+=F.charAt(i[r]);r=0}if(r){for(n=r;n<3;n++)o[n]=0;for(i[0]=(252&o[0])>>2,i[1]=((3&o[0])<<4)+((240&o[1])>>4),i[2]=((15&o[1])<<2)+((192&o[2])>>6),i[3]=63&o[2],n=0;n<r+1;n++)e+=F.charAt(i[n]);for(;r++<3;)e+="="}return e}function x(t){var e,r,n,o,i,s,a,h=new Array,u=0,f=t;if(t=t.replace(/[^A-Za-z0-9\+\/\=]/g,""),f!=t&&alert("Warning! Characters outside Base64 range in input string ignored."),t.length%4)return alert("Error: Input length is not a multiple of 4 bytes."),"";for(var c=0;u<t.length;)o=_.indexOf(t.charAt(u++)),i=_.indexOf(t.charAt(u++)),s=_.indexOf(t.charAt(u++)),a=_.indexOf(t.charAt(u++)),e=o<<2|i>>4,r=(15&i)<<4|s>>2,n=(3&s)<<6|a,h[c++]=e,64!=s&&(h[c++]=r),64!=a&&(h[c++]=n);return h}var B=B||function(t,e){var r={},n=r.lib={},o=function(){},i=n.Base={extend:function(t){o.prototype=this;var e=new o;return t&&e.mixIn(t),e.hasOwnProperty("init")||(e.init=function(){e.$super.init.apply(this,arguments)}),e.init.prototype=e,e.$super=this,e},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}},s=n.WordArray=i.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=void 0!=e?e:4*t.length},toString:function(t){return(t||h).stringify(this)},concat:function(t){var e=this.words,r=t.words,n=this.sigBytes;if(t=t.sigBytes,this.clamp(),n%4)for(var o=0;o<t;o++)e[n+o>>>2]|=(r[o>>>2]>>>24-o%4*8&255)<<24-(n+o)%4*8;else if(65535<r.length)for(o=0;o<t;o+=4)e[n+o>>>2]=r[o>>>2];else e.push.apply(e,r);return this.sigBytes+=t,this},clamp:function(){var e=this.words,r=this.sigBytes;e[r>>>2]&=4294967295<<32-r%4*8,e.length=t.ceil(r/4)},clone:function(){var t=i.clone.call(this);return t.words=this.words.slice(0),t},random:function(e){for(var r=[],n=0;n<e;n+=4)r.push(4294967296*t.random()|0);return new s.init(r,e)}}),a=r.enc={},h=a.Hex={stringify:function(t){var e=t.words;t=t.sigBytes;for(var r=[],n=0;n<t;n++){var o=e[n>>>2]>>>24-n%4*8&255;r.push((o>>>4).toString(16)),r.push((15&o).toString(16))}return r.join("")},parse:function(t){for(var e=t.length,r=[],n=0;n<e;n+=2)r[n>>>3]|=parseInt(t.substr(n,2),16)<<24-n%8*4;return new s.init(r,e/2)}},u=a.Latin1={stringify:function(t){var e=t.words;t=t.sigBytes;for(var r=[],n=0;n<t;n++)r.push(String.fromCharCode(e[n>>>2]>>>24-n%4*8&255));return r.join("")},parse:function(t){for(var e=t.length,r=[],n=0;n<e;n++)r[n>>>2]|=(255&t.charCodeAt(n))<<24-n%4*8;return new s.init(r,e)}},f=a.Utf8={stringify:function(t){try{return decodeURIComponent(escape(u.stringify(t)))}catch(t){throw Error("Malformed UTF-8 data")}},parse:function(t){return u.parse(unescape(encodeURIComponent(t)))}},c=n.BufferedBlockAlgorithm=i.extend({reset:function(){this._data=new s.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=f.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(e){var r=this._data,n=r.words,o=r.sigBytes,i=this.blockSize,a=o/(4*i),a=e?t.ceil(a):t.max((0|a)-this._minBufferSize,0);if(e=a*i,o=t.min(4*e,o),e){for(var h=0;h<e;h+=i)this._doProcessBlock(n,h);h=n.splice(0,e),r.sigBytes-=o}return new s.init(h,o)},clone:function(){var t=i.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0});n.Hasher=c.extend({cfg:i.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){c.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){return t&&this._append(t),this._doFinalize()},blockSize:16,_createHelper:function(t){return function(e,r){return new t.init(r).finalize(e)}},_createHmacHelper:function(t){return function(e,r){return new l.HMAC.init(t,r).finalize(e)}}});var l=r.algo={};return r}(Math);!function(){var t=B,e=t.lib.WordArray;t.enc.Base64={stringify:function(t){var e=t.words,r=t.sigBytes,n=this._map;t.clamp(),t=[];for(var o=0;o<r;o+=3)for(var i=(e[o>>>2]>>>24-o%4*8&255)<<16|(e[o+1>>>2]>>>24-(o+1)%4*8&255)<<8|e[o+2>>>2]>>>24-(o+2)%4*8&255,s=0;4>s&&o+.75*s<r;s++)t.push(n.charAt(i>>>6*(3-s)&63));if(e=n.charAt(64))for(;t.length%4;)t.push(e);return t.join("")},parse:function(t){var r=t.length,n=this._map,o=n.charAt(64);o&&-1!=(o=t.indexOf(o))&&(r=o);for(var o=[],i=0,s=0;s<r;s++)if(s%4){var a=n.indexOf(t.charAt(s-1))<<s%4*2,h=n.indexOf(t.charAt(s))>>>6-s%4*2;o[i>>>2]|=(a|h)<<24-i%4*8,i++}return e.create(o,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),function(t){function e(t,e,r,n,o,i,s){return((t=t+(e&r|~e&n)+o+s)<<i|t>>>32-i)+e}function r(t,e,r,n,o,i,s){return((t=t+(e&n|r&~n)+o+s)<<i|t>>>32-i)+e}function n(t,e,r,n,o,i,s){return((t=t+(e^r^n)+o+s)<<i|t>>>32-i)+e}function o(t,e,r,n,o,i,s){return((t=t+(r^(e|~n))+o+s)<<i|t>>>32-i)+e}for(var i=B,s=i.lib,a=s.WordArray,h=s.Hasher,s=i.algo,u=[],f=0;64>f;f++)u[f]=4294967296*t.abs(t.sin(f+1))|0;s=s.MD5=h.extend({_doReset:function(){this._hash=new a.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(t,i){for(var s=0;16>s;s++){var a=i+s,h=t[a];t[a]=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8)}var s=this._hash.words,a=t[i+0],h=t[i+1],f=t[i+2],c=t[i+3],l=t[i+4],p=t[i+5],g=t[i+6],d=t[i+7],v=t[i+8],m=t[i+9],y=t[i+10],w=t[i+11],S=t[i+12],T=t[i+13],b=t[i+14],O=t[i+15],x=s[0],B=s[1],N=s[2],D=s[3],x=e(x,B,N,D,a,7,u[0]),D=e(D,x,B,N,h,12,u[1]),N=e(N,D,x,B,f,17,u[2]),B=e(B,N,D,x,c,22,u[3]),x=e(x,B,N,D,l,7,u[4]),D=e(D,x,B,N,p,12,u[5]),N=e(N,D,x,B,g,17,u[6]),B=e(B,N,D,x,d,22,u[7]),x=e(x,B,N,D,v,7,u[8]),D=e(D,x,B,N,m,12,u[9]),N=e(N,D,x,B,y,17,u[10]),B=e(B,N,D,x,w,22,u[11]),x=e(x,B,N,D,S,7,u[12]),D=e(D,x,B,N,T,12,u[13]),N=e(N,D,x,B,b,17,u[14]),B=e(B,N,D,x,O,22,u[15]),x=r(x,B,N,D,h,5,u[16]),D=r(D,x,B,N,g,9,u[17]),N=r(N,D,x,B,w,14,u[18]),B=r(B,N,D,x,a,20,u[19]),x=r(x,B,N,D,p,5,u[20]),D=r(D,x,B,N,y,9,u[21]),N=r(N,D,x,B,O,14,u[22]),B=r(B,N,D,x,l,20,u[23]),x=r(x,B,N,D,m,5,u[24]),D=r(D,x,B,N,b,9,u[25]),N=r(N,D,x,B,c,14,u[26]),B=r(B,N,D,x,v,20,u[27]),x=r(x,B,N,D,T,5,u[28]),D=r(D,x,B,N,f,9,u[29]),N=r(N,D,x,B,d,14,u[30]),B=r(B,N,D,x,S,20,u[31]),x=n(x,B,N,D,p,4,u[32]),D=n(D,x,B,N,v,11,u[33]),N=n(N,D,x,B,w,16,u[34]),B=n(B,N,D,x,b,23,u[35]),x=n(x,B,N,D,h,4,u[36]),D=n(D,x,B,N,l,11,u[37]),N=n(N,D,x,B,d,16,u[38]),B=n(B,N,D,x,y,23,u[39]),x=n(x,B,N,D,T,4,u[40]),D=n(D,x,B,N,a,11,u[41]),N=n(N,D,x,B,c,16,u[42]),B=n(B,N,D,x,g,23,u[43]),x=n(x,B,N,D,m,4,u[44]),D=n(D,x,B,N,S,11,u[45]),N=n(N,D,x,B,O,16,u[46]),B=n(B,N,D,x,f,23,u[47]),x=o(x,B,N,D,a,6,u[48]),D=o(D,x,B,N,d,10,u[49]),N=o(N,D,x,B,b,15,u[50]),B=o(B,N,D,x,p,21,u[51]),x=o(x,B,N,D,S,6,u[52]),D=o(D,x,B,N,c,10,u[53]),N=o(N,D,x,B,y,15,u[54]),B=o(B,N,D,x,h,21,u[55]),x=o(x,B,N,D,v,6,u[56]),D=o(D,x,B,N,O,10,u[57]),N=o(N,D,x,B,g,15,u[58]),B=o(B,N,D,x,T,21,u[59]),x=o(x,B,N,D,l,6,u[60]),D=o(D,x,B,N,w,10,u[61]),N=o(N,D,x,B,f,15,u[62]),B=o(B,N,D,x,m,21,u[63]);s[0]=s[0]+x|0,s[1]=s[1]+B|0,s[2]=s[2]+N|0,s[3]=s[3]+D|0},_doFinalize:function(){var e=this._data,r=e.words,n=8*this._nDataBytes,o=8*e.sigBytes;r[o>>>5]|=128<<24-o%32;var i=t.floor(n/4294967296);for(r[15+(o+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),r[14+(o+64>>>9<<4)]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),e.sigBytes=4*(r.length+1),this._process(),e=this._hash,r=e.words,n=0;4>n;n++)o=r[n],r[n]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8);return e},clone:function(){var t=h.clone.call(this);return t._hash=this._hash.clone(),t}}),i.MD5=h._createHelper(s),i.HmacMD5=h._createHmacHelper(s)}(Math),function(){var t=B,e=t.lib,r=e.Base,n=e.WordArray,e=t.algo,o=e.EvpKDF=r.extend({cfg:r.extend({keySize:4,hasher:e.MD5,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){for(var r=this.cfg,o=r.hasher.create(),i=n.create(),s=i.words,a=r.keySize,r=r.iterations;s.length<a;){h&&o.update(h);var h=o.update(t).finalize(e);o.reset();for(var u=1;u<r;u++)h=o.finalize(h),o.reset();i.concat(h)}return i.sigBytes=4*a,i}});t.EvpKDF=function(t,e,r){return o.create(r).compute(t,e)}}(),function(){var t=B,e=t.lib,r=e.WordArray,n=e.Hasher,o=t.algo,i=[],s=o.SM3=n.extend({_doReset:function(){this._hash=new r.init([1937774191,1226093241,388252375,-628488704,-1452330820,372324522,-477237683,-1325724082])},_doProcessBlock:function(t,e){for(var r=this._hash.words,n=r[0],o=r[1],s=r[2],a=r[3],h=r[4],u=0;u<80;u++){if(u<16)i[u]=0|t[e+u];else{var f=i[u-3]^i[u-8]^i[u-14]^i[u-16];i[u]=f<<1|f>>>31}var c=(n<<5|n>>>27)+h+i[u];c+=u<20?1518500249+(o&s|~o&a):u<40?1859775393+(o^s^a):u<60?(o&s|o&a|s&a)-1894007588:(o^s^a)-899497514,h=a,a=s,s=o<<30|o>>>2,o=n,n=c}r[0]=r[0]+n|0,r[1]=r[1]+o|0,r[2]=r[2]+s|0,r[3]=r[3]+a|0,r[4]=r[4]+h|0},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;return e[n>>>5]|=128<<24-n%32,e[14+(n+64>>>9<<4)]=Math.floor(r/4294967296),e[15+(n+64>>>9<<4)]=r,t.sigBytes=4*e.length,this._process(),this._hash},clone:function(){var t=n.clone.call(this);return t._hash=this._hash.clone(),t}
});t.SM3=n._createHelper(s),t.HmacSM3=n._createHmacHelper(s)}();var N={minValue:-parseInt("10000000000000000000000000000000",2),maxValue:parseInt("1111111111111111111111111111111",2),parse:function(t){if(t<this.minValue){for(var e=new Number(-t),r=e.toString(2),n=r.substr(r.length-31,31),o="",i=0;i<n.length;i++){var s=n.substr(i,1);o+="0"==s?"1":"0"}var a=parseInt(o,2);return a+1}if(t>this.maxValue){for(var e=Number(t),r=e.toString(2),n=r.substr(r.length-31,31),o="",i=0;i<n.length;i++){var s=n.substr(i,1);o+="0"==s?"1":"0"}var a=parseInt(o,2);return-(a+1)}return t},parseByte:function(t){if(t<0){for(var e=new Number(-t),r=e.toString(2),n=r.substr(r.length-8,8),o="",i=0;i<n.length;i++){o+="0"==n.substr(i,1)?"1":"0"}return parseInt(o,2)+1}if(t>255){var e=Number(t),r=e.toString(2);return parseInt(r.substr(r.length-8,8),2)}return t}};e.prototype={Init:function(){this.xBuf=new Array(4),this.Reset()},InitDigest:function(t){this.xBuf=new Array(t.xBuf.length),Array.Copy(t.xBuf,0,this.xBuf,0,t.xBuf.length),this.xBufOff=t.xBufOff,this.byteCount=t.byteCount,Array.Copy(t.X,0,this.X,0,t.X.length),this.xOff=t.xOff,Array.Copy(t.v,0,this.v,0,t.v.length)},GetDigestSize:function(){return this.DIGEST_LENGTH},Reset:function(){this.byteCount=0,this.xBufOff=0,Array.Clear(this.xBuf,0,this.xBuf.length),Array.Copy(this.v0,0,this.v,0,this.v0.length),this.xOff=0,Array.Copy(this.X0,0,this.X,0,this.X0.length)},GetByteLength:function(){return this.BYTE_LENGTH},ProcessBlock:function(){var t,e=this.X,r=new Array(64);for(t=16;t<68;t++)e[t]=this.P1(e[t-16]^e[t-9]^this.ROTATE(e[t-3],15))^this.ROTATE(e[t-13],7)^e[t-6];for(t=0;t<64;t++)r[t]=e[t]^e[t+4];var n=this.v,o=this.v_;Array.Copy(n,0,o,0,this.v0.length);var i,s,a,h,u;for(t=0;t<16;t++)u=this.ROTATE(o[0],12),i=N.parse(N.parse(u+o[4])+this.ROTATE(this.T_00_15,t)),i=this.ROTATE(i,7),s=i^u,a=N.parse(N.parse(this.FF_00_15(o[0],o[1],o[2])+o[3])+s)+r[t],h=N.parse(N.parse(this.GG_00_15(o[4],o[5],o[6])+o[7])+i)+e[t],o[3]=o[2],o[2]=this.ROTATE(o[1],9),o[1]=o[0],o[0]=a,o[7]=o[6],o[6]=this.ROTATE(o[5],19),o[5]=o[4],o[4]=this.P0(h);for(t=16;t<64;t++)u=this.ROTATE(o[0],12),i=N.parse(N.parse(u+o[4])+this.ROTATE(this.T_16_63,t)),i=this.ROTATE(i,7),s=i^u,a=N.parse(N.parse(this.FF_16_63(o[0],o[1],o[2])+o[3])+s)+r[t],h=N.parse(N.parse(this.GG_16_63(o[4],o[5],o[6])+o[7])+i)+e[t],o[3]=o[2],o[2]=this.ROTATE(o[1],9),o[1]=o[0],o[0]=a,o[7]=o[6],o[6]=this.ROTATE(o[5],19),o[5]=o[4],o[4]=this.P0(h);for(t=0;t<8;t++)n[t]^=N.parse(o[t]);this.xOff=0,Array.Copy(this.X0,0,this.X,0,this.X0.length)},ProcessWord:function(t,e){var r=t[e]<<24;r|=(255&t[++e])<<16,r|=(255&t[++e])<<8,r|=255&t[++e],this.X[this.xOff]=r,16==++this.xOff&&this.ProcessBlock()},ProcessLength:function(t){this.xOff>14&&this.ProcessBlock(),this.X[14]=this.URShiftLong(t,32),this.X[15]=4294967295&t},IntToBigEndian:function(t,e,r){e[r]=255&t>>24,e[++r]=255&t>>16,e[++r]=255&t>>8,e[++r]=255&t>>0},DoFinal:function(t,e){this.Finish();for(var r=0;r<8;r++)this.IntToBigEndian(this.v[r],t,e+4*r);return this.Reset(),this.DIGEST_LENGTH},Update:function(t){this.xBuf[this.xBufOff++]=t,this.xBufOff==this.xBuf.length&&(this.ProcessWord(this.xBuf,0),this.xBufOff=0),this.byteCount++},BlockUpdate:function(t,e,r){for(;0!=this.xBufOff&&r>0;)this.Update(t[e]),e++,r--;for(;r>this.xBuf.length;)this.ProcessWord(t,e),e+=this.xBuf.length,r-=this.xBuf.length,this.byteCount+=this.xBuf.length;for(;r>0;)this.Update(t[e]),e++,r--},Finish:function(){var t=this.byteCount<<3;for(this.Update(128);0!=this.xBufOff;)this.Update(0);this.ProcessLength(t),this.ProcessBlock()},ROTATE:function(t,e){return t<<e|this.URShift(t,32-e)},P0:function(t){return t^this.ROTATE(t,9)^this.ROTATE(t,17)},P1:function(t){return t^this.ROTATE(t,15)^this.ROTATE(t,23)},FF_00_15:function(t,e,r){return t^e^r},FF_16_63:function(t,e,r){return t&e|t&r|e&r},GG_00_15:function(t,e,r){return t^e^r},GG_16_63:function(t,e,r){return t&e|~t&r},URShift:function(t,e){return(t>N.maxValue||t<N.minValue)&&(t=N.parse(t)),t>=0?t>>e:(t>>e)+(2<<~e)},URShiftLong:function(t,e){var r,n=new BigInteger;if(n.fromInt(t),n.signum()>=0)r=n.shiftRight(e).intValue();else{var o=new BigInteger;o.fromInt(2);var i=~e,s="";if(i<0){for(var a=64+i,h=0;h<a;h++)s+="0";var u=new BigInteger;u.fromInt(t>>e);var f=new BigInteger("10"+s,2);s=f.toRadix(10);r=f.add(u).toRadix(10)}else s=o.shiftLeft(~e).intValue(),r=(t>>e)+s}return r},GetZ:function(t,e){var r=B.enc.Utf8.parse("1234567812345678"),n=4*r.words.length*8;this.Update(n>>8&255),this.Update(255&n);var o=this.GetWords(r.toString());this.BlockUpdate(o,0,o.length);var i=this.GetWords(t.curve.a.toBigInteger().toRadix(16)),s=this.GetWords(t.curve.b.toBigInteger().toRadix(16)),a=this.GetWords(t.getX().toBigInteger().toRadix(16)),h=this.GetWords(t.getY().toBigInteger().toRadix(16)),u=this.GetWords(e.substr(0,64)),f=this.GetWords(e.substr(64,64));this.BlockUpdate(i,0,i.length),this.BlockUpdate(s,0,s.length),this.BlockUpdate(a,0,a.length),this.BlockUpdate(h,0,h.length),this.BlockUpdate(u,0,u.length),this.BlockUpdate(f,0,f.length);var c=new Array(this.GetDigestSize());return this.DoFinal(c,0),c},GetWords:function(t){for(var e=[],r=t.length,n=0;n<r;n+=2)e[e.length]=parseInt(t.substr(n,2),16);return e},GetHex:function(t){for(var e=[],r=0,n=0;n<2*t.length;n+=2)e[n>>>3]|=parseInt(t[r])<<24-n%8*4,r++;return new B.lib.WordArray.init(e,t.length)}},Array.Clear=function(t,e,r){for(var n in t)t[n]=null},Array.Copy=function(t,e,r,n,o){for(var i=t.slice(e,e+o),s=0;s<i.length;s++)r[n]=i[s],n++};var D=function(t){var r=B.enc.Utf8.parse(t),n=new e;r=n.GetWords(r.toString()),n.BlockUpdate(r,0,r.length);var o=new Array(32);return n.DoFinal(o,0),n.GetHex(o).toString()},E=new Array;E[0]=new Array(214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5),E[1]=new Array(43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153),E[2]=new Array(156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98),E[3]=new Array(228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166),E[4]=new Array(71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168),E[5]=new Array(104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53),E[6]=new Array(30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135),E[7]=new Array(212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158),E[8]=new Array(234,191,138,210,64,199,56,181,163,247,242,206,249,97,21,161),E[9]=new Array(224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227),E[10]=new Array(29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111),E[11]=new Array(213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81),E[12]=new Array(141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216),E[13]=new Array(10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176),E[14]=new Array(137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132),E[15]=new Array(24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72);var I=new Array(462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257),A=new Array(2746333894,1453994832,1736282519,2993693404),F="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",_="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";t.crypto={},t.crypto.sm3Hash=function(t){return t.length>0?D(t):""},t.crypto.sm4EcbEncrypt=function(t,e){if(t.length>0&&e.length>0){return O(m(e,t))}return""},t.crypto.sm4EcbDecrypt=function(t,e){if(t.length>0&&e.length>0){return w(x(e),t)}return""},t.crypto.sm4CbcEncrypt=function(t,e){if(t.length>0&&e.length>0){return O(S(e,t,iv))}return""},t.crypto.sm4CbcDecrypt=function(t,e){if(t.length>0&&e.length>0){return T(x(e),t,iv)}return""}}(TKFlowEngine),function(t){Date.prototype.format=function(t){var e={"M+":this.getMonth()+1,"d+":this.getDate(),"h+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds(),"q+":Math.floor((this.getMonth()+3)/3),S:this.getMilliseconds()};/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(this.getFullYear()+"").substr(4-RegExp.$1.length)));for(var r in e){var n=e[r];new RegExp("("+r+")").test(t)&&(t=t.replace(RegExp.$1,1==RegExp.$1.length?n:("00"+n).substr((""+n).length)))}return t},t.log=function(e,r){"1"==t.flowOption.isDebug&&window.console&&window.console.log((new Date).format("yyyy-MM-dd hh:mm:ss:S")+" "+e,r)}}(TKFlowEngine),function(t){var e=function(){for(var t=[],e="0123456789abcdef",r=0;r<36;r++)t[r]=e.substr(Math.floor(16*Math.random()),1);return t[14]="4",t[19]=e.substr(3&t[19]|8,1),t[8]=t[13]=t[18]=t[23]="",t.join("")};t.getUUID=e}(TKFlowEngine),function(TKFlowEngine){var escape=/["\\\x00-\x1f\x7f-\x9f]/g,meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},hasOwn=Object.prototype.hasOwnProperty,objectType=function(t){if(null==t)return String(t);var e=Object.prototype.toString.call(t);return"[object Function]"==e?"function":"[object String]"==e?"string":"[object Number]"==e?"number":"[object Boolean]"==e?"boolean":"[object Array]"==e?"array":"[object Object]"==e?"object":"param is no object type"},isArray=Array.isArray||function(t){return"array"===objectType(t)},toJSON=function t(e){if("object"===("undefined"==typeof JSON?"undefined":_typeof(JSON))&&JSON.stringify)return JSON.stringify(e);if(null===e)return"null";var r,n,o,i,s=objectType(e);if("undefined"!==s){if("number"===s||"boolean"===s)return String(e);if("string"===s)return quoteString(e);if("function"==typeof e.toJSON)return t(e.toJSON());if("date"===s){var a=e.getUTCMonth()+1,h=e.getUTCDate(),u=e.getUTCFullYear(),f=e.getUTCHours(),c=e.getUTCMinutes(),l=e.getUTCSeconds(),p=e.getUTCMilliseconds();return a<10&&(a="0"+a),h<10&&(h="0"+h),f<10&&(f="0"+f),c<10&&(c="0"+c),l<10&&(l="0"+l),p<100&&(p="0"+p),p<10&&(p="0"+p),'"'+u+"-"+a+"-"+h+"T"+f+":"+c+":"+l+"."+p+'Z"'}if(r=[],isArray(e)){for(n=0;n<e.length;n++)r.push(t(e[n])||"null");return"["+r.join(",")+"]"}if("object"===(void 0===e?"undefined":_typeof(e))){for(n in e)if(hasOwn.call(e,n)){if("number"===(s=void 0===n?"undefined":_typeof(n)))o='"'+n+'"';else{if("string"!==s)continue;o=quoteString(n)}s=_typeof(e[n]),"function"!==s&&"undefined"!==s&&(i=t(e[n]),r.push(o+":"+i))}return"{"+r.join(",")+"}"}}},evalJSON=function evalJSON(str){return"object"===("undefined"==typeof JSON?"undefined":_typeof(JSON))&&JSON.parse?JSON.parse(str):eval("("+str+")")},secureEvalJSON=function secureEvalJSON(Str){if("object"===("undefined"==typeof JSON?"undefined":_typeof(JSON))&&JSON.parse)return JSON.parse(str);var filtered=str.replace(/\\["\\\/bfnrtu]/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"");if(/^[\],:{}\s]*$/.test(filtered))return eval("("+str+")");throw new SyntaxError("Error parsing JSON, source is not valid.")},quoteString=function(t){return t.match(escape)?'"'+t.replace(escape,function(t){var e=meta[t];return"string"==typeof e?e:(e=t.charCodeAt(),"\\u00"+Math.floor(e/16).toString(16)+(e%16).toString(16))})+'"':'"'+t+'"'};TKFlowEngine.JSON={},TKFlowEngine.JSON.evalJSON=evalJSON,TKFlowEngine.JSON.toJSON=toJSON}(TKFlowEngine),function(t){var e=function(t,e){e=void 0===e||null===e||e;var r=[];if(t)for(var n in t){var o=t[n];o||(o=""),o=e?encodeURIComponent(o+""):o,r.push(n+"="+o)}return r.join("&")},r=function(){var t=null;return window.XMLHttpRequest?t=new XMLHttpRequest:window.ActiveXObject&&(t=new ActiveXObject("Microsoft.XMLHTTP")),t},n=function(n,o,i,s,a){if(!n||0==n.length)return!1;s=s||"post",o=o||{},i=i||{};var h="",u=!1;if("0"!=t.flowOption.requestMode){if("1"==t.flowOption.requestMode){var f={};f.data=t.JSON.toJSON(o),f.request_id=t.getUUID(),f.encrypted=!1,o=f}else if("2"==t.flowOption.requestMode||"3"==t.flowOption.requestMode){var f={};f.data=t.crypto.sm4EcbEncrypt(t.flowOption.merchantEncryptKey,t.JSON.toJSON(o)),f.request_id=t.getUUID(),f.encrypted=!0,o=f}h=t.crypto.sm3Hash("secret="+t.flowOption.merchantSecret+"data="+o.data+"request_id="+o.request_id+"encrypted="+o.encrypted),u="3"==t.flowOption.requestMode}var c="get"==s?e(o):t.JSON.toJSON(o);"get"==s&&c.length>0&&(n+=n.indexOf("?")>0?"&"+c:"?"+c);var l=r();if(0==n.indexOf("http://")||0==n.indexOf("https://")){var p=window.location.protocol+"//"+window.location.host;n.indexOf(p)<0&&(l.withCredentials=!0)}l.timeout=3e4,l.ontimeout=function(){if(a){var t={};t.code=408,t.msg="网络请求超时",a(t),a=null}},l.onerror=function(){if(a){var t={};t.code=400,t.msg="网络请求异常",a(t),a=null}},l.open(s,n,!0),t.string.isNotEmpty(h)&&(l.setRequestHeader("tk-trans-merchant-key",t.flowOption.merchantKey),l.setRequestHeader("tk-trans-signature",h),l.setRequestHeader("tk-encrypt-response",u));for(var g in i)l.setRequestHeader(g,i[g]);return"post"==s?(l.setRequestHeader("Content-Type","application/json"),l.send(c)):l.send(""),l.onreadystatechange=function(){if(4==this.readyState&&200==this.status){if(a){var e=this.responseText;l.getResponseHeader("tk-encrypt-response")&&(e=t.crypto.sm4EcbDecrypt(t.flowOption.merchantEncryptKey,e)),a(t.JSON.evalJSON(e)),a=null}}else if(200!=this.status&&a){var r={};r.code=this.status,r.msg=this.responseText,a(r),a=null}},setTimeout(function(){if(l&&l.readyState&&4!=l.readyState){if(a){var t={};t.code=408,t.msg="网络请求超时",a(t),a=null}l.abort(),l=null}},3e4),!1},o=function(r,n,o){if(!r||0==r.length)return!1;var i=e(n,!1),s=!1,a=/(=)\?(?=&|$)|\?\?/;if(a.test(r)){var h="JSONP_"+t.getUUID();window[h]=function(t){o&&(o(t),o=null)},r=r.replace(a,"$1"+h),s=!0}i.length>0&&(r+=r.indexOf("?")>0?"&"+i:"?"+i);var u,f=document.head||document.getElementsByTagName("head").item(0)||document.documentElement;return u=document.createElement("script"),u.async=!0,u.src=r,u.onload=u.onreadystatechange=function(t,e){if(e||!u.readyState||/loaded|complete/.test(u.readyState))if(u.onload=u.onreadystatechange=null,u.parentNode&&u.parentNode.removeChild(u),u=null,e||s){if(o){var r={};r.code=400,r.msg="网络请求异常",o(r),o=null}}else if(o){var n={};n.code="0",n.msg="success",o(n),o=null}},f.insertBefore(u,f.firstChild),setTimeout(function(){if(u&&u.readyState&&!/loaded|complete/.test(u.readyState)){if(o){var t={};t.code=408,t.msg="网络请求超时",o(t),o=null}u&&(u.onload(void 0,!0),u=null)}},3e4),!1},i=function(t,e,r,i,s){window.localStorage?n(t,e,r,i,s):o(t+"?callback=?",e,s)};t.ajaxJSON=n,t.ajaxJSONP=o,t.ajax=i}(TKFlowEngine),function(t){var e=function(t){return void 0===t||null==t||""==o(t)||"undefined"==o(t)||"null"==o(t)},r=function(t){return!e(t)},n=function(t,e,r){return t&&e?t.split(e).join(r):""},o=function(t){return t?t.replace(/^\s+|\s+$/gm,""):""},i=function(t,r){return!e(t)&&!e(r)&&(!(t.length<r.length)&&t.substring(t.length-r.length,t.length)===r)},s=function(t,r){return!e(t)&&!e(r)&&(!(t.length<r.length)&&t.substring(0,r.length)===r)};t.string={},t.string.isEmpty=e,t.string.isNotEmpty=r,t.string.replaceAll=n,t.string.trim=o,t.string.endsWith=i,t.string.startWith=s}(TKFlowEngine),function(t){var e=function(e,r,n){if(void 0===r){var o=null;if(document.cookie&&""!=document.cookie)for(var i=document.cookie.split(";"),s=0;s<i.length;s++){var a=t.string.trim(i[s]);if(a.substring(0,e.length+1)==e+"="){o=a.substring(e.length+1);break}}return o}n=n||{},null===r&&(r="",n.expires=-1);var h="";if(n.expires&&("number"==typeof n.expires||n.expires.toUTCString)){var u;"number"==typeof n.expires?(u=new Date,u.setTime(u.getTime()+24*n.expires*60*60*1e3)):u=n.expires,h="; expires="+u.toUTCString()}var f=n.path?"; path="+n.path:"",c=n.domain?"; domain="+n.domain:"",l=n.secure?"; secure":"",p=n.HttpOnly?";HttpOnly":"";document.cookie=[e,"=",r,h,f,c,l,p].join("")},r=function(t){if(t)e(t,"",{path:"/",secure:"",expires:-1});else{var r=document.cookie.match(/[^ =;]+(?=\=)/g);if(r)for(var n=0;n<r.length;n++)e(r[n],"",{path:"/",secure:"",expires:-1})}};t.cookie={},t.cookie.setItem=e,t.cookie.getItem=e,t.cookie.removeItem=r}(TKFlowEngine),function(t){var e=function(e,r,n){r=t.JSON.toJSON(r),r=t.string.trim(r),r=encodeURIComponent(r),t.string.startWith(window.location.href,"file://")?window.sessionStorage?sessionStorage.setItem(e,r):t.storage.SData[e]=r:n?t.cookie.setItem(e,r,{path:"/",secure:""}):window.sessionStorage?sessionStorage.setItem(e,r):t.storage.SData[e]=r},r=function(e,r,n){r=t.JSON.toJSON(r),r=t.string.trim(r),r=encodeURIComponent(r),t.string.startWith(window.location.href,"file://")?window.localStorage?localStorage.setItem(e,r):t.storage.LData[e]=r:n?t.cookie.setItem(e,r,{expires:3600,path:"/",secure:""}):window.localStorage?localStorage.setItem(e,r):t.storage.LData[e]=r},n=function(e){var r="";return t.string.startWith(window.location.href,"file://")?r=window.sessionStorage?sessionStorage.getItem(e):t.storage.SData[e]:(r=t.cookie.getItem(e),t.string.isEmpty(r)&&(r=window.sessionStorage?sessionStorage.getItem(e):t.storage.SData[e])),r&&(r=decodeURIComponent(r),r=t.JSON.evalJSON(r)),r},o=function(e){var r="";return t.string.startWith(window.location.href,"file://")?r=window.localStorage?localStorage.getItem(e):t.storage.LData[e]:(r=t.cookie.getItem(e),t.string.isEmpty(r)&&(r=window.localStorage?localStorage.getItem(e):t.storage.LData[e])),r&&(r=decodeURIComponent(r),r=t.JSON.evalJSON(r)),r},i=function(e){t.string.startWith(window.location.href,"file://")?window.sessionStorage?sessionStorage.removeItem(e):delete t.storage.SData[e]:(t.cookie.setItem(e,"",{path:"/",secure:"",expires:-1}),window.sessionStorage?sessionStorage.removeItem(e):delete t.storage.SData[e])},s=function(e){t.string.startWith(window.location.href,"file://")?window.localStorage?localStorage.removeItem(e):delete t.storage.LData[e]:(t.cookie.setItem(e,"",{path:"/",secure:"",expires:-1}),window.localStorage?localStorage.removeItem(e):delete t.storage.LData[e])};t.storage={},t.storage.SData={},t.storage.LData={},t.storage.setSessionStorage=e,t.storage.setLocalStorage=r,t.storage.getSessionStorage=n,t.storage.getLocalStorage=o,t.storage.removeSessionStorage=i,t.storage.removeLocalStorage=s}(TKFlowEngine),function(t){var e=function(){var t=window.location.href;return t=decodeURI(t),r(t)},r=function(t){var e=t.indexOf("?");if(e>-1&&(e++,t=t.substring(e,t.length)),-1==t.indexOf("="))return{};var r=t.split("&");if(null===r||0===r.length)return{};for(var n={},o=0;o<r.length;o++){var i=r[o].split("="),s=i[0],a=r[o].replace(s+"=","");n[s]=a}return n},n=function(t){var r=e();return null==r?"":r[t]};t.net={},t.net.getCurUrlParameter=e,t.net.getUrlParameter=r,t.net.getCurUrlParameterValue=n}(TKFlowEngine),function(t){var e=function(e,r,n,o,i){var s=t.flowOption.url;t.string.endsWith("/")&&(s=s.substring(0,s.length-1)),s+=e,t.ajax(s,r,n,o,i)},r=function(t,r,n){e(t,r,{},"post",n)},n=function(t,r,n){e(t,r,{},"get",n)};t.requestPost=r,t.requestGet=n}(TKFlowEngine),function(t){var e=function(e){t.flowOption=t.storage.getSessionStorage("TKFlowOption")||{},e=e||{},e.isDebug=e.isDebug||t.flowOption.isDebug||"1",e.url=e.url||t.flowOption.url||"",e.mockPath=e.mockPath||t.flowOption.mockPath||"",e.requestMode=e.requestMode||t.flowOption.requestMode||"0",e.merchantKey=e.merchantKey||t.flowOption.merchantKey||"",e.merchantSecret=e.merchantSecret||t.flowOption.merchantSecret||"",e.merchantEncryptKey=t.string.isEmpty(e.merchantSecret)?e.merchantSecret:t.crypto.sm3Hash(e.merchantSecret).substring(0,16),t.flowOption=e,t.storage.setSessionStorage("TKFlowOption",t.flowOption),t.pushStateFunc=e.pushStateFunc||window.TKPushStateFunc,t.replaceStateFunc=e.replaceStateFunc||window.TKReplaceStateFunc;var r=t.net.getCurUrlParameter();r&&"1"==r._isTKDevelop&&(t.storage.setSessionStorage("TKFlowDevelopMode",r._isTKDevelop),t.storage.setSessionStorage("TKFlowLoginState","0"))};t.init=e}(TKFlowEngine),function(t){var e=function(t){return t.replace(/([A-Z])/g,"_$1").toLowerCase()},r=function(t){var e=t.match(/_(.)/g);if(e&&e.length>0)for(var r=0;r<e.length;r++)t=t.replace(e[r],e[r].replace("_","").toUpperCase());return t},n=function(t){t=t||{};var r={};for(var n in t)r[e(n)]=t[n]||"";return r},o=function(t){t=t||{};var e={};for(var n in t)e[r(n)]=t[n]||"";return e},i=function(){var e={};return e.flowToken=t.storage.getSessionStorage("TKFlowToken")||"",e},s=function(e){t.storage.setSessionStorage("TKFlowToken",e),t.log("saveFlowInsToken----\x3e",e)},a=function(e,r,o){if(t.isTKDevelop()){window.parent.proxy&&window.parent.proxy.saveTKFlowStepTemplateContext?window.parent.proxy.saveTKFlowStepTemplateContext(n(r)):window.saveTKFlowStepContext?window.saveTKFlowStepContext(e+"----\x3e"+t.JSON.toJSON(r)):alert(e+"----\x3e"+t.JSON.toJSON(r));var s={};s.flowNodeNo=e||"",s.contextParam=r||{},t.log("flowInsNext----\x3e",s)}else{var s=i();s.flowNodeNo=e||"",s.contextParam=r||{},t.requestPost("/bf-engine-server/flowins/outer/next",s,function(e){if(0==e.code){var r=e.data||{},n=r.flowToken;t.string.isNotEmpty(n)?t.saveFlowInsToken(n):t.log("flowInsNext----\x3e异常返回:",e)}o&&o(e)}),t.log("flowInsNext----\x3e",s)}},h=function(e,r){if(t.isTKDevelop()){var n=t.net.getCurUrlParameter(),s=n._testId;if(t.string.isNotEmpty(s)){var a={};a.testId=s,t.requestGet("/bf-engine-server/flow/develop/node/query",a,function(e){if(0==e.code){for(var n=e.data||{},i=n.nodeStepContent||"{}",s=n.sessionConfig||"{}",a=n.paramConfig||"[]",h=n.nodeStepMode||"1",u=n.testName||"",f=n.testNo||"",c=t.JSON.evalJSON(a),l={},p=0;p<c.length;p++){var g=c[p],d=g.paramKey,v=g.paramValue;l[d]=v}var m=function(){var e={},n=t.storage.getSessionStorage("FlowInsMockUserInfo")||{};n=o(n),l=o(l);for(var s in l)n[s]=l[s]||"";l=n,e.inProperty=l,e.outProperty={},e.privProperty={},e.flowNodeNo=f,e.stepName=u,e.stepMode=h,e.stepContent=i,e.jumpMode="0";var a={};a.code=0,a.msg="",a.data=e,r&&r(a)};if("1"!=t.storage.getSessionStorage("TKFlowLoginState")){var y=t.flowOption.mockPath,w=t.JSON.evalJSON(s);t.requestGet(y,w,function(e){if(0==e.code){var n=w;window.vm&&window.vm.$store&&window.vm.$store.commit("user/setUserInfo",n),window.$h&&window.$h.setSession("flowNodeNo","flowNodeNo"),t.saveLocalFlowInsCurNode({flowNodeNo:"flowNodeNo"}),t.storage.setSessionStorage("FlowInsMockUserInfo",n),m()}else r&&r(e)})}else m()}else r&&r(e)})}else{if(window.parent.proxy&&window.parent.proxy.getTKFlowStepTemplateUserInfo){var h=window.parent.proxy.getTKFlowStepTemplateUserInfo()||{};window.vm&&window.vm.$store&&window.vm.$store.commit("user/setUserInfo",h),window.$h&&window.$h.setSession("flowNodeNo","flowNodeNo"),t.saveLocalFlowInsCurNode({flowNodeNo:"flowNodeNo"})}var u=window.parent.proxy&&window.parent.proxy.getTKFlowStepTemplateConfig?window.parent.proxy.getTKFlowStepTemplateConfig():{};u.privProperty=o(u.privProperty);var h={};window.parent.proxy&&window.parent.proxy.getTKFlowStepTemplateUserInfo&&(h=window.parent.proxy.getTKFlowStepTemplateUserInfo()||{},h=o(h));var f=u.inProperty||{};f=o(f);for(var c in f)h[c]=f[c]||"";f=h,u.inProperty=f,u.outProperty=o(u.outProperty);var l={};l.code=0,l.msg="",l.data=u,r&&r(l)}}else{var a=i();a.flowNodeNo=e||"",t.requestGet("/bf-engine-server/flowins/outer/node",a,r),t.log("flowInsNode----\x3e",a)}},u=function(e,r){var n=i();n.contextKeys=e||"",t.requestGet("/bf-engine-server/flowins/outer/context",n,r),t.log("flowInsContext----\x3e",n)},f=function(e,r){var n=i();n.flowNodeNo=e||"",t.requestPost("/bf-engine-server/flowins/outer/setCurNode",n,r),t.log("setFlowInsCurNode----\x3e",n)},c=function(e){e=e||{},t.storage.setSessionStorage("LocalFlowInsCurNode",e),t.log("saveLocalFlowInsCurNode----\x3e",e)},l=function(){var e=t.storage.getSessionStorage("LocalFlowInsCurNode")||{};return t.log("getLocalFlowInsCurNode----\x3e",e),e},p=function(e,r){if(e=e||{},t.string.isEmpty(e.flowNodeNo))return void t.log("forward----\x3e",e);t.saveLocalFlowInsCurNode(e),t.pushStateFunc=t.pushStateFunc||window.TKPushStateFunc,t.replaceStateFunc=t.replaceStateFunc||window.TKReplaceStateFunc,"1"==r?t.replaceStateFunc&&t.replaceStateFunc(e):t.pushStateFunc&&t.pushStateFunc(e),t.log("forward----\x3e",e)},g=function(e){if(e=e||{},t.string.isEmpty(e.flowNodeNo))return void t.log("goback----\x3e",e);t.saveLocalFlowInsCurNode(e),t.setFlowInsCurNode(e.flowNodeNo),t.replaceStateFunc=t.replaceStateFunc||window.TKReplaceStateFunc,t.replaceStateFunc&&t.replaceStateFunc(e),t.log("goback----\x3e",e)},d=function(){var e=t.net.getCurUrlParameter();return!!(e&&"1"==e._isTKDevelop||"1"==t.storage.getSessionStorage("TKFlowDevelopMode"))};t.saveFlowInsToken=s,t.flowInsNext=a,t.flowInsNode=h,t.flowInsContext=u,t.setFlowInsCurNode=f,t.saveLocalFlowInsCurNode=c,t.getLocalFlowInsCurNode=l,t.forward=p,t.goback=g,t.isTKDevelop=d}(TKFlowEngine),window.TKFlowEngine=TKFlowEngine;