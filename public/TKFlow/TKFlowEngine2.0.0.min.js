"use strict";var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},TKFlowEngine={};+function(t){function e(t,e,r){null!=t&&("number"==typeof t?this.fromNumber(t,e,r):null==e&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,e))}function r(){return new e(null)}function o(t,e,r,o,n,i){for(;--i>=0;){var s=e*this[t++]+r[o]+n;n=Math.floor(s/67108864),r[o++]=67108863&s}return n}function n(t,e,r,o,n,i){for(var s=32767&e,a=e>>15;--i>=0;){var h=32767&this[t],u=this[t++]>>15,f=a*h+u*s;h=s*h+((32767&f)<<15)+r[o]+(1073741823&n),n=(h>>>30)+(f>>>15)+a*u+(n>>>30),r[o++]=1073741823&h}return n}function i(t,e,r,o,n,i){for(var s=16383&e,a=e>>14;--i>=0;){var h=16383&this[t],u=this[t++]>>14,f=a*h+u*s;h=s*h+((16383&f)<<14)+r[o]+n,n=(h>>28)+(f>>14)+a*u,r[o++]=268435455&h}return n}function s(t){return re.charAt(t)}function a(t,e){var r=oe[t.charCodeAt(e)];return null==r?-1:r}function h(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s}function u(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0}function f(t){var e=r();return e.fromInt(t),e}function l(t,r){var o;if(16==r)o=4;else if(8==r)o=3;else if(256==r)o=8;else if(2==r)o=1;else if(32==r)o=5;else{if(4!=r)return void this.fromRadix(t,r);o=2}this.t=0,this.s=0;for(var n=t.length,i=!1,s=0;--n>=0;){var h=8==o?255&t[n]:a(t,n);h<0?"-"==t.charAt(n)&&(i=!0):(i=!1,0==s?this[this.t++]=h:s+o>this.DB?(this[this.t-1]|=(h&(1<<this.DB-s)-1)<<s,this[this.t++]=h>>this.DB-s):this[this.t-1]|=h<<s,(s+=o)>=this.DB&&(s-=this.DB))}8==o&&0!=(128&t[0])&&(this.s=-1,s>0&&(this[this.t-1]|=(1<<this.DB-s)-1<<s)),this.clamp(),i&&e.ZERO.subTo(this,this)}function c(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t}function p(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var r,o=(1<<e)-1,n=!1,i="",a=this.t,h=this.DB-a*this.DB%e;if(a-- >0)for(h<this.DB&&(r=this[a]>>h)>0&&(n=!0,i=s(r));a>=0;)h<e?(r=(this[a]&(1<<h)-1)<<e-h,r|=this[--a]>>(h+=this.DB-e)):(r=this[a]>>(h-=e)&o,h<=0&&(h+=this.DB,--a)),r>0&&(n=!0),n&&(i+=s(r));return n?i:"0"}function g(){var t=r();return e.ZERO.subTo(this,t),t}function d(){return this.s<0?this.negate():this}function v(t){var e=this.s-t.s;if(0!=e)return e;var r=this.t;if(0!=(e=r-t.t))return this.s<0?-e:e;for(;--r>=0;)if(0!=(e=this[r]-t[r]))return e;return 0}function w(t){var e,r=1;return 0!=(e=t>>>16)&&(t=e,r+=16),0!=(e=t>>8)&&(t=e,r+=8),0!=(e=t>>4)&&(t=e,r+=4),0!=(e=t>>2)&&(t=e,r+=2),0!=(e=t>>1)&&(t=e,r+=1),r}function m(){return this.t<=0?0:this.DB*(this.t-1)+w(this[this.t-1]^this.s&this.DM)}function y(t,e){var r;for(r=this.t-1;r>=0;--r)e[r+t]=this[r];for(r=t-1;r>=0;--r)e[r]=0;e.t=this.t+t,e.s=this.s}function S(t,e){for(var r=t;r<this.t;++r)e[r-t]=this[r];e.t=Math.max(this.t-t,0),e.s=this.s}function T(t,e){var r,o=t%this.DB,n=this.DB-o,i=(1<<n)-1,s=Math.floor(t/this.DB),a=this.s<<o&this.DM;for(r=this.t-1;r>=0;--r)e[r+s+1]=this[r]>>n|a,a=(this[r]&i)<<o;for(r=s-1;r>=0;--r)e[r]=0;e[s]=a,e.t=this.t+s+1,e.s=this.s,e.clamp()}function N(t,e){e.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)return void(e.t=0);var o=t%this.DB,n=this.DB-o,i=(1<<o)-1;e[0]=this[r]>>o;for(var s=r+1;s<this.t;++s)e[s-r-1]|=(this[s]&i)<<n,e[s-r]=this[s]>>o;o>0&&(e[this.t-r-1]|=(this.s&i)<<n),e.t=this.t-r,e.clamp()}function b(t,e){for(var r=0,o=0,n=Math.min(t.t,this.t);r<n;)o+=this[r]-t[r],e[r++]=o&this.DM,o>>=this.DB;if(t.t<this.t){for(o-=t.s;r<this.t;)o+=this[r],e[r++]=o&this.DM,o>>=this.DB;o+=this.s}else{for(o+=this.s;r<t.t;)o-=t[r],e[r++]=o&this.DM,o>>=this.DB;o-=t.s}e.s=o<0?-1:0,o<-1?e[r++]=this.DV+o:o>0&&(e[r++]=o),e.t=r,e.clamp()}function x(t,r){var o=this.abs(),n=t.abs(),i=o.t;for(r.t=i+n.t;--i>=0;)r[i]=0;for(i=0;i<n.t;++i)r[i+o.t]=o.am(0,n[i],r,i,0,o.t);r.s=0,r.clamp(),this.s!=t.s&&e.ZERO.subTo(r,r)}function O(t){for(var e=this.abs(),r=t.t=2*e.t;--r>=0;)t[r]=0;for(r=0;r<e.t-1;++r){var o=e.am(r,e[r],t,2*r,0,1);(t[r+e.t]+=e.am(r+1,2*e[r],t,2*r+1,o,e.t-r-1))>=e.DV&&(t[r+e.t]-=e.DV,t[r+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(r,e[r],t,2*r,0,1)),t.s=0,t.clamp()}function F(t,o,n){var i=t.abs();if(!(i.t<=0)){var s=this.abs();if(s.t<i.t)return null!=o&&o.fromInt(0),void(null!=n&&this.copyTo(n));null==n&&(n=r());var a=r(),h=this.s,u=t.s,f=this.DB-w(i[i.t-1]);f>0?(i.lShiftTo(f,a),s.lShiftTo(f,n)):(i.copyTo(a),s.copyTo(n));var l=a.t,c=a[l-1];if(0!=c){var p=c*(1<<this.F1)+(l>1?a[l-2]>>this.F2:0),g=this.FV/p,d=(1<<this.F1)/p,v=1<<this.F2,m=n.t,y=m-l,S=null==o?r():o;for(a.dlShiftTo(y,S),n.compareTo(S)>=0&&(n[n.t++]=1,n.subTo(S,n)),e.ONE.dlShiftTo(l,S),S.subTo(a,a);a.t<l;)a[a.t++]=0;for(;--y>=0;){var T=n[--m]==c?this.DM:Math.floor(n[m]*g+(n[m-1]+v)*d);if((n[m]+=a.am(0,T,n,y,0,l))<T)for(a.dlShiftTo(y,S),n.subTo(S,n);n[m]<--T;)n.subTo(S,n)}null!=o&&(n.drShiftTo(l,o),h!=u&&e.ZERO.subTo(o,o)),n.t=l,n.clamp(),f>0&&n.rShiftTo(f,n),h<0&&e.ZERO.subTo(n,n)}}}function B(t){var o=r();return this.abs().divRemTo(t,null,o),this.s<0&&o.compareTo(e.ZERO)>0&&t.subTo(o,o),o}function E(t){this.m=t}function D(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t}function I(t){return t}function A(t){t.divRemTo(this.m,null,t)}function C(t,e,r){t.multiplyTo(e,r),this.reduce(r)}function _(t,e){t.squareTo(e),this.reduce(e)}function k(){if(this.t<1)return 0;var t=this[0];if(0==(1&t))return 0;var e=3&t;return e=e*(2-(15&t)*e)&15,e=e*(2-(255&t)*e)&255,e=e*(2-((65535&t)*e&65535))&65535,e=e*(2-t*e%this.DV)%this.DV,e>0?this.DV-e:-e}function M(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}function R(t){var o=r();return t.abs().dlShiftTo(this.m.t,o),o.divRemTo(this.m,null,o),t.s<0&&o.compareTo(e.ZERO)>0&&this.m.subTo(o,o),o}function U(t){var e=r();return t.copyTo(e),this.reduce(e),e}function K(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var r=32767&t[e],o=r*this.mpl+((r*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(r=e+this.m.t,t[r]+=this.m.am(0,o,t,e,0,this.m.t);t[r]>=t.DV;)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)}function q(t,e){t.squareTo(e),this.reduce(e)}function L(t,e,r){t.multiplyTo(e,r),this.reduce(r)}function P(){return 0==(this.t>0?1&this[0]:this.s)}function J(t,o){if(t>4294967295||t<1)return e.ONE;var n=r(),i=r(),s=o.convert(this),a=w(t)-1;for(s.copyTo(n);--a>=0;)if(o.sqrTo(n,i),(t&1<<a)>0)o.mulTo(i,s,n);else{var h=n;n=i,i=h}return o.revert(n)}function j(t,e){var r;return r=t<256||e.isEven()?new E(e):new M(e),this.exp(t,r)}function H(){var t=r();return this.copyTo(t),t}function G(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]}function V(){return 0==this.t?this.s:this[0]<<24>>24}function W(){return 0==this.t?this.s:this[0]<<16>>16}function z(t){return Math.floor(Math.LN2*this.DB/Math.log(t))}function X(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1}function $(t){if(null==t&&(t=10),0==this.signum()||t<2||t>36)return"0";var e=this.chunkSize(t),o=Math.pow(t,e),n=f(o),i=r(),s=r(),a="";for(this.divRemTo(n,i,s);i.signum()>0;)a=(o+s.intValue()).toString(t).substr(1)+a,i.divRemTo(n,i,s);return s.intValue().toString(t)+a}function Z(t,r){this.fromInt(0),null==r&&(r=10);for(var o=this.chunkSize(r),n=Math.pow(r,o),i=!1,s=0,h=0,u=0;u<t.length;++u){var f=a(t,u);f<0?"-"==t.charAt(u)&&0==this.signum()&&(i=!0):(h=r*h+f,++s>=o&&(this.dMultiply(n),this.dAddOffset(h,0),s=0,h=0))}s>0&&(this.dMultiply(Math.pow(r,s)),this.dAddOffset(h,0)),i&&e.ZERO.subTo(this,this)}function Y(t,r,o){if("number"==typeof r)if(t<2)this.fromInt(1);else for(this.fromNumber(t,o),this.testBit(t-1)||this.bitwiseTo(e.ONE.shiftLeft(t-1),st,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(r);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(e.ONE.shiftLeft(t-1),this);else{var n=new Array,i=7&t;n.length=1+(t>>3),r.nextBytes(n),i>0?n[0]&=(1<<i)-1:n[0]=0,this.fromString(n,256)}}function Q(){var t=this.t,e=new Array;e[0]=this.s;var r,o=this.DB-t*this.DB%8,n=0;if(t-- >0)for(o<this.DB&&(r=this[t]>>o)!=(this.s&this.DM)>>o&&(e[n++]=r|this.s<<this.DB-o);t>=0;)o<8?(r=(this[t]&(1<<o)-1)<<8-o,r|=this[--t]>>(o+=this.DB-8)):(r=this[t]>>(o-=8)&255,o<=0&&(o+=this.DB,--t)),0!=(128&r)&&(r|=-256),0==n&&(128&this.s)!=(128&r)&&++n,(n>0||r!=this.s)&&(e[n++]=r);return e}function tt(t){return 0==this.compareTo(t)}function et(t){return this.compareTo(t)<0?this:t}function rt(t){return this.compareTo(t)>0?this:t}function ot(t,e,r){var o,n,i=Math.min(t.t,this.t);for(o=0;o<i;++o)r[o]=e(this[o],t[o]);if(t.t<this.t){for(n=t.s&this.DM,o=i;o<this.t;++o)r[o]=e(this[o],n);r.t=this.t}else{for(n=this.s&this.DM,o=i;o<t.t;++o)r[o]=e(n,t[o]);r.t=t.t}r.s=e(this.s,t.s),r.clamp()}function nt(t,e){return t&e}function it(t){var e=r();return this.bitwiseTo(t,nt,e),e}function st(t,e){return t|e}function at(t){var e=r();return this.bitwiseTo(t,st,e),e}function ht(t,e){return t^e}function ut(t){var e=r();return this.bitwiseTo(t,ht,e),e}function ft(t,e){return t&~e}function lt(t){var e=r();return this.bitwiseTo(t,ft,e),e}function ct(){for(var t=r(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t}function pt(t){var e=r();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e}function gt(t){var e=r();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e}function dt(t){if(0==t)return-1;var e=0;return 0==(65535&t)&&(t>>=16,e+=16),0==(255&t)&&(t>>=8,e+=8),0==(15&t)&&(t>>=4,e+=4),0==(3&t)&&(t>>=2,e+=2),0==(1&t)&&++e,e}function vt(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+dt(this[t]);return this.s<0?this.t*this.DB:-1}function wt(t){for(var e=0;0!=t;)t&=t-1,++e;return e}function mt(){for(var t=0,e=this.s&this.DM,r=0;r<this.t;++r)t+=wt(this[r]^e);return t}function yt(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:0!=(this[e]&1<<t%this.DB)}function St(t,r){var o=e.ONE.shiftLeft(t);return this.bitwiseTo(o,r,o),o}function Tt(t){return this.changeBit(t,st)}function Nt(t){return this.changeBit(t,ft)}function bt(t){return this.changeBit(t,ht)}function xt(t,e){for(var r=0,o=0,n=Math.min(t.t,this.t);r<n;)o+=this[r]+t[r],e[r++]=o&this.DM,o>>=this.DB;if(t.t<this.t){for(o+=t.s;r<this.t;)o+=this[r],e[r++]=o&this.DM,o>>=this.DB;o+=this.s}else{for(o+=this.s;r<t.t;)o+=t[r],e[r++]=o&this.DM,o>>=this.DB;o+=t.s}e.s=o<0?-1:0,o>0?e[r++]=o:o<-1&&(e[r++]=this.DV+o),e.t=r,e.clamp()}function Ot(t){var e=r();return this.addTo(t,e),e}function Ft(t){var e=r();return this.subTo(t,e),e}function Bt(t){var e=r();return this.multiplyTo(t,e),e}function Et(){var t=r();return this.squareTo(t),t}function Dt(t){var e=r();return this.divRemTo(t,e,null),e}function It(t){var e=r();return this.divRemTo(t,null,e),e}function At(t){var e=r(),o=r();return this.divRemTo(t,e,o),new Array(e,o)}function Ct(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()}function _t(t,e){if(0!=t){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}}function kt(){}function Mt(t){return t}function Rt(t,e,r){t.multiplyTo(e,r)}function Ut(t,e){t.squareTo(e)}function Kt(t){return this.exp(t,new kt)}function qt(t,e,r){var o=Math.min(this.t+t.t,e);for(r.s=0,r.t=o;o>0;)r[--o]=0;var n;for(n=r.t-this.t;o<n;++o)r[o+this.t]=this.am(0,t[o],r,o,0,this.t);for(n=Math.min(t.t,e);o<n;++o)this.am(0,t[o],r,o,0,e-o);r.clamp()}function Lt(t,e,r){--e;var o=r.t=this.t+t.t-e;for(r.s=0;--o>=0;)r[o]=0;for(o=Math.max(e-this.t,0);o<t.t;++o)r[this.t+o-e]=this.am(e-o,t[o],r,0,0,this.t+o-e);r.clamp(),r.drShiftTo(1,r)}function Pt(t){this.r2=r(),this.q3=r(),e.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t),this.m=t}function Jt(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=r();return t.copyTo(e),this.reduce(e),e}function jt(t){return t}function Ht(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)}function Gt(t,e){t.squareTo(e),this.reduce(e)}function Vt(t,e,r){t.multiplyTo(e,r),this.reduce(r)}function Wt(t,e){var o,n,i=t.bitLength(),s=f(1);if(i<=0)return s;o=i<18?1:i<48?3:i<144?4:i<768?5:6,n=i<8?new E(e):e.isEven()?new Pt(e):new M(e);var a=new Array,h=3,u=o-1,l=(1<<o)-1;if(a[1]=n.convert(this),o>1){var c=r();for(n.sqrTo(a[1],c);h<=l;)a[h]=r(),n.mulTo(c,a[h-2],a[h]),h+=2}var p,g,d=t.t-1,v=!0,m=r();for(i=w(t[d])-1;d>=0;){for(i>=u?p=t[d]>>i-u&l:(p=(t[d]&(1<<i+1)-1)<<u-i,d>0&&(p|=t[d-1]>>this.DB+i-u)),h=o;0==(1&p);)p>>=1,--h;if((i-=h)<0&&(i+=this.DB,--d),v)a[p].copyTo(s),v=!1;else{for(;h>1;)n.sqrTo(s,m),n.sqrTo(m,s),h-=2;h>0?n.sqrTo(s,m):(g=s,s=m,m=g),n.mulTo(m,a[p],s)}for(;d>=0&&0==(t[d]&1<<i);)n.sqrTo(s,m),g=s,s=m,m=g,--i<0&&(i=this.DB-1,--d)}return n.revert(s)}function zt(t){var e=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(e.compareTo(r)<0){var o=e;e=r,r=o}var n=e.getLowestSetBit(),i=r.getLowestSetBit();if(i<0)return e;for(n<i&&(i=n),i>0&&(e.rShiftTo(i,e),r.rShiftTo(i,r));e.signum()>0;)(n=e.getLowestSetBit())>0&&e.rShiftTo(n,e),(n=r.getLowestSetBit())>0&&r.rShiftTo(n,r),e.compareTo(r)>=0?(e.subTo(r,e),e.rShiftTo(1,e)):(r.subTo(e,r),r.rShiftTo(1,r));return i>0&&r.lShiftTo(i,r),r}function Xt(t){if(t<=0)return 0;var e=this.DV%t,r=this.s<0?t-1:0;if(this.t>0)if(0==e)r=this[0]%t;else for(var o=this.t-1;o>=0;--o)r=(e*r+this[o])%t;return r}function $t(t){var r=t.isEven();if(this.isEven()&&r||0==t.signum())return e.ZERO;for(var o=t.clone(),n=this.clone(),i=f(1),s=f(0),a=f(0),h=f(1);0!=o.signum();){for(;o.isEven();)o.rShiftTo(1,o),r?(i.isEven()&&s.isEven()||(i.addTo(this,i),s.subTo(t,s)),i.rShiftTo(1,i)):s.isEven()||s.subTo(t,s),s.rShiftTo(1,s);for(;n.isEven();)n.rShiftTo(1,n),r?(a.isEven()&&h.isEven()||(a.addTo(this,a),h.subTo(t,h)),a.rShiftTo(1,a)):h.isEven()||h.subTo(t,h),h.rShiftTo(1,h);o.compareTo(n)>=0?(o.subTo(n,o),r&&i.subTo(a,i),s.subTo(h,s)):(n.subTo(o,n),r&&a.subTo(i,a),h.subTo(s,h))}return 0!=n.compareTo(e.ONE)?e.ZERO:h.compareTo(t)>=0?h.subtract(t):h.signum()<0?(h.addTo(t,h),h.signum()<0?h.add(t):h):h}function Zt(t){var e,r=this.abs();if(1==r.t&&r[0]<=ne[ne.length-1]){for(e=0;e<ne.length;++e)if(r[0]==ne[e])return!0;return!1}if(r.isEven())return!1;for(e=1;e<ne.length;){for(var o=ne[e],n=e+1;n<ne.length&&o<ie;)o*=ne[n++];for(o=r.modInt(o);e<n;)if(o%ne[e++]==0)return!1}return r.millerRabin(t)}function Yt(t){var o=this.subtract(e.ONE),n=o.getLowestSetBit();if(n<=0)return!1;var i=o.shiftRight(n);(t=t+1>>1)>ne.length&&(t=ne.length);for(var s=r(),a=0;a<t;++a){s.fromInt(ne[Math.floor(Math.random()*ne.length)]);var h=s.modPow(i,this);if(0!=h.compareTo(e.ONE)&&0!=h.compareTo(o)){for(var u=1;u++<n&&0!=h.compareTo(o);)if(h=h.modPowInt(2,this),0==h.compareTo(e.ONE))return!1;if(0!=h.compareTo(o))return!1}}return!0}var Qt;"Microsoft Internet Explorer"==navigator.appName?(e.prototype.am=n,Qt=30):"Netscape"!=navigator.appName?(e.prototype.am=o,Qt=26):(e.prototype.am=i,Qt=28),e.prototype.DB=Qt,e.prototype.DM=(1<<Qt)-1,e.prototype.DV=1<<Qt;e.prototype.FV=Math.pow(2,52),e.prototype.F1=52-Qt,e.prototype.F2=2*Qt-52;var te,ee,re="0123456789abcdefghijklmnopqrstuvwxyz",oe=new Array;for(te="0".charCodeAt(0),ee=0;ee<=9;++ee)oe[te++]=ee;for(te="a".charCodeAt(0),ee=10;ee<36;++ee)oe[te++]=ee;for(te="A".charCodeAt(0),ee=10;ee<36;++ee)oe[te++]=ee;E.prototype.convert=D,E.prototype.revert=I,E.prototype.reduce=A,E.prototype.mulTo=C,E.prototype.sqrTo=_,M.prototype.convert=R,M.prototype.revert=U,M.prototype.reduce=K,M.prototype.mulTo=L,M.prototype.sqrTo=q,e.prototype.copyTo=h,e.prototype.fromInt=u,e.prototype.fromString=l,e.prototype.clamp=c,e.prototype.dlShiftTo=y,e.prototype.drShiftTo=S,e.prototype.lShiftTo=T,e.prototype.rShiftTo=N,e.prototype.subTo=b,e.prototype.multiplyTo=x,e.prototype.squareTo=O,e.prototype.divRemTo=F,e.prototype.invDigit=k,e.prototype.isEven=P,e.prototype.exp=J,e.prototype.toString=p,e.prototype.negate=g,e.prototype.abs=d,e.prototype.compareTo=v,e.prototype.bitLength=m,e.prototype.mod=B,e.prototype.modPowInt=j,e.ZERO=f(0),e.ONE=f(1),kt.prototype.convert=Mt,kt.prototype.revert=Mt,kt.prototype.mulTo=Rt,kt.prototype.sqrTo=Ut,Pt.prototype.convert=Jt,Pt.prototype.revert=jt,Pt.prototype.reduce=Ht,Pt.prototype.mulTo=Vt,Pt.prototype.sqrTo=Gt;var ne=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],ie=(1<<26)/ne[ne.length-1];e.prototype.chunkSize=z,e.prototype.toRadix=$,e.prototype.fromRadix=Z,e.prototype.fromNumber=Y,e.prototype.bitwiseTo=ot,e.prototype.changeBit=St,e.prototype.addTo=xt,e.prototype.dMultiply=Ct,e.prototype.dAddOffset=_t,e.prototype.multiplyLowerTo=qt,e.prototype.multiplyUpperTo=Lt,e.prototype.modInt=Xt,e.prototype.millerRabin=Yt,e.prototype.clone=H,e.prototype.intValue=G,e.prototype.byteValue=V,e.prototype.shortValue=W,e.prototype.signum=X,e.prototype.toByteArray=Q,e.prototype.equals=tt,e.prototype.min=et,e.prototype.max=rt,e.prototype.and=it,e.prototype.or=at,e.prototype.xor=ut,e.prototype.andNot=lt,e.prototype.not=ct,e.prototype.shiftLeft=pt,e.prototype.shiftRight=gt,e.prototype.getLowestSetBit=vt,e.prototype.bitCount=mt,e.prototype.testBit=yt,e.prototype.setBit=Tt,e.prototype.clearBit=Nt,e.prototype.flipBit=bt,e.prototype.add=Ot,e.prototype.subtract=Ft,e.prototype.multiply=Bt,e.prototype.divide=Dt,e.prototype.remainder=It,e.prototype.divideAndRemainder=At,e.prototype.modPow=Wt,e.prototype.modInverse=$t,e.prototype.pow=Kt,e.prototype.gcd=zt,e.prototype.isProbablePrime=Zt,e.prototype.square=Et,window.BigInteger=e}(),function(t){function e(){this.BYTE_LENGTH=64,this.xBuf=new Array,this.xBufOff=0,this.byteCount=0,this.DIGEST_LENGTH=32,this.v0=[1937774191,1226093241,388252375,-628488704,-1452330820,372324522,-477237683,-1325724082],this.v=new Array(8),this.v_=new Array(8),this.X0=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.X=new Array(68),this.xOff=0,this.T_00_15=2043430169,this.T_16_63=2055708042,arguments.length>0?this.InitDigest(arguments[0]):this.Init()}function r(t,e){return t^e}function o(t,e,r){return r=r||32,e%=r,t<<e|t>>>r-e}function n(t){return E[(4026531840&t)>>>28][(251658240&t)>>>24]<<24|E[(15728640&t)>>>20][(983040&t)>>>16]<<16|E[(61440&t)>>>12][(3840&t)>>>8]<<8|E[(240&t)>>>4][(15&t)>>>0]<<0}function i(t){return t=n(t),r(r(r(t,o(t,2)),r(o(t,10),o(t,18))),o(t,24))}function s(t){return t=n(t),r(t,r(o(t,13),o(t,23)))}function a(t){var e=new Array,o=new Array;e[0]=r(t[0],I[0]),e[1]=r(t[1],I[1]),e[2]=r(t[2],I[2]),e[3]=r(t[3],I[3]);for(var n=0;n<32;n++)e[n+4]=r(e[n],s(r(r(e[n+1],e[n+2]),r(e[n+3],D[n])))),o[n]=e[n+4].toString(16);return o}function h(t,e,o){o=o||"cbc";for(var n=e,s=t,h=a(n),u=0;u<32;u++)s[u+4]=r(s[u],i(r(r(s[u+1],s[u+2]),r(s[u+3],parseInt(h[u],16)))));return new Array(s[35].toString(16),s[34].toString(16),s[33].toString(16),s[32].toString(16))}function u(t,e,o){o=o||"cbc";for(var n=e,s=t,h=a(n),u=new Array,f=h.length-1;f>=0;f--)u[h.length-1-f]=h[f];for(var f=0;f<32;f++)s[f+4]=r(s[f],i(r(r(s[f+1],s[f+2]),r(s[f+3],parseInt(u[f],16)))));return new Array(s[35].toString(16),s[34].toString(16),s[33].toString(16),s[32].toString(16))}function f(t,e){if(!t)return"";for(var r=[],o=0;o<t.length;o++){var n=t[o].toString(16);n.length<2&&(n="0"+n),r.push(n)}return e?r.join(""):"0x"+r.join("")}function l(t){var e=t.trim(),r="0x"===e.substr(0,2).toLowerCase()?e.substr(2):e,o=r.length;if(o%2!=0)return"";for(var n,i=[],s=0;s<o;s+=2)n=parseInt(r.substr(s,2),16),i.push(n);return i}function c(t){for(var e=[],r=t.length,o=0;o<r;o+=2)e[e.length]=parseInt(t.substr(o,2),16);return e}function p(t,e){for(;t.length%16!=0||t.length<16;)t=t.concat(l(e));return t}function g(t,e){e||(e=!1);for(var r=[],o=0;o<4;o++)r.push(f(t.slice(4*o,4*(o+1)),e).toString(16));return r}function d(t){for(var e=[],r=0;r<t.length;r++)if("-"==t[r].substring(0,1)){var o="0x"+t[r].substring(1),n=(4294967295-o+1).toString(16);e.push(v(n))}else e.push(v(t[r]));return e}function v(t){for(;t.length<8;)t="0"+t;return t}function w(t,e){var r=O.enc.Utf8.parse(e);r=c(r.toString()),r=p(r,"00"),r.length>16&&(r=r.slice(0,16));var o=g(r),n=O.enc.Utf8.parse(t);n=c(n.toString());var i=16-n.length%16;i=i.toString(16),i.length<2&&(i="0"+i),n="10"==i?n.concat(p([],i)):p(n,i);for(var s=Math.floor(n.length/16),a=[],u=0;u<s;u++){var f=n.slice(16*u,16*(u+1)),l=g(f);a=a.concat(h(l,o))}return N(d(a).join(""))}function m(t){for(var e=[],r=0,o=0;o<2*t.length;o+=2)e[o>>>3]|=parseInt(t[r])<<24-o%8*4,r++;return new O.lib.WordArray.init(e,t.length)}function y(t,e){var r=O.enc.Utf8.parse(e);r=c(r.toString()),r=p(r,"00"),r.length>16&&(r=r.slice(0,16));for(var o=g(r),n=t.length/16,i=[],s=0;s<n;s++){var a=t.slice(16*s,16*(s+1)),h=g(a);i=i.concat(u(h,o))}var f=d(i),l=f.join(""),v=N(l);if(v.length>0){var w=v[v.length-1];w>0&&(v=v.splice(0,v.length-w))}var y=m(v);return O.enc.Utf8.stringify(y)}function S(t,e,o){o=o||e;var n=O.enc.Utf8.parse(e);n=c(n.toString()),n=p(n,"00"),n.length>16&&(n=n.slice(0,16));var i=g(n),s=O.enc.Utf8.parse(o);s=c(s.toString()),s=p(s,"00"),s.length>16&&(s=s.slice(0,16));var a=O.enc.Utf8.parse(t);a=c(a.toString());var u=16-a.length%16;u=u.toString(16),u.length<2&&(u="0"+u),a="10"==u?a.concat(p([],u)):p(a,u);for(var f=Math.floor(a.length/16),l=[],v=0;v<f;v++){for(var w=[],m=a.slice(16*v,16*(v+1)),y=0;y<16;y++)w.push(r(s[y],m[y]));var S=g(w),T=h(S,i);T=d(T);s=N(T.join("")),l=l.concat(s)}return l}function T(t,e,o){o=o||e;var n=O.enc.Utf8.parse(e);n=c(n.toString()),n=p(n,"00"),n.length>16&&(n=n.slice(0,16));var i=g(n),s=O.enc.Utf8.parse(o);s=c(s.toString()),s=p(s,"00"),s.length>16&&(s=s.slice(0,16));for(var a=t.length/16,h=[],f=a-1;f>=0;f--){var l=s;f>0&&(l=t.slice(16*(f-1),16*f));for(var v=t.slice(16*f,16*(f+1)),w=g(v),y=u(w,i),S=d(y),T=S.join(""),b=N(T),x=b.length-1;x>=0;x--)h.push(r(b[x],l[x]))}if(h.reverse(),h.length>0){var F=h[h.length-1];F>0&&(h=h.splice(0,h.length-F))}var B=m(h);return O.enc.Utf8.stringify(B)}function N(t){for(var e=new Array,r=0;r<t.length/2;r++){var o=t.substr(2*r,2);e[r]=parseInt(o,16)}return e}function b(t){for(var e=new Array,r=0,o=0,n=new Array(3),i=new Array(4),s=t.length,a=0;s--;)if(n[r++]=t[a++],3==r){for(i[0]=(252&n[0])>>2,i[1]=((3&n[0])<<4)+((240&n[1])>>4),i[2]=((15&n[1])<<2)+((192&n[2])>>6),i[3]=63&n[2],r=0;r<4;r++)e+=A.charAt(i[r]);r=0}if(r){for(o=r;o<3;o++)n[o]=0;for(i[0]=(252&n[0])>>2,i[1]=((3&n[0])<<4)+((240&n[1])>>4),i[2]=((15&n[1])<<2)+((192&n[2])>>6),i[3]=63&n[2],o=0;o<r+1;o++)e+=A.charAt(i[o]);for(;r++<3;)e+="="}return e}function x(t){var e,r,o,n,i,s,a,h=new Array,u=0,f=t;if(t=t.replace(/[^A-Za-z0-9\+\/\=]/g,""),f!=t&&alert("Warning! Characters outside Base64 range in input string ignored."),t.length%4)return alert("Error: Input length is not a multiple of 4 bytes."),"";for(var l=0;u<t.length;)n=C.indexOf(t.charAt(u++)),i=C.indexOf(t.charAt(u++)),s=C.indexOf(t.charAt(u++)),a=C.indexOf(t.charAt(u++)),e=n<<2|i>>4,r=(15&i)<<4|s>>2,o=(3&s)<<6|a,h[l++]=e,64!=s&&(h[l++]=r),64!=a&&(h[l++]=o);return h}var O=O||function(t,e){var r={},o=r.lib={},n=function(){},i=o.Base={extend:function(t){n.prototype=this;var e=new n;return t&&e.mixIn(t),e.hasOwnProperty("init")||(e.init=function(){e.$super.init.apply(this,arguments)}),e.init.prototype=e,e.$super=this,e},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}},s=o.WordArray=i.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=void 0!=e?e:4*t.length},toString:function(t){return(t||h).stringify(this)},concat:function(t){var e=this.words,r=t.words,o=this.sigBytes;if(t=t.sigBytes,this.clamp(),o%4)for(var n=0;n<t;n++)e[o+n>>>2]|=(r[n>>>2]>>>24-n%4*8&255)<<24-(o+n)%4*8;else if(65535<r.length)for(n=0;n<t;n+=4)e[o+n>>>2]=r[n>>>2];else e.push.apply(e,r);return this.sigBytes+=t,this},clamp:function(){var e=this.words,r=this.sigBytes;e[r>>>2]&=4294967295<<32-r%4*8,e.length=t.ceil(r/4)},clone:function(){var t=i.clone.call(this);return t.words=this.words.slice(0),t},random:function(e){for(var r=[],o=0;o<e;o+=4)r.push(4294967296*t.random()|0);return new s.init(r,e)}}),a=r.enc={},h=a.Hex={stringify:function(t){var e=t.words;t=t.sigBytes;for(var r=[],o=0;o<t;o++){var n=e[o>>>2]>>>24-o%4*8&255;r.push((n>>>4).toString(16)),r.push((15&n).toString(16))}return r.join("")},parse:function(t){for(var e=t.length,r=[],o=0;o<e;o+=2)r[o>>>3]|=parseInt(t.substr(o,2),16)<<24-o%8*4;return new s.init(r,e/2)}},u=a.Latin1={stringify:function(t){var e=t.words;t=t.sigBytes;for(var r=[],o=0;o<t;o++)r.push(String.fromCharCode(e[o>>>2]>>>24-o%4*8&255));return r.join("")},parse:function(t){for(var e=t.length,r=[],o=0;o<e;o++)r[o>>>2]|=(255&t.charCodeAt(o))<<24-o%4*8;return new s.init(r,e)}},f=a.Utf8={stringify:function(t){try{return decodeURIComponent(escape(u.stringify(t)))}catch(t){throw Error("Malformed UTF-8 data")}},parse:function(t){return u.parse(unescape(encodeURIComponent(t)))}},l=o.BufferedBlockAlgorithm=i.extend({reset:function(){this._data=new s.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=f.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(e){var r=this._data,o=r.words,n=r.sigBytes,i=this.blockSize,a=n/(4*i),a=e?t.ceil(a):t.max((0|a)-this._minBufferSize,0);if(e=a*i,n=t.min(4*e,n),e){for(var h=0;h<e;h+=i)this._doProcessBlock(o,h);h=o.splice(0,e),r.sigBytes-=n}return new s.init(h,n)},clone:function(){var t=i.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0});o.Hasher=l.extend({cfg:i.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){l.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){return t&&this._append(t),this._doFinalize()},blockSize:16,_createHelper:function(t){return function(e,r){return new t.init(r).finalize(e)}},_createHmacHelper:function(t){return function(e,r){return new c.HMAC.init(t,r).finalize(e)}}});var c=r.algo={};return r}(Math);!function(){var t=O,e=t.lib.WordArray;t.enc.Base64={stringify:function(t){var e=t.words,r=t.sigBytes,o=this._map;t.clamp(),t=[];for(var n=0;n<r;n+=3)for(var i=(e[n>>>2]>>>24-n%4*8&255)<<16|(e[n+1>>>2]>>>24-(n+1)%4*8&255)<<8|e[n+2>>>2]>>>24-(n+2)%4*8&255,s=0;4>s&&n+.75*s<r;s++)t.push(o.charAt(i>>>6*(3-s)&63));if(e=o.charAt(64))for(;t.length%4;)t.push(e);return t.join("")},parse:function(t){var r=t.length,o=this._map,n=o.charAt(64);n&&-1!=(n=t.indexOf(n))&&(r=n);for(var n=[],i=0,s=0;s<r;s++)if(s%4){var a=o.indexOf(t.charAt(s-1))<<s%4*2,h=o.indexOf(t.charAt(s))>>>6-s%4*2;n[i>>>2]|=(a|h)<<24-i%4*8,i++}return e.create(n,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),function(t){function e(t,e,r,o,n,i,s){return((t=t+(e&r|~e&o)+n+s)<<i|t>>>32-i)+e}function r(t,e,r,o,n,i,s){return((t=t+(e&o|r&~o)+n+s)<<i|t>>>32-i)+e}function o(t,e,r,o,n,i,s){return((t=t+(e^r^o)+n+s)<<i|t>>>32-i)+e}function n(t,e,r,o,n,i,s){return((t=t+(r^(e|~o))+n+s)<<i|t>>>32-i)+e}for(var i=O,s=i.lib,a=s.WordArray,h=s.Hasher,s=i.algo,u=[],f=0;64>f;f++)u[f]=4294967296*t.abs(t.sin(f+1))|0;s=s.MD5=h.extend({_doReset:function(){this._hash=new a.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(t,i){for(var s=0;16>s;s++){var a=i+s,h=t[a];t[a]=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8)}var s=this._hash.words,a=t[i+0],h=t[i+1],f=t[i+2],l=t[i+3],c=t[i+4],p=t[i+5],g=t[i+6],d=t[i+7],v=t[i+8],w=t[i+9],m=t[i+10],y=t[i+11],S=t[i+12],T=t[i+13],N=t[i+14],b=t[i+15],x=s[0],O=s[1],F=s[2],B=s[3],x=e(x,O,F,B,a,7,u[0]),B=e(B,x,O,F,h,12,u[1]),F=e(F,B,x,O,f,17,u[2]),O=e(O,F,B,x,l,22,u[3]),x=e(x,O,F,B,c,7,u[4]),B=e(B,x,O,F,p,12,u[5]),F=e(F,B,x,O,g,17,u[6]),O=e(O,F,B,x,d,22,u[7]),x=e(x,O,F,B,v,7,u[8]),B=e(B,x,O,F,w,12,u[9]),F=e(F,B,x,O,m,17,u[10]),O=e(O,F,B,x,y,22,u[11]),x=e(x,O,F,B,S,7,u[12]),B=e(B,x,O,F,T,12,u[13]),F=e(F,B,x,O,N,17,u[14]),O=e(O,F,B,x,b,22,u[15]),x=r(x,O,F,B,h,5,u[16]),B=r(B,x,O,F,g,9,u[17]),F=r(F,B,x,O,y,14,u[18]),O=r(O,F,B,x,a,20,u[19]),x=r(x,O,F,B,p,5,u[20]),B=r(B,x,O,F,m,9,u[21]),F=r(F,B,x,O,b,14,u[22]),O=r(O,F,B,x,c,20,u[23]),x=r(x,O,F,B,w,5,u[24]),B=r(B,x,O,F,N,9,u[25]),F=r(F,B,x,O,l,14,u[26]),O=r(O,F,B,x,v,20,u[27]),x=r(x,O,F,B,T,5,u[28]),B=r(B,x,O,F,f,9,u[29]),F=r(F,B,x,O,d,14,u[30]),O=r(O,F,B,x,S,20,u[31]),x=o(x,O,F,B,p,4,u[32]),B=o(B,x,O,F,v,11,u[33]),F=o(F,B,x,O,y,16,u[34]),O=o(O,F,B,x,N,23,u[35]),x=o(x,O,F,B,h,4,u[36]),B=o(B,x,O,F,c,11,u[37]),F=o(F,B,x,O,d,16,u[38]),O=o(O,F,B,x,m,23,u[39]),x=o(x,O,F,B,T,4,u[40]),B=o(B,x,O,F,a,11,u[41]),F=o(F,B,x,O,l,16,u[42]),O=o(O,F,B,x,g,23,u[43]),x=o(x,O,F,B,w,4,u[44]),B=o(B,x,O,F,S,11,u[45]),F=o(F,B,x,O,b,16,u[46]),O=o(O,F,B,x,f,23,u[47]),x=n(x,O,F,B,a,6,u[48]),B=n(B,x,O,F,d,10,u[49]),F=n(F,B,x,O,N,15,u[50]),O=n(O,F,B,x,p,21,u[51]),x=n(x,O,F,B,S,6,u[52]),B=n(B,x,O,F,l,10,u[53]),F=n(F,B,x,O,m,15,u[54]),O=n(O,F,B,x,h,21,u[55]),x=n(x,O,F,B,v,6,u[56]),B=n(B,x,O,F,b,10,u[57]),F=n(F,B,x,O,g,15,u[58]),O=n(O,F,B,x,T,21,u[59]),x=n(x,O,F,B,c,6,u[60]),B=n(B,x,O,F,y,10,u[61]),F=n(F,B,x,O,f,15,u[62]),O=n(O,F,B,x,w,21,u[63]);s[0]=s[0]+x|0,s[1]=s[1]+O|0,s[2]=s[2]+F|0,s[3]=s[3]+B|0},_doFinalize:function(){var e=this._data,r=e.words,o=8*this._nDataBytes,n=8*e.sigBytes;r[n>>>5]|=128<<24-n%32;var i=t.floor(o/4294967296);for(r[15+(n+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),r[14+(n+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),e.sigBytes=4*(r.length+1),this._process(),e=this._hash,r=e.words,o=0;4>o;o++)n=r[o],r[o]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8);return e},clone:function(){var t=h.clone.call(this);return t._hash=this._hash.clone(),t}}),i.MD5=h._createHelper(s),i.HmacMD5=h._createHmacHelper(s)}(Math),function(){var t=O,e=t.lib,r=e.Base,o=e.WordArray,e=t.algo,n=e.EvpKDF=r.extend({cfg:r.extend({keySize:4,hasher:e.MD5,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){for(var r=this.cfg,n=r.hasher.create(),i=o.create(),s=i.words,a=r.keySize,r=r.iterations;s.length<a;){h&&n.update(h);var h=n.update(t).finalize(e);n.reset();for(var u=1;u<r;u++)h=n.finalize(h),n.reset();i.concat(h)}return i.sigBytes=4*a,i}});t.EvpKDF=function(t,e,r){return n.create(r).compute(t,e)}}(),function(){var t=O,e=t.lib,r=e.WordArray,o=e.Hasher,n=t.algo,i=[],s=n.SM3=o.extend({_doReset:function(){this._hash=new r.init([1937774191,1226093241,388252375,-628488704,-1452330820,372324522,-477237683,-1325724082])},_doProcessBlock:function(t,e){for(var r=this._hash.words,o=r[0],n=r[1],s=r[2],a=r[3],h=r[4],u=0;u<80;u++){if(u<16)i[u]=0|t[e+u];else{var f=i[u-3]^i[u-8]^i[u-14]^i[u-16];i[u]=f<<1|f>>>31}var l=(o<<5|o>>>27)+h+i[u];l+=u<20?1518500249+(n&s|~n&a):u<40?1859775393+(n^s^a):u<60?(n&s|n&a|s&a)-1894007588:(n^s^a)-899497514,h=a,a=s,s=n<<30|n>>>2,n=o,o=l}r[0]=r[0]+o|0,r[1]=r[1]+n|0,r[2]=r[2]+s|0,r[3]=r[3]+a|0,r[4]=r[4]+h|0},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,o=8*t.sigBytes;return e[o>>>5]|=128<<24-o%32,e[14+(o+64>>>9<<4)]=Math.floor(r/4294967296),e[15+(o+64>>>9<<4)]=r,t.sigBytes=4*e.length,this._process(),this._hash},clone:function(){var t=o.clone.call(this);return t._hash=this._hash.clone(),t}
});t.SM3=o._createHelper(s),t.HmacSM3=o._createHmacHelper(s)}();var F={minValue:-parseInt("10000000000000000000000000000000",2),maxValue:parseInt("1111111111111111111111111111111",2),parse:function(t){if(t<this.minValue){for(var e=new Number(-t),r=e.toString(2),o=r.substr(r.length-31,31),n="",i=0;i<o.length;i++){var s=o.substr(i,1);n+="0"==s?"1":"0"}var a=parseInt(n,2);return a+1}if(t>this.maxValue){for(var e=Number(t),r=e.toString(2),o=r.substr(r.length-31,31),n="",i=0;i<o.length;i++){var s=o.substr(i,1);n+="0"==s?"1":"0"}var a=parseInt(n,2);return-(a+1)}return t},parseByte:function(t){if(t<0){for(var e=new Number(-t),r=e.toString(2),o=r.substr(r.length-8,8),n="",i=0;i<o.length;i++){n+="0"==o.substr(i,1)?"1":"0"}return parseInt(n,2)+1}if(t>255){var e=Number(t),r=e.toString(2);return parseInt(r.substr(r.length-8,8),2)}return t}};e.prototype={Init:function(){this.xBuf=new Array(4),this.Reset()},InitDigest:function(t){this.xBuf=new Array(t.xBuf.length),Array.Copy(t.xBuf,0,this.xBuf,0,t.xBuf.length),this.xBufOff=t.xBufOff,this.byteCount=t.byteCount,Array.Copy(t.X,0,this.X,0,t.X.length),this.xOff=t.xOff,Array.Copy(t.v,0,this.v,0,t.v.length)},GetDigestSize:function(){return this.DIGEST_LENGTH},Reset:function(){this.byteCount=0,this.xBufOff=0,Array.Clear(this.xBuf,0,this.xBuf.length),Array.Copy(this.v0,0,this.v,0,this.v0.length),this.xOff=0,Array.Copy(this.X0,0,this.X,0,this.X0.length)},GetByteLength:function(){return this.BYTE_LENGTH},ProcessBlock:function(){var t,e=this.X,r=new Array(64);for(t=16;t<68;t++)e[t]=this.P1(e[t-16]^e[t-9]^this.ROTATE(e[t-3],15))^this.ROTATE(e[t-13],7)^e[t-6];for(t=0;t<64;t++)r[t]=e[t]^e[t+4];var o=this.v,n=this.v_;Array.Copy(o,0,n,0,this.v0.length);var i,s,a,h,u;for(t=0;t<16;t++)u=this.ROTATE(n[0],12),i=F.parse(F.parse(u+n[4])+this.ROTATE(this.T_00_15,t)),i=this.ROTATE(i,7),s=i^u,a=F.parse(F.parse(this.FF_00_15(n[0],n[1],n[2])+n[3])+s)+r[t],h=F.parse(F.parse(this.GG_00_15(n[4],n[5],n[6])+n[7])+i)+e[t],n[3]=n[2],n[2]=this.ROTATE(n[1],9),n[1]=n[0],n[0]=a,n[7]=n[6],n[6]=this.ROTATE(n[5],19),n[5]=n[4],n[4]=this.P0(h);for(t=16;t<64;t++)u=this.ROTATE(n[0],12),i=F.parse(F.parse(u+n[4])+this.ROTATE(this.T_16_63,t)),i=this.ROTATE(i,7),s=i^u,a=F.parse(F.parse(this.FF_16_63(n[0],n[1],n[2])+n[3])+s)+r[t],h=F.parse(F.parse(this.GG_16_63(n[4],n[5],n[6])+n[7])+i)+e[t],n[3]=n[2],n[2]=this.ROTATE(n[1],9),n[1]=n[0],n[0]=a,n[7]=n[6],n[6]=this.ROTATE(n[5],19),n[5]=n[4],n[4]=this.P0(h);for(t=0;t<8;t++)o[t]^=F.parse(n[t]);this.xOff=0,Array.Copy(this.X0,0,this.X,0,this.X0.length)},ProcessWord:function(t,e){var r=t[e]<<24;r|=(255&t[++e])<<16,r|=(255&t[++e])<<8,r|=255&t[++e],this.X[this.xOff]=r,16==++this.xOff&&this.ProcessBlock()},ProcessLength:function(t){this.xOff>14&&this.ProcessBlock(),this.X[14]=this.URShiftLong(t,32),this.X[15]=4294967295&t},IntToBigEndian:function(t,e,r){e[r]=255&t>>24,e[++r]=255&t>>16,e[++r]=255&t>>8,e[++r]=255&t>>0},DoFinal:function(t,e){this.Finish();for(var r=0;r<8;r++)this.IntToBigEndian(this.v[r],t,e+4*r);return this.Reset(),this.DIGEST_LENGTH},Update:function(t){this.xBuf[this.xBufOff++]=t,this.xBufOff==this.xBuf.length&&(this.ProcessWord(this.xBuf,0),this.xBufOff=0),this.byteCount++},BlockUpdate:function(t,e,r){for(;0!=this.xBufOff&&r>0;)this.Update(t[e]),e++,r--;for(;r>this.xBuf.length;)this.ProcessWord(t,e),e+=this.xBuf.length,r-=this.xBuf.length,this.byteCount+=this.xBuf.length;for(;r>0;)this.Update(t[e]),e++,r--},Finish:function(){var t=this.byteCount<<3;for(this.Update(128);0!=this.xBufOff;)this.Update(0);this.ProcessLength(t),this.ProcessBlock()},ROTATE:function(t,e){return t<<e|this.URShift(t,32-e)},P0:function(t){return t^this.ROTATE(t,9)^this.ROTATE(t,17)},P1:function(t){return t^this.ROTATE(t,15)^this.ROTATE(t,23)},FF_00_15:function(t,e,r){return t^e^r},FF_16_63:function(t,e,r){return t&e|t&r|e&r},GG_00_15:function(t,e,r){return t^e^r},GG_16_63:function(t,e,r){return t&e|~t&r},URShift:function(t,e){return(t>F.maxValue||t<F.minValue)&&(t=F.parse(t)),t>=0?t>>e:(t>>e)+(2<<~e)},URShiftLong:function(t,e){var r,o=new BigInteger;if(o.fromInt(t),o.signum()>=0)r=o.shiftRight(e).intValue();else{var n=new BigInteger;n.fromInt(2);var i=~e,s="";if(i<0){for(var a=64+i,h=0;h<a;h++)s+="0";var u=new BigInteger;u.fromInt(t>>e);var f=new BigInteger("10"+s,2);s=f.toRadix(10);r=f.add(u).toRadix(10)}else s=n.shiftLeft(~e).intValue(),r=(t>>e)+s}return r},GetZ:function(t,e){var r=O.enc.Utf8.parse("1234567812345678"),o=4*r.words.length*8;this.Update(o>>8&255),this.Update(255&o);var n=this.GetWords(r.toString());this.BlockUpdate(n,0,n.length);var i=this.GetWords(t.curve.a.toBigInteger().toRadix(16)),s=this.GetWords(t.curve.b.toBigInteger().toRadix(16)),a=this.GetWords(t.getX().toBigInteger().toRadix(16)),h=this.GetWords(t.getY().toBigInteger().toRadix(16)),u=this.GetWords(e.substr(0,64)),f=this.GetWords(e.substr(64,64));this.BlockUpdate(i,0,i.length),this.BlockUpdate(s,0,s.length),this.BlockUpdate(a,0,a.length),this.BlockUpdate(h,0,h.length),this.BlockUpdate(u,0,u.length),this.BlockUpdate(f,0,f.length);var l=new Array(this.GetDigestSize());return this.DoFinal(l,0),l},GetWords:function(t){for(var e=[],r=t.length,o=0;o<r;o+=2)e[e.length]=parseInt(t.substr(o,2),16);return e},GetHex:function(t){for(var e=[],r=0,o=0;o<2*t.length;o+=2)e[o>>>3]|=parseInt(t[r])<<24-o%8*4,r++;return new O.lib.WordArray.init(e,t.length)}},Array.Clear=function(t,e,r){for(var o in t)t[o]=null},Array.Copy=function(t,e,r,o,n){for(var i=t.slice(e,e+n),s=0;s<i.length;s++)r[o]=i[s],o++};var B=function(t){var r=O.enc.Utf8.parse(t),o=new e;r=o.GetWords(r.toString()),o.BlockUpdate(r,0,r.length);var n=new Array(32);return o.DoFinal(n,0),o.GetHex(n).toString()},E=new Array;E[0]=new Array(214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5),E[1]=new Array(43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153),E[2]=new Array(156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98),E[3]=new Array(228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166),E[4]=new Array(71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168),E[5]=new Array(104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53),E[6]=new Array(30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135),E[7]=new Array(212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158),E[8]=new Array(234,191,138,210,64,199,56,181,163,247,242,206,249,97,21,161),E[9]=new Array(224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227),E[10]=new Array(29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111),E[11]=new Array(213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81),E[12]=new Array(141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216),E[13]=new Array(10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176),E[14]=new Array(137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132),E[15]=new Array(24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72);var D=new Array(462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257),I=new Array(2746333894,1453994832,1736282519,2993693404),A="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",C="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";t.crypto={},t.crypto.sm3Hash=function(e){try{if(e.length>0)return B(e)}catch(e){t.log("sm3Hash异常",e)}return""},t.crypto.sm4EcbEncrypt=function(e,r){try{if(e.length>0&&r.length>0){return b(w(r,e))}}catch(e){t.log("sm4EcbEncrypt异常",e)}return""},t.crypto.sm4EcbDecrypt=function(e,r){try{if(e.length>0&&r.length>0){return y(x(r),e).trim()}}catch(e){t.log("sm4EcbDecrypt异常",e)}return""},t.crypto.sm4CbcEncrypt=function(e,r,o){try{if(e.length>0&&o.length>0){r=r||e;return b(S(o,e,r))}}catch(e){t.log("sm4CbcEncrypt异常",e)}return""},t.crypto.sm4CbcDecrypt=function(e,r,o){try{if(e.length>0&&o.length>0){r=r||e;return T(x(o),e,r).trim()}}catch(e){t.log("sm4CbcDecrypt异常",e)}return""}}(TKFlowEngine),function(t){Date.prototype.format=function(t){var e={"M+":this.getMonth()+1,"d+":this.getDate(),"h+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds(),"q+":Math.floor((this.getMonth()+3)/3),S:this.getMilliseconds()};/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(this.getFullYear()+"").substr(4-RegExp.$1.length)));for(var r in e){var o=e[r];new RegExp("("+r+")").test(t)&&(t=t.replace(RegExp.$1,1==RegExp.$1.length?o:("00"+o).substr((""+o).length)))}return t},t.log=function(e,r){"1"==t.flowOption.isDebug&&window.console&&window.console.log((new Date).format("yyyy-MM-dd hh:mm:ss:S")+" "+e,r)}}(TKFlowEngine),function(t){var e=function(){for(var t=[],e="0123456789abcdef",r=0;r<36;r++)t[r]=e.substr(Math.floor(16*Math.random()),1);return t[14]="4",t[19]=e.substr(3&t[19]|8,1),t[8]=t[13]=t[18]=t[23]="",t.join("")};t.getUUID=e}(TKFlowEngine),function(TKFlowEngine){var escape=/["\\\x00-\x1f\x7f-\x9f]/g,meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},hasOwn=Object.prototype.hasOwnProperty,objectType=function(t){if(null==t)return String(t);var e=Object.prototype.toString.call(t);return"[object Function]"==e?"function":"[object String]"==e?"string":"[object Number]"==e?"number":"[object Boolean]"==e?"boolean":"[object Array]"==e?"array":"[object Object]"==e?"object":"param is no object type"},isArray=Array.isArray||function(t){return"array"===objectType(t)},toJSON=function t(e){if("object"===("undefined"==typeof JSON?"undefined":_typeof(JSON))&&JSON.stringify)return JSON.stringify(e);if(null===e)return"null";var r,o,n,i,s=objectType(e);if("undefined"!==s){if("number"===s||"boolean"===s)return String(e);if("string"===s)return quoteString(e);if("function"==typeof e.toJSON)return t(e.toJSON());if("date"===s){var a=e.getUTCMonth()+1,h=e.getUTCDate(),u=e.getUTCFullYear(),f=e.getUTCHours(),l=e.getUTCMinutes(),c=e.getUTCSeconds(),p=e.getUTCMilliseconds();return a<10&&(a="0"+a),h<10&&(h="0"+h),f<10&&(f="0"+f),l<10&&(l="0"+l),c<10&&(c="0"+c),p<100&&(p="0"+p),p<10&&(p="0"+p),'"'+u+"-"+a+"-"+h+"T"+f+":"+l+":"+c+"."+p+'Z"'}if(r=[],isArray(e)){for(o=0;o<e.length;o++)r.push(t(e[o])||"null");return"["+r.join(",")+"]"}if("object"===(void 0===e?"undefined":_typeof(e))){for(o in e)if(hasOwn.call(e,o)){if("number"===(s=void 0===o?"undefined":_typeof(o)))n='"'+o+'"';else{if("string"!==s)continue;n=quoteString(o)}s=_typeof(e[o]),"function"!==s&&"undefined"!==s&&(i=t(e[o]),r.push(n+":"+i))}return"{"+r.join(",")+"}"}}},evalJSON=function evalJSON(str){return"object"===("undefined"==typeof JSON?"undefined":_typeof(JSON))&&JSON.parse?JSON.parse(str):eval("("+str+")")},secureEvalJSON=function secureEvalJSON(Str){if("object"===("undefined"==typeof JSON?"undefined":_typeof(JSON))&&JSON.parse)return JSON.parse(str);var filtered=str.replace(/\\["\\\/bfnrtu]/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"");if(/^[\],:{}\s]*$/.test(filtered))return eval("("+str+")");throw new SyntaxError("Error parsing JSON, source is not valid.")},quoteString=function(t){return t.match(escape)?'"'+t.replace(escape,function(t){var e=meta[t];return"string"==typeof e?e:(e=t.charCodeAt(),"\\u00"+Math.floor(e/16).toString(16)+(e%16).toString(16))})+'"':'"'+t+'"'};TKFlowEngine.JSON={},TKFlowEngine.JSON.evalJSON=evalJSON,TKFlowEngine.JSON.toJSON=toJSON}(TKFlowEngine),function(t){var e=function(t,e){e=void 0===e||null===e||e;var r=[];if(t)for(var o in t){var n=t[o];n||(n=""),n=e?encodeURIComponent(n+""):n,r.push(o+"="+n)}return r.join("&")},r=function(){var t=null;return window.XMLHttpRequest?t=new XMLHttpRequest:window.ActiveXObject&&(t=new ActiveXObject("Microsoft.XMLHTTP")),t},o=function(o,n,i,s,a){if(!o||0==o.length)return!1;s=s||"post",n=n||{},i=i||{};var h="",u=!1;if("0"!=t.flowOption.requestMode){if("1"==t.flowOption.requestMode){var f={};f.data=t.JSON.toJSON(n),f.request_id=t.getUUID(),f.encrypted=!1,n=f}else if("2"==t.flowOption.requestMode||"3"==t.flowOption.requestMode){var f={};f.data=t.crypto.sm4EcbEncrypt(t.flowOption.merchantEncryptKey,t.JSON.toJSON(n)),f.request_id=t.getUUID(),f.encrypted=!0,n=f}h=t.crypto.sm3Hash("secret="+t.flowOption.merchantSecret+"data="+n.data+"request_id="+n.request_id+"encrypted="+n.encrypted),u="3"==t.flowOption.requestMode}var l="get"==s?e(n):t.JSON.toJSON(n);"get"==s&&l.length>0&&(o+=o.indexOf("?")>0?"&"+l:"?"+l);var c=r();if(0==o.indexOf("http://")||0==o.indexOf("https://")){var p=window.location.protocol+"//"+window.location.host;o.indexOf(p)<0&&(c.withCredentials=!0)}c.open(s,o,!0),c.timeout=3e4,c.ontimeout=function(){if(a){var t={};t.code=408,t.msg="网络请求超时",a(t),a=null}},c.onerror=function(){if(a){var t={};t.code=400,t.msg="网络请求异常",a(t),a=null}},t.string.isNotEmpty(h)&&(c.setRequestHeader("tk-trans-merchant-key",t.flowOption.merchantKey),c.setRequestHeader("tk-trans-signature",h),c.setRequestHeader("tk-encrypt-response",u));for(var g in i)c.setRequestHeader(g,i[g]);return"post"==s?(c.setRequestHeader("Content-Type","application/json"),c.send(l)):c.send(""),c.onreadystatechange=function(){if(4==this.readyState&&200==this.status){if(a){var e=this.responseText;"0"!=t.flowOption.requestMode&&c.getResponseHeader("tk-encrypt-response")&&(e=t.crypto.sm4EcbDecrypt(t.flowOption.merchantEncryptKey,e)),a(t.JSON.evalJSON(e)),a=null}}else if(200!=this.status&&a){var r={};r.code=this.status,r.msg=this.responseText,a(r),a=null}},setTimeout(function(){if(c&&c.readyState&&4!=c.readyState){if(a){var t={};t.code=408,t.msg="网络请求超时",a(t),a=null}c.abort(),c=null}},3e4),!1},n=function(r,o,n){if(!r||0==r.length)return!1;var i=e(o,!1),s=!1,a=/(=)\?(?=&|$)|\?\?/;if(a.test(r)){var h="JSONP_"+t.getUUID();window[h]=function(t){n&&(n(t),n=null)},r=r.replace(a,"$1"+h),s=!0}i.length>0&&(r+=r.indexOf("?")>0?"&"+i:"?"+i);var u,f=document.head||document.getElementsByTagName("head").item(0)||document.documentElement;return u=document.createElement("script"),u.async=!0,u.src=r,u.onload=u.onreadystatechange=function(t,e){if(e||!u.readyState||/loaded|complete/.test(u.readyState))if(u.onload=u.onreadystatechange=null,u.parentNode&&u.parentNode.removeChild(u),u=null,e||s){if(n){var r={};r.code=400,r.msg="网络请求异常",n(r),n=null}}else if(n){var o={};o.code="0",o.msg="success",n(o),n=null}},f.insertBefore(u,f.firstChild),setTimeout(function(){if(u&&u.readyState&&!/loaded|complete/.test(u.readyState)){if(n){var t={};t.code=408,t.msg="网络请求超时",n(t),n=null}u&&(u.onload(void 0,!0),u=null)}},3e4),!1},i=function(t,e,r,i,s){window.localStorage?o(t,e,r,i,s):n(t+"?callback=?",e,s)};t.ajaxJSON=o,t.ajaxJSONP=n,t.ajax=i}(TKFlowEngine),function(t){var e=function(t){return void 0===t||null==t||""==n(t)||"undefined"==n(t)||"null"==n(t)},r=function(t){return!e(t)},o=function(t,e,r){return t&&e?t.split(e).join(r):""},n=function(t){return t?(t+="",t.replace(/^\s+|\s+$/gm,"")):""},i=function(t,r){return!e(t)&&!e(r)&&(!(t.length<r.length)&&t.substring(t.length-r.length,t.length)===r)},s=function(t,r){return!e(t)&&!e(r)&&(!(t.length<r.length)&&t.substring(0,r.length)===r)};t.string={},t.string.isEmpty=e,t.string.isNotEmpty=r,t.string.replaceAll=o,t.string.trim=n,t.string.endsWith=i,t.string.startWith=s}(TKFlowEngine),function(t){var e=function(e,r,o){if(void 0===r){var n=null;if(document.cookie&&""!=document.cookie)for(var i=document.cookie.split(";"),s=0;s<i.length;s++){var a=t.string.trim(i[s]);if(a.substring(0,e.length+1)==e+"="){n=a.substring(e.length+1);break}}return n}o=o||{},null===r&&(r="",o.expires=-1);var h="";if(o.expires&&("number"==typeof o.expires||o.expires.toUTCString)){var u;"number"==typeof o.expires?(u=new Date,u.setTime(u.getTime()+24*o.expires*60*60*1e3)):u=o.expires,h="; expires="+u.toUTCString()}var f=o.path?"; path="+o.path:"",l=o.domain?"; domain="+o.domain:"",c=o.secure?"; secure":"",p=o.HttpOnly?";HttpOnly":"";document.cookie=[e,"=",r,h,f,l,c,p].join("")},r=function(t){if(t)e(t,"",{path:"/",secure:"",expires:-1});else{var r=document.cookie.match(/[^ =;]+(?=\=)/g);if(r)for(var o=0;o<r.length;o++)e(r[o],"",{path:"/",secure:"",expires:-1})}};t.cookie={},t.cookie.setItem=e,t.cookie.getItem=e,t.cookie.removeItem=r}(TKFlowEngine),function(t){var e=function(e,r,o){r=t.JSON.toJSON(r),r=t.string.trim(r),r=encodeURIComponent(r),t.string.startWith(window.location.href,"file://")?window.sessionStorage?sessionStorage.setItem(e,r):t.storage.SData[e]=r:o?t.cookie.setItem(e,r,{path:"/",secure:""}):window.sessionStorage?sessionStorage.setItem(e,r):t.storage.SData[e]=r},r=function(e,r,o){r=t.JSON.toJSON(r),r=t.string.trim(r),r=encodeURIComponent(r),t.string.startWith(window.location.href,"file://")?window.localStorage?localStorage.setItem(e,r):t.storage.LData[e]=r:o?t.cookie.setItem(e,r,{expires:3600,path:"/",secure:""}):window.localStorage?localStorage.setItem(e,r):t.storage.LData[e]=r},o=function(e){var r="";return t.string.startWith(window.location.href,"file://")?r=window.sessionStorage?sessionStorage.getItem(e):t.storage.SData[e]:(r=t.cookie.getItem(e),t.string.isEmpty(r)&&(r=window.sessionStorage?sessionStorage.getItem(e):t.storage.SData[e])),r&&(r=decodeURIComponent(r),r=t.JSON.evalJSON(r)),r},n=function(e){var r="";return t.string.startWith(window.location.href,"file://")?r=window.localStorage?localStorage.getItem(e):t.storage.LData[e]:(r=t.cookie.getItem(e),t.string.isEmpty(r)&&(r=window.localStorage?localStorage.getItem(e):t.storage.LData[e])),r&&(r=decodeURIComponent(r),r=t.JSON.evalJSON(r)),r},i=function(e){t.string.startWith(window.location.href,"file://")?window.sessionStorage?sessionStorage.removeItem(e):delete t.storage.SData[e]:(t.cookie.setItem(e,"",{path:"/",secure:"",expires:-1}),window.sessionStorage?sessionStorage.removeItem(e):delete t.storage.SData[e])},s=function(e){t.string.startWith(window.location.href,"file://")?window.localStorage?localStorage.removeItem(e):delete t.storage.LData[e]:(t.cookie.setItem(e,"",{path:"/",secure:"",expires:-1}),window.localStorage?localStorage.removeItem(e):delete t.storage.LData[e])};t.storage={},t.storage.SData={},t.storage.LData={},t.storage.setSessionStorage=e,t.storage.setLocalStorage=r,t.storage.getSessionStorage=o,t.storage.getLocalStorage=n,t.storage.removeSessionStorage=i,t.storage.removeLocalStorage=s}(TKFlowEngine),function(t){var e=function(){var t=window.location.href;return t=decodeURI(t),r(t)},r=function(t){var e=t.indexOf("?");if(e>-1&&(e++,t=t.substring(e,t.length)),-1==t.indexOf("="))return{};var r=t.split("&");if(null===r||0===r.length)return{};for(var o={},n=0;n<r.length;n++){var i=r[n].split("="),s=i[0],a=r[n].replace(s+"=","");o[s]=a}return o},o=function(t){var r=e();return null==r?"":r[t]};t.net={},t.net.getCurUrlParameter=e,t.net.getUrlParameter=r,t.net.getCurUrlParameterValue=o}(TKFlowEngine),function(t){var e=function(e,r,o,n,i){var s=t.flowOption.url;t.string.endsWith(s,"/")&&(s=s.substring(0,s.length-1)),s+=e,t.ajax(s,r,o,n,i)},r=function(t,r,o){e(t,r,{},"post",o)},o=function(t,r,o){e(t,r,{},"get",o)};t.requestPost=r,t.requestGet=o}(TKFlowEngine),function(t){var e=function(){for(var t="",e=[84,72,73,78,75,73,86,69],r=0;r<e.length;r++)t+=String.fromCharCode(e[r]);return t+=t,t.toUpperCase()},r=function(r){t.flowOption=t.storage.getSessionStorage("TKFlowOption")||{},r=r||{},r.isDebug=void 0===r.isDebug?null:r.isDebug+"",r.isDebug=(r.isDebug||t.flowOption.isDebug||"1")+"",r.url=r.url||t.flowOption.url||"",r.isUseCrossFlow=void 0===r.isUseCrossFlow?null:r.isUseCrossFlow+"",r.isUseCrossFlow=(r.isUseCrossFlow||t.flowOption.isUseCrossFlow||"0")+"",r.mockPath=r.mockPath||t.flowOption.mockPath||"",window.tkRequestConfig=window.tkRequestConfig||{},r.requestMode=void 0===r.requestMode?null:r.requestMode+"",window.tkRequestConfig.requestMode=void 0===window.tkRequestConfig.requestMode?null:window.tkRequestConfig.requestMode+"",r.requestMode=(r.requestMode||t.flowOption.requestMode||window.tkRequestConfig.requestMode||"0")+"",r.merchantKey=r.merchantKey||t.flowOption.merchantKey||window.tkRequestConfig.merchantKey||"",r.merchantSecret=r.merchantSecret||t.flowOption.merchantSecret||window.tkRequestConfig.merchantSecret||"";var o=r.merchantSecret.length>16?t.crypto.sm4EcbDecrypt(e(),r.merchantSecret):r.merchantSecret;o&&o.length>=16&&(r.merchantSecret=o.trim()),r.merchantEncryptKey=t.string.isEmpty(r.merchantSecret)?r.merchantSecret:t.crypto.sm3Hash(r.merchantSecret).substring(0,16),t.flowOption=r,t.storage.setSessionStorage("TKFlowOption",t.flowOption),t.pushStateFunc=r.pushStateFunc||window.TKPushStateFunc,t.replaceStateFunc=r.replaceStateFunc||window.TKReplaceStateFunc;var n=t.net.getCurUrlParameter();n&&"1"==n._isTKDevelop&&(t.storage.setSessionStorage("TKFlowDevelopMode",n._isTKDevelop),t.storage.setSessionStorage("TKFlowLoginState","0"))};t.init=r}(TKFlowEngine),function(t){var e=function(t){return t.replace(/([A-Z])/g,"_$1").toLowerCase()},r=function(t){var e=t.match(/_(.)/g);if(e&&e.length>0)for(var r=0;r<e.length;r++)t=t.replace(e[r],e[r].replace("_","").toUpperCase());return t},o=function(t){t=t||{};var r={};for(var o in t)r[e(o)]=t[o]||"";return r},n=function(t){t=t||{};var e={};for(var o in t)e[r(o)]=t[o]||"";return e},i=function(){var e={};return e.flowToken=t.getLocalFlowInsToken(),e},s=function(e,r){t.storage.setSessionStorage("TKFlowToken",e),"1"==t.flowOption.isUseCrossFlow&&t.string.isNotEmpty(r)&&t.storage.setSessionStorage("TKFlowToken_"+r,e),t.log("saveFlowInsToken----\x3e",e)},a=function(e,r,n){if(t.isTKDevelop()){window.parent.proxy&&window.parent.proxy.saveTKFlowStepTemplateContext?window.parent.proxy.saveTKFlowStepTemplateContext(o(r)):window.saveTKFlowStepContext?window.saveTKFlowStepContext(e+"----\x3e"+t.JSON.toJSON(r)):alert(e+"----\x3e"+t.JSON.toJSON(r));var s={};s.flowNodeNo=e||"",s.contextParam=r||{},t.log("flowInsNext----\x3e",s)}else{var s=i();s.flowNodeNo=e||"",s.contextParam=r||{},t.requestPost("/bf-engine-server/flowins/outer/next",s,function(e){if(0==e.code){var r=e.data||{},o=r.flowToken,i=r.flowTokenKey;t.string.isNotEmpty(o)?t.saveFlowInsToken(o,i):t.log("flowInsNext----\x3e异常返回:",e)}n&&n(e)}),t.log("flowInsNext----\x3e",s)}},h=function(e,r,n){if(t.isTKDevelop()){window.parent.proxy&&window.parent.proxy.saveTKFlowStepTemplateContext?window.parent.proxy.saveTKFlowStepTemplateContext(o(r)):window.saveTKFlowStepContext?window.saveTKFlowStepContext(e+"----\x3e"+t.JSON.toJSON(r)):alert(e+"----\x3e"+t.JSON.toJSON(r));var s={};s.flowNodeNo=e||"",s.contextParam=r||{},t.log("flowInsSave----\x3e",s)}else{var s=i();s.flowNodeNo=e||"",s.contextParam=r||{},t.requestPost("/bf-engine-server/flowins/outer/save",s,function(t){n&&n(t)}),t.log("flowInsSave----\x3e",s)}},u=function(e,r,o){if(t.isTKDevelop()){var s=t.net.getCurUrlParameter(),a=s._testId;if(t.string.isNotEmpty(a)){var h={};h.testId=a,t.requestGet("/bf-engine-server/flow/develop/node/query",h,function(e){if(0==e.code){var o=e.data||{},i=o.flowToken||"";t.saveFlowInsToken(i,"");for(var s=o.nodeStepContent||"{}",a=o.sessionConfig||"{}",h=o.paramConfig||"[]",u=o.nodeStepMode||"1",f=o.testName||"",l=o.testNo||"",c=t.JSON.evalJSON(h),p={},g=0;g<c.length;g++){var d=c[g],v=d.paramKey,w=d.paramValue;p[v]=w}var m=function(){var e={},o=t.storage.getSessionStorage("FlowInsMockUserInfo")||{};o=n(o),p=n(p);for(var i in p)o[i]=p[i]||"";p=o,e.inProperty=p,e.outProperty={},e.privProperty={},e.flowNodeNo=l,e.stepName=f,e.stepMode=u,e.stepContent=s,e.jumpMode="0";var a={};a.code=0,a.msg="",a.data=e,r&&r(a)},y=function(e){window.vm&&window.vm.$store&&window.vm.$store.commit("user/setUserInfo",e),window.$h&&window.$h.setSession("flowNodeNo","flowNodeNo"),t.saveLocalFlowInsCurNode({flowNodeNo:"flowNodeNo"}),t.storage.setSessionStorage("FlowInsMockUserInfo",e),t.storage.setSessionStorage("TKFlowLoginState","1"),m()};if("1"!=t.storage.getSessionStorage("TKFlowLoginState")){var S=t.flowOption.mockPath,T=t.JSON.evalJSON(a);t.string.isNotEmpty(S)&&"{}"!=a?t.requestGet(S,T,function(t){0==t.code?y(T):r&&r(t)}):y(T)}else m()}else r&&r(e)})}else{if(window.parent.proxy&&window.parent.proxy.getTKFlowStepTemplateUserInfo){var u=window.parent.proxy.getTKFlowStepTemplateUserInfo()||{};window.vm&&window.vm.$store&&window.vm.$store.commit("user/setUserInfo",u),window.$h&&window.$h.setSession("flowNodeNo","flowNodeNo"),t.saveLocalFlowInsCurNode({flowNodeNo:"flowNodeNo"})}var f=window.parent.proxy&&window.parent.proxy.getTKFlowStepTemplateConfig?window.parent.proxy.getTKFlowStepTemplateConfig():{};f.privProperty=n(f.privProperty);var u={};window.parent.proxy&&window.parent.proxy.getTKFlowStepTemplateUserInfo&&(u=window.parent.proxy.getTKFlowStepTemplateUserInfo()||{},u=n(u));var l=f.inProperty||{};l=n(l);for(var c in l)u[c]=l[c]||"";l=u,f.inProperty=l,f.outProperty=n(f.outProperty);var p={};p.code=0,p.msg="",p.data=f,r&&r(p)}}else{var h=i();h.flowNodeNo=e||"",h.isReturnCurAllRejectInfo=(void 0===o?null:o+"")||"0",t.requestGet("/bf-engine-server/flowins/outer/node",h,r),t.log("flowInsNode----\x3e",h)}},f=function(e,r){var o=i();o.contextKeys=e||"",t.requestGet("/bf-engine-server/flowins/outer/context",o,r),t.log("flowInsContext----\x3e",o)},l=function(e,r){var o=i();o.flowNodeNo=e||"",t.requestPost("/bf-engine-server/flowins/outer/setCurNode",o,r),t.log("setFlowInsCurNode----\x3e",o)},c=function(e){e=e||{},t.storage.setSessionStorage("LocalFlowInsCurNode",e),t.log("saveLocalFlowInsCurNode----\x3e",e)},p=function(){var e={},r=x(),o=t.storage.getSessionStorage("LocalFlowInsCurNode")||{},n=o.flowNodeNo||"";if(t.string.isNotEmpty(n)){r=n.split("-")[0]+"-"+r,e.flowNodeNo=r}return t.string.isNotEmpty(e.flowNodeNo)&&t.string.isNotEmpty(o.flowNodeNo)&&e.flowNodeNo!=o.flowNodeNo&&(v(e,"0"),o=t.storage.getSessionStorage("LocalFlowInsCurNode")||{}),t.log("getLocalFlowInsCurNode----\x3e",o),o},g=function(){var e=null;if("1"==t.flowOption.isUseCrossFlow){var r=b();t.string.isNotEmpty(r)&&(e=t.storage.getSessionStorage("TKFlowToken_"+r))}return e=e||t.storage.getSessionStorage("TKFlowToken")||""},d=function(e,r){if(r=(void 0===r?null:r+"")||"0",e=e||{},t.string.isNotEmpty(e.flowNodeNo)){var o=t.storage.getSessionStorage("TKFlowStack")||[],n=o.length>0?o[o.length-1]:null;(!n||n.flowNodeNo!=e.flowNodeNo||t.string.isNotEmpty(n.flowToken)&&t.string.isNotEmpty(e.flowToken)&&n.flowToken!=e.flowToken)&&("0"==r?o.push(e):(o.pop(),o.push(e)),t.storage.setSessionStorage("TKFlowStack",o),t.saveLocalFlowInsCurNode(e),t.log("pushFlowStack----\x3e",e))}},v=function(e,r){e=e||{},r=(void 0===r?null:r+"")||"1";var o=t.storage.getSessionStorage("TKFlowStack")||[];if(o.length>0){var n=0;if(t.string.isNotEmpty(e.flowNodeNo)){for(var i=-1,s=o.length-1;s>=0;s--){var a=o[s].flowNodeNo,h=a.split("-");h.shift(),a=h.join("_");var u=e.flowNodeNo,f=u.split("-");if(f.shift(),u=f.join("_"),a==u){i=s,n=s-o.length+1;break}}i<0?(o.pop(),o.push(e)):o=o.slice(0,i+1)}else o.pop(),n=-1;t.storage.setSessionStorage("TKFlowStack",o);var l=o.length>0?o[o.length-1]:null;l&&t.string.isNotEmpty(l.flowNodeNo)&&t.saveLocalFlowInsCurNode(l),t.log("popFlowStack----\x3e",e),n<0&&"1"==r&&(n+window.history.length<=0&&(n=1-window.history.length),window.history.go(n))}},w=function(){var e=t.storage.getSessionStorage("TKFlowStack")||[];return t.log("getFlowStack----\x3e"),e},m=function(e,r,o){e=e||{},r=(void 0===r?null:r+"")||"0",o=(void 0===o?null:o+"")||r,t.pushFlowStack(e,o),t.string.isNotEmpty(e.flowNodeNo)&&(t.pushStateFunc=t.pushStateFunc||window.TKPushStateFunc,t.replaceStateFunc=t.replaceStateFunc||window.TKReplaceStateFunc,"1"==r?t.replaceStateFunc&&t.replaceStateFunc(e):t.pushStateFunc&&t.pushStateFunc(e),t.log("forward----\x3e",e))},y=function(e,r,o){r=(void 0===r?null:r+"")||"1",o=(void 0===o?null:o+"")||"1",e=e||{},t.popFlowStack(e,r),t.string.isNotEmpty(e.flowNodeNo)&&(t.setFlowInsCurNode(e.flowNodeNo),"1"==o&&(t.replaceStateFunc=t.replaceStateFunc||window.TKReplaceStateFunc,t.replaceStateFunc&&setTimeout(function(){t.replaceStateFunc(e)},100)),t.log("goback----\x3e",e))},S=function(e){e=(void 0===e?null:e+"")||"1";var r=0,o=t.storage.getSessionStorage("TKFlowStack")||[];o.length>0&&(r=-o.length),t.storage.setSessionStorage("TKFlowStack",[]),t.saveLocalFlowInsCurNode(null),r<0&&"1"==e&&(r+window.history.length<=0&&(r=1-window.history.length),window.history.go(r))},T=function(e,r){e=(void 0===e?null:e+"")||"1",r=(void 0===r?null:r+"")||"0";var o=t.storage.getSessionStorage("TKFlowStack")||[];if(o.length>0){for(var n=0,i=null,s=null,a=-1,h=o.length-1;h>=0;h--){var u=o[h].flowNodeNo,f=o[h].flowToken,l=u.split("_");if(l.pop(),l=l.join("_"),i||(i=l,s=f),i!=l||"1"!=r&&t.string.isNotEmpty(s)&&t.string.isNotEmpty(f)&&s!=f){a=h,n=h-o.length+1,o=o.slice(0,a+1);break}}a<0&&(n=-o.length,o=[]),t.storage.setSessionStorage("TKFlowStack",o);var c=o.length>0?o[o.length-1]:null;c&&t.string.isNotEmpty(c.flowNodeNo)&&t.saveLocalFlowInsCurNode(c),t.log("jumpout----\x3e",c),n<0&&"1"==e&&(n+window.history.length<=0&&(n=1-window.history.length),window.history.go(n))}},N=function(){var e=t.net.getCurUrlParameter();return!!(e&&"1"==e._isTKDevelop||"1"==t.storage.getSessionStorage("TKFlowDevelopMode"))},b=function(){var t=window.location.href;t=t.split("?")[0];var e=t.split("/");if(e.length>0){var r=e.pop();if(r.indexOf("Flow_")>=0){var o=r.split("_");return o.pop(),o.join("_")}}return""},x=function(){var t=window.location.href;t=t.split("?")[0];var e=t.split("/");if(e.length>0){var r=e.pop();if(r.indexOf("Flow_")>=0)return r=r.split("#")[0]}return""},O=function(){var e=[],r=t.storage.getSessionStorage("TKFlowStack")||[];if(r.length>0)for(var o=null,n=null,i=0;i<r.length;i++){var s=r[i].flowNodeNo,a=r[i].flowToken,h=s.split("_");h.pop(),h=h.join("_"),(o!=h||t.string.isNotEmpty(n)&&t.string.isNotEmpty(a)&&n!=a)&&(o=h,n=a,e.push(o))}return t.log("getCurTotalFlows----\x3e",e),e};t.saveFlowInsToken=s,t.flowInsNext=a,t.flowInsSave=h,t.flowInsNode=u,t.flowInsContext=f,t.setFlowInsCurNode=l,t.saveLocalFlowInsCurNode=c,t.getLocalFlowInsCurNode=p,t.getLocalFlowInsToken=g,t.forward=m,t.goback=y,t.goHome=S,t.jumpout=T,t.pushFlowStack=d,t.popFlowStack=v,t.getFlowStack=w,t.getCurTotalFlows=O,t.isTKDevelop=N}(TKFlowEngine),window.TKFlowEngine=TKFlowEngine;