//桥接客户端
var uniqueId = 1;
var respCallbacks = {};
var respCallbacks1001 = {};

/**
 * 调用native功能
 * @param funcId native功能号
 * @param param 入参
 * @param respCallback 回调
 */
function callNative(funcId, param, respCallback) {
    var callbackId = '';
    if (respCallback) {
        //创建请求id
        callbackId = 'cb_' + (uniqueId ++) + '_' + new Date().getTime();
        //以请求id为键，将回调放入回调列表s
        respCallbacks[callbackId] = respCallback;
    }
    //alert('callNative[funcId=' + funcId + '] [callbackId=' + callbackId + '] [param=' + param + ']');
    //当yjbInteface未被有效绑定时，undefined会导致js报错，所以这个检测很有必要

    // if (window.yjbInterface == {} || window.yjbInterface == undefined || window.yjbInterface.execute == undefined) {
    //     alert('window.yjbInterface未绑定');
    // }
    // window.yjbInterface.execute(funcId, param, callbackId);

    var userAgent = navigator? (navigator.userAgent? navigator.userAgent : '' ): '';
    var isWKWebView = userAgent.indexOf('ios/wkwebview') > -1 ;
    if(isWKWebView){
        if (window.webkit.messageHandlers.yjbInterface == {} || window.webkit.messageHandlers.yjbInterface == undefined || window.webkit.messageHandlers.yjbInterface.postMessage == undefined) {
            console.error('未与native进行桥接！');
            return;
        }
        window.webkit.messageHandlers.yjbInterface.postMessage({functionId:funcId, param:param, callbackId:callbackId});
    }else{
        //当yjbInteface未被有效绑定时，undefined会导致js报错，所以这个检测很有必要
        if (window.yjbInterface == {} || window.yjbInterface == undefined || window.yjbInterface.execute == undefined) {
            // typeof noNativeInterfaceAvailable === 'function' && noNativeInterfaceAvailable(new ReferenceError('未与native进行桥接！'));
            alert('window.yjbInterface未绑定');
        }
        window.yjbInterface.execute(funcId, param, callbackId);
    }
}

/**
 * 供native调用，用来统一处理native的返回信息
 * @param resp 处理结果
 */
function respFromNative(data, callbackId) {
    //alert('respFromNative[callbackId=' + callbackId + '] [data=' + JSON.stringify(data) + ']');
    if (callbackId) {
        //取出回调
        var respCallback = respCallbacks[callbackId];
        if (!respCallback) {
            console.log('respCallback is non-existent');
            return;
        }
        //调用前端回调
        respCallback(data);
        delete respCallbacks[callbackId];
    } else {
        console.log('native resp failed');
        return;
    }
}























