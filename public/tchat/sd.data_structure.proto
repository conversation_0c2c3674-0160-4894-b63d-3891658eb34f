option optimize_for = LITE_RUNTIME;

//服务注册
message SERVICE_REGISTER_REQ
{
	required uint64 nodeid                  = 1;    //服务器nodeid [ip地址+端口]
	required int32 nodetype                 = 2;    //服务器结点类型
	optional string media_wanip             = 3;    //媒体转发服务器公网地址
	optional int32 media_wanport            = 4;    //媒体转发服务器公网端口
	optional string media_lanip             = 5;    //媒体转发服务器局域网地址
	optional int32 media_lanport            = 6;    //媒体转发服务器局域网端口
	optional string recordip                = 7;    //合成流录像地址
	optional int32 recordport               = 8;    //合成流录像端口
	optional string content                 = 9;    //备用
}

message SERVICE_REGISTER_RESP
{
	optional string content                 = 1;    //备用
}



//服务器心跳
message SERVICE_HEARTBEAT_REQ
{
	optional uint64 nodeid                  = 1;     //服务器nodeid
	optional int32 nodetype                 = 2;     //服务器结点类型
	optional int32 usersize                 = 3;     //房间用户数
	optional string content                 = 4;     //备用

}

message SERVICE_HEARTBEAT_RESP
{
	optional string content                 = 1;     //备用
}



//创建房间-同步
message CREATE_ROOM_PUSH_REQ
{
	required int32 roomid                   = 1;     //房间ID
	required int32 creator                  = 2;     //创建人
	optional string roomname                = 3;     //房间名称
	optional string content                 = 4;     //备用
}

message CREATE_ROOM_PUSH_RESP
{
	optional string content                 = 1;     //备用
}


//删除房间-同步
message DELETE_ROOM_PUSH_REQ
{
	required int32 roomid                   = 1;     //房间ID
}

message DELETE_ROOM_PUSH_RESP
{
	optional string content                 = 1;     //备用
}


//用户进入房间-同步
message ENTER_ROOM_PUSH_REQ
{
	required int32 roomid                   = 1;     //房间ID
	required int32 userid                   = 2;     //用户ID
}

message ENTER_ROOM_PUSH_RESP
{
	optional string content                 = 1;     //备用
}


//用户离开房间-同步
message LEAVE_ROOM_PUSH_REQ
{
	required int32 roomid                   = 1;     //房间ID
	required int32 userid                   = 2;     //用户ID
}

message LEAVE_ROOM_PUSH_RESP
{
	optional string content                 = 1;     //备用
}



//录像ID列表
message RECORD_ID_LIST
{
	optional int32 userid                   = 1;     //用户ID
}


//开始录像-同步
message START_RECORD_PUSH_REQ
{
	optional int32 roomid                   = 1;     //房间ID
	optional int32 userid                   = 2;     //用户ID
	repeated RECORD_ID_LIST list            = 3;     //录像ID列表
	optional int32 type                     = 4;     //录像类型 10以下:录制音频  10以上:录制视频
	optional int32 connectid                = 5;     //连接ID-业务服务器触发录像需要
	optional int32 frame                    = 6;     //帧率
	optional int32 audiorate                = 7;     //音频码率
	optional int32 videorate                = 8;     //视频码率
	optional int32 resolutewidth            = 9;     //分辨率宽
	optional int32 resolutehigh             = 10;    //分辨率高
	optional int32 flag                     = 11;    //标志
	optional int32 param                    = 12;    //用户自定义参数(整形)
	optional string uservalue               = 13;		 //用户自定义参数(字符串)
	optional int32 mode                     = 14;		 //录像模式   1:服务端录制   2:客户端合成流录制  3:业务服务器录制
	optional string token                   = 15;    //通信秘钥   客户端录制录制需要
	optional int32 recordid                 = 16;    //录像ID
	optional int32 showtype                 = 17;    //录像显示类型
	optional int32 displayscale             = 18;    //画中画显示比例
	optional string content                 = 19;    //备用
}

message START_RECORD_PUSH_RESP
{
	optional int32 userid                   = 1;     //用户ID
	optional string filename                = 2;     //文件名称
	repeated RECORD_ID_LIST list            = 3;     //录像ID列表
	optional int32 type                     = 4;     //录像类型 10以下:录制音频  10以上:录制视频
	optional int32 connectid                = 5;     //连接ID-业务服务器触发录像需要
	optional int32 flag                     = 6;     //标志
	optional int32 param                    = 7;     //用户自定义参数(整形)
	optional string uservalue               = 8;		 //用户自定义参数(字符串)
	optional int32 mode                     = 9;		 //录像模式   1:服务端录制   2:客户端合成流录制  3:业务服务器录制
	optional int32 recordid                 = 10;    //录像ID
	optional string content                 = 11;    //备用
}


//停止录像-同步
message STOP_RECORD_PUSH_REQ
{
	optional int32 roomid                   = 1;     //房间ID
	optional int32 userid                   = 2;     //用户ID
	optional string filename                = 3;     //录像文件名称
	optional int32 mode                     = 4;		 //录像模式   1:服务端录制   2:客户端合成流录制  3:业务服务器录制
	optional string content                 = 5;     //备用
}

message STOP_RECORD_PUSH_RESP
{
	optional string content                 = 1;     //备用
}



//设置录像参数-同步
message SET_VIDEO_PUSH_REQ
{
	optional int32 roomid                   = 1;     //房间ID
	optional int32 userid                   = 2;     //用户ID
	optional int32 frame                    = 3;     //帧率
	optional int32 rate                     = 4;     //码率
	optional string content                 = 5;     //备用
}

message SET_VIDEO_PUSH_RESP
{
	optional string content                 = 1;     //备用
}




//用户登录
message LOGIN_REQ
{
	required string username                = 1;     //用户名
	required string passwd                  = 2;     //用户密码
	optional int32 devicetype               = 3;     //终端类型
        optional int32 userid                   = 4;     //重连用户ID
	optional string content                 = 5;     //备用
}

message LOGIN_RESP
{
	required int32 userid                   = 1;     //用户id
	optional string nickname                = 2;     //用户昵称
	optional int32 timeout                  = 3;     //心跳超时时间
	optional string key                     = 4;     //通信秘钥
	optional int32  remoteuserid		= 5;	 //座席端userid，中心服务器不处理，壳子端添加
	optional string content                 = 6;     //备用
}



//获取房间列表
message GET_ROOMLIST_REQ
{
	optional string content                 = 1;     //备用
}


message ROOM_LIST
{
	required int32 roomid                   = 1;     //房间ID
	optional int32 onlinenumber             = 2;     //房间在线人数
	optional bool ispasswd                  = 3;     //房间是否设置密码
	optional string roomname                = 4;     //房间名称
	optional int32	createid				= 5;     //创建者
	optional string content                 = 6;     //备用
}

message GET_ROOMLIST_RESP
{
	repeated ROOM_LIST list                 = 1;     //房间列表
	optional string content                 = 2;     //备用
}





//获取用户列表
message GET_USERLIST_REQ
{
	required int32 roomid                   = 1;     //房间ID
	optional string content                 = 2;     //备用
}


message USER_LIST
{
	required int32 userid                   = 1;     //用户ID
	optional string username                = 2;     //用户名
	optional string nickname                = 3;     //用户昵称
	optional int32 devicetype               = 4;     //终端类型
	optional bool videostatus               = 5;     //视频状态  true:开启  false:关闭
	optional bool audiostatus               = 6;     //音频状态  true:开启  false:关闭
	optional string content                 = 7;     //备用
	
}

message GET_USERLIST_RESP
{
	required int32 roomid                   = 1;     //房间ID
	repeated USER_LIST list                 = 2;     //用户列表
	optional string content                 = 3;     //备用
}



//用户退出登录
message LOGOUT_REQ
{
	optional string content                 = 1;     //备用
}

message LOGOUT_RESP
{
	optional string content                 = 1;     //备用
}



//用户心跳
message USER_HEARTBEAT_REQ
{
	required int32 userid                   = 1;     //用户id 
	optional string content                 = 2;     //备用
}

message USER_HEARTBEAT_RESP
{
	optional string content                 = 1;     //备用
}


//用户进入房间
message ENTER_ROOM_REQ
{
	optional int32 roomid                   = 1;     //房间ID
	optional string password                = 2;     //房间密码
	optional string roomname                = 3;     //房间名称
	optional string content                 = 4;     //备用
}

message ENTER_ROOM_RESP
{
	required int32 roomid                   = 1;     //房间ID
	optional string roomname                = 2;     //房间名称
	optional string vserverip               = 3;     //媒体转发服务器地址
	optional int32 vserverport              = 4;     //媒体转发服务器端口
	optional string rserverip               = 5;     //合成流录像地址
	optional int32 rserverport              = 6;     //合成流录像端口
	repeated USER_LIST list                 = 7;     //用户列表
	optional string content                 = 8;     //备用
}



//用户离开房间
message LEAVE_ROOM_REQ
{
	optional string content                 = 1;     //备用
}

message LEAVE_ROOM_RESP
{
	optional int32 roomid                   = 1;     //房间ID
	optional string content                 = 2;     //备用
}



//用户被踢出房间
message KICK_OUT_ROOM_REQ
{
	optional string content                 = 1;     //备用
}

message KICK_OUT_ROOM_RESP
{
	optional string content                 = 1;     //备用
}


//用户消息发送-指定用户
message SEND_MESSAGE_REQ
{
	required int32 userid                   = 1;     //用户ID
	required string msg                      = 2;     //发送数据内容
	optional int32 msgid                    = 3;     //消息ID
	optional string content                 = 4;     //备用
}


message SEND_MESSAGE_RESP
{
	optional int32 msgid                    = 1;     //消息ID
	optional string content                 = 2;     //备用
}



//用户消息发送-群发
message BROAD_MESSAGE_REQ
{
	required bytes msg                      = 1;     //发送数据内容
	optional int32 msgid                    = 2;     //消息ID
	optional string content                 = 3;     //备用
}


message BROAD_MESSAGE_RESP
{
	optional int32 msgid                    = 1;     //消息ID
	optional string content                 = 2;     //备用
}




//用户上线通知
message USER_ONLINE_REQ
{
	required int32 userid                   = 1;     //用户ID
	optional string username                = 2;     //用户名
	optional string nickname                = 3;     //用户昵称
	optional int32 devicetype               = 4;     //终端类型
	optional string content                 = 5;     //备用
	
}

message USER_ONLINE_RESP
{
	optional string content                 = 1;     //备用
}


//用户下线通知
message USER_OFFLINE_REQ
{
	required int32 userid                   = 1;     //用户ID
	optional string username                = 2;     //用户名
	optional string nickname                = 3;     //用户昵称
	optional string content                 = 4;     //备用
}

message USER_OFFLINE_RESP
{
	optional string content                 = 1;     //备用
}

//建立UDP通道
message CREATE_CHANNEL_REQ
{
	required int32 roomid                   = 1;     //房间ID
	required int32 userid                   = 2;     //用户ID

}


message CREATE_CHANNEL_RESP
{
	optional int32 roomid                   = 1;     //房间ID
	optional int32 userid                   = 2;     //用户ID
	optional string content                 = 3;     //备用
}



//服务器消息转发
message MESSAGE_PUSH_REQ
{
	required int32 userid                   = 1;     //用户ID
	required string username                = 2;     //用户名
	required bytes msg                      = 3;     //发送内容
	optional bool isprivate                 = 4;     //是否是私有消息 true:私有 false:群发
}


message MESSAGE_PUSH_RESP
{
	optional string content                 = 1;     //备用
}


//客户端发送透明通道数据
message SEND_TRANS_BUFFER_REQ
{
	required int32 userid                   = 1;     //用户ID
	required bytes cmdmsg                   = 2;     //自定义数据
	optional int32 msgid                    = 3;     //消息ID
	optional string content                 = 4;     //备用
}


message SEND_TRANS_BUFFER_RESP
{
	optional int32 msgid                    = 1;     //消息ID
	optional string content                 = 2;     //备用
}



//更换转发服务器
message CHANGE_SERVER_REQ
{
	required string serverip                = 1;     //视频数据包转发服务器地址
	required int32 serverport               = 2;     //视频数据包转发服务器端口
	optional string content                 = 3;     //备用
}


message CHANGE_SERVER_RESP
{
	optional string content                 = 1;     //备用
}



//新建房间通知
message ADD_NEWROOM_REQ
{
	required string username                = 1;     //房间创建人
	required int32 roomid                   = 2;     //房间ID
	optional string roomname                = 3;     //房间名称
	optional bool ispasswd                  = 4;     //房间是否设置密码
	optional string content                 = 5;     //备用
}


message ADD_NEWROOM_RESP
{
	optional string content                 = 1;     //备用
}


//删除房间通知
message SEND_DELETE_ROOM_REQ
{
	required int32 roomid                   = 1;     //房间ID
	optional string roomname                = 2;     //房间名称
	optional string content                 = 3;     //备用
}


message SEND_DELETE_ROOM_RESP
{
	optional string content                 = 1;     //备用
}




//客户端视频呼叫请求
message VIDEO_CALL_REQ
{
	required int32 userid                   = 1;     //用户ID
	optional int32 calltype                 = 2;     //呼叫类型 1:视频呼叫  2.音频呼叫
	optional int32 param                    = 3;     //用户自定义参数(整形)
	optional string uservalue               = 4;		 //用户自定义参数(字符串)
	optional string content                 = 5;     //备用
}


message VIDEO_CALL_RESP
{
	required int32 userid                   = 1;     //用户ID
	required bool isagree                   = 2;     //是否同意视频请求
	optional string username                = 3;     //用户名
	optional int32 param                    = 4;     //用户自定义参数(整形)
	optional string uservalue               = 5;		 //用户自定义参数(字符串)
	optional string content                 = 6;     //备用
}


//服务端转发视频呼叫请求
message VIDEO_CALL_PUSH_REQ
{
	optional int32 userid                   = 1;     //用户ID
	optional string username                = 2;     //用户名
	optional int32 calltype                 = 3;     //呼叫类型 1:视频呼叫  2.音频呼叫
	optional int32 param                    = 4;     //用户自定义参数(整形)
	optional string uservalue               = 5;		 //用户自定义参数(字符串)
	optional string content                 = 6;     //备用
}


message VIDEO_CALL_PUSH_RESP
{
	optional int32 userid                   = 1;     //用户ID
	optional bool isagree                   = 2;     //是否同意视频请求
	optional int32 calltype                 = 3;     //呼叫类型 1:视频呼叫  2.音频呼叫
	optional int32 param                    = 4;     //用户自定义参数(整形)
	optional string uservalue               = 5;		 //用户自定义参数(字符串)
	optional string content                 = 6;     //备用
}


//服务端推送用户进入房间请求
message SEND_ENTERN_ROOM_REQ
{
	optional int32 roomid                   = 1;     //房间ID
	optional string password                = 2;     //房间密码
	optional int32 userid                   = 3;     //用户ID
	optional int32 calltype                 = 4;     //呼叫类型 1:视频呼叫  2.音频呼叫
	optional string content                 = 5;     //备用
}


message SEND_ENTERN_ROOM_RESP
{
	optional string content                 = 1;     //备用
}



//用户开始录像请求
message START_RECORD_REQ
{
	repeated RECORD_ID_LIST list            = 1;     //录像ID列表
	optional int32 type                     = 2;     //录像类型   10以下:录制音频  10以上:录制视频
	optional int32 frame                    = 3;     //帧率
	optional int32 audiorate                = 4;     //音频码率
	optional int32 videorate                = 5;     //视频码率
	optional int32 resolutewidth            = 6;     //分辨率宽
	optional int32 resolutehigh             = 7;     //分辨率高
	optional int32 flag                     = 8;     //标志
	optional int32 param                    = 9;     //用户自定义参数(整形)
	optional string uservalue               = 10;		 //用户自定义参数(字符串)
	optional int32 mode                     = 11;		 //录像模式   1:服务端录制   2:客户端合成流录制  3:业务服务器录制
	optional int32 recordid                 = 12;    //录像ID
	optional int32 showtype                 = 13;    //录像显示类型
	optional int32 displayscale             = 14;    //画中画显示比例
	optional string content                 = 15;    //备用
}


message START_RECORD_RESP
{
	optional string filename                = 1;     //文件名称
	optional int32 flag                     = 2;     //标志
	optional int32 param                    = 3;     //用户自定义参数(整形)
	optional string uservalue               = 4;		 //用户自定义参数(字符串)
	optional int32 recordid                 = 5;     //录像ID
	optional string content                 = 6;     //备用
}



//用户停止录像请求
message STOP_RECORD_REQ
{
	optional string filename                = 1;     //文件名称
	optional int32 mode                     = 2;		 //录像模式   1:服务端录制   2:客户端合成流录制  3:业务服务器录制
	optional string content                 = 3;     //备用
}


message STOP_RECORD_RESP
{
	optional string content                 = 1;     //备用
}

//用户设置录像参数请求
message SET_VIDEO_REQ
{
	optional int32 frame                    = 1;     //帧率
	optional int32 rate                     = 2;     //码率
	optional string content                 = 3;     //备用
}


message SET_VIDEO_RESP
{
	optional string content                 = 1;     //备用
}


//服务端完成录像请求
message COMPLETE_VIDEO_REQ 
{
	required int32 userid                   = 1;     //用户ID
	optional string filename                = 2;     //文件名称/路径
	optional int32 elapse                   = 3;     //录像时间
	optional int32 flag                     = 4;     //标志
	optional int32 param                    = 5;     //用户自定义参数(整形)
	optional string uservalue               = 6;		 //用户自定义参数(字符串)
	optional string content                 = 7;     //备用
}


message COMPLETE_VIDEO_RESP
{
	optional string content                 = 1;     //备用
}


//服务端推送完成录像请求
message FINISH_RECORD_PUSH_REQ 
{
	required int32 userid                   = 1;     //用户ID
	optional string filename                = 2;     //文件名称/路径
	optional int32 elapse                   = 3;     //录像时间
	optional int32 flag                     = 4;     //标志
	optional int32 param                    = 5;     //用户自定义参数(整形)
	optional string uservalue               = 6;		 //用户自定义参数(字符串)
	optional string content                 = 7;     //备用
}


message FINISH_RECORD_PUSH_RESP
{
	optional string content                 = 1;     //备用
}


//透明通道传送数据请求
message TRANS_BUFFER_REQ 
{
	required int32 userid                   = 1;     //用户ID
	required bytes cmdmsg                   = 2;     //自定义数据
	optional string content                 = 3;     //备用
}


message TRANS_BUFFER_RESP
{
	required int32 userid                   = 1;     //用户ID
	optional string content                 = 2;     //备用
}


//录像控制请求
message RECORD_CTRL_REQ 
{
	required int32 userid                   = 1;     //用户ID
	required bool opstatus                  = 2;     //true:开始录像,false:停止录像
	optional string filename                = 3;		 //停止视频需要文件名称
	optional int32 frame                    = 4;     //帧率
	optional int32 audiorate                = 5;     //音频码率
	optional int32 videorate                = 6;     //视频码率
	optional int32 resolutewidth            = 7;     //分辨率宽
	optional int32 resolutehigh             = 8;     //分辨率高
	optional int32 flag                     = 9;     //标志参数
	optional int32 param                    = 10;    //用户自定义参数(整形)
	optional string uservalue               = 11;		 //用户自定义参数(字符串)
	optional int32 serverid                 = 12;    //录像服务ID
	optional string content                 = 13;    //备用
}


message RECORD_CTRL_RESP
{
	optional int32 userid                   = 1;     //用户ID
	optional string filename                = 2;     //开始录像返回文件名称
	optional string content                 = 3;     //备用
}


//视频呼叫控制请求
message VIDEO_CALL_CTRL_REQ 
{
	required int32 userid                   = 1;     //用户ID
	required bool opstatus                  = 2;     //true:开始视频,false: 停止视频
	optional int32 eventtype                = 3;     //事件类型
	optional int32 code                     = 4;     //出错代码
	optional int32 flag                     = 5;     //呼叫标志
	optional int32 param                    = 6;     //用户自定义参数(整形)
	optional string uservalue               = 7;		 //用户自定义参数(字符串)
	optional string content                 = 8;     //备用
}


message VIDEO_CALL_CTRL_RESP
{
	required int32 userid                   = 1;     //用户ID
	optional string content                 = 2;     //备用
}



//透明通道数据推送到客户端请求
message TRANS_BUFFER_PUSH_REQ 
{
	required int32 userid                   = 1;     //用户ID
	required bytes cmdmsg                   = 2;     //自定义数据
	optional string content                 = 3;     //备用
}


message TRANS_BUFFER_PUSH_RESP
{
	optional string content                 = 1;     //备用
}



//数据认证请求
message DATA_VERIFIER_REQ 
{
	required string version                 = 1;     //版本号
	required string secretkey               = 2;     //认证秘钥
	optional int32	roomid					= 3;	 //排队分配的roomid
	optional int32  userid					= 4;	 //呼叫模式时指定坐席userid
	optional int32  connid					= 5;	 //H5重连的时候带上connid
	optional string content                 = 6;     //备用 roomid、userid均为0则为呼叫模式信令随机选择坐席
}


message DATA_VERIFIER_RESP
{
	optional string content                 = 1;     //备用
}

//座席端(webrtc代理)通知排队房间号
message WEBRTC_NOTIFY_ROOMID_REQ
{
	optional int32 roomid					= 1;	//排队生成的roomid
	optional int32 userid					= 2;	//呼叫模式时坐席userid
	optional string content					= 3;	//备用
}

message WEBRTC_NOTIFY_ROOMID_RESP
{
	optional string content					= 1;	//备用
}


//客户端停止视频请求
message STOP_VIDEO_REQ 
{
	optional int32 userid                   = 1;     //用户ID
	optional int32 param                    = 2;     //用户自定义参数(整形)
	optional string uservalue               = 3;		 //用户自定义参数(字符串)
	optional string content                 = 4;     //备用
}


message STOP_VIDEO_RESP
{
	optional string content                 = 1;     //备用
}



//客户端停止视频推送到客户端请求
message STOP_VIDEO_PUSH_REQ 
{
	optional int32 userid                   = 1;     //用户ID
	optional string username                = 2;     //用户名
	optional int32 param                    = 3;     //用户自定义参数(整形)
	optional string uservalue               = 4;		 //用户自定义参数(字符串)
	optional string content                 = 5;     //备用
}


message STOP_VIDEO_PUSH_RESP
{
	optional string content                 = 1;     //备用
}




//获取在线用户列请求
message ONLINE_USER_LIST_REQ
{
	optional string content                 = 1;     //备用
}


message ONLINE_USER_LIST
{
	required int32 userid                   = 1;     //用户ID
	optional string username                = 2;     //用户名
	optional string nickname                = 3;     //用户昵称
	optional int32 devicetype               = 4;     //终端类型
	optional string clientip                = 5;     //用户IP
	optional string content                 = 6;     //备用
	
}

message ONLINE_USER_LIST_RESP
{
	repeated ONLINE_USER_LIST list          = 1;     //在线用户列
	optional string content                 = 2;     //备用
}



//用户进入房间通知
message INROOM_PUSH_REQ
{
	required int32 userid                   = 1;     //用户ID
	optional int32 roomid                   = 2;     //房间ID
	optional string username                = 3;     //用户名
	optional string nickname                = 4;     //用户昵称
	optional int32 devicetype               = 5;     //终端类型
	optional string content                 = 6;     //备用
	
}

message INROOM_PUSH_RESP
{
	optional string content                 = 1;     //备用
}


//用户离开房间通知
message OUTROOM_PUSH_REQ
{
	required int32 userid                   = 1;     //用户ID
	optional int32 roomid                   = 2;     //房间ID
	optional string username                = 3;     //用户名
	optional string nickname                = 4;     //用户昵称
	optional string content                 = 5;     //备用
	
}

message OUTROOM_PUSH_RESP
{
	optional string content                 = 1;     //备用
}



//用户操作视频数据请求(CS -> VS)
message OPERATE_VIDEO_PUSH_REQ
{
	required int32 userid                   = 1;     //用户ID
	required int32 roomid                   = 2;     //房间ID
	required int32 requestid                = 3;     //请求用户ID
	optional bool status                    = 4;     //true:请求视频  false:停止视频
	optional string content                 = 5;     //备用
	
}

message OPERATE_VIDEO_PUSH_RESP
{
	required int32 userid                   = 1;     //用户ID
	required int32 requestid                = 2;     //请求用户ID
	optional bool status                    = 3;     //true:请求视频  false:停止视频
	optional string content                 = 4;     //备用
}


//用户操作音频数据请求(CS -> VS)
message OPERATE_AUDIO_PUSH_REQ
{
	required int32 userid                   = 1;     //用户ID
	required int32 roomid                   = 2;     //房间ID
	optional int32 requestid                = 3;     //请求用户ID
	optional bool status                    = 4;     //true:请求音频  false:停止音频
	optional string content                 = 5;     //备用
	
}

message OPERATE_AUDIO_PUSH_RESP
{
	required int32 userid                   = 1;     //用户ID
	optional int32 requestid                = 2;     //请求用户ID
	optional bool status                    = 3;     //true:请求音频  false:停止音频
	optional string content                 = 4;     //备用
}




//用户视频状态改变通知(CS -> Client)
message VIDEO_CHANGE_PUSH_REQ
{
	required int32 userid                   = 1;     //用户ID
	required bool status                    = 2;     //true:打开视频  false:关闭视频
	optional string username                = 3;     //用户名
	optional string nickname                = 4;     //用户昵称
	optional string content                 = 5;     //备用
	
}

message VIDEO_CHANGE_PUSH_RESP
{
	optional string content                 = 1;     //备用
}


//用户音频状态改变通知(CS -> Client)
message AUDIO_CHANGE_PUSH_REQ
{
	required int32 userid                   = 1;     //用户ID
	required bool status                    = 2;     //true:打开音频  false:关闭音频
	optional string username                = 3;     //用户名
	optional string nickname                = 4;     //用户昵称
	optional string content                 = 5;     //备用
	
}

message AUDIO_CHANGE_PUSH_RESP
{
	optional string content                 = 1;     //备用
}


//用户改变视频状态请求
message VIDEO_STATUS_REQ
{
	required bool status                    = 1;     //true:打开视频  false:关闭视频
	optional string content                 = 2;     //备用
	
}

message VIDEO_STATUS_RESP
{
	optional string content                 = 1;     //备用
}



//用户改变音频状态请求
message AUDIO_STATUS_REQ
{
	required bool status                    = 1;     //true:打开音频  false:关闭音频
	optional string content                 = 2;     //备用
	
}

message AUDIO_STATUS_RESP
{
	optional string content                 = 1;     //备用
}




//用户操作视频数据请求
message OPERATE_VIDEO_REQ
{
	required int32 userid                   = 1;     //请求用户ID
	optional bool status                    = 2;     //true:请求视频  false:停止视频
	optional string content                 = 3;     //备用
	
}

message OPERATE_VIDEO_RESP
{
	required int32 userid                   = 1;     //请求用户ID
	optional bool status                    = 2;     //true:请求视频  false:停止视频
	optional string content                 = 3;     //备用
}




//用户操作音频数据请求
message OPERATE_AUDIO_REQ
{
	required int32 userid                   = 1;     //请求用户ID
	optional bool status                    = 2;     //true:请求音频  false:停止音频
	optional string content                 = 3;     //备用
	
}

message OPERATE_AUDIO_RESP
{
	required int32 userid                   = 1;     //请求用户ID
	optional bool status                    = 2;     //true:请求音频  false:停止音频
	optional string content                 = 3;     //备用
}







//P2P通道建立请求
message P2P_CHANNEL_REQ
{
	optional int32 userid                   = 1;     //用户ID
	optional string content                 = 2;     //备用
}

message P2P_CHANNEL_RESP
{
	optional string content                 = 1;     //备用
}



//P2P通道建立请求(CS -> VS)
message P2P_CHANNEL_PUSH_REQ
{
	optional int32 userid                   = 1;     //用户ID
	optional int32 requestid                = 2;     //请求用户ID
	optional string content                 = 3;     //备用
}

message P2P_CHANNEL_PUSH_RESP
{
	optional int32 userid                   = 1;     //用户ID
	optional int32 requestid                = 2;     //请求用户ID
	optional string userip                  = 3;     //用户地址
	optional int32 userport                 = 4;     //用户端口
	optional string requestip               = 5;     //被请求用户地址
	optional int32 requestport              = 6;     //被请求用户端口
	optional string content                 = 7;     //备用
}



//推送P2P通道建立请求(CS -> Client)
message P2P_CREATE_PUSH_REQ
{
	optional int32 userid                   = 1;     //用户ID
	optional string userip                  = 2;     //用户地址
	optional int32 userport                 = 3;     //用户端口
	optional string content                 = 4;     //备用
}

message P2P_CREATE_PUSH_RESP
{
	optional string content                 = 1;     //备用
}



//发送内网地址请求
message P2P_ADDRESS_REQ
{
	optional int32 userid                   = 1;     //用户ID
	optional string userip                  = 2;     //用户地址
	optional int32 userport                 = 3;     //用户端口
	optional string content                 = 4;     //备用
}

message P2P_ADDRESS_RESP
{
	optional string content                 = 1;     //备用
}



//推送P2P内网地址请求(CS -> Client)
message P2P_ADDRESS_PUSH_REQ
{
	optional int32 userid                   = 1;     //用户ID
	optional string content                 = 2;     //备用
}

message P2P_ADDRESS_PUSH_RESP
{
	optional string content                 = 1;     //备用
}




//获取加密随机串请求
message SECRET_KEY_REQ 
{
	optional string content                 = 1;     //备用
}


message SECRET_KEY_RESP
{
	optional string secretkey               = 1;     //加密串
	optional int32  connid					= 2;	 //H5连接ID
	optional string content                 = 3;     //备用
}




//录像认证状态推送(SS ->CS ->Client)
message VERIFY_STATUS_PUSH_REQ
{
	optional int32 userid                   = 1;     //用户ID
	optional bool status                    = 2;     //认证状态 true:认证成功  false:认证失败
	optional string filename                = 3;     //文件名称
	optional int32 recordid                 = 4;     //录像ID
	optional string content                 = 5;     //备用
}


message VERIFY_STATUS_PUSH_RESP
{
	optional string content                 = 1;     //备用
}




//客户端合成流录制请求(CLIENT -> SS)
message STREAM_RECORD_PUSH_REQ
{
	optional string token                   = 1;     //通信秘钥
	optional string filename                = 2;     //文件名称
	optional int32 recordid                 = 3;     //录像ID
	optional string content                 = 4;     //备用
}


message STREAM_RECORD_PUSH_RESP
{
	optional string content                 = 1;     //备用
}




//客户端获取视频配置信息
message MEDIA_CONFIG_REQ
{
	optional int32 type                     = 1;     //客户端类型
	optional string content                 = 2;     //备用
}


message MEDIA_CONFIG_RESP
{
	optional int32 dpiw                 		= 1;     //视频采集宽度
	optional int32 dpih                 		= 2;     //视频采集高度
	optional int32 vb                 			= 3;     //视频编码码率 
	optional int32 fps                 			= 4;     //视频编码帧率
	optional int32 gop                 			= 5;     //视频关键帧间隔
	optional int32 recab                 	  = 6;     //录像音频码率
	optional int32 recvb                 	  = 7;     //录像视频码率
	optional int32 recdpiw                  = 8;     //录像视频宽度
	optional int32 recdpih                  = 9;     //录像视频高度
	optional string content                 = 10;     //备用
}

//录像开始通知推送
message START_VIDEO_PUSH_REQ
{
	optional int32 userid					= 1;	
}

message START_VIDEO_PUSH_RESP
{

}

//推送SDP信息到同房间客户端请求
message EXCHANGE_SDP_REQ
{
	optional string sdpinfo					= 1;        //sdp信息
	optional int32	ownuserid				= 2;		//自身用户id
	optional int32	peeruserid				= 3;		//对端用户id
	optional int32	roomid					= 4;		//房间id
	optional string reserve   				= 5;		//备用
}

message EXCHANGE_SDP_RESP
{
	optional bool   flag					= 1;        //对方是否成功接收 0:success 1:failed
	optional string reserve   				= 2;		//备用
}

message EXCHANGE_ICE_REQ
{
	optional string iceinfo					= 1;		//ice信息
	optional int32	ownuserid				= 2;		//自身用户id
	optional int32	peeruserid				= 3;		//对端用户id
	optional int32	roomid					= 4;		//房间id
	optional string reserve    				= 5;		//备用
}

message EXCHANGE_ICE_RESP
{
	optional bool   flag					= 1;        //对方是否成功接收 0:success 1:failed
	optional string reserve   				= 2;		//备用
}

message USER_CAMERA_REQ
{
        optional bool isopen                                    = 1;        //摄像头是否打开
        optional string reserve                                 = 2;        //备用
}

message USER_CAMERA_RESP
{
        optional string reserve                                 = 1;        //备用
}

message H5USER_CONNECTION_REQ
{
	optional	string	ip_addr				= 1;		//用户ip地址
	optional	int32	port				= 2;		//用户端口
	optional    bool    flag				= 3;		//true: 连接到来  false:连接关闭
	optional    string  reserve				= 4;		//备用
}

message H5USER_CONNECTION_RESP
{
	optional 	bool    success				= 1;		//是否连接成功
	optional    string  reserve				= 2;		//备用
}

message CONN_GROUPNAME_REQ
{
	required	string	groupname			= 1;		//连接组名
	optional    string  reserve				= 2;		//备用
}

message CONN_GROUPNAME_RESP
{
	optional 	bool	success				= 1;		//成功与否
	optional    string  reserve				= 2;		//备用
}

message BAND_USING_INFO_REQ
{
	optional	string	username			= 1;		//用户名
	optional 	int32   devicetype          = 2;     	//终端类型	
	optional    int32 	duration			= 3;		//持续时间
	optional	int32	senddata			= 4;		//发送数据量
	optional	int32	recvdata			= 5;		//接收数据量	
	optional    string	reserve				= 6;		//备用
}

message BAND_USING_INFO_RESP
{
	optional	bool	success				= 1;		//成功与否
	optional	string	reserve				= 2;		//备用
}

//import"sd.data_define.proto";

//服务器节点类型
enum ServerType
{
	//中心控制服务器(CS)
	TYPE_CENTRESERVER       = 1;
	
	//业务处理服务器(BS)
	TYPE_BUSINESSSERVER     = 2;
	
	//视频转发服务器(VS)
	TYPE_VIDEOSERVER        = 3;
	
	//视频存储服务器(SS)
	TYPE_STORAGESERVER      = 4;
}


//客户端类型
enum ClientType
{
	//客户端-电脑
	TYPE_CLIENT_PC          = 1;
	
	//客户端-安卓
	TYPE_CLIENT_ANDROID     = 2;
	
	//客户端-苹果
	TYPE_CLIENT_IOS         = 3;
	
	//客户端-网页
	TYPE_CLIENT_WEB         = 4;
}





//服务端消息命令字
enum MsgType_Server
{
	//服务注册
	SERVER_SERVICE_REGISTER_REQ    = 0x1011;
	SERVER_SERVICE_REGISTER_RESP   = 0x1012;
	                               
	//服务器心跳                   
	SERVER_SERVICE_HEARTBEAT_REQ   = 0x1013;
	SERVER_SERVICE_HEARTBEAT_RESP  = 0x1014;
	                               
	//创建房间(CS -> VS)                
	SERVER_CREATE_ROOM_PUSH_REQ    = 0x1015;
	SERVER_CREATE_ROOM_PUSH_RESP   = 0x1016;
	                               
	//删除房间(CS -> VS)             
	SERVER_DELETE_ROOM_PUSH_REQ    = 0x1017;
	SERVER_DELETE_ROOM_PUSH_RESP   = 0x1018;
	                               
	//用户进入房间(CS -> VS)           
	SERVER_ENTER_ROOM_PUSH_REQ     = 0x1019;
	SERVER_ENTER_ROOM_PUSH_RESP    = 0x101A;
	                               
	//用户离开房间(CS -> VS)           
	SERVER_LEAVE_ROOM_PUSH_REQ     = 0x101B;
	SERVER_LEAVE_ROOM_PUSH_RESP    = 0x101C;
                                 
	//开始录像(CS -> VS)              
	SERVER_START_RECORD_PUSH_REQ   = 0x101D;
	SERVER_START_RECORD_PUSH_RESP  = 0x101E;
	                               
	//停止录像(CS -> VS)              
	SERVER_STOP_RECORD_PUSH_REQ    = 0x101F;
	SERVER_STOP_RECORD_PUSH_RESP   = 0x1020;
	                               
	//设置录像参数(CS -> VS)          
	SERVER_SET_VIDEO_PUSH_REQ      = 0x1021;
	SERVER_SET_VIDEO_PUSH_RESP     = 0x1022;
	                               
	//用户被踢出房间(CS -> Client)               
	SERVER_KICK_OUT_ROOM_REQ       = 0x1023;
	SERVER_KICK_OUT_ROOM_RESP      = 0x1024;
                                 
	//用户上线通知(CS -> Client)                  
	SERVER_USER_ONLINE_REQ         = 0x1025;
	SERVER_USER_ONLINE_RESP        = 0x1026;
	                               
	//用户下线通知(CS -> Client)                  
	SERVER_USER_OFFLINE_REQ        = 0x1027;
	SERVER_USER_OFFLINE_RESP       = 0x1028;
	                               
	//服务器消息转发(CS -> Client)               
	SERVER_MESSAGE_PUSH_REQ        = 0x1029;
	SERVER_MESSAGE_PUSH_RESP       = 0x102A;
	                               
	//更换转发服务器(CS -> Client)               
	SERVER_CHANGE_SERVER_REQ       = 0x102B;
	SERVER_CHANGE_SERVER_RESP      = 0x102C;
	                               
	//新建房间通知(CS -> Client)                 
	SERVER_ADD_NEWROOM_REQ         = 0x102D;
	SERVER_ADD_NEWROOM_RESP        = 0x102E;
	                               
	//删除房间通知(CS -> Client)                 
	SERVER_SEND_DELETE_ROOM_REQ    = 0x102F;
	SERVER_SEND_DELETE_ROOM_RESP   = 0x1030;
	                               
	//推送视频呼叫(CS -> Client)       
	SERVER_VIDEO_CALL_PUSH_REQ     = 0x1031;
	SERVER_VIDEO_CALL_PUSH_RESP    = 0x1032;
	                               
	//推送用户进入房间(CS -> Client)   
	SERVER_SEND_ENTERN_ROOM_REQ    = 0x1033;
	SERVER_SEND_ENTERN_ROOM_RESP   = 0x1034;
	                               
	//完成录像通知(SS -> CS)       
	SERVER_COMPLETE_VIDEO_REQ      = 0x1035;
	SERVER_COMPLETE_VIDEO_RESP     = 0x1036;
	                               
	//推送完成录像通知(CS -> Client)      
	SERVER_FINISH_RECORD_PUSH_REQ  = 0x1037;
	SERVER_FINISH_RECORD_PUSH_RESP = 0x1038;
	
	//透明通道传送数据(BS -> CS)
	SERVER_TRANS_BUFFER_REQ        = 0x1039;
	SERVER_TRANS_BUFFER_RESP       = 0x103A;
	                               
	//录像控制请求(BS -> CS)       
	SERVER_RECORD_CTRL_REQ         = 0x103B;
	SERVER_RECORD_CTRL_RESP        = 0x103C;
	                               
	//视频呼叫控制请求(BS -> CS)   
	SERVER_VIDEO_CALL_CTRL_REQ     = 0x103D;
	SERVER_VIDEO_CALL_CTRL_RESP    = 0x103E;
	                               
	//推送透明通道数据(CS -> Client) 
	SERVER_TRANS_BUFFER_PUSH_REQ   = 0x103F;
	SERVER_TRANS_BUFFER_PUSH_RESP  = 0x1040;
	
	//推送停止视频请求(CS -> Client)
	SERVER_STOP_VIDEO_PUSH_REQ     = 0x1041;
	SERVER_STOP_VIDEO_PUSH_RESP    = 0x1042;
	
	//用户进入房间通知(CS -> Client)                 
	SERVER_INROOM_PUSH_REQ         = 0x1043;
	SERVER_INROOM_PUSH_RESP        = 0x1044;
	                               
	//用户离开房间通知(CS -> Client)                 
	SERVER_OUTROOM_PUSH_REQ        = 0x1045;
	SERVER_OUTROOM_PUSH_RESP       = 0x1046;
	
	//用户操作视频数据请求(CS -> VS)       
	SERVER_OPERATE_VIDEO_PUSH_REQ  = 0x1047;
	SERVER_OPERATE_VIDEO_PUSH_RESP = 0x1048;
	
	//用户操作音频数据请求(CS -> VS)        
	SERVER_OPERATE_AUDIO_PUSH_REQ  = 0x1049;
	SERVER_OPERATE_AUDIO_PUSH_RESP = 0x104A;
	
	//用户视频状态改变通知(CS -> Client)                 
	SERVER_VIDEO_CHANGE_PUSH_REQ   = 0x104B;
	SERVER_VIDEO_CHANGE_PUSH_RESP  = 0x104C;
	                               
	//用户音频状态改变通知(CS -> Client)                 
	SERVER_AUDIO_CHANGE_PUSH_REQ   = 0x104D;
	SERVER_AUDIO_CHANGE_PUSH_RESP  = 0x104E;
	
	//P2P通道建立请求(CS -> VS)
	SERVER_P2P_CHANNEL_PUSH_REQ    = 0x104F;
	SERVER_P2P_CHANNEL_PUSH_RESP   = 0x1050;
	
	//推送P2P通道建立请求(CS -> Client)
	SERVER_P2P_CREATE_PUSH_REQ     = 0x1051;
	SERVER_P2P_CREATE_PUSH_RESP    = 0x1052;
	
	//推送P2P内网地址请求(CS -> Client)
	SERVER_P2P_ADDRESS_PUSH_REQ    = 0x1053;
	SERVER_P2P_ADDRESS_PUSH_RESP   = 0x1054;
	
	//录像认证状态推送(SS ->CS ->Client)
	SERVER_VERIFY_STATUS_PUSH_REQ  = 0x1055;
	SERVER_VERIFY_STATUS_PUSH_RESP = 0x1056;
	
	//开始视频推送(cs->client)
	SERVER_START_VIDEO_PUSH_REQ = 0x1057;
    SERVER_START_VIDEO_PUSH_RESP = 0x1058;
    
    //推送用户连接消息(ss->client)
    SERVER_PUSH_H5USER_CONNECTION_REQ	= 0x1059;
    SERVER_PUSH_H5USER_CONNECTION_RESP  = 0x105A;
   
}


//客户端消息命令字
enum MsgType_Client
{
	//用户登录
	CLIENT_LOGIN_REQ               = 0x2011;
	CLIENT_LOGIN_RESP              = 0x2012;
	                               
	//用户退出登录                 
	CLIENT_LOGOUT_REQ              = 0x2013;
	CLIENT_LOGOUT_RESP             = 0x2014;
	                               
	//用户心跳                     
	CLIENT_USER_HEARTBEAT_REQ      = 0x2015;
	CLIENT_USER_HEARTBEAT_RESP     = 0x2016;
	                               
	//用户进入房间                 
	CLIENT_ENTER_ROOM_REQ          = 0x2017;
	CLIENT_ENTER_ROOM_RESP         = 0x2018;
	                               
	//用户离开房间                 
	CLIENT_LEAVE_ROOM_REQ          = 0x2019;
	CLIENT_LEAVE_ROOM_RESP         = 0x201A;
	                               
	//用户消息发送-指定用户        
	CLIENT_SEND_MESSAGE_REQ        = 0x201B;
	CLIENT_SEND_MESSAGE_RESP       = 0x201C;
	                               
	//用户消息发送-群发            
	CLIENT_BROAD_MESSAGE_REQ       = 0x201D;
	CLIENT_BROAD_MESSAGE_RESP      = 0x201E;
	                               
	//建立UDP通道                  
	CLIENT_CREATE_CHANNEL_REQ      = 0x201F;
	CLIENT_CREATE_CHANNEL_RESP     = 0x2020;
	                               
	//客户端发送透明通道数据       
	CLIENT_SEND_TRANS_BUFFER_REQ   = 0x2021;
	CLIENT_SEND_TRANS_BUFFER_RESP  = 0x2022;
	                               
	//获取房间内用户列表                 
	CLIENT_GET_USERLIST_REQ        = 0x2023;
	CLIENT_GET_USERLIST_RESP       = 0x2024;
	                               
	//获取房间列表                 
	CLIENT_GET_ROOMLIST_REQ        = 0x2025;
	CLIENT_GET_ROOMLIST_RESP       = 0x2026;
	                               
	//客户端视频呼叫请求           
	CLIENT_VIDEO_CALL_REQ          = 0x2027;
	CLIENT_VIDEO_CALL_RESP         = 0x2028;
	                               
	//用户开始录像请求             
	CLIENT_START_RECORD_REQ        = 0x2029;
	CLIENT_START_RECORD_RESP       = 0x202A;
	                               
	//用户停止录像请求             
	CLIENT_STOP_RECORD_REQ         = 0x202B;
	CLIENT_STOP_RECORD_RESP        = 0x202C;
	                               
	//用户设置录像参数请求         
	CLIENT_SET_VIDEO_REQ           = 0x202D;
	CLIENT_SET_VIDEO_RESP          = 0x202E;
	                               
	//数据认证请求                 
	CLIENT_DATA_VERIFIER_REQ       = 0x202F;
	CLIENT_DATA_VERIFIER_RESP      = 0x2030;
	                               
	//客户端停止视频请求           
	CLIENT_STOP_VIDEO_REQ          = 0x2031;
	CLIENT_STOP_VIDEO_RESP         = 0x2032;
	
	//获取在线用户列请求           
	CLIENT_ONLINE_USER_LIST_REQ    = 0x2033;
	CLIENT_ONLINE_USER_LIST_RESP   = 0x2034;
	
	//用户改变视频状态请求           
	CLIENT_VIDEO_STATUS_REQ        = 0x2035;
	CLIENT_VIDEO_STATUS_RESP       = 0x2036;
	
	//用户改变音频状态请求           
	CLIENT_AUDIO_STATUS_REQ        = 0x2037;
	CLIENT_AUDIO_STATUS_RESP       = 0x2038;
	
	//用户操作视频数据请求       
	CLIENT_OPERATE_VIDEO_REQ       = 0x2039;
	CLIENT_OPERATE_VIDEO_RESP      = 0x203A;
	
	//用户操作音频数据请求        
	CLIENT_OPERATE_AUDIO_REQ       = 0x203B;
	CLIENT_OPERATE_AUDIO_RESP      = 0x203C;
	
	//P2P通道建立请求
	CLIENT_P2P_CHANNEL_REQ         = 0x203D;
	CLIENT_P2P_CHANNEL_RESP        = 0x203E;
	
	//发送内网地址请求
	CLIENT_P2P_ADDRESS_REQ         = 0x203F;
	CLIENT_P2P_ADDRESS_RESP        = 0x2040;
	
	//获取加密随机串请求
	CLIENT_SECRET_KEY_REQ          = 0x2041;
	CLIENT_SECRET_KEY_RESP         = 0x2042;
	
	//客户端合成流录制请求(CLIENT -> SS)
	CLIENT_STREAM_RECORD_PUSH_REQ  = 0x2043;
	CLIENT_STREAM_RECORD_PUSH_RESP = 0x2044;
	
	//客户端获取视频配置信息
	CLIENT_MEDIA_CONFIG_REQ        = 0x2045;
	CLIENT_MEDIA_CONFIG_RESP       = 0x2046;
	
	//推送SDP信息到客户端
	CLIENT_EXCHANGE_SDP_REQ		   = 0x2047;
	CLIENT_EXCHANGE_SDP_RESP	   = 0x2048;
	
	//交换ICE_CANDIDATE信息
	CLIENT_EXCHANGE_ICE_REQ		   = 0x2049;
	CLIENT_EXCHANGE_ICE_RESP	   = 0x204A;
	
	//用户摄像头打开情况
    CLIENT_USER_CAMERA_REQ          = 0x204B;
    CLIENT_USER_CAMERA_RESP         = 0x204C;
    
    //wenrtc模拟客户端提交组名
    CLIENT_CONN_GROUPNAME_REQ		= 0x204D;
    CLIENT_CONN_GROUPNAME_RESP      = 0x204E;
	
	//H5通知带宽使用情况
	CLIENT_BAND_USING_INFO_REQ		= 0x204F;
	CLIENT_BAND_USING_INFO_RESP		= 0x2050;
	
	//座席端(webrtc代理)通知排队房间号/用户ID
	CLIENT_WEBRTC_NOTIFY_ROOMID_REQ = 0x2051;
	CLIENT_WEBRTC_NOTIFY_ROOMID_RESP= 0x2052;
}




message Request
{
	optional SERVICE_REGISTER_REQ srreq                = 1;   //服务注册请求
	optional SERVICE_HEARTBEAT_REQ shreq               = 2;   //服务心跳请求
	optional CREATE_ROOM_PUSH_REQ crpreq               = 3;   //创建房间-同步请求
	optional DELETE_ROOM_PUSH_REQ drpreq               = 4;   //删除房间-同步请求
	optional ENTER_ROOM_PUSH_REQ erpreq                = 5;   //用户进入房间-同步请求
	optional LEAVE_ROOM_PUSH_REQ lrpreq                = 6;   //用户离开房间-同步请求
	optional LOGIN_REQ lreq                            = 7;   //用户登录请求
	optional LOGOUT_REQ loreq                          = 8;   //用户退出登录请求
	optional USER_HEARTBEAT_REQ uhreq                  = 9;   //用户心跳请求
	optional ENTER_ROOM_REQ erreq                      = 10;  //用户进入房间请求
	optional LEAVE_ROOM_REQ lrreq                      = 11;  //用户离开房间请求
	optional KICK_OUT_ROOM_REQ korreq                  = 12;  //用户被踢出房间请求
	optional SEND_MESSAGE_REQ smreq                    = 13;  //用户消息发送-指定用户请求
	optional BROAD_MESSAGE_REQ bmreq                   = 14;  //用户消息发送-群发请求
	optional USER_ONLINE_REQ uoreq                     = 15;  //用户上线通知请求
	optional USER_OFFLINE_REQ ufreq                    = 16;  //用户下线通知请求
	optional CREATE_CHANNEL_REQ ccreq                  = 17;  //建立UDP通道请求
	optional MESSAGE_PUSH_REQ mpreq                    = 18;  //服务器消息转发请求
	optional SEND_TRANS_BUFFER_REQ stbreq              = 19;  //客户端发送透明通道数据请求
	optional CHANGE_SERVER_REQ csreq                   = 20;  //更换转发服务器请求
	optional GET_USERLIST_REQ gureq                    = 21;  //获取用户列表请求
	optional GET_ROOMLIST_REQ grreq                    = 22;  //获取房间列表请求
	optional ADD_NEWROOM_REQ anrreq                    = 23;  //新建房间通知请求
	optional SEND_DELETE_ROOM_REQ sdrreq               = 24;  //删除房间通知请求
	optional VIDEO_CALL_REQ vcreq                      = 25;  //客户端视频呼叫请求
	optional VIDEO_CALL_PUSH_REQ vcpreq                = 26;  //服务端转发视频呼叫请求
	optional SEND_ENTERN_ROOM_REQ serreq               = 27;  //服务端推送用户进入房间请求
	optional START_RECORD_REQ strreq                   = 28;  //开始录像请求
	optional STOP_RECORD_REQ sprreq                    = 29;  //停止录像请求
	optional SET_VIDEO_REQ svreq                       = 30;  //录像设置请求
	optional START_RECORD_PUSH_REQ strpreq             = 31;  //开始录像-同步请求
	optional STOP_RECORD_PUSH_REQ sprpreq              = 32;  //停止录像-同步请求
	optional SET_VIDEO_PUSH_REQ svpreq                 = 33;  //录像设置-同步请求
	optional COMPLETE_VIDEO_REQ cpvreq                 = 34;  //服务端完成录像请求
	optional FINISH_RECORD_PUSH_REQ frpreq             = 35;  //服务端推送完成录像请求
	optional TRANS_BUFFER_REQ tbreq                    = 36;  //透明通道传送数据请求
	optional RECORD_CTRL_REQ rcreq                     = 37;  //录像控制请求
	optional VIDEO_CALL_CTRL_REQ vccreq                = 38;  //视频呼叫控制请求
	optional TRANS_BUFFER_PUSH_REQ tbpreq              = 39;  //透明通道数据推送到客户端请求
	optional DATA_VERIFIER_REQ dvreq                   = 40;  //数据认证请求
	optional STOP_VIDEO_REQ stvreq                     = 41;  //客户端停止视频请求
	optional STOP_VIDEO_PUSH_REQ stvpreq               = 42;  //客户端停止视频推送到客户端请求
	optional ONLINE_USER_LIST_REQ oulreq               = 43;  //获取在线用户列请求
	optional INROOM_PUSH_REQ irpreq                    = 44;  //用户进入房间通知请求
	optional OUTROOM_PUSH_REQ orpreq                   = 45;  //用户离开房间通知请求
	optional OPERATE_VIDEO_PUSH_REQ ovpreq             = 46;  //用户操作视频数据请求(CS -> VS)
	optional OPERATE_AUDIO_PUSH_REQ oapreq             = 47;  //用户操作音频数据请求(CS -> VS)
	optional VIDEO_CHANGE_PUSH_REQ vchpreq             = 48;  //用户视频状态改变通知(CS -> Client)
	optional AUDIO_CHANGE_PUSH_REQ acpreq              = 49;  //用户音频状态改变通知(CS -> Client)
	optional VIDEO_STATUS_REQ vsreq                    = 50;  //用户改变视频状态请求
	optional AUDIO_STATUS_REQ asreq                    = 51;  //用户改变音频状态请求
	optional OPERATE_VIDEO_REQ ovreq                   = 52;  //用户操作视频数据请求
	optional OPERATE_AUDIO_REQ oareq                   = 53;  //用户操作音频数据请求
	optional P2P_CHANNEL_REQ pchreq                    = 54;  //P2P通道建立请求
	optional P2P_CHANNEL_PUSH_REQ pchpreq              = 55;  //P2P通道建立请求(CS -> VS)
	optional P2P_CREATE_PUSH_REQ pcrpreq               = 56;  //推送P2P通道建立请求(CS -> Client)
	optional P2P_ADDRESS_REQ pareq                     = 57;  //发送内网地址请求
	optional P2P_ADDRESS_PUSH_REQ papreq               = 58;  //推送P2P内网地址请求(CS -> Client)
	optional SECRET_KEY_REQ skreq                      = 59;  //获取加密随机串请求
	optional VERIFY_STATUS_PUSH_REQ vspreq             = 60;  //录像认证状态推送(SS ->CS ->Client)
	optional STREAM_RECORD_PUSH_REQ srpreq             = 61;  //客户端合成流录制请求(CLIENT -> SS)
	optional MEDIA_CONFIG_REQ mcreq              	   = 62;  //客户端获取视频配置信息请求
	optional START_VIDEO_PUSH_REQ	svipreq			   = 63;
	optional EXCHANGE_SDP_REQ   esreq				   = 64;   //推送SDP信息到客户端请求
	optional EXCHANGE_ICE_REQ   eireq				   = 65;	//推送ice candidate信息到客户端请求
	optional USER_CAMERA_REQ    ucreq                  = 66;  //用户摄像头打开情况	
	optional H5USER_CONNECTION_REQ  hucreq			   = 67;	//信令服务器推送用户到来或者断开连接
	optional CONN_GROUPNAME_REQ cgnreq				   = 68;	//模拟客户端提交组名
	optional BAND_USING_INFO_REQ buireq				   = 69;	//H5通知带宽使用情况
	optional WEBRTC_NOTIFY_ROOMID_REQ wnrreq		   = 70;	//座席端通知排队房间号
	
}

	
message Response
{
	optional SERVICE_REGISTER_RESP srresp              = 1;   //服务注册响应
	optional SERVICE_HEARTBEAT_RESP shresp             = 2;   //服务心跳响应
	optional CREATE_ROOM_PUSH_RESP crpresp             = 3;   //创建房间-同步响应
	optional DELETE_ROOM_PUSH_RESP drpresp             = 4;   //删除房间-同步响应
	optional ENTER_ROOM_PUSH_RESP erpresp              = 5;   //用户进入房间-同步响应
	optional LEAVE_ROOM_PUSH_RESP lrpresp              = 6;   //用户离开房间-同步响应
	optional LOGIN_RESP lresp                          = 7;   //用户登录响应
	optional LOGOUT_RESP loresp                        = 8;   //用户退出登录响应
	optional USER_HEARTBEAT_RESP uhresp                = 9;   //用户心跳响应
	optional ENTER_ROOM_RESP erresp                    = 10;  //用户进入房间响应
	optional LEAVE_ROOM_RESP lrresp                    = 11;  //用户离开房间响应
	optional KICK_OUT_ROOM_RESP korresp                = 12;  //用户被踢出房间响应
	optional SEND_MESSAGE_RESP smresp                  = 13;  //用户消息发送-指定用户响应
	optional BROAD_MESSAGE_RESP bmresp                 = 14;  //用户消息发送-群发响应
	optional USER_ONLINE_RESP uoresp                   = 15;  //用户上线通知响应
	optional USER_OFFLINE_RESP ufresp                  = 16;  //用户下线通知响应
	optional CREATE_CHANNEL_RESP ccresp                = 17;  //建立UDP通道响应
	optional MESSAGE_PUSH_RESP mpcresp                 = 18;  //服务器消息转发响应
	optional SEND_TRANS_BUFFER_RESP stbresp            = 19;  //客户端发送透明通道数据响应
	optional CHANGE_SERVER_RESP csresp                 = 20;  //更换转发服务器响应
	optional GET_USERLIST_RESP guresp                  = 21;  //获取用户列表响应
	optional GET_ROOMLIST_RESP grresp                  = 22;  //获取房间列表响应
	optional ADD_NEWROOM_RESP anrresp                  = 23;  //新建房间通知响应
	optional SEND_DELETE_ROOM_RESP sdrresp             = 24;  //删除房间通知响应
	optional VIDEO_CALL_RESP vcresp                    = 25;  //客户端视频呼叫响应
	optional VIDEO_CALL_PUSH_RESP vcpresp              = 26;  //服务端转发视频呼叫响应
	optional SEND_ENTERN_ROOM_RESP serresp             = 27;  //服务端推送用户进入房间响应
	optional START_RECORD_RESP strresp                 = 28;  //开始录像响应
	optional STOP_RECORD_RESP sprresp                  = 29;  //停止录像响应
	optional SET_VIDEO_RESP svresp                     = 30;  //录像设置响应
	optional START_RECORD_PUSH_RESP strpresp           = 31;  //开始录像-同步响应
	optional STOP_RECORD_PUSH_RESP sprpresp            = 32;  //停止录像-同步响应
	optional SET_VIDEO_PUSH_RESP svpresp               = 33;  //录像设置-同步响应
	optional COMPLETE_VIDEO_RESP cpvresp               = 34;  //服务端完成录像响应
	optional FINISH_RECORD_PUSH_RESP frpresp           = 35;  //服务端推送完成录像响应
	optional TRANS_BUFFER_RESP tbresp                  = 36;  //透明通道传送数据响应
	optional RECORD_CTRL_RESP rcresp                   = 37;  //录像控制响应
	optional VIDEO_CALL_CTRL_RESP vccresp              = 38;  //视频呼叫控制响应
	optional TRANS_BUFFER_PUSH_RESP tbpresp            = 39;  //明通道数据推送到客户端响应
	optional DATA_VERIFIER_RESP dvresp                 = 40;  //数据认证响应
	optional STOP_VIDEO_RESP stvresp                   = 41;  //客户端停止视频响应
	optional STOP_VIDEO_PUSH_RESP stvpresp             = 42;  //客户端停止视频推送到客户端响应
	optional ONLINE_USER_LIST_RESP oulresp             = 43;  //获取在线用户列响应
	optional INROOM_PUSH_RESP irpresp                  = 44;  //用户进入房间通知响应
	optional OUTROOM_PUSH_RESP orpresp                 = 45;  //用户离开房间通知响应
	optional OPERATE_VIDEO_PUSH_RESP ovpresp           = 46;  //用户操作视频数据响应(CS -> VS)
	optional OPERATE_AUDIO_PUSH_RESP oapresp           = 47;  //用户操作音频数据响应(CS -> VS)
	optional VIDEO_CHANGE_PUSH_RESP vchpresp           = 48;  //用户视频状态改变通知响应(CS -> VS)
	optional AUDIO_CHANGE_PUSH_RESP acpresp            = 49;  //用户音频状态改变通知响应(CS -> VS)
	optional VIDEO_STATUS_RESP vsresp                  = 50;  //用户改变视频状态响应
	optional AUDIO_STATUS_RESP asresp                  = 51;  //用户改变音频状态响应
	optional OPERATE_VIDEO_RESP ovresp                 = 52;  //用户操作视频数据响应
	optional OPERATE_AUDIO_RESP oaresp                 = 53;  //用户操作音频数据响应
	optional P2P_CHANNEL_RESP pchresp                  = 54;  //P2P通道建立响应
	optional P2P_CHANNEL_PUSH_RESP pchpresp            = 55;  //P2P通道建立响应(CS -> VS)
	optional P2P_CREATE_PUSH_RESP pcrpresp             = 56;  //推送P2P通道建立响应(CS -> Client)
	optional P2P_ADDRESS_RESP paresp                   = 57;  //发送内网地址响应
	optional P2P_ADDRESS_PUSH_RESP papresp             = 58;  //推送P2P内网地址响应(CS -> Client)
	optional SECRET_KEY_RESP skresp                    = 59;  //获取加密随机串响应
	optional VERIFY_STATUS_PUSH_RESP vspresp           = 60;  //录像认证状态推送(SS ->CS ->Client)响应
	optional STREAM_RECORD_PUSH_RESP srpresp           = 61;  //客户端合成流录制响应(CLIENT -> SS)
	optional MEDIA_CONFIG_RESP mcresp                  = 62;  //客户端获取视频配置信息响应
	optional START_VIDEO_PUSH_RESP svipresp			   = 63;  
	optional EXCHANGE_SDP_RESP   esresp				   = 64;   //推送SDP信息到客户端响应
	optional EXCHANGE_ICE_RESP   eiresp				   = 65;   //推送icecandidate信息响应
	optional USER_CAMERA_RESP ucresp			       = 66;	//用户打开摄像头情况响应
	optional H5USER_CONNECTION_RESP  hucresp		   = 67;   //用户连接响应
	optional CONN_GROUPNAME_RESP cgnresp			   = 68;	//模拟客户端提交组名响应
	optional BAND_USING_INFO_RESP buiresp			   = 69;	//H5通知带宽使用情况响应
	optional WEBRTC_NOTIFY_ROOMID_RESP wnrresp		   = 70;	//座席端通知排队房间号响应	
}


//传输信息体
message Message
{	
	required uint32 msgtype     = 1;                            //指令类型
	optional uint32 errcode     = 2 [default = 0];              //错误代码
	optional string errmsg      = 3 [default = ""];             //错误信息，如果有

	optional Request request    = 4;
	optional Response response  = 5;
}
