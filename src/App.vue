<template>
  <div
    id="app"
    class="app_wrap"
    :class="{ 'hidden-header': isHiddenHeader }"
    data-page="home"
  >
    <router-view />
  </div>
</template>

<script>
import { setTheme } from '@/theme/theme';

// import { fundLogin } from '@/service/service';
// import { tokenCheck } from '@/service/service.js';

export default {
  name: 'App',
  data() {
    return {
      isHiddenHeader: false,
      sd_token: ''
    };
  },
  watch: {
    $route: {
      async handler(e) {
        console.log(e);
        const {
          bizType,
          flowNo,
          channel_type = '',
          app_id = '',
          backUrl = '',
          account_type = '',
          op_station = '',
          ...pageParams
        } = e.query;
        const { userInfo, ssoLoginFlag } = this.$store.state.user;
        if (app_id !== '') {
          $h.setSession('appId', app_id);
          this.$store.commit('user/setAppId', app_id);
          //佣金宝生成的二维码，限制只能使用微信进入
          const { checkedUserAgent } = await import('@/common/util.js');
          const weixinEntry = checkedUserAgent().weixin;
          if (app_id === 'yjbwx' && !weixinEntry) {
            this.$router.replace({
              name: 'browserTips'
            });
            return;
          }
        }
        if(Object.values(pageParams).length !== 0) {
          $h.setSession('pageParams', pageParams);
        }
        if (account_type !== '') {
          $h.setSession('accountType', account_type);
        }
        if (backUrl !== '') {
          $h.setSession('backUrl', backUrl);
        }
        if (channel_type !== '') {
          $h.setSession('channelType', channel_type);
        }
        if (e.query.optType && e.query.optType === '1') {
          $h.setSession('optType', e.query.optType);
        }
        if (e.query.from && e.query.from === 'pc') {
          $h.setSession('from', e.query.from);
        }
        if (account_type !== '') {
          $h.setSession('from', e.query.account_type);
          $h.setSession('accountType', account_type);
        }
        if (op_station !== '') {
          sessionStorage.setItem('originOpStation', op_station);
        }
        if (this.$route.path === '/login' && !userInfo) {
          console.log('toLoginPage');
        } else if (e.query.instant_token && e.query.op_station) {
          if (channel_type === '*************') {
            // 支付宝渠道no_token清除cookie
            if (e.query.instant_token && e.query.instant_token !== 'no_token') {
              $h.setCookie('tk-token-authorization', '');
            }
          }

          let instant_token = e.query.instant_token;
          const loginFlag =
            !$hvue.config.noSsoLoginList.includes(e.name) &&
            !($h.getSession('instant_token') === instant_token);
          let op_station = e.query.op_station;
          if ($hvue.platform == 0) {
            // H5统一登录
            console.log('H5ssoLogin');
            let cookie = '';
            if (channel_type === '*************') {
              // 支付宝渠道,从cookie中获取登录token
              cookie = $h.getCookie('tk-token-authorization')
                ? $h.getCookie('tk-token-authorization').replace(/\+/g, ' ')
                : '';
            }
            if (cookie && cookie !== '' && channel_type === '*************') {
              // 支付宝渠道,从cookie中获取登录token
              $h.setSession('authorization', cookie);
              let userInfo = {
                clientId: 'noToken'
              };
              this.$store.commit('user/setUserInfo', userInfo);
              window.bonreeJsBridge?.setUserID?.(String(userInfo.clientId));
            } else {
              const { errorNo } = await this.H5ssoLogin(
                loginFlag,
                instant_token,
                op_station
              );
            }
            if (instant_token !== '') {
              $h.setSession('instant_token', instant_token);
            }
            if (
              [
                'introduce',
                'lrIntroduce',
                'businessIntroduce',
                'doubleRecord',
                'doubleRecordHistory'
              ].includes(e.name)
            ) {
              // introduce介绍页不跳转
              return;
            }
            if (e.query.noHome) {
              return;
            }
            if (e.query.bizType || e.query.flowNo) {
              // import('@/common/flowMixin.js').then((a) => {
              //   a.initFlow.call(this, bizType, flowNo);
              // });
              import('@/common/flowMixinV2.js').then((a) => {
                a.initFlow.call(this, { bizType: bizType, initJumpMode: '1' });
              });
            }
          } else {
            // APP统一登录
            const { errorNo } = await this.ssoLogin(
              loginFlag,
              instant_token,
              op_station
            );
            if (errorNo === 0) {
              this.$store.commit('user/setSsoLoginFlag', true);
            }
            if (
              [
                'introduce',
                'lrIntroduce',
                'businessIntroduce',
                'doubleRecord',
                'doubleRecordHistory'
              ].includes(e.name)
            ) {
              // introduce介绍页不跳转
              return;
            }
            if (instant_token !== '') {
              $h.setSession('instant_token', instant_token);
            }
            if (e.query.backurl) {
              $h.setSession('backurl', e.query.backurl);
            }
            if (e.query.noHome) {
              return;
            }
            if (e.query.bizType || e.query.flowNo) {
              // import('@/common/flowMixin.js').then((a) => {
              //   a.initFlow.call(this, bizType, flowNo);
              // });
              import('@/common/flowMixinV2.js').then((a) => {
                a.initFlow.call(this, { bizType: bizType, initJumpMode: '1' });
              });
            }
          }
        } else {
          if (e.query.bizType || e.query.flowNo) {
            if (
              [
                'introduce',
                'lrIntroduce',
                'businessIntroduce',
                'doubleRecord',
                'doubleRecordHistory'
              ].includes(e.name)
            ) {
              // introduce介绍页不跳转
              return;
            }
            import('@/common/flowMixinV2.js').then((a) => {
              a.initFlow.call(this, { bizType: bizType, initJumpMode: '1' });
            });
          }
        }
      },
      deep: true
    }
  },
  created() {
    // 访至因为IOS回退或操作导致当前页面生命周期未加载
    if ($hvue.iBrowser.ios) {
      
      // 在页面刷新时获取样式配置
      setTheme('custom', $hvue.themeConfig);
      $h.callMessageNative({
        // 设置状态栏颜色
        funcNo: '50119',
        moduleName: $hvue.customConfig.moduleName,
        color: '#FFFFFF',
        style: '0', // 0黑色  1白色
        iphoneXBottomColor: '#000'
      });
      // if (
      //   $h.getSession('theme') &&
      //   JSON.stringify($h.getSession('theme')) ===
      //     JSON.stringify($hvue.themeConfig)
      // ) {
      //   setTheme('custom', $h.getSession('theme'));
      // } else {
      //   setTheme('custom', $hvue.themeConfig);
      //   $h.setSession('theme', $hvue.themeConfig);
      // }
      // 在页面加载时读取session里的状态信息
      if ($h.getSession('store')) {
        this.$store.replaceState(
        Object.assign({}, this.$store.state, JSON.parse($h.getSession('store')))
        );
      }
      // 在页面刷新时将vuex里的信息保存到session里
      import('@/common/util.js').then(({ checkedUserAgent }) => {
        const weixinEntry = checkedUserAgent().weixin;
        const iOSEntry = checkedUserAgent().ios;
      const listenEvent = weixinEntry || iOSEntry ? 'pagehide' : 'beforeunload';
        window.addEventListener(listenEvent, this.setSessionVuex, true);
      });
    console.info("流程节点重新加载--IOS");
      this.initTKFlow();
      this.initTrackEvent();
      this.setEnv();
      const isApp = $hvue.platform !== '0';
      if (!isApp && PACK_ENV !== 'local') {
        this.isHiddenHeader = true;
      }
    }

    // 在页面刷新时获取样式配置
    setTheme('custom', $hvue.themeConfig);
    // $h.callMessageNative({
    //   // 设置状态栏颜色
    //   funcNo: '50119',
    //   moduleName: $hvue.customConfig.moduleName,
    //   color: $hvue.themeConfig.mainColor,
    //   style: '1' // 0黑色  1白色
    // });
    // if (
    //   $h.getSession('theme') &&
    //   JSON.stringify($h.getSession('theme')) ===
    //     JSON.stringify($hvue.themeConfig)
    // ) {
    //   setTheme('custom', $h.getSession('theme'));
    // } else {
    //   setTheme('custom', $hvue.themeConfig);
    //   $h.setSession('theme', $hvue.themeConfig);
    // }
    // 在页面加载时读取session里的状态信息
    if ($h.getSession('store')) {
      this.$store.replaceState(
        Object.assign({}, this.$store.state, JSON.parse($h.getSession('store')))
      );
    }
    // 在页面刷新时将vuex里的信息保存到session里
    import('@/common/util.js').then(({ checkedUserAgent }) => {
      const weixinEntry = checkedUserAgent().weixin;
      const iOSEntry = checkedUserAgent().ios;
      const listenEvent = weixinEntry || iOSEntry ? 'pagehide' : 'beforeunload';
      window.addEventListener(listenEvent, this.setSessionVuex, true);
    });
    console.info("流程节点重新加载");
    this.initTKFlow();
    this.initTrackEvent();
    this.setEnv();
    const isApp = $hvue.platform !== '0';
    if (!isApp && PACK_ENV !== 'local') {
      this.isHiddenHeader = true;
    }
  },
  destroyed() {
    // 在页面刷新时将vuex里的信息保存到session里
    import('@/common/util.js').then(({ checkedUserAgent }) => {
      const weixinEntry = checkedUserAgent().weixin;
      const iOSEntry = checkedUserAgent().ios;
      const listenEvent = weixinEntry || iOSEntry ? 'pagehide' : 'beforeunload';
      window.removeEventListener(listenEvent, this.setSessionVuex, true);
    });
  },
  methods: {
    setSessionVuex() {
      $h.setSession('store', JSON.stringify(this.$store.state));
    },
    initTrackEvent() {
      // 国金初始化火山埋点
      window.collectEvent('init', {
        app_id: Number(10000001),
        autotrack: false, //全埋点开关
        channel_domain: window.serviceOptions.channelDomain,
        log: true,
        // Native: true,
        enable_ab_test: false
        // enable_native,
        // disable_auto_pv,
        // spa,
        // cross_subdomain,
        // cookie_domain,
        // max_report,
        // reportTime,
        // timeout,
      });

      window.collectEvent('start');
    },

    initTKFlow() {
      let _this = this;
      TKFlowEngine.init({
        isUseCrossFlow: '1',
        isDebug: $hvue.customConfig.tKFlowConfig.isDebug, // 是否输出调试日志,0:关闭，1：启动，默认是1
        url: $hvue.customConfig.tKFlowConfig.serverUrl, // 请求服务器,不跨域的时候不用传，跨域的时候传地址
        // requestMode: $hvue.customConfig.tKFlowConfig.requestMode, //请求模式0:不加密，不加签，1：加签不加密，2：加密加签, 3:加密加签,响应头也加密
        //浏览器push动作函数 必填
        pushStateFunc: function (node) {
          let stepPath = node.stepPath;
          if (stepPath === '-1') {
            stepPath = '/index';
          }
          console.log('流程跳转模式push');
          _this.$router.push({
            path: stepPath
          });
        },
        // 浏览器replace动作函数 必填
        replaceStateFunc: function (node) {
          let stepPath = node.stepPath;
          if (stepPath === '-1') {
            stepPath = '/index';
          }
          console.log('流程跳转模式replace');
          _this.$router.replace({
            path: stepPath
          });
        }
      });
    },
    goBack() {
      this.$store.commit('flow/setFlowBack', true);
    },
    /**
     * 设置运行宿主环境变量 browser浏览器 thinkive思迪sdk ths同花顺
     */
    setEnv() {
      let env = 'browser';
      const e = sessionStorage.getItem('tksdk_env'); // 凡泰容器
      if (
        e === '1' ||
        e === '2' ||
        e === '3' ||
        navigator.userAgent.includes('thinkive')
      ) {
        if (e) {
          $hvue.platform = e;
        }
        env = 'thinkive';
      }
      $hvue.env = env;
    },

    async H5ssoLogin(loginFlag, token, opStation) {
      const { H5ssoLoginUtil, trackUuid } = await import('@/common/util.js');
      const userInfo = this.$store.state.user.userInfo;
      return new Promise(async (resolve) => {
        if (loginFlag) {
          try {
            const loginData = await H5ssoLoginUtil(token, opStation);
            let res = loginData.data;
            console.log(loginData);
            let authorization =
              loginData.responseHeaders['tk-token-authorization'];
            $h.setSession('authorization', authorization);
            this.$store.commit('user/setUserInfo', res);
            window.bonreeJsBridge?.setUserID?.(String(res.clientId));
            console.log('~~~~~~~authorization~~~~~~===' + authorization);
            console.log('ssoLogin success');
            console.log('finder store==', this.$store);
            console.log('finder fundAccount==', res.fundAccount);
            trackUuid(res.fundAccount);
            resolve({ errorNo: 0 });
          } catch (e) {
            _hvueToast({ mes: e });
          }
        } else {
          console.log('ssoLogin complete');
          resolve({ errorNo: 0 });
        }
      });
    },

    async ssoLogin(loginFlag, token, opStation) {
      const { APPssoLoginUtil, getSsoLoginCache, trackUuid } = await import(
        '@/common/util.js'
      );
      const isApp = $hvue.platform !== '0';
      const userInfo = this.$store.state.user.userInfo;
      return new Promise(async (resolve) => {
        if (loginFlag && isApp && !userInfo) {
          try {
            const loginCache = getSsoLoginCache();
            let loginData;
            if (loginCache) {
              loginData = loginCache;
            } else {
              loginData = await APPssoLoginUtil(token, opStation);
            }
            console.log(loginData);
            let res = loginData.data;
            let authorization =
              loginData.responseHeaders['tk-token-authorization'];
            $h.setSession('authorization', authorization);
            this.$store.commit('user/setUserInfo', res);
            window.bonreeJsBridge?.setUserID?.(String(res.clientId));
            console.log('~~~~~~~authorization~~~~~~===' + authorization);
            console.log('ssoLogin success');
            console.log('finder store==', this.$store);
            console.log('finder fundAccount==', res.fundAccount);
            trackUuid(res.fundAccount);
            resolve({ errorNo: 0 });
          } catch (e) {
            _hvueToast({ mes: e });
          }
        } else {
          console.log('ssoLogin complete');
          resolve({ errorNo: 0 });
        }
      });
    }
  }
};
</script>
<style scoped>
.hidden-header >>> header {
  display: none !important;
}
</style>
<style lang="scss">
@import './assets/scss/flex.scss';
@import './assets/scss/common.scss';
</style>
