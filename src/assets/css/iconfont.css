@font-face {
  font-family: "wt-iconfont"; 
  src: url('../fonts/wt-iconfont.woff2?t=1659518783211') format('woff2'),
       url('../fonts/wt-iconfont.woff?t=1659518783211') format('woff'),
       url('../fonts/wt-iconfont.ttf?t=1659518783211') format('truetype');
}

.iconfont {
  font-size: 16px;
  font-family: "wt-iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}


.icon-shouye:before {
  content: "\e62c";
}


.icon-hm-open:before {
  content: "\e6b3";
}

.icon-hm-hot:before {
  content: "\e629";
}

.icon-hm-other:before {
  content: "\e62a";
}

.icon-hm-user:before {
  content: "\e62b";
}

.icon-search_2:before {
  content: "\e8b9";
}

.icon-search:before {
  content: "\e67e";
}

.icon-danwei:before {
  content: "\e635";
}

.icon-result_ing:before {
  content: "\e628";
}

.icon-photo01:before {
  content: "\e8bc";
}

.icon-cycle_loading:before {
  content: "\e6e9";
}

.icon-vd_sound:before {
  content: "\e622";
}

.icon-vd_time:before {
  content: "\e624";
}

.icon-vd_light:before {
  content: "\e625";
}

.icon-vd_wifi:before {
  content: "\e626";
}

.icon-queue_cs:before {
  content: "\e627";
}

.icon-circle_warn:before {
  content: "\e609";
}

.icon-view:before {
  content: "\e633";
}

.icon-view_on:before {
  content: "\e634";
}

.icon-circle_ok:before {
  content: "\e651";
}

.icon-circle_error:before {
  content: "\e652";
}

.icon-circle_imp:before {
  content: "\e654";
}

.icon-location:before {
  content: "\e677";
}

.icon-active02:before {
  content: "\e61b";
}

.icon-active:before {
  content: "\e61c";
}

.icon-fail:before {
  content: "\e61e";
}

.icon-checked_ic:before {
  content: "\e61f";
}

.icon-suc:before {
  content: "\e621";
}

.icon-arrow01:before {
  content: "\e619";
}

.icon-back:before {
  content: "\e61a";
}

.icon-close:before {
  content: "\e61d";
}

.icon-phone_img:before {
  content: "\e620";
}

.icon-txt_clear:before {
  content: "\e623";
}