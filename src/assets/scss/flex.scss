/**
 * Flex布局小助手
 *
 * Resource:
 * 1. https://css-tricks.com/snippets/css/a-guide-to-flexbox/
 * 2. https://developer.mozilla.org/zh-CN/docs/Web/CSS/CSS_Flexible_Box_Layout/Using_CSS_flexible_boxes
 **/


/**
 * 1.在容器上声明的属性
 */

// 1.1 （★必须声明其一）主轴（堆叠）方向
.fx-row {
  display: flex;
  flex-direction: row;
}

.fx-col {
  display: flex;
  flex-direction: column;
}

// 1.2 是否允许换行
.fx-wrap {
  flex-wrap: wrap;
}

.fx-nowrap {
  flex-wrap: nowrap;
}

// 1.3 主轴(Main axis)排列方式
// 默认
.fx-m-start {
  justify-content: flex-start;
}

.fx-m-center {
  justify-content: center;
}

.fx-m-end {
  justify-content: flex-end;
}

.fx-m-between {
  justify-content: space-between;
}

// 兼容性不佳
.fx-m-around {
  justify-content: space-around; 
}

// 1.4 侧轴(Cross axis)排列方式：元素在行中的位置 （只有一行时行占满整个侧轴，具体和1.5的区别见： http://stackoverflow.com/questions/27539262/whats-the-difference-between-align-content-and-align-items）
// 默认，如果项目未设置高度或设为auto，将占满整个容器的高度。
.fx-c-stretch {
  align-items: flex-stretch; 
}

.fx-c-start {
  align-items: flex-start;
}

.fx-c-center {
  align-items: center;
}

.fx-c-end {
  align-items: flex-end;
}

// 各子元素的第一行文字的基线对齐。
.fx-c-baseline {
  align-items: flex-baseline; 
}

// 1.5 侧轴(Cross axis)排列方式：多行(Line)在容器中的位置，只有一行时不生效
// 默认
.fx-cl-stretch {
  align-content: stretch;
}

.fx-cl-start {
  align-content: flex-start;
}

.fx-cl-center {
  align-content: center;
}

.fx-cl-end {
  align-content: flex-end;
}

.fx-cl-between {
  align-content: space-between;
}

// Caution:这个在很多安卓机上不能用
.fx-cl-around {
  align-content: space-around; 
}


/**
 * 2. 在子元素上声明的属性
 */

// 2.1 子元素排列顺序(Order)，越小越靠前，默认为0. 一般用不上，注释掉……
// @for $i from 1 through 6 {
//   .fx-o-#{$i} {
//     order: $i;
//   }
// }
// 2.2 子元素如何分配行空间 .fx-1 ~ .fx-7
// 取消原有大小，完全重新分配
@for $i from 0 through 7 {
  .fx-#{$i} {
    flex-basis: 0;
    flex-grow: $i;
  }
}

.fx-grow {
	flex-grow: 1;
}

// 2.3 单个子元素在侧轴上的对齐方式(align-Self)（覆盖1.4 align-items）
.fx-s-stretch {
  align-self: stretch;
}

.fx-s-start {
  align-self: flex-start;
}

.fx-s-center {
  align-self: center;
}

.fx-s-end {
  align-self: flex-end;
}

.fx-s-baseline {
  align-self: baseline;
}


/**
 * 3. 经常使用的快捷方式
 * (谨慎添加哦)
 */

// 3.1 各种居中
// 子元素行排列，垂直居中
.fx-v-center {
  display: flex;
  align-items: center;
}

// 子元素行排列，水平居中
.fx-h-center {
  display: flex;
  justify-content: center;
}

// 子元素行排列，水平垂直居中
.fx-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
