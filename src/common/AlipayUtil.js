export default class AlipayUtil {
  constructor(config) {
    this.config = config;
    this.checkAlipay = this.#channelFlag();
  }
  #zfbReady(callback) {
    console.log('进入zfbReady方法');
    if (window.AlipayJSBridge) {
      console.log('window.AlipayJSBridge属性存活');
      callback && callback();
    } else {
      document.addEventListener('AlipayJSBridgeReady', callback, false);
    }
  }
  #channelFlag() {
    return $h.getSession('channelType') === '2095000000000';
  }
  toIndex() {
    console.log('alipayUtil 进入toIndex方法');
    this.#zfbReady(function () {
      AlipayJSBridge.call('popWindow');
      // AlipayJSBridge.call('exitApp');
    });
  }
  goHistory(index = -1) {
    console.log('alipayUtil 进入goHistory方法');
    // if (history.length === 1) {
    //   this.toIndex();
    // } else {
    //   history.go(index);
    // }
    this.#zfbReady(function () {
      AlipayJSBridge.call('popWindow');
    });
  }
  setTitle(title = '') {
    if (title === '') return;
    console.log('alipayUtil 进入setTitle方法 title=' + title);
    this.#zfbReady(function () {
      AlipayJSBridge.call('setTitle', { title });
    });
  }
  videoRecord() {
    const config = this.config;
    let {
      recordTime = 10,
      mainContent = '',
      copyrightText = '',
      successFunc
    } = config == undefined ? {} : config;
    this.#zfbReady(function () {
      AlipayJSBridge.call(
        'videoRecordNative',
        {
          hasAvatarLayer: true, //是否显示头像轮廓蒙层，默认值false，可以不传
          RecordTime: recordTime, //视频录制时间限制, 单位：秒; 默认值10，传0为默认值10秒，显示绿⾊时间倒数进度条，可以不传
          mainContent, //⽤户需要朗读的验证⽂案，必传
          copyrightText // 底部⻚⾯版权⽂案，可以不传
        },
        function (result) {
          successFunc(result.imageData);
        }
      );
    });
  }
  chooseImageByZfb() {
    const config = this.config;
    let { type, successFunc } = config == undefined ? {} : config;
    let yjbCallBack = (res) => {
      if (res && res.success) {
        console.log('调用客户端选取图片成功', res);
        let base64Data = res.dataURL;
        successFunc(base64Data);
      } else {
        console.log('取消或获取失败', res.error);
      }
    };
    let sourceType = ['camera', 'album'];
    if (type == 'both') {
      sourceType = ['camera', 'album'];
    } else {
      sourceType = [type];
    }
    if (config.front == '1') {
      // 自拍
      this.#zfbReady(() => {
        AlipayJSBridge.call('stockFrontPhoto', {}, (res) => {
          res.success = res.success == '1';
          res.dataURL = res.image;
          res.error = res.errorMessage;
          yjbCallBack(res);
        });
      });
    } else if (config.floatingLayer) {
      // 上传身份证
      let captureTip =
        config.floatingLayer === '1'
          ? '拍摄身份证<AFWSFont color=#ffffff>⼈⾯像</AFWSFont>，请确保身份证<AFWSFont color=#ffffff>在相框中</AFWSFont>'
          : '拍摄身份证<AFWSFont color=#FFFFFF>国徽⾯</AFWSFont>，请确保身份证<AFWSFont color=#FFFFFF>在相框中</AFWSFont>';
      this.#zfbReady(() => {
        AlipayJSBridge.call(
          'stockLimitPhoto',
          {
            quality: 75,
            captureTip,
            sourceType,
            isCrop: true,
            faceSide: config.floatingLayer === '1'
          },
          yjbCallBack
        );
      });
    } else {
      // 普通拍照
      this.#zfbReady(() => {
        AlipayJSBridge.call(
          'stockPhotoNative',
          {
            sourceType
          },
          yjbCallBack
        );
      });
    }
  }
}
