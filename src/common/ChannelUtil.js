// import { exitApp } from '@/common/util';

export default class ChannelClass {
  constructor({ vm, appCode }) {
    this.vm = vm;
    this.appCode = appCode;
    this.isChannel = this._checkChannel();
  }
  _checkChannel() {
    return window.$hvue.customConfig.thirdPartyChannels.includes(this.appCode);
  }
  beforeLogin() {
    console.log('第三方 beforeLogin');
    this.vm.showResetPwd = false;
  }
  afterLogin() {
    console.log('第三方 afterLogin');
    const bizType = $h.getSession('bizType');
    import('@/common/flowMixinV2.js').then((a) => {
      a.initFlow.call(this.vm, { bizType });
    });
  }
  beforeFlow() {
    console.log('第三方 beforeFlow');
  }
  afterFlow() {
    console.log('第三方 afterFlow');
    window.$router.replace({
      path: '/home'
    });
  }
}
