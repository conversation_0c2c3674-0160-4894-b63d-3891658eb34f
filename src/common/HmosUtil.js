import { checkedUserAgent } from '@/common/util';
import {
  setTitleHM,
  setTitleBGColorHM,
  closeWebHM,
  closeRiskFromLogin,
  callPhoneHM
} from '@/nativeShell/hmosCallH5';

export default class HmosUtil {
  constructor({ config }) {
    this.config = config;
    this.checkHM = checkedUserAgent().harmony;
  }
  closePage(p) {
    if (!this.checkHM) {
      console.error('请在鸿蒙端使用该功能');
      return;
    }
    let accountType = $h.getSession('accountType');
    const { isHarmonyWeb = '', from = '' } = $h.getSession('pageParams') || {};
    let params = {};
    if (from === 'login') {
      if (accountType == '0') {
        accountType = 1;
      } else if (accountType == '1') {
        accountType = 2;
      }
      params = { type: accountType, ...p };
      closeRiskFromLogin(params);
    } else if (isHarmonyWeb === 'h5') {
      window.history.go(-1);
    } else {
      closeWebHM(params);
    }
  }
  setTitle(title) {
    if (!this.checkHM) {
      console.error('请在鸿蒙端使用该功能');
      return;
    }
    const { from = '' } = $h.getSession('pageParams') || {};
    console.log('hmos Util setTitle');
    if (from !== 'login') {
      let param = {
        navBarBgColor: '#FFFFFF',
        titleColor: '#333333',
        title,
        rightTextColor: '#333333',
        leftText: '/common/imgs/left_arrow_grey.png',
        fullScreen: 0,
        leftType: 98,
        leftJS: 'window.closePageView()',
        rightType: 0
      };
      setTitleHM(param);
      setTitleBGColorHM({ style: 0 });
      window.closePageView = () => {
        closeWebHM({});
      };
    } else {
      let accountType = $h.getSession('accountType');
      let param = {
        navBarBgColor: '#FFFFFF',
        titleColor: '#333333',
        title,
        rightTextColor: '#333333',
        leftText: '/common/imgs/left_arrow_grey.png',
        fullScreen: 0,
        leftType: 98,
        leftJS: 'window.closeRisk()',
        rightType: 0
      };
      setTitleHM(param);
      setTitleBGColorHM({ style: 0 });
      if (accountType === '0') {
        accountType = 1;
      } else if (accountType === '1') {
        accountType = 2;
      }
      window.closeRisk = () => {
        closeRiskFromLogin({ type: accountType, valid: 0 });
      };
    }
  }
  callPhone(phone) {
    callPhoneHM({ phone });
  }
}
