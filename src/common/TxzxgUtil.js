/**
 * @description 腾讯自选股渠道工具类
 */
import { execQQStockJSBridge } from '@/common/util';
import { resetPasswordCallbackNotify } from '@/service/service'

export default class TxzxgUtil {
  static CHANNEL_TYPE_ZXG = '2003000000000'; // 腾讯自选股渠道类型
  static CHANNEL_TYPE_WZQ = '2429000000000'; // 腾讯微证券渠道类型
  // static API_ENDPOINT = window.location.origin + '/jwlapi/';

  /**
   * 初始化腾讯自选股工具类
   */
  constructor() {
    this.checkTxzxg = this.isTxzxgChannel();
  }

  /**
   * @private
   * @returns {boolean} 是否为腾讯自选股渠道
   */
  isTxzxgChannel() {
    return [TxzxgUtil.CHANNEL_TYPE_ZXG, TxzxgUtil.CHANNEL_TYPE_WZQ].includes(
      $h.getSession('channelType')
    );
  }

  /**
   * @private
   * @throws {Error} 非腾讯自选股渠道时抛出错误
   */
  validateChannel() {
    if (!this.checkTxzxg) {
      throw new Error('请在腾讯自选股渠道使用该功能');
    }
  }

  /**
   * 关闭当前页面
   */
  closePage() {
    try {
      this.validateChannel();
      execQQStockJSBridge(() => {
        window.StockJSBridge.invoke('exit', {});
      });
    } catch (error) {
      console.error(error.message);
    }
  }

  /**
   * 退出登录
   * @param {Object} params - 退出登录参数
   * @param {string} params.fundAccount - 资金账号
   * @param {Function} callback - 回调函数
   */
  async logout({ fundAccount }, callback) {
    try {
      if (!fundAccount) {
        throw new Error('资金账号不能为空');
      }

      if (!this.checkTxzxg) {
        console.warn('非腾讯自选股渠道');
        callback?.();
        return;
      }

      const { wzq_id = '' } = $h.getSession('pageParams') || {};
      const regParams = {
        fundAccount,
        wzqId: wzq_id
      }
      const response = await resetPasswordCallbackNotify(regParams);
      console.info('regParams === ', regParams);
      console.log('退出登录成功:', response);
    } catch (error) {
      console.error('退出登录失败:', error);
    } finally {
      callback?.();
    }
  }
}
