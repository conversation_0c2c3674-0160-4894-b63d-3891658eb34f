import { checkedUserAgent } from '@/common/util';
export default class WeixinUtil {
  constructor(config) {
    this.config = config;
    this.checkWx = checkedUserAgent().weixin;
  }
  weixinClosePage() {
    if (typeof WeixinJSBridge == 'undefined') {
      if (document.addEventListener) {
        document.addEventListener(
          'WeixinJSBridgeReady',
          this.#closePage,
          false
        );
      } else if (document.attachEvent) {
        document.attachEvent('WeixinJSBridgeReady', this.#closePage);
        document.attachEvent('onWeixinJSBridgeReady', this.#closePage);
      }
    } else {
      this.#closePage();
    }
  }
  #closePage() {
    WeixinJSBridge.call('closeWindow');
  }
}
