/**************************************************************
 @description vue过滤器常用方法
******************************************************************/
const formatMoney = (num) => {
  if (!num) {
    return '0.00';
  }
  let info = parseFloat(num).toFixed(2).toString().split('.');
  num = info[0];
  let result = '';
  while (num.length > 3) {
    result = ',' + num.slice(-3) + result;
    num = num.slice(0, num.length - 3);
  }
  if (num) {
    result = num + result;
  }
  info[0] = result;
  return info.join('.');
};

const formatMoneyV2 = (num, decimals = 0) => {
  if (!num) {
    return decimals > 0 ? '0' + '.' + '0'.repeat(decimals) : '0';
  }

  // 根据decimals参数格式化小数位
  let info = parseFloat(num).toFixed(decimals).toString().split('.');
  num = info[0];
  let result = '';

  // 处理千分位分隔符
  while (num.length > 3) {
    result = ',' + num.slice(-3) + result;
    num = num.slice(0, num.length - 3);
  }
  if (num) {
    result = num + result;
  }

  info[0] = result;
  // 如果decimals为0，则不返回小数部分
  return decimals > 0 ? info.join('.') : result;
};

const formatIdno = (value) => {
  if (value === '' || value === null || value === undefined) {
    return '--';
  } else {
    let str =
      value.substr(0, 6) +
      '*****' +
      value.substr(value.length - 4, value.length);
    return str;
  }
};

const formatMobileNo = (value) => {
  if (value === '' || value === null || value === undefined) {
    return '';
  }else if(value.length <= 7){
    return value;
  } else {
    let formatVal = '';
    for(let i = 0; i < value.length; i++) {
      if(i < 3 || value.length - 4 <= i) {
        formatVal += value.charAt(i)
      }else {
        formatVal += '*'
      }
    }
    return formatVal;
  }
};

const formatBankCardNo = (value) => {
  if (value === '' || value === null || value === undefined) {
    return '--';
  } else {
    let str =
      value.substr(0, 4) +
      ' **** ****' +
      value.substr(value.length - 4, value.length);
    return str;
  }
};

const formatDate = (date, format) => {
  if (date === '********') {
    return '长期';
  }
  let TimeDate;
  if (typeof date === 'string') {
    TimeDate = new Date();
    TimeDate.setFullYear(
      date.substr(0, 4),
      date.substr(4, 2) - 1,
      date.substr(6)
    );
  }
  if ($h.isDate(date)) {
    TimeDate = date;
  }
  if (!TimeDate || TimeDate === 'Invalid Date') {
    // alert("当前传入时间格式存在问题");
    return '';
  }
  return TimeDate.format(format);
};
export {
  formatMoney,
  formatMoneyV2,
  formatIdno,
  formatDate,
  formatMobileNo,
  formatBankCardNo
};
