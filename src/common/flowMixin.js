import { flowInit, flowInitV2, getConfigMap } from '@/service/service';
import { announcementQry } from '@/service/specialService';
import ChannelUtil from '@/common/ChannelUtil';
import { execQQStockJSBridge, isWeixin } from '@/common/util';
import { getNumber } from '@/common/tipsCommon';
// import AlipayUtil from '@/common/AlipayUtil';
// const alipayUtil = new AlipayUtil();
import AlipayUtil from '@/common/AlipayUtil';
import { useService } from '@/common/utils/service';
const { countServiceAdd, countServiceSub } = useService();
const alipayUtil = new AlipayUtil();

export default {
  data() {
    return {
      flowNodeNo: '',
      jumpMode: '', // 当前步骤跳转模式
      flowOutputInfo: {
        bizName: '',
        inProperty: {}, // 入参属性
        outProperty: {}, // 出参属性
        privProperty: {}, // 私有属性
        taskStatus: '',
        stepName: '',
        rejectFields: [], //驳回字段
        prevFlowNodeNo: '',
        prevStepName: '',
        prevStepPath: '',
        prevJumpMode: '',
        stepMode: '', // 表单模式 1表单节点
        stepContent: {
          jsonSchema: '',
          pageConfig: [],
          interfaceConfig: [],
          validation: ''
        } //如果是表单节点,该值为JSON Schema内容
      }
    };
  },
  computed: {
    showBackAlert() {
      const pageConfigList = this.flowOutputInfo?.stepContent?.pageConfig;
      if (pageConfigList?.length > 0) {
        return pageConfigList.some(({ propKey, propValue }) => {
          return propKey && propValue === true && propKey === 'backAlert';
        });
      } else {
        return false;
      }
    }
  },
  methods: {
    getFlowInfo() {
      $h.setSession('onResultPage', false);
      $h.setSession('initJumpMode', null);
      // $h.setSession('showRejectResult', false);
      let _this = this;
      // _hvueLoading.open();
      const requestKey = Math.random().toString(36).slice(-8);
      countServiceAdd({
        url: 'flowInsNode',
        requestKey: requestKey,
        headers: { isLoading: true }
      });
      TKFlowEngine.flowInsNode(
        this.flowNodeNo,
        function (data) {
          // _hvueLoading.close();
          countServiceSub({ url: 'flowInsNode', requestKey: requestKey });
          if (data.code !== 0) {
            _this.$TAlert({
              tips: data.msg
            });
            return;
          }
          // isFlowBack用于判断是浏览器返回还是手动左上角返回，true为手动左上角返回。false为浏览器返回
          $h.setSession('isFlowBack', false);
          let node = {
            flowNodeNo: data.data.flowNodeNo,
            flowToken: data.data.flowToken,
            stepName: data.data.stepName,
            stepPath: data.data.stepPath,
            jumpMode: data.data.jumpMode
          };
          const PreNode = {
            flowNodeNo: data.data.prevFlowNodeNo,
            flowToken: data.data.flowToken,
            stepName: data.data.prevStepName,
            stepPath: data.data.prevStepPath,
            jumpMode: data.data.prevJumpMode
          };
          $h.setSession('PreNode', PreNode);
          TKFlowEngine.saveLocalFlowInsCurNode(node);
          if (data.code === 0) {
            let res = data.data;
            if (!res)
              _this.$TAlert({
                tips: '网络异常,请重新进入业务',
                confirm: () => _this.toIndex()
              });
            if (res.stepMode === '1') {
              // 表单模式
              try {
                res.stepContent = Object.assign(
                  {},
                  JSON.parse(res.stepContent)
                );
              } catch (e) {
                _hvueToast({ mes: e });
              }
            }
            _this.jumpMode = res.jumpMode;
            _this.flowOutputInfo = Object.assign(_this.flowOutputInfo, res);
            console.log(_this.flowOutputInfo);
            if (alipayUtil.checkAlipay)
              alipayUtil.setTitle(_this.flowOutputInfo.stepName);

            if (
              Object.prototype.toString.call(_this.renderingView) ===
              '[object Function]'
            ) {
              _this.renderingView();
            }
          } else {
            _hvueToast({
              mes: data.msg
            });
          }
        },
        '1'
      );
    },
    nextFlow(context = {}, path = '', callback) {
      // _hvueLoading.open();
      let _this = this;
      const requestKey = Math.random().toString(36).slice(-8);
      countServiceAdd({
        url: 'flowInsNext',
        requestKey: requestKey,
        headers: { isLoading: true }
      });
      TKFlowEngine.flowInsNext(this.flowNodeNo, context, function (data) {
        // _hvueLoading.close();
        countServiceSub({ url: 'flowInsNext', requestKey: requestKey });
        if (Object.prototype.toString.call(callback) === '[object Function]') {
          callback(data);
        }
        if (data.code === 0) {
          if (data.data.flowNodeNo === '-1') {
            // _hvueToast({
            //   mes: '预约流程已完成'
            // });
            return;
          }
          if (path && data.data.flowNodeNo !== '-1') {
            _this.$router.replace({
              path: path
            });
          } else {
            TKFlowEngine.forward(data.data, _this.jumpMode);
          }
        } else {
          try {
            const msg = JSON.parse(data.msg);
            const { title, tips } = msg;
            _this.$TAlert({
              title,
              tips
            });
          } catch (err) {
            _hvueToast({
              mes: data.msg
            });
          }
        }
      });
    },
    prevFlow() {
      // isFlowBack用于判断是浏览器返回还是手动左上角返回，true为手动左上角返回。false为浏览器返回
      $h.setSession('isFlowBack', true);
      console.log('~~~~prevFlow~~~~~');
      // 获取API模拟堆栈数量
      console.log(TKFlowEngine.getFlowStack());
      if (this.showBackAlert) {
        this.$TAlert({
          tips: '请确认是否退出办理流程',
          hasCancel: true,
          confirmBtn: '确认',
          confirm: () => {
            this.toIndex();
          }
        });
        return;
      }
      console.log('fromBizIntroduce' + $h.getSession('fromBizIntroduce'));
      if ($h.getSession('fromBizIntroduce')) {
        this.$router.replace({
          name: 'businessIntroduce',
          query: {
            bizType: $h.getSession('bizType'),
            toPage: 'introduce'
          }
        });
        return;
      }
      console.log('fromIntroduce' + $h.getSession('fromIntroduce'));
      if ($h.getSession('fromIntroduce')) {
        this.$router.replace({
          name: 'introduce',
          query: {
            bizType: $h.getSession('bizType'),
            toPage: 'introduce'
          }
        });
        return;
      }
      if ($h.getSession('inExchangFoundAccount')) {
        this.$router.replace({
          name: 'inExchangFoundAccount'
        });
        return;
      }
      if ($h.getSession('addShareHoldAccount')) {
        this.$router.replace({
          name: 'addShareHoldAccount'
        });
        return;
      }
      if ($hvue.platform == 0) {
        // H5
      } else {
        // APP
        if ($h.getSession('optType') === '1') {
          let reqParams = {
            funcNo: '60099',
            actionType: '5',
            params: {
              optType: $h.getSession('account_type'),
              isSuccess: '0'
            }
          };
          const res = $h.callMessageNative(reqParams);
          console.log(`请求结果为: ~~${JSON.stringify(res)}`);
        }
        if ($h.getSession('optType') === 'loginSuccess') {
          let reqParams = {
            funcNo: '60099',
            actionType: '5',
            params: {
              optType: $h.getSession('account_type'),
              isSuccess: '1'
            }
          };
          const res = $h.callMessageNative(reqParams);
          console.log(`请求结果为: ~~${JSON.stringify(res)}`);
        }
      }
      if (TKFlowEngine.getFlowStack().length > 0) {
        const node = {
          flowNodeNo: this.flowOutputInfo.prevFlowNodeNo,
          stepName: this.flowOutputInfo.prevStepName,
          stepPath: this.flowOutputInfo.prevStepPath,
          jumpMode: this.flowOutputInfo.prevJumpMode
        };
        TKFlowEngine.goback(node);
      } else {
        this.toIndex();
      }
    },
    toIndex() {
      console.log('--------toIndex---------');
      sessionStorage.removeItem('TKFlowToken');
      if ($h.getSession('channelType') === '*************') {
        execQQStockJSBridge(function () {
          window.StockJSBridge.invoke('exit', {});
        });
        return;
      }
      if (alipayUtil.checkAlipay) {
        alipayUtil.toIndex();
        return;
      }
      console.log('fromBizIntroduce' + $h.getSession('fromBizIntroduce'));
      if ($h.getSession('fromBizIntroduce')) {
        this.$router.replace({
          name: 'businessIntroduce',
          query: {
            bizType: $h.getSession('bizType'),
            toPage: 'introduce'
          }
        });
        return;
      }
      if ($h.getSession('fromIntroduce')) {
        this.$router.replace({
          name: 'introduce',
          query: {
            bizType: $h.getSession('bizType'),
            toPage: 'introduce'
          }
        });
        return;
      }
      if ($h.getSession('inExchangFoundAccount')) {
        this.$router.replace({
          name: 'inExchangFoundAccount'
        });
        return;
      }
      if ($h.getSession('addShareHoldAccount')) {
        this.$router.replace({
          name: 'addShareHoldAccount'
        });
        return;
      }
      if ($hvue.platform == 0) {
        if (PACK_ENV !== 'local') {
          if (isWeixin()) {
            if ($h.getSession('backUrl') !== '') {
              // 处理微信登录页跳转风测问题
              window.location.replace($hvue.customConfig.thirdPartyUrl.index);
            } else {
              window.history.go(TKFlowEngine.getFlowStack().length * -1);
            }
          } else {
            window.location.href = $hvue.customConfig.thirdPartyUrl.index;
          }
          // window.location.href = $hvue.customConfig.thirdPartyUrl.index;
        } else {
          TKFlowEngine.goHome();
        }
      } else {
        console.log(`optTypeValue--->${$h.getSession('optType')}`);
        if ($h.getSession('optType') === '1') {
          let reqParams = {
            funcNo: '60099',
            actionType: '5',
            params: {
              optType: $h.getSession('account_type'),
              isSuccess: '0'
            }
          };
          const res = $h.callMessageNative(reqParams);
          console.log(`请求结果为: ~~${JSON.stringify(res)}`);
          $h.callMessageNative({
            funcNo: '50114',
            moduleName: $hvue.customConfig.moduleName,
            targetModule: $hvue.customConfig.moduleName
          });
        } else if ($h.getSession('optType') === 'loginSuccess') {
          let reqParams = {
            funcNo: '60099',
            actionType: '5',
            params: {
              optType: $h.getSession('account_type'),
              isSuccess: '1'
            }
          };
          const res = $h.callMessageNative(reqParams);
          console.log(`请求结果为: ~~${JSON.stringify(res)}`);
          $h.callMessageNative({
            funcNo: '50114',
            moduleName: $hvue.customConfig.moduleName,
            targetModule: $hvue.customConfig.moduleName
          });
        } else {
          $h.callMessageNative({
            funcNo: '50114',
            moduleName: $hvue.customConfig.moduleName,
            targetModule: $hvue.customConfig.moduleName
          });
        }
      }
    },
    toLogin() {
      $h.clearSession();
      this.toIndex();
    }
  },
  created() {
    /* let t = window.location.href;
    t = t.split('?')[0];
    let e = t.split('/');
    let flowPath = '';
    if (e.length > 0) {
      var r = e.pop();
      if (r.indexOf('Flow_') >= 0) {
        var o = r.split('_');
        flowPath = (o.pop(), o.join('_'));
      }
    }
    const flowToken = TKFlowEngine.storage.getSessionStorage(
      'TKFlowToken_' + flowPath
    ); */
    const flowToken = TKFlowEngine.getLocalFlowInsToken();
    if (flowToken)
      TKFlowEngine.storage.setSessionStorage('TKFlowToken', flowToken);
    console.log('flowToken=' + flowToken);
  },
  mounted() {
    console.log('mounted****************************');
    setTimeout(
      () =>
        this.$nextTick(function () {
          this.flowNodeNo = TKFlowEngine.getLocalFlowInsCurNode().flowNodeNo;
          if (!this.flowNodeNo) {
            _hvueToast({
              mes: '流程编号丢失，请重新登录'
            });
            // this.toLogin();
          } else {
            this.$store.commit('flow/setWhiteBg', false);
            this.getFlowInfo();
            console.log(this.flowNodeNo);
          }
        }),
      10
    );
  }
  // beforeRouteLeave(to, from, next) {
  //   // 流程内部走goback(),外部走浏览器返回
  //   console.log('beforeLeave****************************');

  //   if (TKFlowEngine.getFlowStack().length > 0) {
  //     const node = {
  //       flowNodeNo: this.flowOutputInfo.prevFlowNodeNo,
  //       stepName: this.flowOutputInfo.prevStepName,
  //       stepPath: this.flowOutputInfo.prevStepPath,
  //       jumpMode: this.flowOutputInfo.prevJumpMode
  //     };
  //     TKFlowEngine.goback(node);
  //     next();
  //   } else {
  //     TKFlowEngine.goHome();
  //     next();
  //   }
  //   next(false);
  // }
};

export function initFlow(
  bizType,
  flowNo,
  isReset,
  contextParam,
  flowInsId,
  extendParam = {}
) {
  //   const { clientId, fundAccount } = this.$store.state.user.userInfo;
  const appCode = $h.getSession('appCode');
  const channelUtil = new ChannelUtil({ appCode });
  const { initCallBack } = extendParam;
  // 如果存在用户信息，初始化的时候就需要调用
  console.log(window.$hvue.customConfig.flowVersion);
  announcementQry({
    announcementType: '1002',
    announcementSubType: getNumber(bizType)
  }).then((res) => {
    if (res.data.announcementList.length > 0 && getNumber(bizType) !== '') {
      this.$TAlert({
        title: '温馨提示',
        tips: res.data.announcementList[0].content,
        confirmBtn: '我知道了',
        confirm: () => {}
      });
    } else {
      if (flowNo || bizType) {
        if (window.$hvue.customConfig.flowVersion === '1.0') {
          // v1版本流程初始化
          flowInit({
            bizType,
            flowNo,
            isForceNew: isReset ? 1 : null,
            isFormNew: isReset ? 1 : null,
            contextParam,
            flowInsId,
            appId: $h.getSession('appId')
          })
            .then((flowInitData) => {
              if (flowInitData.code === 0) {
                console.log('-------flowInit-----');
                let res = flowInitData.data;
                let node = {
                  flowNodeNo: res.routeNode.flowNodeNo,
                  stepName: res.routeNode.stepName,
                  stepPath: res.routeNode.stepPath,
                  jumpMode: res.routeNode.jumpMode,
                  flowToken: res.flowToken,
                  flowTokenKey: res.flowTokenKey
                };
                TKFlowEngine.saveLocalFlowInsCurNode(node);
                TKFlowEngine.saveFlowInsToken(res.flowToken, res.flowTokenKey);
                TKFlowEngine.forward(res.routeNode);
                if (channelUtil.isChannel) channelUtil.beforeFlow();
              } else {
                return Promise.reject(new Error(flowInitData.msg));
              }
            })
            .catch((err) => {
              try {
                const { title, tips } = JSON.parse(err);
                const toBizType = JSON.parse(err)?.bizType;
                this.$TAlert({
                  title,
                  tips,
                  confirmBtn:
                    toBizType && !channelUtil.isChannel
                      ? '前往完善'
                      : '我知道了',
                  confirm: () => {
                    if (toBizType && !channelUtil.isChannel) {
                      initFlow(toBizType);
                    }
                  }
                });
              } catch (jsError) {
                _hvueToast({
                  mes: err
                });
              }
            });
        } else if (window.$hvue.customConfig.flowVersion === '2.0') {
          // v2版本流程初始化
          flowInitV2({
            bizType,
            flowNo,
            isForceNew: isReset ? 1 : null,
            isFormNew: isReset ? 1 : null,
            contextParam,
            flowInsId,
            appId: $h.getSession('appId')
          })
            .then((flowInitData) => {
              if (flowInitData.code === 0) {
                console.log('-------flowInit-----');
                let res = flowInitData.data;
                let jumpMode = '0';
                if (
                  PACK_ENV !== 'local' &&
                  $hvue.platform === '0' &&
                  this.$route.name.indexOf('introduce') === -1 &&
                  this.$route.name.indexOf('addShareHoldAccount') === -1
                ) {
                  $h.clearSession('addShareHoldAccount');
                  jumpMode = '1';
                }
                if ($h.getSession('initJumpMode') === '0') {
                  jumpMode = '0';
                }
                if ($h.getSession('initJumpMode') === '1') {
                  jumpMode = '1';
                }
                if (
                  bizType !== '010044' &&
                  bizType !== '010729' &&
                  bizType !== '010728'
                ) {
                  $h.clearSession('addShareHoldAccount');
                }
                let node = {
                  flowNodeNo: res.routeNode.flowNodeNo,
                  stepName: res.routeNode.stepName,
                  stepPath: res.routeNode.stepPath,
                  jumpMode: res.routeNode.jumpMode,
                  flowToken: res.flowToken,
                  flowTokenKey: res.flowTokenKey
                };
                if (initCallBack?.constructor?.name === 'Function') {
                  initCallBack(res)
                    .then(({ code = 0 }) => {
                      if (code === 0) {
                        TKFlowEngine.saveFlowInsToken(
                          res.flowToken,
                          res.flowTokenKey
                        );
                        TKFlowEngine.saveLocalFlowInsCurNode(node);
                        $h.setSession('nowBizType', bizType);
                        TKFlowEngine.forward(
                          {
                            ...res.routeNode,
                            flowToken: res.flowToken
                          },
                          jumpMode,
                          '0'
                        );
                        if (channelUtil.isChannel) channelUtil.beforeFlow();
                      }
                    })
                    .catch((err) => {
                      this.$TAlert({
                        tips: err
                      });
                    });
                } else {
                  TKFlowEngine.saveFlowInsToken(
                    res.flowToken,
                    res.flowTokenKey
                  );
                  TKFlowEngine.saveLocalFlowInsCurNode(node);
                  $h.setSession('nowBizType', bizType);
                  TKFlowEngine.forward(
                    {
                      ...res.routeNode,
                      flowToken: res.flowToken
                    },
                    jumpMode,
                    '0'
                  );
                  if (channelUtil.isChannel) channelUtil.beforeFlow();
                }
              } else {
                return Promise.reject(new Error(flowInitData.msg));
              }
            })
            .catch((err) => {
              try {
                const { title, tips } = JSON.parse(err);
                const toBizType = JSON.parse(err)?.bizType;
                this.$TAlert({
                  title,
                  tips,
                  confirmBtn:
                    toBizType && !channelUtil.isChannel
                      ? '前往完善'
                      : '我知道了',
                  confirm: () => {
                    if (alipayUtil.checkAlipay) {
                      alipayUtil.toIndex();
                      return;
                    }
                    $h.callMessageNative({
                      funcNo: '50114',
                      moduleName: $hvue.customConfig.moduleName,
                      // targetModule: $h.getSession('tkModuleName') || 'open'
                    });
                  }
                });
              } catch (jsError) {
                _hvueToast({
                  mes: err
                });
              }
            });
        }
      } else {
        _hvueToast({
          mes: '流程信息缺失'
        });
        this.toIndex();
      }
    }
  });
  return;
}

export async function initFlowV2({
  bizType,
  flowNo,
  isReset,
  contextParam,
  flowInsId,
  extendParam = {},
  initJumpMode
}) {
  const appCode = $h.getSession('appCode');
  const { initCallBack } = extendParam;

  // 查询总开关(暂无,后续调整)

  // 查询业务单独开关
  let configKey = 'bc.opt.urlback.' + bizType;
  let { data } = await getConfigMap({ configKey });
  console.log(data);
  // 从配置文件中读取url地址,公告,bizType相关
  let jumpUrl = $hvue.customConfig.businessPath.filter(
    (item) => item.bizType === bizType
  )[0];
  if (data[configKey].configValue === '0') {
    // 跳转思迪业务办理
    // 先检查有无公告
    let { data } = await announcementQry({
      announcementType: '1002',
      announcementSubType: jumpUrl.announcementNo
    });
    if (data.announcementList.length > 0) {
      // 有公告,拦截
      this.$TAlert({
        title: data.announcementList[0].announcementTitle,
        tips: data.announcementList[0].content,
        confirmBtn: '我知道了',
        confirm: () => {}
      });
    } else {
      // 无公告,进入业务
      flowInitV2({
        bizType,
        flowNo,
        isForceNew: isReset ? 1 : null,
        isFormNew: isReset ? 1 : null,
        contextParam,
        flowInsId,
        appId: $h.getSession('appId')
      })
        .then((flowInitData) => {
          if (flowInitData.code === 0) {
            console.log('-------flowInit-----');
            let res = flowInitData.data;
            let jumpMode = initJumpMode ? initJumpMode : '1';
            let node = {
              flowNodeNo: res.routeNode.flowNodeNo,
              stepName: res.routeNode.stepName,
              stepPath: res.routeNode.stepPath,
              jumpMode: res.routeNode.jumpMode,
              flowToken: res.flowToken,
              flowTokenKey: res.flowTokenKey
            };
            if (initCallBack?.constructor?.name === 'Function') {
              initCallBack(res)
                .then(({ code = 0 }) => {
                  if (code === 0) {
                    TKFlowEngine.saveFlowInsToken(
                      res.flowToken,
                      res.flowTokenKey
                    );
                    TKFlowEngine.saveLocalFlowInsCurNode(node);
                    $h.setSession('nowBizType', bizType);
                    TKFlowEngine.forward(
                      {
                        ...res.routeNode,
                        flowToken: res.flowToken
                      },
                      jumpMode,
                      '0'
                    );
                  }
                })
                .catch((err) => {
                  this.$TAlert({
                    tips: err
                  });
                });
            } else {
              TKFlowEngine.saveFlowInsToken(res.flowToken, res.flowTokenKey);
              TKFlowEngine.saveLocalFlowInsCurNode(node);
              $h.setSession('nowBizType', bizType);
              TKFlowEngine.forward(
                {
                  ...res.routeNode,
                  flowToken: res.flowToken
                },
                jumpMode,
                '0'
              );
            }
          } else {
            return Promise.reject(new Error(flowInitData.msg));
          }
        })
        .catch((err) => {
          try {
            const { title, tips } = JSON.parse(err);
            const toBizType = JSON.parse(err)?.bizType;
            this.$TAlert({
              title,
              tips,
              confirmBtn: toBizType ? '前往完善' : '我知道了',
              confirm: () => {
                if (alipayUtil.checkAlipay) {
                  alipayUtil.toIndex();
                  return;
                }
                $h.callMessageNative({
                  funcNo: '50114',
                  moduleName: $hvue.customConfig.moduleName,
                  targetModule: $hvue.customConfig.moduleName
                });
              }
            });
          } catch (jsError) {
            _hvueToast({
              mes: err
            });
          }
        });
    }
  } else {
    // 跳转国金自研
    if ($hvue.platform == 0) {
      if ($h.getSession('channelType') === '2095000000000') {
        targetUrl =
          jumpUrl.gjzqBizUrl + '&sd_token=' + $h.getSession('authorization');
        // 支付宝渠道
        AlipayJSBridge.call('pushWindow', {
          url: targetUrl,
          param: {
            readTitle: true
          }
        });
      } else {
        window.location.href = jumpUrl.gjzqBizUrl.replace('${appId}', 'web').replace('${channelType}', '1009000000000');
      }
    } else {
      let reqParams = {
        funcNo: '60099',
        moduleName: $h.getSession('tkModuleName') || $hvue.customConfig.moduleName,
        actionType: '6',
        // targetModule: 'open',
        params: {
          url: jumpUrl.gjzqBizUrl.replace('${appId}', 'yjb3.0').replace('${channelType}', '1009000000001'),
          leftType: 1,
          rightType: 99,
          rightText: ''
        }
      };
      console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
      const res = $h.callMessageNative(reqParams);
      console.log(`请求结果为: ~~${JSON.stringify(res)}`);
    }
  }
}
