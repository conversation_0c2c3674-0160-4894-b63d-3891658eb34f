import { flowInitV2, getConfigMap, previousAcceptanceFormDataQry } from '@/service/service';
import { announcementQry } from '@/service/specialService';
import { execQQStockJSBridge, isWeixin } from '@/common/util';
import AlipayUtil from '@/common/AlipayUtil';
import { useService } from '@/common/utils/service';
import Vue from 'vue';
import rejectResultV2 from '@/views/form/components/rejectResultV2';
const { countServiceAdd, countServiceSub } = useService();
const alipayUtil = new AlipayUtil();

export default {
  data() {
    return {
      flowNodeNo: '',
      jumpMode: '', // 当前步骤跳转模式
      flowOutputInfo: {
        bizName: '',
        inProperty: {}, // 入参属性
        outProperty: {}, // 出参属性
        privProperty: {}, // 私有属性
        taskStatus: '',
        stepName: '',
        rejectFields: [], //驳回字段
        prevFlowNodeNo: '',
        prevStepName: '',
        prevStepPath: '',
        prevJumpMode: '',
        stepMode: '', // 表单模式 1表单节点
        stepContent: {
          jsonSchema: '',
          pageConfig: [],
          interfaceConfig: [],
          validation: ''
        } //如果是表单节点,该值为JSON Schema内容
      }
    };
  },
  computed: {
    showBackAlert() {
      const pageConfigList = this.flowOutputInfo?.stepContent?.pageConfig;
      if (pageConfigList?.length > 0) {
        return pageConfigList.some(({ propKey, propValue }) => {
          return propKey && propValue === true && propKey === 'backAlert';
        });
      } else {
        return false;
      }
    }
  },
  methods: {
    getFlowInfo() {
      $h.setSession('onResultPage', false);
      let _this = this;
      // _hvueLoading.open();
      const requestKey = Math.random().toString(36).slice(-8);
      countServiceAdd({
        url: 'flowInsNode',
        requestKey: requestKey,
        headers: { isLoading: true }
      });
      TKFlowEngine.flowInsNode(
        this.flowNodeNo,
        function (data) {
          // _hvueLoading.close();
          countServiceSub({ url: 'flowInsNode', requestKey: requestKey });
          if (data.code !== 0) {
            _this.$TAlert({
              tips: data.msg
            });
            return;
          }
          // isFlowBack用于判断是有没有走宝哥流程引擎返回还是手动左上角返回，true为手动左上角返回。false为浏览器返回
          $h.setSession('isFlowBack', false);
          let node = {
            flowNodeNo: data.data.flowNodeNo,
            flowToken: data.data.flowToken,
            stepName: data.data.stepName,
            stepPath: data.data.stepPath,
            jumpMode: data.data.jumpMode
          };
          const PreNode = {
            flowNodeNo: data.data.prevFlowNodeNo,
            flowToken: data.data.flowToken,
            stepName: data.data.prevStepName,
            stepPath: data.data.prevStepPath,
            jumpMode: data.data.prevJumpMode
          };
          $h.setSession('PreNode', PreNode);
          TKFlowEngine.saveLocalFlowInsCurNode(node);
          if (data.code === 0) {
            let res = data.data;
            if (!res)
              _this.$TAlert({
                tips: '网络异常,请重新进入业务',
                confirm: () => _this.toIndex()
              });
            if (res.stepMode === '1') {
              // 表单模式
              try {
                res.stepContent = Object.assign(
                  {},
                  JSON.parse(res.stepContent)
                );
              } catch (e) {
                _hvueToast({ mes: e });
              }
            }
            _this.jumpMode = res.jumpMode;
            _this.flowOutputInfo = Object.assign(_this.flowOutputInfo, res);
            console.log(_this.flowOutputInfo);
            if (alipayUtil.checkAlipay)
              alipayUtil.setTitle(_this.flowOutputInfo.stepName);
            if (
              Object.prototype.toString.call(_this.renderingView) ===
              '[object Function]'
            ) {
              _this.renderingView();
            }
          } else {
            _hvueToast({
              mes: data.msg
            });
          }
        },
        '1'
      );
    },
    nextFlow(context = {}, path = '', callback) {
      // _hvueLoading.open();
      let _this = this;
      const requestKey = Math.random().toString(36).slice(-8);
      countServiceAdd({
        url: 'flowInsNext',
        requestKey: requestKey,
        headers: { isLoading: true }
      });
      TKFlowEngine.flowInsNext(this.flowNodeNo, context, function (data) {
        // _hvueLoading.close();
        countServiceSub({ url: 'flowInsNext', requestKey: requestKey });
        if (Object.prototype.toString.call(callback) === '[object Function]') {
          callback(data);
        }
        if (data.code === 0) {
          if (data.data.flowNodeNo === '-1') {
            // _hvueToast({
            //   mes: '预约流程已完成'
            // });
            return;
          }
          if (path && data.data.flowNodeNo !== '-1') {
            _this.$router.replace({
              path: path
            });
          } else {
            TKFlowEngine.forward(data.data, _this.jumpMode);
          }
        } else {
          try {
            const msg = JSON.parse(data.msg);
            const { title, tips } = msg;
            _this.$TAlert({
              title,
              tips
            });
          } catch (err) {
            _hvueToast({
              mes: data.msg
            });
          }
        }
      });
    },
    prevFlow() {
      // 统一上一步方法
      // isFlowBack用于判断是有没有走宝哥流程引擎返回还是手动左上角返回，true为手动流程引擎返回。false为浏览器返回
      $h.setSession('isFlowBack', true);
      console.log('---------prevFlow---------');
      // 获取API模拟堆栈数量
      console.log(TKFlowEngine.getFlowStack());
      // TKFlowEngine.getFlowStack().length === 1表示当前在宝哥堆栈的第一个节点,此时再返回就是走完成流程的方法退出流程
      if (!this.flowOutputInfo.prevFlowNodeNo) {
        // 没有上一个节点的情况下，返回时
        this.toComplete();
      } else {
        const node = {
          flowNodeNo: this.flowOutputInfo.prevFlowNodeNo,
          stepName: this.flowOutputInfo.prevStepName,
          stepPath: this.flowOutputInfo.prevStepPath,
          jumpMode: this.flowOutputInfo.prevJumpMode
        };
        TKFlowEngine.goback(node);
      }
    },

    toIndex() {
      // 固定返回首页方法
      console.log('--------toIndex---------');
      sessionStorage.removeItem('TKFlowToken');
      if ($h.getSession('channelType') === '2003000000000') {
        execQQStockJSBridge(function () {
          window.StockJSBridge.invoke('exit', {});
        });
        return;
      }
      if (alipayUtil.checkAlipay) {
        alipayUtil.toIndex();
        return;
      }

      // 国金返回首页描述统一为退出流程，此代码国金特定
      this.toComplete();
      return;

      // 正常返回固定首页方法
      if ($hvue.platform == 0) {
        // isFlowBack用于判断是有没有走宝哥流程引擎返回还是手动左上角返回，true为手动流程引擎返回。false为浏览器返回
        $h.setSession('isFlowBack', true);
        // 按照路由堆栈，宝哥流程内有多少个堆栈加上已经过的流程外页面的堆栈总数
        // 返回由前端维护的路由堆栈数量
        // let history = $h.getSession('history_list').records;
        // index为当前页面在路由堆栈中的位置
        let index = $h.getSession('history_list').index;
        this.$router.go(-index);
        // 需要清空宝哥堆栈信息
        TKFlowEngine.goHome('0');
      } else {
        // APP目前直接关闭sdk
        $h.callMessageNative({
          funcNo: '50114',
          moduleName: $h.getSession('_tkModuleName') || 'open',
          targetModule: $h.getSession('_tkModuleName') || 'open'
        });
      }
    },

    toComplete() {
      /* if (isWeixin()) {
        if (
          $h.getSession('backUrl') !== '' &&
          $h.getSession('backUrl') !== undefined
        ) {
          // 处理微信登录页跳转风测问题
          TKFlowEngine.goHome('0');
          window.location.replace($hvue.customConfig.thirdPartyUrl.index);
        }
      } */
      // 固定完成，退出当前业务方法
      // 当前所在的业务在宝哥流程堆栈中有多少个length，就返回go(-length)
      console.log('--------toComplete---------');

      const backUrl = $h.getSession('backUrl');
      if (!!backUrl) {
        TKFlowEngine.goHome('0');
        window.location.replace(backUrl);
      }

      let flowStack = TKFlowEngine.getFlowStack().map(
        (item) => item.flowNodeNo
      ); //宝哥维护的流程堆栈
      let history =
        $h.getSession('history_list').records[
          $h.getSession('history_list').index
        ].path; //当前历史堆栈位置的地址
      const bizType = $h.getSession('bizType');
      if ($hvue.platform == 0) {
        // isFlowBack用于判断是有没有走宝哥流程引擎返回还是手动左上角返回，true为手动流程引擎返回。false为浏览器返回
        $h.setSession('isFlowBack', true);
        // 获取当前流程在宝哥堆栈中业务编号bizType相同的数量有多少个，就返回多少个length
        // TKFlowEngine.jumpout();
        /**
         * 20241203 updateby wusm 兼容作废流程之后不经过介绍页，直接重新进入新流程的场景，isForceNoSameFlow 需要传 1
         *  isUseHistoryGoback:
         *        是否使用浏览器的goback(0:不使用，1：使用)，默认是1
         *  isForceNoSameFlow:
         *        是否强制跨流程跳出模式，默认是0，代表跨流程或者跨流程实例都会跳出
         * var jumpout = function(isUseHistoryGoback,isForceNoSameFlow)
         */
        TKFlowEngine.jumpout(1, bizType == '014001' ? 1 : 0);
      } else {
        // APP
        // ?需要判断是没有业务跳转了需要退出APP还是直接返回上一页
        // 判断第一个当前记录节点，是否有bizType关键词
        let history_first = $h.getSession('history_list').records[0].path;
        if (!history_first.includes('Flow_')) {
          // isFlowBack用于判断是有没有走宝哥流程引擎返回还是手动左上角返回，true为手动流程引擎返回。false为浏览器返回
          $h.setSession('isFlowBack', true);
          // 第一个堆栈是流程外页面，直接按照正常逻辑返回到流程外的页面
          // TKFlowEngine.jumpout();
          /**
           * 20241203 updateby wusm 兼容作废流程之后不经过介绍页，直接重新进入新流程的场景，isForceNoSameFlow 需要传 1
           *  isUseHistoryGoback:
           *        是否使用浏览器的goback(0:不使用，1：使用)，默认是1
           *  isForceNoSameFlow:
           *        是否强制跨流程跳出模式，默认是0，代表跨流程或者跨流程实例都会跳出
           * var jumpout = function(isUseHistoryGoback,isForceNoSameFlow)
           */
          TKFlowEngine.jumpout(1, bizType == '014001' ? 1 : 0);
        } else {
          $h.setSession('isFlowBack', true);
          // 第一个堆栈是流程内页面，判断当前流程是否为宝哥流程堆栈第一个流程,是则关闭sdk,否则按照退出当前流程操作返回
          let _bizType = history.split('Flow_')[1].split('_')[0]; //当前业务编号
          let bizList = flowStack.map(
            (item) => item.split('Flow_')[1].split('_')[0]
          );
          const _bizList = Array.from(new Set(bizList));
          if (_bizList.indexOf(_bizType) !== 0) {
            console.log('执行的jumpout方法');
            // 当前流程非宝哥堆栈中第一个流程，需要返回到跳转前页面
            // TKFlowEngine.jumpout();
            /**
             * 20241203 updateby wusm 兼容作废流程之后不经过介绍页，直接重新进入新流程的场景，isForceNoSameFlow 需要传 1
             *  isUseHistoryGoback:
             *        是否使用浏览器的goback(0:不使用，1：使用)，默认是1
             *  isForceNoSameFlow:
             *        是否强制跨流程跳出模式，默认是0，代表跨流程或者跨流程实例都会跳出
             * var jumpout = function(isUseHistoryGoback,isForceNoSameFlow)
             */
            TKFlowEngine.jumpout(1, bizType == '014001' ? 1 : 0);
          } else {
            console.log('执行退出sdk方法');
            // 当前流程在宝哥堆栈的第一个流程，此时需要退出sdk
            console.log(`optTypeValue--->${$h.getSession('optType')}`);
            if ($h.getSession('optType') === '1') {
              // optType为1，表示退出sdk时需要给到通知退出登录，国金特有
              let reqParams = {
                funcNo: '60099',
                actionType: '5',
                params: {
                  optType: '1',
                  isSuccess: '0'
                }
              };
              const res = $h.callMessageNative(reqParams);
              console.log(`请求结果为: ~~${JSON.stringify(res)}`);
              $h.callMessageNative({
                funcNo: '50114',
                moduleName: $hvue.customConfig.moduleName,
                targetModule: $hvue.customConfig.moduleName
              });
            } else if ($h.getSession('optType') === 'loginSuccess') {
              // optType为loginSuccess，表示退出sdk时需要给到通知登录成功，国金特有
              let reqParams = {
                funcNo: '60099',
                actionType: '5',
                params: {
                  optType: '1',
                  isSuccess: '1'
                }
              };
              const res = $h.callMessageNative(reqParams);
              console.log(`请求结果为: ~~${JSON.stringify(res)}`);
              $h.callMessageNative({
                funcNo: '50114',
                moduleName: $hvue.customConfig.moduleName,
                targetModule: $hvue.customConfig.moduleName
              });
            } else {
              // 正常关闭sdk
              $h.callMessageNative({
                funcNo: '50114',
                moduleName: $hvue.customConfig.moduleName,
                targetModule: $hvue.customConfig.moduleName
              });
            }
          }
        }
      }
    },

    toLogin() { }
  },
  created() {
    const flowToken = TKFlowEngine.getLocalFlowInsToken();
    if (flowToken)
      TKFlowEngine.storage.setSessionStorage('TKFlowToken', flowToken);
    console.log('flowToken=' + flowToken);
  },
  mounted() {
    console.log('mounted****************************');
    setTimeout(
      () =>
        this.$nextTick(function () {
          this.flowNodeNo = TKFlowEngine.getLocalFlowInsCurNode().flowNodeNo;
          if (!this.flowNodeNo) {
            _hvueToast({
              mes: '流程编号丢失，请重新登录'
            });
            // this.toLogin();
          } else {
            this.$store.commit('flow/setWhiteBg', false);
            this.getFlowInfo();
            console.log(this.flowNodeNo);
            const flowToken = TKFlowEngine.getLocalFlowInsToken();
            if (flowToken)
              TKFlowEngine.storage.setSessionStorage('TKFlowToken', flowToken);
            console.log('flowToken=' + flowToken);
          }
        }),
      10
    );
  }
  // beforeRouteLeave(to, from, next) {

  // }
};

// 思迪流程初始化方法
async function toInitFlow(
  {
    bizType,
    flowNo,
    isReset,
    contextParam,
    flowInsId,
    extendParam = {},
    initJumpMode,
    cancelFlage
  },
  a
) {
  const { initCallBack } = extendParam;
  flowInitV2({
    bizType,
    flowNo,
    isForceNew: isReset ? 1 : null,
    isFormNew: isReset ? 1 : null,
    contextParam,
    flowInsId,
    appId: $h.getSession('appId'),
    cancelFlage: cancelFlage ? '1' : '0' // 是否提示作废信息  0否 1是
  }, { filter: true })
    .then((flowInitData) => {
      // flowInitData.code = '3001'
      // flowInitData.msg = JSON.stringify({
      //   time: '1111-11-11 11:11:11',
      //   msg: JSON.stringify(['1234']),
      //   step: 'cancelInvalid'
      // })
      if (flowInitData.code === 0) {
        console.log('-------flowInit-----');
        let res = flowInitData.data;
        // jumpMode:0 push  1 replace
        let jumpMode = initJumpMode ? initJumpMode : '1';
        console.log('flowInit 跳转模式：' + jumpMode);
        let node = {
          flowNodeNo: res.routeNode.flowNodeNo,
          stepName: res.routeNode.stepName,
          stepPath: res.routeNode.stepPath,
          jumpMode: res.routeNode.jumpMode,
          flowToken: res.flowToken,
          flowTokenKey: res.flowTokenKey
        };
        if (initCallBack?.constructor?.name === 'Function') {
          initCallBack(res)
            .then(({ code = 0 }) => {
              if (code === 0) {
                TKFlowEngine.saveFlowInsToken(res.flowToken, res.flowTokenKey);
                TKFlowEngine.saveLocalFlowInsCurNode(node);
                $h.setSession('nowBizType', bizType);
                TKFlowEngine.forward(
                  {
                    ...res.routeNode,
                    flowToken: res.flowToken
                  },
                  jumpMode,
                  '0'
                );
              }
            })
            .catch((err) => {
              a.$TAlert({
                tips: err
              });
            });
        } else {
          TKFlowEngine.saveFlowInsToken(res.flowToken, res.flowTokenKey);
          TKFlowEngine.saveLocalFlowInsCurNode(node);
          $h.setSession('nowBizType', bizType);
          TKFlowEngine.forward(
            {
              ...res.routeNode,
              flowToken: res.flowToken
            },
            jumpMode,
            '0'
          );
        }
      } else {
        if (flowInitData.code == '3001') {
          mountRejectResultComponent(flowInitData.msg, {
            rejectConfirm: () => {
              $h.setSession('bizType', bizType);
              try {
                // 20241203 by wusm  兼容审核作废流程导致的native_version、origin 参数非当前设备信息问题
                if (contextParam) {
                  const _contextParam = JSON.parse(contextParam);
                  let native_version = '';
                  if ($hvue.platform !== '0') {
                    const result = $h.callMessageNative({
                      funcNo: '50001'
                    });
                    result.results = result.results || [];
                    let data = result.results[0];
                    native_version = data.nativeVersion;
                  }
                  _contextParam.native_version = native_version;
                  _contextParam.origin = $hvue.platform;
                  contextParam = JSON.stringify(_contextParam);
                }
              } catch (e) {
                console.log(e);
              }
              toInitFlow({
                bizType,
                flowNo,
                isReset,
                contextParam,
                flowInsId,
                extendParam,
                initJumpMode,
                cancelFlage: false
              },
                a)
            },
            back: () => { },
            toIndex: () => {
              if (alipayUtil.checkAlipay) {
                alipayUtil.toIndex();
                return;
              }
              // 此处仅针对账户权限视图进行判断调整
              if (!toBizType) {
                if (a.$router.currentRoute.name == 'accountPermissionAttempt')
                  return;
              }
              $h.callMessageNative({
                funcNo: '50114',
                moduleName: $hvue.customConfig.moduleName,
                targetModule: $hvue.customConfig.moduleName
              });
            }
          });
          return true;
        }
        return Promise.reject(flowInitData.msg);
      }
    })
    .catch((err) => {
      try {
        const { title, tips } = JSON.parse(err);
        const toBizType = JSON.parse(err)?.bizType;
        a.$TAlert({
          title,
          tips,
          confirmBtn: toBizType ? '前往完善' : '我知道了',
          confirm: () => {
            if (alipayUtil.checkAlipay) {
              alipayUtil.toIndex();
              return;
            }
            // 此处仅针对账户权限视图进行判断调整
            if (!toBizType) {
              if (a.$router.currentRoute.name == 'accountPermissionAttempt')
                return;
            }
            $h.callMessageNative({
              funcNo: '50114',
              moduleName: $hvue.customConfig.moduleName,
              targetModule: $hvue.customConfig.moduleName
            });
          }
        });
      } catch (jsError) {
        _hvueToast({
          mes: err
        });
      }
    });
}

export function mountRejectResultComponent(data, { title = '审核不通过', rejectConfirm, back, toIndex }) {
  try {
    const { msg, time, step } = JSON.parse(data);
    let isCancelXH = false;
    // step: 驳回作废等于 rejectInvalid  取消作废等于 cancelInvalid
    if (step == 'cancelInvalid') {
      isCancelXH = true;
      title = '转销户业务';
    }
    // 创建Vue实例挂载rejectResult组件
    const vm = new Vue({
      el: document.createElement('div'),
      render: h => h(rejectResultV2, {
        props: {
          title,
          isCancelXH,
          showRejectResult: true,
          acceptCompTime: time,
          rejectList: JSON.parse(msg).map((t) => {
            return { reason: t }
          }),
        },
        on: {
          confirm: rejectConfirm,
          back: back,
          toIndex: toIndex
        }
      })
    });

    // 将挂载了组件的DOM元素添加到页面中合适的位置，这里添加到body元素下
    document.body.appendChild(vm.$el);

    return vm; // 返回创建的Vue实例，以便在其他地方可能需要使用它
  } catch (err) {

  }
}

// 跳转国金url
function gjzqJump(url, a) {
  if ($hvue.platform == 0) {
    if ($h.getSession('channelType') === '2095000000000') {
      targetUrl = url + '&sd_token=' + $h.getSession('authorization');
      // 支付宝渠道
      AlipayJSBridge.call('pushWindow', {
        url: targetUrl,
        param: {
          readTitle: true
        }
      });
    } else {
      $h.setSession('store', JSON.stringify(a.$store.state));
      window.location.href = url;
    }
  } else {
    let reqParams = {
      funcNo: '60099',
      moduleName: $hvue.customConfig.moduleName,
      actionType: '6',
      // targetModule: 'open',
      params: {
        url: url,
        leftType: 1,
        rightType: 99,
        rightText: ''
      }
    };
    console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
    const res = $h.callMessageNative(reqParams);
    console.log(`请求结果为: ~~${JSON.stringify(res)}`);
  }
}

export async function initFlow({
  bizType,
  flowNo,
  isReset,
  contextParam,
  flowInsId,
  extendParam = {},
  initJumpMode,
  isJump = false,
  cancelFlage = bizType == '014001'
}) {
  // 查询总开关(暂无,后续调整)

  // 查询业务单独开关
  let configKey = 'bc.opt.urlback.' + bizType;
  let { data } = await getConfigMap({ configKey });
  // 从配置文件中读取url地址,公告,bizType相关
  let jumpUrl = $hvue.customConfig.businessPath.filter(
    (item) => item.bizType === bizType
  )[0];
  if (!data[configKey] || data[configKey].configValue === '0') {
    // 跳转思迪业务办理data[configKey].configValue === '0',配置开关为跳转思迪
    // 先检查有无公告
    let announcementList = [];
    if (jumpUrl && jumpUrl.announcementNo) {
      let { data } = await announcementQry({
        announcementType: '1002',
        announcementSubType: jumpUrl.announcementNo
      });
      announcementList = data.announcementList;
    }
    if (announcementList.length > 0) {
      // 有公告,拦截
      this.$TAlert({
        title: announcementList[0].announcementTitle,
        tips: announcementList[0].content,
        confirmBtn: '我知道了',
        confirm: () => { }
      });
    } else {
      // 无公告,进入业务
      // 检查配置文件中对应业务是直接进入流程还是有单独路由需要先进入流程外页面
      console.log(jumpUrl, this.$router.history.current.name);
      if (
        !isJump &&
        jumpUrl &&
        jumpUrl.routName !== this.$router.history.current.name &&
        jumpUrl.routName !== ''
      ) {
        this.$router.push({ name: jumpUrl.routName, query: jumpUrl.routquery });
        return;
      }

      // let { clientId } = this.$store.state.user?.userInfo;
      // previousAcceptanceFormDataQry({
      //   bizType,
      //   clientId
      // })
      //   .then((res) => {
      //     console.log('previousAcceptanceFormDataQry', res)
      //   })
      //   .catch((err) => {
      //     _hvueToast({
      //       mes: err
      //     });
      //   });
      // return;
      toInitFlow(
        {
          bizType,
          flowNo,
          isReset,
          contextParam,
          flowInsId,
          extendParam,
          initJumpMode,
          cancelFlage
        },
        this
      );
    }
  } else {
    // 跳转国金自研
    gjzqJump(jumpUrl.gjzqBizUrl, this);
  }
}
