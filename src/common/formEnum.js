// 接口出参对接模式
export const ELEMENT_SOURCE_MODE = {
  FLOW: '0', // 流程实例中上下文
  SERVER: '1', //服务接口
  CONSTANT: '2' //其他常量值
};

// 自定义组件事件类型
export const EVENT_NAME = {
  SUBMIT: 'submit',
  NEXT_STEP: 'nextStep',
  PREV_FLOW: 'prevFlow',
  TO_INDEX: 'toIndex',
  NEXT_BTN: 'nextBtn',
  BACK_BTN: 'backBtn',
  INDEX_BTN: 'indexBtn',
  TO_BIZ_TYPE: 'toBizType',
  CLOSE_REJECT: 'closeReject',
  DISPLAY_HEADER: 'displayHeader',
  $JUMP_PAGE: '$jumpPage'
};
