/**
 * 协议相关工具
 */

import { queryAgreementExtV3, queryAgreementPdf } from '@/service/service';
/**
 * @desc 获取协议详情
 * @param {Object} agreementExt - 协议查询扩展字段
 * @param {String} agreementBizType - 协议分类编号
 * @param {String} agreementNodeNo - 协议节点编号
 * @param {String} agreementNo - 协议编号，用于查询协议详情
 * @param {Object} agreementParams - 填充字段集合
 * @param {String} agrDockType - 协议文档类型 04顶点协议
 * @param {String} agreementPath - 协议文件路径
 * @param {String} agreementExt.paddingFlag - 填充标识 0没有填充 1有填充
 * @returns {Promise<Object>} 返回一个包含协议PDF路径和/或HTML内容的对象
 * @property {String|Blob} agreementPDF - 协议pdf文件流或文件地址
 * @property {String} agreementHtml - 协议HTML字符串
 * @throws {Error} 异常信息
 */
export async function getProtocolDetail({
  agreementExt = {},
  agreementBizType,
  agreementNodeNo,
  agreementNo,
  agreementParams = {},
  agrDockType,
  agreementPath = ''
}) {
  let agreementPDF = '',
    agreementHtml = '';

  try {
    let reqParams = {
      agreementBizType,
      agreementNodeNo,
      agreementNo
    };
    if (agreementExt.paddingFlag === '1') {
      const { data } = await queryAgreementPdf({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        paddingFlag: agreementExt.paddingFlag,
        ...reqParams
      });
      agreementPDF = URL.createObjectURL(data);
    } else if (agreementPath !== '') {
      agreementPDF = $hvue.customConfig.pdfFileUrl + agreementPath;
      if (agrDockType === '04') {
        agreementPDF = agreementPath;
      }
    } else {
      if (Object.values(agreementExt).length !== 0) {
        reqParams.agreementExt = encodeURIComponent(
          JSON.stringify(agreementExt)
        );
      }
      if (Object.values(agreementParams).length !== 0) {
        reqParams.agreementParams = encodeURIComponent(
          JSON.stringify(agreementParams)
        )
      }
      const { data } = await queryAgreementExtV3({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        ...reqParams
      });
      const agreementList = data.agreementList;
      if (agreementList[0].agreementPath) {
        agreementPDF =
          $hvue.customConfig.pdfFileUrl + agreementList[0].agreementPath;
        // agrDoctype 04 顶点协议
        if (agreementList[0].agrDockType === '04') {
          agreementPDF = agreementList[0].agreementPath;
        }
      } else {
        agreementHtml = agreementList[0].agreementContent;
      }
    }
  } catch (e) {
    throw e;
  }
  return {
    agreementPDF,
    agreementHtml
  };
}
