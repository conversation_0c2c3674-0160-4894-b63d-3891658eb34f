import { cancelAllRequest } from '@thinkive/axios';
// import { Loading } from 'thinkive-hui';
import { Toast } from 'vant';
/**
 * @name 请求包装异步
 */
export async function awaitRequest(promise) {
  try {
    const data = await promise;
    // code不为0，返回错误信息
    if (data.code != 0) return [data.msg, data];
    return [null, data];
  } catch (e) {
    return [e, { data: {} }];
  }
}

// 将service提至外层防止获取问题
const service = {};
/**
 * @name 用于监听当前service请求数量用于控制Loading层的内容展示
 */
export const useService = () => {
  return {
    countServiceAdd: (config) => {
      // 记录当前请求
      if (config.headers.isLoading && config.url && config.requestKey) {
        service[`${config.url}-${config.requestKey}`] = config;
      }
      // 判断是否展示出对应的请求
      // Object.keys(service).length > 0 && Loading.open();
      Object.keys(service).length > 0 && Toast.loading({
        forbidClick: true,
        duration: 0, // 持续展示 toast
        className: 'van-toast-bg'
      })
    },
    countServiceSub: (config) => {
      // 删除对应请求记录
      delete service[`${config.url}-${config.requestKey}`];
      // 延迟关闭loading 放置串联调用接口
      setTimeout(() => {
        // 无请求状态下清空loading
        // Object.keys(service).length <= 0 && Loading.close();
        Object.keys(service).length <= 0 && Toast.clear();
      }, 500);
    },
    countServiceEmpty: async () => {
      // 消杀所有请求
      cancelAllRequest();
      // 清空记录所有请求
      Object.keys(service).map((item) => delete service[item]);
    }
  };
};
