<template>
  <fragment>
    <div class="layer_tit">
      <h3>{{ title }}</h3>
      <a class="close" @click="cancel"></a>
    </div>
    <div class="layer_cont">
      <!-- 支持7X24小时银证转账银行 -->
      <div class="bank_group_title" v-show="supportBanks.length > 0">
        支持7X24小时银证转账银行
      </div>
      <ul class="select_list">
        <li
          v-for="({ logo, label, value }, i) in supportBanks"
          :key="'support-' + i"
          @click="onConfirm(value)"
        >
          <span>
            <img v-show="logo" :src="`data:image/jpeg;base64,${logo}`" />
            {{ label }}
          </span>
          <i v-if="isSelected(value)" class="selected_icon"></i>
        </li>
      </ul>

      <!-- 其他银行 -->
      <div class="bank_group_title" v-show="supportBanks.length > 0">
        其他银行
      </div>
      <ul class="select_list">
        <li
          v-for="({ logo, label, value }, i) in otherBanks"
          :key="'other-' + i"
          @click="onConfirm(value)"
        >
          <span>
            <img v-show="logo" :src="`data:image/jpeg;base64,${logo}`" />
            {{ label }}
          </span>
          <i v-if="isSelected(value)" class="selected_icon"></i>
        </li>
      </ul>
    </div>
  </fragment>
</template>

<script>
export default {
  name: 'BankPicker',
  model: {
    prop: 'selectData',
    event: 'change'
  },
  props: {
    type: {
      type: String,
      default: 'bank'
    },
    title: {
      type: String,
      default: '所属银行'
    },
    selectData: {
      type: Object,
      default: () => {}
    },
    defaultValue: {
      type: [String, Number],
      default: ''
    },
    columns: {
      type: Array,
      default: () => []
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileUrl: $hvue.customConfig.fileUrl
    };
  },
  computed: {
    // 所有银行列表
    selectList() {
      let arr = [];
      if (this.type === 'bank') {
        arr = this.columns.map((item) => {
          item.label = item.bankName;
          item.value = item.bankNo;
          item.logo = item.bankLogo;
          item.support24h = item.support24h || false; // 是否支持7X24小时
          return item;
        });
      }
      return arr;
    },
    // 支持7X24小时银证转账的银行
    supportBanks() {
      return this.selectList.filter(
        ({ support7x24Flag = '', counterKind = '' }) => {
          // counterKind 0普通柜台 1信用柜台
          // support7x24Flag 0不支持 1普通柜台支持 2信用柜台支持
          if (!support7x24Flag) return false;
          const support7x24FlagList = support7x24Flag.split(',');
          const counterKindList = counterKind.split(',');

          return counterKindList.some((kind) => {
            const expectedFlag = String(parseInt(kind, 10) + 1);
            return support7x24FlagList.includes(expectedFlag);
          });
        }
      );
    },
    // 其他银行
    otherBanks() {
      return this.selectList.filter((item) => {
        return !this.supportBanks.some((it) => item.bankNo === it.bankNo)
      });
    }
  },
  methods: {
    cancel() {
      this.$emit('cancel');
    },
    onConfirm(value) {
      if (this.readonly) return;
      const selectedItem = this.selectList.find((item) => item.value === value);
      if (selectedItem) {
        this.$emit('change', selectedItem);
        this.$emit('cancel');
      }
    },
    // 判断是否为当前选中的银行
    isSelected(value) {
      return this.selectData && this.selectData.value === value;
    }
  }
};
</script>

<style scoped>
.bank_group_title {
  padding: 10px 15px;
  font-size: 14px;
  color: #999;
  background-color: #f5f5f5;
}

.select_list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
}

.select_list li span {
  display: flex;
  align-items: center;
}

.select_list li span img {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}

.selected_icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e54545"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/></svg>')
    no-repeat;
  background-size: contain;
}
</style>
