<template>
  <transition name="fade">
    <div v-show="toShow">
      <div class="dialog_overlay"></div>
      <div class="dialog_box">
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>{{ title }}</h3>
          <div v-if="tipsHtml !== ''" ref="tipsContent"></div>
          <div v-else>
            <p>{{ tips }}</p>
            <p style="color: #999999">{{ smallTips }}</p>
          </div>
        </div>
        <div class="dialog_btn">
          <a
            v-if="hasCancel"
            :class="{ cancel: !cancelRight ? true : false }"
            @click.stop="cancel"
            >{{ cancelBtn }}</a
          >
          <a
            v-if="hasConfirm"
            :class="{ cancel: cancelRight ? true : false }"
            @click.stop="confirm"
            >{{ confirmBtn }}</a
          >
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  data() {
    return {
      smallTips: '',
      toShow: false,
      title: '请确认',
      tips: '',
      tipsHtml: '',
      smallTips: '',
      hasConfirm: true,
      hasCancel: false,
      cancelBtn: '取消',
      confirmBtn: '我知道了',
      cancelRight: false,
      btnCallback: () => {},
      cancelCallback: () => {}
    };
  },
  methods: {
    show(data) {
      console.log(data.hasConfirm);
      this.toShow = true;
      this.title = data.title;
      this.tips = data.tips;
      this.tipsHtml = data.tipsHtml || '';
      this.smallTips = data.smallTips;
      this.hasCancel = data.hasCancel;
      this.hasConfirm =
        data.hasConfirm === '' || data.hasConfirm === undefined
          ? true
          : data.hasConfirm;
      this.cancelBtn = data.cancelBtn ? data.cancelBtn : '取消';
      this.confirmBtn = data.confirmBtn ? data.confirmBtn : '我知道了';
      this.btnCallback = data.confirm;
      this.cancelCallback = data.cancel;
      this.cancelRight = data.cancelRight ? data.cancelRight : false;

      if (this.tipsHtml !== '') {
        this.$nextTick(() => {
          this.$refs.tipsContent.innerHTML = this.tipsHtml;
        });
      }
    },

    cancel() {
      this.toShow = false;
      if (this.cancelCallback?.constructor.name === 'Function') {
        this.cancelCallback();
      }
    },

    confirm() {
      this.toShow = false;
      if (this.btnCallback?.constructor.name === 'Function') {
        this.btnCallback();
      }
    }
  }
};
</script>
<style scoped>
div.dialog_cont >>> table {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
}
div.dialog_cont >>> tbody td,
tbody th {
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
  padding: 3px 5px;
  min-height: 30px;
}
</style>
