import TAlert from '@/components/TAlert/TAlert';
import Vue from 'vue';

TAlert.newInstance = (properties) => {
  const props = properties || {};
  const Instance = new Vue({
    data: props,
    render(h) {
      return h(TAlert, { props });
    }
  });
  const component = Instance.$mount();
  document.body.appendChild(component.$el);
  const alert = Instance.$children[0];
  return {
    show(data) {
      alert.show(data);
    }
  };
};

export default TAlert;
