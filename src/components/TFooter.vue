<template>
  <footer class="footer">
    <slot>
      <div class="ce_btn">
        <a class="p_button" @click="toNextBtn">{{ buttonTxt }}</a>
      </div>
    </slot>
  </footer>
</template>

<script>
export default {
  name: 'TFooter',
  props: {
    buttonTxt: {
      type: String,
      default: '下一步'
    }
  },
  data() {
    return {};
  },
  methods: {
    toNextBtn() {
      this.$emit('triggerEvent');
    }
  }
};
</script>

<style scoped></style>
