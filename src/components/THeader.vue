<template>
  <header class="header">
    <div class="top_height"></div>
    <div class="header_inner">
      <a v-if="showBack" class="icon_back" @click="back"></a>
      <h1 v-if="showTitle" class="title">{{ title || $route.meta.title }}</h1>
      <slot></slot>
      <a v-if="showClose" class="icon_close" @click="logout"></a>
    </div>
    <div v-if="showProgressBar" class="step_box">
      <div class="item">
        <b :style="{ width: percentCount + '%' }">
          <span>{{ percentCount + '%' }}</span>
        </b>
      </div>
    </div>
  </header>
</template>

<script>
export default {
  name: 'THeader',
  props: {
    title: {
      type: String
    },
    showTitle: {
      type: Boolean,
      default: true
    },
    showBack: {
      type: Boolean,
      default: true
    },
    showProgressBar: {
      type: Boolean,
      default: false
    },
    barTitle: {
      type: String
    },
    percent: {
      type: Number,
      default: 0
    },
    isNode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showClose: false,
      percentCount: 0
    };
  },

  created() {},
  methods: {
    back(e) {
      this.$emit('back', e);
    }
  }
};
</script>
