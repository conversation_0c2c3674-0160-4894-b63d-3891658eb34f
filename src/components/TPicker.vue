<template>
  <fragment>
    <div class="layer_tit">
      <h3>{{ title }}</h3>
      <a class="close" @click="cancel"></a>
    </div>
    <div class="layer_cont">
      <ul class="select_list">
        <li
          v-for="({ logo, label }, i) in selectList"
          :key="i"
          @click="onConfirm(i)"
        >
          <span>
            <img v-show="logo" :src="`data:image/jpeg;base64,${logo}`" />{{ label }}
          </span>
        </li>
      </ul>
    </div>
  </fragment>
</template>

<script>
export default {
  name: 'TPicker',
  model: {
    prop: 'selectData',
    event: 'change'
  },
  props: {
    type: {
      type: String,
      default: 'bank'
    },
    title: {
      type: String,
      default: '请选择'
    },
    selectData: {
      type: Object,
      default: () => {}
    },
    defaultValue: {
      type: [String, Number],
      default: ''
    },
    columns: {
      type: Array,
      default: () => []
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileUrl: $hvue.customConfig.fileUrl
    };
  },
  computed: {
    selectList() {
      let arr = [];
      if (this.type === 'bank') {
        arr = this.columns.map((item) => {
          item.label = item.bankName;
          item.value = item.bankNo;
          item.logo = item.bankLogo;
          return item;
        });
      }
      return arr;
    }
  },
  created() {},
  methods: {
    cancel() {
      this.$emit('cancel');
    },
    onConfirm(index) {
      if (this.readonly) return;
      this.$emit('change', this.selectList[index]);
      this.$emit('cancel');
    }
  }
};
</script>
