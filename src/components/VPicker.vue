<template>
  <fragment>
    <div class="layer_tit">
      <h3>{{ title }}</h3>
      <a
        class="close"
        @click="onCancel"
      />
    </div>
    <div class="layer_cont">
      <ul class="select_list">
        <li
          v-for="(item, index) in selectList"
          :key="index"
          :class="{ active: item.checked }"
          @click="onConfirm(item, index)"
        >
          <span>{{ item.label }}</span>
        </li>
      </ul>
    </div>
  </fragment>
</template>

<script>
export default {
  name: 'VPicker',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    showPicker: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '请选择'
    },
    columns: {
      type: Array,
      default: () => []
    },
    value: {
       type: String,
      default: ''
    }
  },
  data() {
    return {
      selectList: []
    };
  },
  watch: {
    value: {
      handler(newValue) {
        this.setValue(newValue)
      }
    }
  },
  created() {
    this.selectList = this.cloneDeep(this.columns);
    this.setValue(this.value);
  },
  methods: {
    onConfirm(item) {
      this.$emit('change', item.value);
      this.$emit('onConfirm', item);
      this.onCancel();
    },
    onCancel() {
      this.$emit('onCancel');
    },
    onChange(item) {
      this.$emit('change', item.value);
      this.$emit('onChange', item);
    },
    setValue(val){
      this.selectList.forEach((a) => {
        this.$set(a, 'checked',a.value === val);
      });
    }
  }
};
</script>

<style scoped></style>
