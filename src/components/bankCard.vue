<template>
  <div class="bank_card">
    <div class="top" :class="bankItem.mainFlag == 1 ? 'main' : 'auxiliary'">
      <div class="left">
        <div class="icon_container">
          <img
            class="bank_icon"
            :src="`data:image/jpeg;base64,${bankItem.bankLogo}`"
          />
        </div>
        <div class="bank_info">
          <div class="bank_name">
            <span>{{ bankItem.bankName }}</span>
            <span class="verticalLine"></span>
            <span>CNY</span>
          </div>
          <div class="bank_account">
            {{
              bankItem.bkAccountRegflag == 1
                ? ''
                : formatBankCardNoFn(bankItem.bankAccount)
            }}
          </div>
        </div>
      </div>
      <div class="right">
        {{ bankItem.bkAccountRegflag == 1 ? '预指定未激活' : '' }}
      </div>
    </div>
    <div class="bottom">
      <div class="account_info">
        <template v-if="!isCredit">
          <span>{{ bankItem.mainFlag == 1 ? '主：' : '辅：' }}</span>
          <span>{{ bankItem.fundAccount }}</span>
        </template>
      </div>
      <div class="button" v-if="bankItem.mainFlag == '1'">
        <span
          v-if="
            bankItem.supportBusiness && bankItem.supportBusiness.includes('2')
          "
          class="button_item"
          @click="bindBank(bankItem)"
          >绑卡</span
        >
        <span
          v-if="
            bankItem.supportBusiness && bankItem.supportBusiness.includes('1')
          "
          class="button_item"
          @click="changeBank(bankItem)"
          >变更</span
        >
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    bankItem: {
      type: Object,
      default: () => {}
    },
    heightLightCode: {
      type: String,
      default: ''
    },
    bindBank: {
      type: Function,
      default: null
    },
    changeBank: {
      type: Function,
      default: null
    },
    isCredit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    formatBankCardNoFn(value) {
      if (value === '' || value === null || value === undefined) {
        return '';
      } else {
        let str =
          '**** **** ****' + value.substr(value.length - 4, value.length);
        return str;
      }
    }
  }
};
</script>

<style scoped lang="less">
.bank_card {
  margin-bottom: 16px;
  .main {
    background: linear-gradient(270deg, #f3575d 0%, #ca373c 100%);
    border-radius: 5px 5px 0 0;
  }
  .auxiliary {
    background: var(
      --unnamed,
      linear-gradient(270deg, #626e9e 0%, #49517a 100%)
    );
    border-radius: 5px 5px 0 0;
  }
  .top {
    background-color: skyblue;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 16px;
    color: #fff;
    font-family: PingFang SC;
    font-size: 12px;
    .left {
      display: flex;
      .bank_name {
        font-size: 16px;
        font-weight: 500;
        .verticalLine {
          height: 20px;
          margin: 3px 12px 0;
          width: 1px;
          border-left: 0.5px solid #fff;
        }
      }
      .icon_container {
        background: #fff;
        margin-right: 12px;
        width: 42px;
        height: 42px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .bank_icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
      }
    }
  }
  .bottom {
    background: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 16px;
    border-radius: 0 0 5px 5px;
    .button {
      .button_item {
        margin-left: 12px;
        padding: 4px 12px;
        border-radius: 18px;
        border: 1px solid var(--primary-b-500, #ff2840);
        color: var(--primary-b-500, #ff2840);
        text-align: center;
        font-family: PingFang SC;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
      }
    }
  }
}
</style>
