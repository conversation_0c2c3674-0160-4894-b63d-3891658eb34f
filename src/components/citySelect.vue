<template>
  <div v-if="isShow" class="citySelct">
    <!-- 遮罩层 -->
    <div class="dialog_overlay" @click="_onHide"></div>
    <!-- 弹出层 -->
    <div class="layer_box">
      <div class="layer_tit">
        <h3 class="text_left">{{ title }}</h3>
        <a class="close" @click="_onHide"></a>
      </div>
      <div class="sele_lycont fixed">
        <div class="sele_lyinfo">
          <span
            v-show="showItem >= 1"
            :class="selProvinceName === '请选择' ? 'active' : 'off'"
            @click="showItem = 1"
            v-text="selProvinceName"
          ></span>
          <span
            v-show="showItem >= 2"
            :class="selCityName === '请选择' ? 'active' : 'off'"
            @click="showItem = 2"
            v-text="selCityName"
          ></span>
          <span
            v-show="showItem >= 3"
            :class="selAreaName === '请选择' ? 'active' : 'off'"
            @click="showItem = 3"
            v-text="selAreaName"
          ></span>
        </div>
        <div class="layer_cont">
          <ul v-show="showItem === 1" class="select_list">
            <li
              v-for="(item, index) in provinces"
              :key="index"
              :class="{ active: provincesIndex === index }"
              @click="provinceSelect(item, index)"
            >
              <span>{{ item.provinceName }}</span>
            </li>
          </ul>
          <ul v-show="showItem === 2" class="select_list">
            <li
              v-for="(item, index) in curCity"
              :key="index"
              :class="{ active: cityIndex === index }"
              @click="citySelect(item, index)"
            >
              <span>{{ item.cityName }}</span>
            </li>
          </ul>
          <ul v-show="showItem === 3" class="select_list">
            <li
              v-for="(item, index) in curXzqy"
              :key="index"
              :class="{ active: xzqyIndex === index }"
              @click="xzqySelect(item, index)"
            >
              <span>{{ item.xzqyName }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAdressTree } from '@/service/commonService';
export default {
  name: 'CitySelect',
  model: {
    prop: 'isShow',
    event: 'change'
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '请选择地区'
    },
    isShowXzqy: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      provinces: [],
      provincesIndex: -1,
      cityIndex: -1,
      xzqyIndex: -1,
      curCity: [],
      curXzqy: [],
      selProvinceName: '请选择',
      selCityName: '请选择',
      selAreaName: '请选择',
      showItem: 1 // 展示第几列
    };
  },
  computed: {
    showCity() {
      return this.provincesIndex > -1;
    },
    showXzqy() {
      return this.cityIndex > -1;
    }
  },
  watch: {
    isShow(data) {
      if (data) {
        this.updataData();
      }
    }
  },
  methods: {
    updataData() {
      getAdressTree()
        .then((res) => {
          if (res.code === 0) {
            this.provinces = res.data;
          } else {
            return Promise.reject(res.error_info);
          }
        })
        .catch((err) => {
          _hvueToast({ mes: err });
        });
    },

    _onHide() {
      this.curCity = [];
      this.curXzqy = [];
      this.provincesIndex = -1;
      this.cityIndex = -1;
      this.xzqyIndex = -1;
      this.$emit('onHide');
      this.$emit('change', false);
    },
    provinceSelect(item, index) {
      this.provincesIndex = index;
      this.selCityName = '请选择';
      this.selProvinceName = this.provinces[index].provinceName;
      this.showItem = 2;
      this.curCity = this.provinces[index].sysCityVOs;
    },
    citySelect(item, index) {
      this.cityIndex = index;
      this.selAreaName = '请选择';
      this.selCityName = this.curCity[index].cityName;
      this.curXzqy = this.curCity[index].sysXzqyVOs;
      if (!this.isShowXzqy) {
        this.$emit('selCallBack', {
          province: this.provinces[this.provincesIndex],
          city: this.curCity[this.cityIndex]
        });
        this._onHide();
      }
      this.showItem = 3;
    },
    xzqySelect(item, index) {
      this.xzqyIndex = index;
      this.selAreaName = this.curXzqy[index].xzqyName;
      this.$emit('selCallBack', {
        province: this.provinces[this.provincesIndex],
        city: this.curCity[this.cityIndex],
        xzqy: this.curXzqy[this.xzqyIndex]
      });
      this._onHide();
    }
  }
};
</script>

<style></style>
