<template>
  <section
    v-show="showPage"
    class="main fixed white_bg"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click="back"></a>
        <h1 class="title">联系住址</h1>
      </div>
    </header>
    <article class="content">
      <div class="upload_infobox">
        <div class="input_form">
          <div class="input_text text">
            <span class="tit active">选择地区</span>
            <!-- <div class="dropdown">河北省 保定市 竞秀区</div> -->
            <div
              class="dropdown"
              placeholder="选择地区（省/市/县）"
              @click="openAreaSelect()"
            >
              <span v-if="areaSelVal">{{ areaSelVal }}</span>
            </div>
          </div>
          <div class="input_text text">
            <span class="tit active">详细地址</span>
            <!-- <div
              class="tarea1 needsclick"
              contenteditable="true"
              placeholder="请输入您的详细地址（xx路小区x单元xx室）"
            >
              阳光南街39号阳光新城2单元607室
            </div> -->
            <multLineInput
              v-model="addressDetail"
              class="tarea1 needsclick"
              :maxlength="120"
              placeholder="请输入您的详细地址"
              autocomplete="off"
            />
          </div>
          <div class="input_text text">
            <span class="tit">邮政编码</span>
            <input
              v-model="zipcode"
              class="t1"
              type="text"
              maxlength="6"
              placeholder="请输入邮政编码"
            />
          </div>
          <div class="imp_c_tips">
            <p>
              地址填写样例<br />城市地址:XX小区XX栋XX门牌号(如是整栋需备注整栋/自建房/别墅)<br />乡镇地址:XX镇(乡)XX路XX号<br />农村地址:X镇(乡)XX村XX组(号、排)<br />公司地址:XX大厦XX公司XX部门或者XX大厦XX公司<br />商铺地址:XX路XX号XX店铺<br />学校地址:XX学校XX学院XX班级或XX职XX学校XX栋XX门牌号
            </p>
          </div>
        </div>
      </div>
      <div class="ce_btn mt20">
        <a class="p_button" @click="submit">保存</a>
      </div>
    </article>
    <openAreaSelect
      v-if="showCitySelect"
      v-model="areaSelVal"
      @finish="areaCallback"
    ></openAreaSelect>
  </section>
</template>

<script>
import openAreaSelect from '@/components/openAreaSelect';
import multLineInput from '@/components/multLineInput';
import { getZipCode } from '@/service/service';
import { rules } from '@/common/rule';

export default {
  name: 'ChangeAddress',
  components: {
    openAreaSelect,
    multLineInput
  },
  data() {
    return {
      showCitySelect: false,
      showPage: false,
      message: '',
      addressDetail: '',
      address: '',
      email: '',
      areaSelVal: '',
      zipcode: '',
      timer: null
    };
  },
  watch: {
    areaSelVal() {
      this.address = this.areaSelVal + this.addressDetail;
      this.getZipcode();
    },
    addressDetail() {
      this.address = this.areaSelVal + this.addressDetail;
      this.getZipcode();
    }
  },
  methods: {
    show() {
      this.showPage = true;
    },

    back() {
      this.showPage = false;
    },

    getZipcode() {
      if (this.timer !== null) clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        getZipCode(
          {
            address: this.address
          },
          { loading: false }
        ).then(({ data = {} }) => {
          if (data.post) {
            this.zipcode = data.post || '';
          }
        });
      }, 1000);
    },

    openAreaSelect() {
      this.showCitySelect = true;
    },

    areaCallback(data) {
      this.showCitySelect = false;
      if (!data) return;
      const [{ text: prov }, { text: city } = '', { text: area } = ''] = data;
      this.areaSelVal = Array.from(new Set([prov, city, area])).join('');
    },

    submit() {
      if (!this.areaSelVal) {
        _hvueToast({
          mes: '请选择省市区'
        });
        return false;
      }
      if (!this.addressDetail) {
        _hvueToast({
          mes: '请输入您的详细地址'
        });
        return false;
      }
      if (this.addressDetail.length < 5 || this.addressDetail.length > 120) {
        _hvueToast({
          mes: '详细地址格式不正确'
        });
        return false;
      }
      if (this.areaSelVal !== '海外') {
        const addressRules = rules.client_address;
        if (!addressRules.validate(this.address)) {
          _hvueToast({
            mes: '详细地址格式不正确'
          });
          return false;
        }
      }
      if (!/[0-9]{6}$/.test(this.zipcode)) {
        _hvueToast({
          mes: '邮政编码格式不正确'
        });
        return false;
      }

      this.$emit('result', 'address', {
        value: {
          address: this.address,
          zipcode: this.zipcode
        }
      });
      this.showPage = false;
    }
  }
};
</script>

<style></style>
