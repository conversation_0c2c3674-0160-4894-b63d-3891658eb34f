<template>
  <section
    v-show="showPage"
    class="main fixed white_bg"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click="back"></a>
        <h1 class="title">手机号码修改</h1>
      </div>
    </header>
    <article class="content">
      <div class="phone_num_page white_bg">
        <div class="phone_info">
          <div class="icon"></div>
          <p>请填写您的新手机号码，我们将为您发送验证码</p>
          <div class="num">
            原手机号码：<span>{{ oldMobile | formatMobileNo }}</span>
          </div>
        </div>
        <div class="input_form">
          <div class="input_text text">
            <span class="tit">新手机号</span>
            <input
              v-model="mobileTel"
              class="t1"
              type="tel"
              maxlength="11"
              placeholder="请输入新手机号码"
            />
          </div>
          <div class="input_text text code">
            <span class="tit">图形码</span>
            <input
              v-model="captcha"
              class="t1"
              type="text"
              maxlength="4"
              placeholder="请输入图形码"
            />
            <a class="code_img" @click="imgClick"><img :src="imgSrc" /></a>
          </div>
          <div class="input_text text code">
            <span class="tit">验证码</span>
            <input
              v-model="smsCode"
              class="t1"
              type="text"
              maxlength="6"
              placeholder="请输入短信验证码"
            />
            <sms-code-btn
              v-model="uuid"
              :need-img-code="needImgCode"
              :mobile-no="mobileTel"
              :captcha="captcha"
              :captcha-token="captchaToken"
              @send-result="SMSCodeCallback"
            />
          </div>
        </div>
        <div class="ce_btn">
          <a class="p_button" @click="submit">保存</a>
        </div>
      </div>
    </article>
  </section>
</template>

<script>
import { getImgCode, smsCheckCodeVer } from '@/service/service';
import SmsCodeBtn from '@/components/register/verificationCode/SmsCodeBtn';

export default {
  name: 'ChangeMobile',
  components: { SmsCodeBtn },
  props: {
    oldMobile: {
      type: String,
      default: ''
    },
    defaultValue: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      showPage: false,
      mobileTel: '',
      captcha: '',
      captchaToken: '',
      uuid: '',
      imgSrc: '',
      smsCode: '',
      needImgCode: true
    };
  },
  mounted() {
    this.captcha = '';
    this.captchaToken = '';
    this.uuid = '';
    this.imgClick();
  },
  methods: {
    submit() {
      if (!this.mobileTel) {
        _hvueToast({
          mes: '请输入手机号'
        });
        return false;
      }
      if (!this.uuid) {
        _hvueToast({
          mes: '请先发送短信验证码'
        });
        return false;
      }
      if (!this.captcha) {
        _hvueToast({
          mes: '请输入图形验证码'
        });
        return false;
      }
      if (!this.smsCode) {
        _hvueToast({
          mes: '请输入短信验证码'
        });
        return false;
      }
      if (!/1[3-9][\d]{9}/.test(this.mobileTel)) {
        _hvueToast({
          mes: '手机号格式不正确'
        });
        return false;
      }
      smsCheckCodeVer({
        mobile: this.mobileTel,
        captcha: this.smsCode,
        expireNow: '2',
        captchaCode: this.smsCode,
        captchaToken: this.captchaToken,
        serialNumber: this.uuid
      })
        .then(({ data }) => {
          if (data.verificationvFlag !== '1') {
            _hvueToast({ mes: '输入的验证码有误，请重新输入' });
            this.smsCode = '';
            return;
          }
          this.$emit('result', 'mobileTel', { value: this.mobileTel });
          this.mobileTel = '';
          this.captcha = '';
          this.captchaToken = '';
          this.smsCode = '';
          this.back();
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },

    show() {
      this.showPage = true;
    },

    back() {
      this.showPage = false;
    },

    imgClick() {
      getImgCode({}, { loading: false }).then((data) => {
        if (data.code === 0) {
          let results = data.data;
          this.captcha = '';
          this.imgSrc = results.image;
          this.captchaToken = results.token;
        }
      });
    },

    SMSCodeCallback(flag) {
      if (!flag) {
        // 重新发送图形验证码
        this.imgClick();
      }
    }
  }
};
</script>

<style></style>
