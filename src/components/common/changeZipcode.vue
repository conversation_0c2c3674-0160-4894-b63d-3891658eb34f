<template>
  <section
    v-show="showPage"
    class="main fixed white_bg"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click="back"></a>
        <h1 class="title">邮政编码</h1>
      </div>
    </header>
    <article class="content">
      <div class="upload_infobox">
        <div class="input_form">
          <div class="input_text text">
            <span class="tit active">邮政编码</span>
            <input
              v-model="zipcode"
              class="t1"
              type="text"
              maxlength="6"
              placeholder="请输入邮政编码"
            />
          </div>
        </div>
      </div>
      <div class="ce_btn mt20">
        <a class="p_button" @click="submit">保存</a>
      </div>
    </article>
  </section>
</template>

<script>
export default {
  name: 'ChangeZipcode',
  props: {
    defaultValue: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      showPage: false,
      message: '',
      zipcode: ''
    };
  },
  watch: {
    defaultValue(val) {
      if (val) {
        this.zipcode = this.defaultValue.zipcode;
      }
    },
    showPage(val) {
      if (val) {
        this.zipcode = this.defaultValue.zipcode;
      }
    }
  },
  methods: {
    show() {
      this.showPage = true;
    },

    back() {
      this.showPage = false;
    },

    submit() {
      let reg = /[0-9]{6}$/;
      // 校验邮编格式是否正确
      if (!reg.test(this.zipcode)) {
        _hvueToast({
          mes: '邮政编码格式不正确'
        });
        return false;
      }
      this.$emit('result', 'zipcode', { value: this.zipcode });
      this.showPage = false;
    }
  }
};
</script>

<style></style>
