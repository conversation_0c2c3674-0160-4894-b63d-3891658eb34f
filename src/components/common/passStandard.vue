<template>
    <section
      v-if="showPage"
      class="main fixed white_bg"
      data-page="home"
      style="position: fixed; z-index: 1400"
    >
      <header class="header">
        <div class="header_inner">
          <a class="icon_back" @click="back"></a>
          <h1 class="title">认定标准说明</h1>
        </div>
      </header>
      <article class="content">
        <div class="card">
          <h3 class="tit">一、资产认定标准：</h3>
          <p>
            1.可用于计算投资者资产的证券账户，应为中国结算开立的证券账户，以及投资者在会员开立的账户。中国结算开立的账户包括A股账户、B股账户、封闭式基金账户、开放式基金账户、衍生品合约账户及中国结算根据业务需要设立的其他证券账户。可用于计算投资者资产的资金账户，包括客户交易结算资金账户、股票期权保证金账户等。
          </p>
          <p>
            2.中国结算开立的证券账户内的下列资产可计入投资者资产：股票，包括A股、B股、优先股和全国中小企业股份转让系统挂牌股票；存托凭证；公募基金份额；债券；资产支持证券；资产管理计划份额；股票期权合约，其中权利仓合约按照结算价计增资产，义务仓合约按照结算价计减资产；回购类资产，包括债券质押式回购逆回购、质押式报价回购；通过港股通持有的上市证券；本所认定的其他证券资产。
          </p>
          <p>
            3.投资者在会员开立的账户的下列资产可计入投资者资产：公募基金份额、私募基金份额、银行理财产品、贵金属资产、场外衍生品资产等。
            四是资金账户内的下列资产可计入投资者资产：客户交易结算资金账户内的交易结算资金；股票期权保证金账户内的交易结算资金，包括义务仓对应的保证金；本所认定的其他资金资产。
            五是计算各类融资类业务相关资产时，应按照净资产计算，不包括融入的证券和资金。
          </p>
        </div>
        <div class="card">
          <h3 class="tit">二、交易经验认定标准：</h3>
          <p>
            投资者参与A股、B股、存托凭证和全国中小企业股份转让系统挂牌股票交易的，均可计入其参与证券交易的时间。相关交易经历自投资者本人一码通账户下任一证券账户在本所、深圳证券交易所及全国中小企业股份转让系统发生首次交易起算。首次交易日期可通过会员向中国结算查询。
          </p>
        </div>
      </article>
      <footer class="footer white_bg">
        <div class="ce_btn black">
          <a class="p_button" @click="back">我已知晓</a>
        </div>
      </footer>
    </section>
  </template>
  
  <script>
  export default {
    data() {
      return {};
    },
    model: {
      prop: 'showPage',
      event: 'change'
    },
    props: {
      showPage: {
        type: Boolean,
        default: false
      }
    },
    methods: {
      back() {
        this.$emit('change', false);
      }
    }
  };
  </script>
  
  <style lang="less" scoped>
  .card {
    background: #ffffff;
    margin-bottom: 0.1rem;
    padding: 0.18rem 0.18rem;
    .tit {
      font-size: 16px;
      margin: 8px 0 5px 0;
    }
    p {
      color: #777777;
    }
  }
  </style>
  
