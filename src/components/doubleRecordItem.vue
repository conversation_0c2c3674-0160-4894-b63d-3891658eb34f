<template>
  <div class="double_record_item">
    <div class="top" @click="updateLocal">
      <div class="name" v-html="name"></div>
      <div class="code" v-html="code"></div>
    </div>
    <div class="bottom">
      <div class="left">
        {{getLeftContent(doubleRecordType === 'doubleRecordListModel'? doubleRecordItem.dualVideoState: doubleRecordItem.overdueState)}}
      </div>
      <div :class="goFlow ? 'button' : 'text'" @click="goInitFlow">
        {{getRightContent(doubleRecordType === 'doubleRecordListModel'? doubleRecordItem.dualVideoState: doubleRecordItem.overdueState)}}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    doubleRecordItem: {
      type: Object,
      default: () => {}
    },
    doubleRecordType: {
      type: String,
      default: ''
    },
    heightLightName: {
      type: String,
      default: ''
    },
    heightLightCode: {
      type: String,
      default: ''
    },
    onSearch: {
      type:Function,
      default:null
    }
  },
  data() {
    return {
      name: '',
      code: '',
      goFlow: false
    };
  },
  watch: {
    heightLightName(newValue, oldValue) {
      this.getName();
    },
    heightLightCode(newValue, oldValue) {
      this.getCode();
    }
  },
  mounted() {
    this.getName();
    this.getCode();
  },
  methods: {
    getName() {
      if (this.heightLightName) {
        this.name = '双录产品：' + this.heightLightName;
      } else {
        this.name = '双录产品：' + this.doubleRecordItem.dualVideoProductName;
      }
    },
    getCode() {
      if (this.heightLightCode) {
        this.code = '产品代码：' + this.heightLightCode;
      } else {
        this.code = '产品代码：' + this.doubleRecordItem.productCode;
      }
    },
    getLeftContent(status) {
      if (this.doubleRecordType === 'doubleRecordListModel') {
        if (status === '3' || status === '4') {
          return '有效期至：' + this.doubleRecordItem.overdueTime;
        } else {
          return '';
        }
      } else {
        if (status === '1') {
          return '有效期至：' + this.doubleRecordItem.overdueTime;
        } else {
          return '双录已过期';
        }
      }
    },
    getRightContent(status) {
      if (this.doubleRecordType === 'doubleRecordListModel') {
        if (status === '1' || status === '4' || status === '6') {
          this.goFlow = true;
          return '马上双录';
        } else if (status === '2' || status === '5' || status === '7') {
          return '审核中';
        } else if (status === '3') {
          return '已双录';
        }
      } else {
        return '双录日期：'+  this.doubleRecordItem.dualVideoTime;
      }
    },
    updateLocal(){
      if(this.onSearch){
        this.onSearch(this.doubleRecordItem.dualVideoProductName)
      }
    },
    goInitFlow() {
      if(this.onSearch){
        this.onSearch(this.doubleRecordItem.dualVideoProductName)
      }
      let dualVideoState = this.doubleRecordItem.dualVideoState;
      if (
        this.doubleRecordType === 'doubleRecordListModel' &&
        (dualVideoState === '1' ||
          dualVideoState === '4' ||
          dualVideoState === '6')
      ) {
        let contextParam = JSON.stringify({
          doubleRecordId: this.doubleRecordItem.doubleRecordId,
          doubleRecordName: this.doubleRecordItem.dualVideoProductName,
          doubleRecordRiskLevel: this.doubleRecordItem.doubleRecordRiskLevel,
          prodList: this.doubleRecordItem.prodList
        });
        console.log('进入流程 ====== >', contextParam);
        $h.setSession('bizType', '010170');
        import('@/common/flowMixinV2.js').then((a) => {
          // a.initFlow.call(this, '010170', '0-010170','1',contextParam);
          a.initFlow.call(this, {bizType: '010170', flowNo: '0-010170', isReset: '1', contextParam, initJumpMode: '0'});
        });
      }
    }
  }
};
</script>

<style scoped lang="less">
.double_record_item {
  background: #fff;
  padding: 0.12rem 0.16rem 0.14rem;
  margin-bottom: 0.1rem;
  .top {
    padding-bottom: 0.12rem;
    border-bottom: 1px solid #e5e5e5;
    .name {
      color: var(--typography-333, #333);
      font-size: 0.16rem;
      font-weight: 500;
      line-height: 0.24rem;
      margin-bottom: 0.06rem;
      /deep/ span {
        color: #f0392f;
      }
    }
    .code {
      color: var(--typography-666, #666);
      font-size: 0.14rem;
      line-height: 0.22rem;
      /deep/ span {
        color: #f0392f;
      }
    }
  }
  .bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 0.14rem;
    font-size: 0.14rem;
    .left {
      color: var(--typography-999, #999);
      line-height: 0.22rem;
    }
    .button {
      padding: 0.04rem 0.12rem;
      border-radius: 0.18rem;
      border: 1px solid #ff2840;
      background: var(--primary-b-500, #fff);
      color: var(--typography-fff, #ff2840);
      font-weight: 500;
      line-height: 0.2rem;
    }
    .text {
      color: var(--typography-999, #999);
      line-height: 0.22rem;
    }
  }
}
</style>
