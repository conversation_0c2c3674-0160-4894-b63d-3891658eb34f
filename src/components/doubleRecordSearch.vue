<template>
  <div class="double_record_search">
    <van-search
      v-model="value"
      show-action
      placeholder="请输入您想要查找的双录产品/产品代码"
      autofocus
      @search="onSearch"
      @cancel="onCancel"
      @input="onInput"
    />
    <div class="search_content" v-show="cacheList.length > 0 && !searchKey">
      <div class="search_title">
        <div>历史搜索</div>
        <img src="@/assets/images/delete.png" class="delete_icon" @click="deleteCache"/>
      </div>
      <div class="search_item_box">
        <div class="search_item" v-for="(item, index) in cacheList" :key="index" @click="searchFn(item)" >
          {{ item }}
        </div>
      </div>
    </div>
    <div class="search_result" v-show="searchKey">
      <div v-for="(item, index) in searchList" :key="index">
        <doubleRecordItem
          :doubleRecordItem="item"
          :doubleRecordType="doubleRecordType"
          :heightLightName="warpTag(item.dualVideoProductName, searchKey)"
          :heightLightCode="warpTag(item.productCode, searchKey)"
          :onSearch="onSearch"
        />
      </div>
      <div class="no_search_result" v-show="searchList.length === 0">
        抱歉，没有找到匹配内容
      </div>
    </div>
  </div>
</template>

<script>
import doubleRecordItem from '@/components/doubleRecordItem';
export default {
  props: {
    doubleRecordType: {
      type: String,
      default: ''
    },
    changeModel: {
      type: Function,
      default: null
    },
    renderList: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      value: '',
      htmlA: `<span style='color:red'>123</span>`,
      searchKey: '',
      searchList: [],
      cacheList: []
    };
  },
  components: {
    doubleRecordItem
  },
  mounted() {
    this.cacheList =
      JSON.parse(window.localStorage.getItem(this.doubleRecordType === 'doubleRecordListModel' ? 'searchDoubleRecordListCache' : 'searchDoubleRecordHistoryCache')) || [];
  },
  methods: {
    // 关键字高亮
    warpTag(content, keyword) {
      const a = content.toLowerCase();
      const b = keyword.toLowerCase();

      const indexof = a.indexOf(b);
      const c = indexof > -1 ? content.substr(indexof, keyword.length) : '';
      const val = `<span style="color:#F0392F;">${c}</span>`;
      const regS = new RegExp(keyword, 'gi');
      return content.replace(regS, val);
    },
    // 更新本地缓存
    onSearch(val) {
      console.log('搜索的内容value =====>', val);
      const value = val.replace(/\s+/g, '');
      if (value == '') return;
      let temp = this.doubleRecordType == 'doubleRecordListModel' 
        ? JSON.parse(window.localStorage.getItem('searchDoubleRecordListCache')) || []
        : JSON.parse(window.localStorage.getItem('searchDoubleRecordHistoryCache')) || [];
      const length = temp.unshift(value);
      temp = [...new Set(temp)];
      if (length > 8) {
        temp = temp.slice(0, 8);
      }
      this.doubleRecordType == 'doubleRecordListModel' 
        ? window.localStorage.setItem('searchDoubleRecordListCache',JSON.stringify(temp)) 
        : window.localStorage.setItem('searchDoubleRecordHistoryCache',JSON.stringify(temp));
      this.cacheList = temp;
    },
    searchFn(item){
      this.onSearch(item)
      this.value = item
      this.onInput(item)
    },
    onCancel() {
      this.changeModel(this.doubleRecordType);
    },
    onInput(searchKey) {
      this.searchKey = searchKey.replace(/\s+/g, '');
      if (!searchKey || !searchKey.trim()) {
        this.searchList = [];
        return;
      }
      this.searchList = this.renderList.filter(
        (item) => item.dualVideoProductName?.toLowerCase().indexOf(searchKey.toLowerCase().replace(/\s+/g, '')) > -1 || item.productCode?.toLowerCase().indexOf(searchKey.toLowerCase().replace(/\s+/g, '')) > -1
      );
      // console.log('过滤后的数组', this.searchList);
    },
    // 清空本地缓存
    deleteCache() {
      this.doubleRecordType == 'doubleRecordListModel'
        ? window.localStorage.removeItem('searchDoubleRecordListCache')
        : window.localStorage.removeItem('searchDoubleRecordHistoryCache');
      this.cacheList = [];
    }
  }
};
</script>

<style scoped lang="less">
.double_record_search{
    /deep/ .van-search {
    width: 100%;
    padding: 0.06rem 0.16rem;
    .van-search__content {
      border-radius: 0.47rem;
    }
    .van-cell {
      min-height: 0.36rem;
    }
    .van-field__control {
      font-size: 0.14rem;
    }
    .van-search__action {
      color: var(--primary-b-500, #ff2840);
      font-size: 0.14rem;
      line-height: 0.16rem;
      padding-right: 0;
    }
  }
}
.search_content {
  .search_title {
    padding: 0.15rem 0.2rem;
    color: var(--typography-333, #333);
    font-size: 0.15rem;
    font-weight: 500;
    line-height: 0.18rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .delete_icon {
      width: 0.144rem;
      height: 0.16rem;
    }
  }
  .search_item_box {
    padding-left: 0.2rem;
    padding-right: 0.2rem;
    .search_item {
      padding: 0.08rem 0.12rem;
      display: inline-block;
      max-width: 12em;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      border-radius: 0.23rem;
      background: var(--typography-fff, #fff);
      margin-right: 0.1rem;
      margin-bottom: 0.1rem;
      color: var(--typography-333, #333);
      font-size: 0.14rem;
    }
  }
}
.search_result {
  padding-top: 0.1rem;
}
.no_search_result{
  text-align: center;
}
</style>
