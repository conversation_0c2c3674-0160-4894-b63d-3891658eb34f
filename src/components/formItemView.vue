<template>
  <div class="com_box">
    <div class="input_form">
      <template v-for="(item, index) in formItem">
        <div
          :key="index"
          class="input_text text"
          :class="{ error: errors.first(item.key) }"
        >
          <span :ref="item.key" :for="item.key + formIndex" class="tit">{{
            item.label
          }}</span>
          <multLineInput
            :id="item.key + formIndex"
            v-model="form[item.key]"
            v-validate="item.key"
            class="t1"
            type="text"
            :maxlength="item.maxLength"
            :placeholder="item.placeholder"
            :name="item.key"
          />
          <span v-if="item.unit" class="unit_span">{{ item.unit }}</span>
          <p class="error_tips">{{ errors.first(item.key) }}</p>
        </div>
      </template>
    </div>
    <div class="opea_ctbox">
      <a class="add_btn_01" @click="addBtn">添加</a>
      <a v-if="showDelete" class="delete_btn_01" @click="deleteBtn">删除</a>
    </div>
  </div>
</template>

<script>
import multLineInput from '@/components/multLineInput';

export default {
  components: {
    multLineInput
  },
  model: {
    prop: 'form',
    event: 'change'
  },
  props: {
    form: {
      // 需要双向绑定的数据
      type: Object,
      default: () => {}
    },
    formIndex: {
      type: Number,
      default: 0
    },
    formItem: {
      type: Array,
      default: () => []
    },
    showDelete: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {};
  },
  methods: {
    verify(cb) {
      this.$validate((res) => {
        cb(res);
      });
    },

    addBtn() {
      this.$emit('add');
    },

    deleteBtn() {
      this.$emit('delete');
    }
  }
};
</script>

<style lang="scss" scope>
.errTips {
  // position: absolute;
  font-size: 0.1rem;
}
</style>
