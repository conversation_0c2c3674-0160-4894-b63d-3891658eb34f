<template>
  <div v-show="showSelImgBox">
    <!-- 遮罩层 -->
    <div class="dialog_overlay" style="display: block" @click="close"></div>
    <!-- 弹出层 -->
    <div class="upload_select show">
      <h5>请选择上传方式</h5>
      <ul>
        <li @click="selImg(1)">
          <a>{{ scan ? '拍照扫描' : '拍照' }}</a>
        </li>
        <!-- <li @click="selImg(2)">
          <a>从相册上传</a>
        </li> -->
      </ul>
      <a class="cancel" @click="close">取消</a>
    </div>
  </div>
</template>

<script>
import '@/nativeShell/nativeCallH5';

export default {
  name: 'GetImgGrounPhoto',

  props: {
    scan: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showSelImgBox: false,
      imgType: ''
    };
  },

  methods: {
    getImg(imgType) {
      this.imgType = imgType;
      this.showSelImgBox = true;
    },
    close() {
      this.showSelImgBox = false;
    },
    selImg(selType) {
      console.log(selType);
      window.imgCallBack = this.getImgCallBack;
      if (selType == 'signimg') {
        return;
      }
      var phoneConfig = {
        funcNo: 61001,
        moduleName: $hvue.customConfig.moduleName, // 必须为open
        takeType: '1', // 拍摄类型0 ：单人 （竖屏）1：双人（横屏）默认0
        topTip: '拍摄合照', //顶部提示 单人拍照顶部提示（目前双人拍照无顶部提示）
        needFaceBox: '1' // 是否需要人像框 0不需要 1需要
      };
      console.log(phoneConfig);
      let result = $h.callMessageNative(phoneConfig);
      this.close();
      if (result.error_no !== '0') {
        console.log({ mes: result.error_info });
      }
    },
    getImgCallBack(data) {
      console.log(data);
      // let result = {
      // 	idNo: data.idNo,
      // 	name: data.custName,
      // 	address: data.native,
      // 	ethnicname: data.ethnicName,
      // 	idbegindate: data.idbeginDate,
      // 	idenddate: data.idendDate,
      // 	validity: data.idbeginDate + '@' + data.idendDate,
      // 	issueAuthority: data.policeOrg,
      // 	path: data.backFilePath || data.frontFilePath,
      // 	secret: data.backSecret || data.frontSecret
      // };
      this.$emit('getImgCallBack', {
        base64: this.filterBase64Pre(data.frontBase64 || data.backBase64)
        // ocrInfo: result
      });
    },
    filterBase64Pre(ndata) {
      let arr = ndata.split('base64,');
      return arr[arr.length - 1];
    }
  }
};
</script>
