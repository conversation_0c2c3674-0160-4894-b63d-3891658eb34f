<template>
  <div>
    <input ref="input" type="file" accept="image/*" />
  </div>
</template>

<script>
import <PERSON><PERSON>yUtil from '@/common/AlipayUtil';
export default {
  name: 'GetImgAlipay',
  data() {
    return {};
  },
  props: {
    openType: {
      // camera相机 album相册 both同时使用
      type: String,
      default: 'both'
    },
    front: {
      // 1自拍
      type: String,
      default: ''
    },
    floatingLayer: {
      // 1身份证正面 0身份证反面
      type: String,
      default: ''
    }
  },
  methods: {
    getImg() {
      const aliPayUtil = new AlipayUtil({
        front: this.front,
        floatingLayer: this.floatingLayer,
        type: this.openType,
        successFunc: this.successFunc
      });
      aliPayUtil.chooseImageByZfb();
    },
    successFunc(base64) {
      this.$emit('getImgCallBack', {
        base64
      });
    }
  }
};
</script>
<style scoped>
input {
  width: 0px;
  height: 0px;
  position: absolute;
  top: 0;
  left: 0;
  display: none;
}
</style>
