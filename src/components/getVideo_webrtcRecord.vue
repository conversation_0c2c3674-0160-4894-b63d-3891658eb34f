<template>
  <article class="content">
    <div v-if="!playeUrl && !errorPageHint" class="video_main">
      <div v-if="!isReciveLocalStream" class="video_loading">
        <div class="pic"><img src="@/assets/images/v_loading.png" /></div>
        <span>正在连接服务…</span>
      </div>

      <div class="common_video">
        <div
          id="localDiv"
          class="one_video_local_div"
          style="width: 100%; height: 100%"
        ></div>
        <div id="remoteDiv" style="display: none"></div>
      </div>
      <div v-show="isReciveLocalStream" class="video_flex_wrap">
        <div class="video_flex_head">
          <a class="back_btn" @click="cancelClick"></a>
          <div v-if="isStartRecord" class="v_record_time">
            <span class="ing">{{ showFormatTime }}</span>
          </div>
        </div>
        <div class="video_flex_top">
          <div class="table_wrap">
            <div class="table_td">
              <div class="bc_wrapbox">
                <h5><i class="imp"></i>请使用普通话朗读</h5>
                <div class="bc_text">
                  <p>
                    <span class="readed">{{ readedStr }}</span
                    >{{ readStr.replace(readedStr, '') }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="video_flex_middle">
          <div class="portrait_line"></div>
          <div v-show="errorHint" class="video_errortips">
            <span>{{ errorHint }}</span>
          </div>
        </div>
        <div class="video_flex_bottom">
          <div class="table_wrap">
            <div class="table_td">
              <div class="v_com_btn">
                <a
                  v-show="recordTime == 0"
                  :class="{ disabled: !checkFlag }"
                  @click="startRecord"
                  ><i class="begin_icon"></i>开始录制</a
                >
                <a
                  v-show="recordTime != 0"
                  class="finish"
                  :class="{ disabled: recordTime < minTime }"
                  @click="stopRecord"
                  >完成录制</a
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-show="networkStatus != 0" class="network_state">
        <span v-if="networkStatus == 1 || networkStatus == 2" class="ok"
          >网络正常</span
        >
        <span v-else-if="networkStatus == 3" class="warn">网络卡顿</span>
        <span v-else-if="networkStatus == 4" class="error">网络异常</span>
      </div>
    </div>
    <div v-else-if="errorPageHint" class="video_compage">
      <a class="icon_back" @click="cancelClick"></a>
      <div class="video_result">
        <div class="pic"><img src="@/assets/images/video_error.png" /></div>
        <h5>{{ errorPageHint }}</h5>
      </div>
      <div class="video_btn">
        <a @click="start">重新录制</a>
      </div>
    </div>
    <div v-else class="video_compage">
      <a class="icon_back" @click="cancelClick"></a>
      <div class="review_video">
        <div class="window">
          <video
            v-if="playeUrl && isShowVideo"
            ref="video"
            x5-video-player-type="h5"
            webkit-playsinline
            playsinline
            :src="playeUrl"
            controls
            :poster="faceBase64"
          ></video
          ><img
            v-if="!isShowVideo"
            :src="faceBase64"
            style="width: 100%; height: 100%; position: relative"
          />
        </div>
        <!-- <a class="btn">点击预览</a><a style="display: none;" class="btn on">点击暂停</a> -->
      </div>
      <div class="video_info">
        <h5>请确认影像和声音正确后提交</h5>
        <ul>
          <li v-if="isFaceComparePass == 0" class="tip_error">
            <i></i>请确保全程为本人录制
          </li>
          <li v-if="isFaceComparePass == 1" class="tip_ok">
            <i></i>人脸比对通过
          </li>
          <li v-if="isFaceCheckPass == 0" class="tip_error">
            <i></i>请确保全程人脸对准屏幕
          </li>
          <li v-if="isSingleFace == 0" class="tip_error">
            <i></i>检测到视频中有他人出现
          </li>
          <li v-if="isFaceCheckPass == 1" class="tip_ok">
            <i></i>人脸检测通过
          </li>
        </ul>
      </div>
      <div class="video_btn">
        <a class="border" @click="start">重新录制</a>
        <a @click="getVideoCallBack">确认提交</a>
      </div>
    </div>
  </article>
</template>

<script>
import { cancelRequest } from 'thinkive-hvue';
import '@/plugins/tchat/protobuf.min.js';
import '@/plugins/tchat/md5.js';
import '@/plugins/tchat/sdp-transform.js';
import '@/plugins/tchat/TChatRTC.js';
import {
  getRoomNo,
  faceCheck,
  livingCheckAndCompare
} from '@/service/videoOneService';
import {
  playerAudio,
  getFaceRect,
  filterBase64Pre,
  takePic
} from '@/common/util';

export default {
  name: 'GetVideoWebrtcRecord',
  props: {
    userInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      readStr: '', // 跟读字符串
      readedStr: '',
      readIntv: null,
      isReciveRmoteStream: false,
      isReciveLocalStream: false,
      maxTime: 30,
      minTime: 5,
      recordTime: 0,
      playeUrl: '',
      videoPath: '',
      recordIntv: null,
      faceBase64: '',
      errorHint: '',
      livingRecognitionIntv: null,
      noticeRecordPrepareAudio: './audio/fxc_common_notice_record_prepare.mp3',
      noticeErrorAudio: './audio/fxc_common_notice_error.mp3',
      recordStartAudio: './audio/tk_record_start.mp3',
      recordEndAudio: './audio/tk_record_end.mp3',
      checkFlag: false, // 是否检测通过高亮限制下一步
      isStartRecord: false, // 是否开始录制
      room_no: '',
      signalServer: '',
      loginId: '',
      authKey: '',
      errorPageHint: '',
      isFaceCompare: false,
      recordFaceFailCount: 0,
      recordFaceTotalFailCount: 0,
      recordFaceCompareFailCount: 0,
      recordFaceCompareTotalFailCount: 0,
      isFaceComparePass: -8, // -8 未检测 1 检测通过 其他检测不通过
      isFaceCheckPass: -8, // -8 未检测 1 检测通过 其他检测不通过
      isSingleFace: -8, // -8 未检测 1 检测通过 其他检测不通过
      audioObj: {},
      isShowVideo: true, // 点击预览时删除video标签，防止vivo自带浏览器视频挡住确认框问题
      networkStatus: 0 // 网络状态   网络卡顿 网络不稳定  0未知 1很 2较好 3较差 4很差
    };
  },
  computed: {
    showFormatTime() {
      let time = this.maxTime - this.recordTime;
      let m = parseInt(time / 60);
      let s = parseInt(time % 60);
      return `${m.toString().padStart(2, '0')}:${s
        .toString()
        .padStart(2, '0')}`;
    }
  },
  watch: {
    recordTime: function (v) {
      if (v >= this.maxTime) {
        this.stopRecord();
      }
    }
  },
  deactivated() {
    document.removeEventListener('visibilitychange', this.visibilitychange);
  },
  activated() {
    document.addEventListener('visibilitychange', this.visibilitychange);
  },

  beforeDestroy() {
    this.clearPage();
  },
  mounted() {
    this.readStr = this.userInfo.read_str;
    this.start();
  },
  methods: {
    cancelClick() {
      this.clearPage();
      this.$emit('onerror');
    },
    // 清理录制组件信息
    clearPage() {
      this.isReciveLocalStream = false;
      this.recordFaceCompareTotalFailCount = 0;
      this.recordFaceTotalFailCount = 0;
      this.recordClear();
    },
    // 开始或从新开始录制前清理
    recordClear() {
      this.playeUrl = '';
      this.faceBase64 = '';
      this.errorPageHint = '';
      this.isFaceComparePass = -8;
      this.isFaceCheckPass = -8;
      this.isSingleFace = -8;
      this.clearVideoPage();
    },
    // 清理摄像头页面信息
    clearVideoPage() {
      this.networkStatus = 0;
      this.audioObj.preAudioFinish = false;
      this.recordFaceFailCount = 0;
      this.recordFaceCompareFailCount = 0;
      this.readedStr = '';
      this.isStartRecord = false;
      this.checkFlag = false;
      this.recordTime = 0;
      this.isFaceCompare = false;
      window.clearInterval(this.readIntv);
      window.clearInterval(this.livingRecognitionIntv);
      window.clearInterval(this.recordIntv);
      try {
        TChatRTC.closeAll();
        if (this.audioObj.buffSource) {
          this.audioObj.buffSource.stop();
        }
      } catch (e) {
        console.log(e);
      }
    },
    faceCheck() {
      var faceBase64 = filterBase64Pre(takePic());
      (this.isFaceCompare ? livingCheckAndCompare : faceCheck)(
        {
          flowNo: this.userInfo.flow_no,
          faceImageData: faceBase64,
          rect: getFaceRect()
        },
        { loading: false }
      ).then((data) => {
        let result = data.data;
        if (data.code == '0') {
          if (
            data.funcNo == 'faceLiving/faceDetectAndCompare' &&
            this.isStartRecord
          ) {
            // 从视频开始录制后出现1次人脸比对不通过 提示不通过
            if (result.comparePass == 1 && this.isFaceComparePass == -8) {
              this.isFaceComparePass = 1;
            } else if (result.comparePass == 0) {
              this.isFaceComparePass = 0;
            }
          }
          if (result.isSingleFace == 0 && this.isSingleFace == -8) {
            // 从视频开始录制后出现1次多人脸 提示检测到多人脸
            this.isSingleFace = 0;
          }
          if (
            (data.funcNo == 'faceLiving/faceDetect' &&
              result.facePass == '1') ||
            (data.funcNo == 'faceLiving/faceDetectAndCompare' &&
              result.comparePass == '1' &&
              result.isSingleFace == '1' &&
              result.facePass == '1')
          ) {
            this.checkFlag = true;
            if (this.isFaceCheckPass == -8 && this.isStartRecord) {
              this.isFaceCheckPass = 1;
            }
            if (!this.faceBase64) {
              this.faceBase64 = 'data:image/jpeg;base64,' + faceBase64;
            }
            return;
          } else {
            if (this.isStartRecord && result.facePass == 0) {
              this.isFaceCheckPass = 0; // 从视频开始录制后出现1次人脸在框不通过
            }
            data.msg = result.checkDesc;
          }
        }
        if (
          (this.recordFaceTotalFailCount >=
            $hvue.customConfig.video.recordFaceTotalFailNum - 1 ||
            this.recordFaceCompareTotalFailCount >=
              $hvue.customConfig.video.recordFaceCompareTotalFailNum - 1) &&
          (this.recordFaceFailCount >=
            $hvue.customConfig.video.recordFaceFailNum - 1 ||
            this.recordFaceCompareFailCount >=
              $hvue.customConfig.video.recordFaceCompareFailNum - 1)
        ) {
          this.cancelClick();
          _hvueAlert({
            title: '温馨提示',
            mes: '由于长时间未完成视频录制，我们为您切换到一对一人工见证服务。',
            callback: () => {
              this.$router.replace({ path: 'witnessTwo' });
            }
          });
        } else {
          if (data.funcNo == 'faceLiving/faceDetect') {
            // 在框检测
            if (
              this.recordFaceFailCount >=
              $hvue.customConfig.video.recordFaceFailNum - 1
            ) {
              this.recordFaceTotalFailCount++;
              this.errorPageHint =
                '由于长时间未检测到面部在框，本次视频录制失败，请重新录制。';
              this.clearVideoPage();
              cancelRequest();
            } else {
              this.showErrorHint(data.msg);
              if (this.audioObj.preAudioFinish) {
                if (!this.isStartRecord) {
                  // playerAudio(this.noticeErrorAudio, this.audioObj);
                } else {
                  this.recordFaceFailCount++;
                }
              }
            }
          } else {
            // 人脸对比
            if (
              this.recordFaceCompareFailCount >=
              $hvue.customConfig.video.recordFaceCompareFailNum - 1
            ) {
              this.recordFaceCompareTotalFailCount++;
              this.errorPageHint =
                '人脸比对不通过，请确保为本人录制，本次视频录制失败，请重新录制。';
              this.clearVideoPage();
              cancelRequest();
            } else {
              this.showErrorHint(data.msg);
              if (this.isStartRecord) {
                if (
                  result &&
                  (!result.comparePass || result.comparePass == -8)
                ) {
                  this.recordFaceFailCount++;
                } else {
                  this.recordFaceCompareFailCount++;
                }
              }
            }
          }
        }
      });
      this.isFaceCompare = !this.isFaceCompare;
    },
    showErrorHint(msg, time) {
      time = time || 1;
      if (msg) {
        this.errorHint = msg;
        window.clearTimeout(window.showErrorHintTimeout);
        window.showErrorHintTimeout = window.setTimeout(() => {
          this.errorHint = '';
        }, time * 1000);
      }
    },
    getRoomNo() {
      this.isReciveRmoteStream = false;
      this.isReciveLocalStream = false;
      getRoomNo({ flowNo: this.userInfo.flow_no }, { loading: false }).then(
        (data) => {
          if (data.code == '0') {
            let result = data.data;
            if (result.sercurityNo == '-1') {
              _hvueToast({ mes: '视频人数太多，请稍后再试。' });
              this.cancelClick();
            } else {
              this.room_no = result.sercurityNo;
              this.videoPath = result.videoPath;
              this.signalServer = result.signalServer;
              this.loginId = result.loginId;
              this.authKey = result.authKey;
              TChatRTC.openLocalCamera(
                (stream) => {
                  this.isReciveLocalStream = true;
                  this.startWebRtc();
                },
                (e) => {
                  if (e.name == 'NotAllowedError') {
                    this.cancelClick();
                    _hvueAlert({ mes: '无摄像头权限' });
                  } else {
                    _hvueAlert({ mes: `获取摄像头失败，${e.name || e}` });
                    this.cancelClick();
                  }
                }
              );
            }
          } else {
            _hvueToast({
              mes: data.msg,
              callback: () => {
                this.cancelClick();
              }
            });
          }
        }
      );
    },
    startWebRtc() {
      let tchatParamConfig = {
        signalServer: this.signalServer, //信令服务器地址
        protobufFileLoaction:
          $hvue.customConfig.video.tchat.protobufFileLoaction, //pb文件地址
        secretkey: $hvue.customConfig.video.tchat.tchat_verifier_secret_key, //信令服务器认证秘钥
        roomid: this.room_no //排队获取的房间号
      };
      TChatRTC.init(
        {
          addNewUser: function () {}, //新用户加入
          exitRoomNotify: () => {
            _hvueToast({ mes: '录制异常' });
            this.cancelClick();
          }, //坐席离开房间
          onMsgNotify: () => {
            _hvueToast({ mes: '文本消息' });
          }, //坐席发送文本消息回调
          onTransBufferNotify: this.onTransBufferNotify, //坐席发送指派指令
          onRemoteStreamAdd: (stream) => {
            //远程坐席视频流加入回调
            console.log('远程流加入');
            if (!this.isReciveRmoteStream) {
              this.isReciveRmoteStream = true;
            }
          },
          timeout: () => {
            //信令服务器超时
            // _hvueToast({ mes: '连接信令服务器超时' });
            _hvueToast({ mes: '录制异常' });
            this.cancelClick();
          },
          netError: () => {
            //网络错误
            _hvueToast({ mes: '您的网络异常' });
            this.cancelClick();
          },
          socketError: () => {
            //坐席网络异常
            _hvueToast({ mes: '网络连接异常，请退出重试' });
            this.cancelClick();
          },
          destory: function () {}
        },
        tchatParamConfig,
        this.videoInitSuccessBack
      );
    },
    videoInitSuccessBack(data) {
      if (data.errcode == 0) {
        TChatRTC.login(
          {
            //登录信令服务器
            username: this.loginId
          },
          (data) => {
            if (data.errcode == 0) {
              TChatRTC.enterRoom(
                { roomid: this.room_no, password: this.authKey },
                (data) => {
                  //进入房间
                  if (data.errcode == 0) {
                    TChatRTC.startWebRtc(data.result);
                  } else {
                    _hvueToast({ mes: data.errmsg });
                    this.cancelClick();
                  }
                }
              );
            } else {
              _hvueToast({ mes: data.errmsg });
              this.cancelClick();
            }
          }
        );
      } else {
        if (data.errcode == -1003) {
          data.errmsg = '网络连接异常，请退出重试!';
        }
        _hvueToast({ mes: data.errmsg });
        this.cancelClick();
      }
    },
    startRecord() {
      if (!this.isReciveRmoteStream) {
        this.showErrorHint('请等待视频就绪');
        return;
      }
      if (!this.checkFlag) {
        this.showErrorHint('请您保持全脸在人像框内');
        return;
      }
      if (this.isStartRecord) return;
      this.isStartRecord = true;
      console.log('开始录制');
      playerAudio(this.recordStartAudio, this.audioObj);
      this.startFlowRead();
      TChatRTC.sendMsgByTransBuffer({
        msg: `h5cmd@{"type":"start_record","path":"${this.videoPath}"}`
      });
    },
    // 开始跟读
    startFlowRead() {
      this.readIntv = window.setInterval(() => {
        if (this.readedStr.length >= this.readStr) {
          window.clearInterval(this.readIntv);
        } else {
          this.readedStr = this.readStr.substring(0, this.readedStr.length + 1);
        }
      }, 250);
    },
    stopRecord() {
      if (this.recordTime < this.minTime) return;
      cancelRequest();
      window.clearInterval(this.livingRecognitionIntv);
      console.log('停止录制');
      playerAudio(this.recordEndAudio, this.audioObj);
      TChatRTC.sendMsgByTransBuffer({ msg: 'h5cmd@{"type":"stop_record"}' });
    },
    getVideoCallBack() {
      let video_length = this.$refs.video.duration;
      if (this.userInfo.confirm_tips) {
        this.isShowVideo = false;
        _hvueConfirm({
          title: '请您确认',
          mes: this.userInfo.confirm_tips,
          opts: [
            {
              txt: '取消',
              color: '#333333',
              callback: () => {
                this.isShowVideo = true;
              }
            },
            {
              txt: '确定',
              callback: () => {
                this.$emit('getVideoCallBack', {
                  error_no: '0',
                  videoPath: this.videoPath,
                  video_length: video_length
                });
              }
            }
          ]
        });
      } else {
        this.$emit('getVideoCallBack', {
          error_no: '0',
          videoPath: this.videoPath,
          video_length: video_length
        });
      }
    },
    onTransBufferNotify(data) {
      if (data.errcode == '0') {
        var msg = decodeURI(
          String.fromCharCode.apply(null, data.result.cmdmsg)
        );
        msg = msg.replace(/\\&quot;/g, '"'); //将返回信息中的引用还原
        msg = msg.replace(/&quot;/g, '"'); //将返回json中多余的引号去掉
        console.log(msg);
        if (msg.includes('h5ret@')) {
          msg = JSON.parse(msg.replace('h5ret@', ''));
          if (msg.type == 'record_start') {
            if (msg.error_code == '0') {
              console.log('开始录制成功');
              this.recordIntv = window.setInterval(() => {
                this.recordTime++;
                if (this.recordTime >= this.maxTime) {
                  window.clearInterval(this.recordIntv);
                }
              }, 1 * 1000);
            } else {
              _hvueAlert({ mes: msg.error_code });
            }
          } else if (msg.type == 'record_stop') {
            if (msg.error_code == '0') {
              console.log('停止录制成功');
              let videoUrl = `${
                $hvue.customConfig.video.videoServer
              }/auth-common-server/faceLiving/videoPlay?videoPath=${
                msg.path
              }&tk-jwt-authorization=${encodeURIComponent(
                this.userInfo.authorization
              )}`;
              console.log(videoUrl);
              this.playeUrl = videoUrl;
              this.videoPath = msg.path;
              this.clearVideoPage();
            } else {
              _hvueAlert({ mes: msg.error_code });
            }
          } else if (msg.type == 'tts_stop') {
            console.log('停止播报回调');
            if (msg.name) {
              // 播放mp3提示回调
              window.clearTimeout(window.playerAudioTimeout);
              window.playerAudioCallBack &&
                window.playerAudioCallBack(msg.name);
              window.playerAudioCallBack = null;
              return;
            }
          } else if (msg.type == 'record_error') {
            _hvueAlert({ mes: `录制异常,错误码：${msg.error_code}` });
            this.cancelClick();
          } else if (msg.type == 'channel_ready') {
            playerAudio(this.noticeRecordPrepareAudio, this.audioObj, () => {
              this.audioObj.preAudioFinish = true;
              this.startFaceCheck();
            });
          }
        }
      }
      console.log(data);
    },
    startFaceCheck() {
      this.livingRecognitionIntv = window.setInterval(
        this.faceCheck,
        $hvue.customConfig.video.checkIntvTime
      );
    },
    start() {
      this.recordClear();
      this.getRoomNo();
    },
    visibilitychange() {
      var isHidden = document.hidden;
      console.log(document.visibilityState);
      if (isHidden) {
        this.cancelClick();
        _hvueAlert({ mes: '视频认证已取消' });
      }
    }
  }
};
</script>
