<template>
  <section class="main fixed" style="position: fixed; z-index: 9999">
    <div class="video_main">
      <div class="common_video">
        <video ref="video" autoplay playsinline></video>
      </div>
      <div class="video_flex_wrap">
        <div class="video_flex_head">
          <a class="back_btn" @click="close"></a>
        </div>
        <div class="video_flex_top">
          <div class="table_wrap">
            <div class="table_td">
              <div class="bc_wrapbox">
                <h5><i class="imp"></i>{{ tips }}</h5>
                <div class="bc_text">
                  <p>{{ context }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="video_flex_middle">
          <div class="portrait_line"></div>
        </div>
        <div class="video_flex_bottom">
          <div class="table_wrap">
            <div class="table_td">
              <div class="v_com_btn">
                <a v-if="count === 0" @click="start">开始录制</a>
                <a
                  v-else
                  class="finish"
                  :class="{ disabled: count < enVideoLengthMin }"
                  @click="finish"
                  >完成录制</a
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  data() {
    return {
      mediaRecorder: undefined,
      countInterval: undefined,
      chunks: [],
      count: 0
    };
  },
  props: {
    tips: {
      type: String,
      default: '请使用普通话朗读'
    },
    // 自述式引导文案
    context: {
      type: String,
      default: ''
    },
    // 视频文件最大限制
    maxSize: {
      type: Number,
      default: 20 * 1024 * 1024
    },
    // 视频文件最小限制
    minSize: {
      type: Number,
      default: 0.5 * 1024 * 1024
    },
    // 视频长度最低限制
    enVideoLengthMin: {
      type: Number | String,
      default: 3
    },
    // 视频长度最高限制
    enVideoLengthMax: {
      type: Number | String,
      default: 30
    }
  },
  mounted() {
    this.intend();
  },
  destroyed() {
    clearInterval(this.countInterval);
  },
  methods: {
    intend() {
      const t = this;
      // 检查浏览器是否支持相关API
      if (
        'mediaDevices' in navigator &&
        'getUserMedia' in navigator.mediaDevices &&
        'MediaRecorder' in window
      ) {
        const videoElement = t.$refs.video;

        this.mediaRecorder = undefined;
        this.countInterval = undefined;
        this.chunks = [];
        this.count = 0;

        const constraints = {
          audio: true,
          video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            facingMode: 'user'
          }
        };
        // 获取媒体流
        navigator.mediaDevices
          .getUserMedia(constraints)
          .then((stream) => {
            console.info('stream', stream);
            videoElement.srcObject = stream;
            videoElement.play();
            // 创建MediaRecorder实例
            this.mediaRecorder = new MediaRecorder(stream);
            // 监听数据可用事件，将录制的数据块存入chunks数组
            this.mediaRecorder.addEventListener('dataavailable', (e) => {
              console.info('dataavailable', e);
              this.chunks.push(e.data);
            });

            this.mediaRecorder.addEventListener('stop', () => {
              console.info('录制结束，准备处理数据');
              const blob = new Blob(this.chunks, { type: 'video/webm' });

              const fileSize = blob.size; // 视频文件大小
              if (fileSize > this.maxSize) {
                t.$emit('callback', {
                  error_no: 999,
                  msg: ' 视频文件过大, 请控制录制时间!'
                });
                return;
              }
              if (fileSize < this.minSize) {
                !t.$emit('callback', {
                  error_no: 999,
                  msg: '视频文件过小, 请延长录制时间'
                });
                return;
              }

              // 将Blob转换为Base64格式
              const reader = new FileReader();
              reader.onloadend = function () {
                const videoBase64 = reader.result;
                console.info('返回文件base64', videoBase64.slice(0,500));
                t.$emit('callback', { error_no: 0, videoBase64 });
              };
              reader.readAsDataURL(blob);
            });

            // 组件销毁时，关闭所有的媒体轨道
            this.$once('hook:destroyed', () => {
              console.log('hook:destroyed start');
              clearInterval(this.countInterval);
              if (stream) {
                stream.getTracks().forEach((track) => track.stop());
              }
            });
          })
          .catch((error) => {
            t.$emit('callback', { code: 999, msg: '访问媒体设备出错' });
            console.error('访问媒体设备出错: ', error);
          });
      } else {
        t.$emit('callback', { code: 999, msg: '浏览器不支持相关功能' });
        console.error('浏览器不支持相关功能');
      }
    },
    start() {
      console.info('开始录制');
      let t = this;
      let mediaRecorder = t.mediaRecorder;
      console.info('mediaRecorder===', mediaRecorder);
      if (mediaRecorder && mediaRecorder.state === 'inactive') {
        t.count++;
        t.countInterval = setInterval(() => {
          t.count++;
          // 到达最长录制时间长度后，自动完成录制
          if (t.count > t.enVideoLengthMax) {
            t.finish();
          }
        }, 1000);
        t.chunks = [];
        mediaRecorder.start();
        console.info('mediaRecorder.start');
      }
    },
    finish() {
      console.info('完成录制');
      if (this.count < this.enVideoLengthMin) return;
      clearInterval(this.countInterval);
      let mediaRecorder = this.mediaRecorder;
      console.info('mediaRecorder===', mediaRecorder);
      if (mediaRecorder && mediaRecorder.state === 'recording') {
        mediaRecorder.stop();
        console.info('mediaRecorder.stop');
      } else {
        console.info('MediaRecorder状态不是recording，无法停止');
      }
    },
    close() {
      clearInterval(this.countInterval);
      this.$emit('callback', { error_no: -1 });
    }
  }
};
</script>

<style></style>
