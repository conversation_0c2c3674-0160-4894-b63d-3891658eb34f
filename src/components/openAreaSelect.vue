<template>
  <fragment>
    <van-popup v-model="show" round position="bottom" @closed="closed">
      <van-cascader
        v-model="cascaderValue"
        :title="label"
        :options="options"
        @close="onFinish"
        @finish="onFinish"
      />
    </van-popup>
  </fragment>
</template>

<script>
import { getAdressTree } from '@/service/service';
import { addressParse } from '@/service/service';
export default {
  name: 'OpenAreaSelect',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    label: {
      type: String,
      default: ''
    },
    defaultValue: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: true,
      isInit: false,
      cascaderValue: '',
      options: [],
      fieldArray: []
    };
  },
  watch: {
    defaultValue: {
      handler: function (newVal) {
        if (newVal && !this.isInit) {
          addressParse({
            address: newVal
          }).then((res) => {
            this.fieldArray = [
              res.data.proRegionName,
              res.data.cityRegionName,
              res.data.areaRegionName
            ];
            this.isInit = true;
          });
        }
      },
      immediate: false
    }
  },
  created() {
    getAdressTree({})
      .then((res) => {
        this.options = res.data.map((item) => {
          let treeList = {
            text: item.provinceName,
            value: item.provinceCode
          };
          if (item.sysCityVOs) {
            treeList.children = item.sysCityVOs.map((it) => {
              let cityTreeList = {
                text: it.cityName,
                value: it.cityCode
              };
              if (it.sysXzqyVOs) {
                cityTreeList.children = it.sysXzqyVOs.map((t) => {
                  return {
                    text: t.xzqyName,
                    value: t.xzqyCode
                  };
                });
              }
              return cityTreeList;
            });
          }
          return treeList;
        });
        this.cascaderValue = this.value;

        console.log(this.cascaderValue);
      })
      .catch((err) => {
        console.log(err);
      });
  },
  methods: {
    iptFocus() {
      // 禁止弹出手机键盘
      document.activeElement.blur();
    },

    closed() {
      this.show = true;
      this.$emit('finish');
    },

    onFinish(data) {
      if (!data) {
        this.$emit('finish');
        return;
      }
      const { selectedOptions = '' } = data;
      this.$emit('change', this.cascaderValue);
      this.$emit('finish', selectedOptions);
    }
  }
};
</script>

<style lang="less" scope>
.component {
  width: 100%;
  .van-cell {
    min-height: 0.24rem;
    line-height: 0.24rem;
    padding: 0;
  }
  .van-cell::after {
    display: none;
  }
}
</style>
