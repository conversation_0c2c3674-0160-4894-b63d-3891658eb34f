<template>
  <div v-show="isShow" class="citySelct">
    <!-- 遮罩层 -->
    <div class="dialog_overlay" @click="_onHide"></div>
    <!-- 弹出层 -->
    <div class="layer_box">
      <div class="layer_tit">
        <h3 class="text_left">{{ title }}</h3>
        <a class="close" @click="_onHide"></a>
      </div>
      <div class="sele_lycont fixed">
        <div class="sele_lyinfo">
          <span
            v-show="showItem >= 1"
            :class="selProvinceName === '请选择' ? 'active' : 'off'"
            @click="showItem = 1"
            v-text="selProvinceName"
          ></span>
          <span
            v-show="showItem >= 2"
            :class="selCityName === '请选择' ? 'active' : 'off'"
            @click="showItem = 2"
            v-text="selCityName"
          ></span>
          <span
            v-show="showItem >= 3"
            :class="selAreaName === '请选择' ? 'active' : ''"
            @click="showItem = 3"
            v-text="selAreaName"
          ></span>
        </div>
        <div class="layer_cont">
          <ul v-show="showItem === 1" class="select_list">
            <li
              v-for="(item, index) in provinces"
              :key="index"
              :class="{ active: provincesIndex === index }"
              @click="provinceSelect(item, index)"
            >
              <span>{{ item.provinceName }}</span>
            </li>
          </ul>
          <ul v-show="showItem === 2" class="select_list">
            <li
              v-for="(item, index) in curCity"
              :key="index"
              :class="{ active: cityIndex === index }"
              @click="citySelect(item, index)"
            >
              <span>{{ item.cityName }}</span>
            </li>
          </ul>
          <ul v-show="showItem === 3" class="network_list">
            <li
              v-for="(item, index) in curOpen"
              :key="index"
              :class="{ active: openIndex === index }"
              @click="openSelect(item, index)"
            >
              <h5>{{ item.branchName }}</h5>
              <p>{{ item.address }}</p>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getSysBranchInfo } from '@/service/service';
export default {
  name: 'CitySelect',
  model: {
    prop: 'isShow',
    event: 'change'
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '请选择营业部'
    },
    isOpenDress: {
      type: Boolean,
      default: false
    },
    default: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      provincesIndex: -1,
      cityIndex: -1,
      openIndex: -1,
      provinces: $h.getSession('provinces') || [],
      curCity: [],
      curOpen: [],
      selProvinceName: '请选择',
      selCityName: '请选择',
      selAreaName: '请选择',
      showItem: 1 // 展示第几列
    };
  },
  computed: {
    showCity() {
      return this.provincesIndex > -1;
    }
  },
  watch: {
    isShow(data) {
      if (data) {
        this.updataData();
      }
    }
  },
  created() {
    this.updataData();
  },
  methods: {
    updataData() {
      // if (this.provinces.length > 0) {
      // 	return;
      // }
      getSysBranchInfo()
        .then((res) => {
          if (res.code === 0) {
            this.provinces = this.handleArr(res.data);
            $h.setSession('provinces', this.provinces);
            if (Object.keys(this.default).length > 0) {
              this.selProvinceName = this.default.provinceName;
              this.selCityName = this.default.cityName;
              this.selAreaName = this.default.branchName;
              this.showItem = 3;
              let provincesIndex = this.provinces.findIndex(
                (item) => item.provinceName === this.default.provinceName
              );
              this.provinceSelect(null, provincesIndex);
              let curCity = this.provinces[provincesIndex].children;
              let cityIndex = curCity.findIndex(
                (item) => item.cityName === this.default.cityName
              );
              this.citySelect(null, cityIndex);
              let curOpen = curCity[cityIndex].children;
              this.openIndex = curOpen.findIndex(
                (item) => item.branchNo === this.default.branchNo
              );
              this.selAreaName = this.default.branchName;
            }
          } else {
            return Promise.reject(res.error_info);
          }
        })
        .catch((err) => {
          _hvueToast({ mes: err });
        });
    },

    _onHide() {
      this.$emit('onHide');
      this.$emit('change', false);
    },
    provinceSelect(item, index) {
      this.provincesIndex = index;
      this.selCityName = '请选择';
      this.selProvinceName = this.provinces[index].provinceName;
      this.showItem = 2;
      this.curCity = this.provinces[index].children;
    },
    citySelect(item, index) {
      this.cityIndex = index;
      this.selAreaName = '请选择';
      this.selCityName = this.curCity[index].cityName;
      this.curOpen = this.curCity[index].children;
      this.showItem = 3;
    },
    openSelect(item, index) {
      this.openIndex = index;
      this.selAreaName = this.curOpen[index].branchName;
      this.$emit('selCallBack', this.curOpen[this.openIndex]);
      this._onHide();
    },
    handleArr(arr) {
      let level1 = 'provinceName'; //获取一级属性名称
      let level2 = 'cityName'; //获取二级属性名称
      let level3 = 'branchName'; //获取三级属性名称
      let list = Array.from(
        new Set(
          arr.map((item) => {
            return item[level1];
          })
        )
      );
      let subList = [];
      list.forEach((res) => {
        arr.forEach((ele) => {
          if (ele[level1] === res) {
            let nameArr = subList.map((item) => item.provinceName);
            if (nameArr.indexOf(res) !== -1) {
              let nameArr2 = subList[nameArr.indexOf(res)].children.map(
                (item) => item.cityName
              );
              if (nameArr2.indexOf(ele[level2]) !== -1) {
                subList[nameArr.indexOf(res)].children[
                  nameArr2.indexOf(ele[level2])
                ].children.push({
                  branchName: ele[level3],
                  branchNo: ele['branchNo'],
                  dress: ele['address']
                });
              } else {
                subList[nameArr.indexOf(res)].children.push({
                  cityName: ele[level2],
                  children: [
                    {
                      branchName: ele[level3],
                      branchNo: ele['branchNo'],
                      dress: ele['address']
                    }
                  ]
                });
              }
            } else {
              subList.push({
                provinceName: res,
                children: [
                  {
                    cityName: ele[level2],
                    children: [
                      {
                        branchName: ele[level3],
                        branchNo: ele['branchNo'],
                        dress: ele['address']
                      }
                    ]
                  }
                ]
              });
            }
          }
        });
      });
      return subList;
    }
  }
};
</script>

<style></style>
