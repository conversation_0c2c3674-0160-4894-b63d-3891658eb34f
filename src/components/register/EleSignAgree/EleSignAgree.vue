<template>
  <div class="agree_fixed">
    <t-header :title="agreementData.agreementName" @back="back" />
    <article class="content top_border white_bg">
      <div class="protocol_box">
        <div
          class="protocol_cont"
          v-html="agreementData.agreementContent"
        ></div>
      </div>
    </article>
    <footer class="footer">
      <div class="rule_check" @click="selectAgree">
        <span class="icon_check" :class="{ checked: isChecked }"></span>
        <label>我已阅读并同意签署以上协议内容</label>
      </div>
      <div class="ce_btn">
        <a v-if="countdown" class="p_button disabled">请阅读{{ countdown }}</a>
        <a v-else class="p_button" @click="signAgree">开始签署</a>
      </div>
    </footer>
  </div>
</template>

<script>
import {
  doAgreementRecord,
  getJwtToken,
  queryAgreement
} from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
let timer;

export default {
  name: 'EleSignAgree',
  inject: ['tkFlowInfo', 'setPropsByForm', 'eventMessage'],
  data() {
    return {
      count: 0,
      isChecked: false,
      agreementData: {}
    };
  },
  computed: {
    countdown() {
      return false;
      /*if (this.count <= 0) {
        return false;
      } else {
        return `（${this.count} 秒）`;
      }*/
    }
  },
  created() {
    this.getEleSignAgree();
  },
  methods: {
    async getEleSignAgree() {
      const { flowNodeNo, inProperty } = this.tkFlowInfo();
      const { groupId, contractType } = this.$attrs;
      const tokenRes = await getJwtToken({
        flowNo: flowNodeNo,
        businessType: inProperty.bizType
      });
      $h.setSession('jwtToken', tokenRes.data);
      queryAgreement({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        bizType: inProperty.bizType,
        groupId: groupId,
        contractType: contractType
      })
        .then(({ data }) => {
          this.agreementData = data[0];
          this.count = this.agreementData.readTime || 0;
          timer = setInterval(() => {
            if (this.count <= 0) {
              clearInterval(timer);
            } else {
              this.count--;
            }
          }, 1000);
          this.$once('hook:deactivated', () => {
            clearInterval(timer);
          });
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    },
    selectAgree() {
      this.isChecked = !this.isChecked;
    },
    async signAgree() {
      if (!this.isChecked) {
        _hvueToast({
          mes: '请先阅读协议'
        });
        return;
      }
      const { flowNodeNo, inProperty } = this.tkFlowInfo();
      const { groupId, contractType } = this.$attrs;
      const tokenRes = await getJwtToken({
        flowNo: flowNodeNo,
        businessType: inProperty.bizType
      });
      $h.setSession('jwtToken', tokenRes.data);
      doAgreementRecord({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        bizType: inProperty.bizType,
        signBatchno: inProperty.epaperSignJson
          ? JSON.parse(inProperty.epaperSignJson).epaperSignJson
          : '',
        agreementId: this.agreementData.agreementId,
        agreementVersion: this.agreementData.agreementVersion,
        readTime: this.agreementData.readTime,
        contractType,
        groupId
      })
        .then((data) => {
          let batchNo = data.data.signBatchno;
          let nodeId = flowNodeNo;
          let epaper_sign_json;
          if (
            inProperty.epaperSignJson &&
            JSON.parse(inProperty.epaperSignJson).length > 0
          ) {
            let defEpaper = JSON.parse(inProperty.epaperSignJson);
            let nowNodeIndex = defEpaper.findIndex(
              (item) => item.nodeId === nodeId
            );
            if (nowNodeIndex >= 0) {
              defEpaper[nowNodeIndex].batchNo = batchNo;
            } else {
              defEpaper.push({ nodeId, batchNo });
            }
            epaper_sign_json = JSON.stringify(defEpaper);
          } else {
            let arr = [];
            arr.push({ nodeId, batchNo });
            epaper_sign_json = JSON.stringify(arr);
          }
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, { epaper_sign_json });
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    },
    back() {
      clearInterval(timer);
      timer = null;
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    }
  }
};
</script>

<style scoped>
div.agree_fixed {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  -webkit-flex-direction: column;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: fixed;
  left: 0;
  top: 0;
}
div.agree_fixed article.content {
  -moz-box-flex: 1;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  height: 100%;
  -webkit-overflow-scrolling: auto;
  position: relative;
}
div.agree_fixed article.content::-webkit-scrollbar {
  width: 0;
  display: none;
}
</style>
