<template>
  <section
    class="main fixed qq_bg"
    data-page="home"
    style="position: fixed; z-index: 200"
  >
    <header class="header fixed_header">
      <div class="header_inner">
        <a class="icon_back" @click="back"></a>
      </div>
    </header>
    <article class="content">
      <div class="qq_hm_page">
        <div class="qq_bannerbox">
          <img src="@/assets/images/qq_banner01.png" />
        </div>
        <div class="qq_hm_cont">
          <div class="qq_hm_title">
            <h5><i></i><span>填写申请信息</span><i></i></h5>
          </div>
          <div class="qq_hm_form">
            <div class="s_input_item">
              <div class="tit">姓名</div>
              <div class="ct">
                <input
                  class="t1"
                  type="text"
                  placeholder="请输入"
                  v-model="clientName"
                  readonly
                />
              </div>
            </div>
            <div class="s_input_item">
              <div class="tit">预约手机号</div>
              <div class="ct">
                <input
                  class="t1"
                  type="tel"
                  maxlength="11"
                  placeholder="请输入"
                  v-model="phoneNumInput"
                  readonly
                />
                <a class="code_btn" @click="changeMobile">修改</a>
              </div>
            </div>
            <div class="s_input_item">
              <div class="tit">验证码</div>
              <div class="ct">
                <input
                  v-model="smsCode"
                  @input="smsCode = smsCode.replace(/[^\d]/g, '')"
                  class="t1"
                  type="tel"
                  maxlength="6"
                  placeholder="请输入"
                  autocomplete="off"
                />
                <sms-code-btn
                  v-model="uuid"
                  :need-img-code="false"
                  :mobile-no="mobileTel"
                  @send-result="SMSCodeCallback"
                />
              </div>
            </div>
            <div class="s_input_item">
              <div class="tit">资金账号</div>
              <div class="ct">
                <input
                  class="t1"
                  type="text"
                  placeholder="请输入"
                  v-model="fundAccount"
                  readonly
                />
              </div>
            </div>
            <div class="s_form_tips">
              短信验证码收不到？试试<a class="com_link" @click="smsVoice"
                >语音验证码</a
              >吧！
            </div>
            <div class="ce_btn">
              <a class="p_button" :class="{ disabled }" @click="submit"
                >申请开通</a
              >
            </div>
          </div>
          <div class="qq_hm_tips">
            <div class="txt">
              <p class="txt_center">
                提交手机号码即同意国金证券通过该手机号码为您提供后续服务
              </p>
            </div>
            <div>
              <p>
                客服热线：<span class="com_link" @click="callTelphone"
                  >95310</span
                >
              </p>
              <p>投资有风险，入市需谨慎</p>
            </div>
          </div>
        </div>
      </div>
    </article>
  </section>
</template>

<script>
import SmsCodeBtn from '@/components/SmsCodeBtn.vue';
import {
  smsCodeVerification,
  businessAcceptInfoQry,
  addClientCritMark,
  clientInfoQryV2
} from '@/service/service';
import { formatMobileNo } from '@/common/filter';
import { EVENT_NAME } from '@/common/formEnum';
import HmosUtil from '@/common/HmosUtil';
const hmosUtil = new HmosUtil({});

export default {
  name: 'OpenOptDemoAccount',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {
    SmsCodeBtn
  },
  data() {
    return {
      clientName: '',
      mobileTel: '',
      smsCode: '',
      fundAccount: '',
      uuid: ''
    };
  },
  computed: {
    phoneNumInput() {
      return formatMobileNo(this.mobileTel);
    },
    disabled() {
      return this.smsCode === '' || this.uuid === '';
    }
  },
  created() {
    this.init();
  },
  methods: {
    back() {
      this.eventMessage(this, EVENT_NAME.PREV_FLOW);
    },
    init() {
      const { inProperty } = this.tkFlowInfo();
      const { fundAccount } = inProperty;
      clientInfoQryV2()
        .then(({ data }) => {
          this.clientName = data.clientName;
          this.mobileTel = data.mobileTel;
          this.fundAccount = fundAccount;
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    },
    submit() {
      if (this.disabled) return;
      if (!this.uuid) {
        this.$TAlert({
          title: '温馨提示',
          tips: '请先发送短信验证码'
        });
        return false;
      }
      if (this.smsCode.length !== 6) {
        this.$TAlert({
          title: '温馨提示',
          tips: '短信验证码不匹配'
        });
        return false;
      }
      smsCodeVerification({
        mobile: this.mobileTel,
        captchaCode: this.smsCode,
        serialNumber: this.uuid
      })
        .then((res) => {
          const { inProperty } = this.tkFlowInfo();
          const { bizType } = inProperty;
          if (res.data.verificationvFlag !== '1') {
            this.$TAlert({
              title: '温馨提示',
              tips: '输入的验证码有误，请重新输入'
            });
            this.smsCode = '';
            return;
          }
          businessAcceptInfoQry({
            flowToken: sessionStorage.getItem('TKFlowToken'),
            bizType,
            queryStatus: '1,3'
          }).then(({ data }) => {
            const { flowInsList } = data;
            if (flowInsList.length !== 0) {
              this.$TAlert({
                title: '温馨提示',
                tips: '您之前已申请开立期权模拟账户，如有疑问可拨打客服95310咨询。',
                confirmBtn: '我知道了'
              });
            } else {
              this.$TAlert({
                title: '风险揭示说明',
                tipsHtml:
                  '<p style="text-align: left;">尊敬的客户：<br />您现在申请办理的是股票期权全真模拟账户开户业务，若您参与期权全真模拟交易不会发生资金的实质性盈利或者损失，但您也需要知晓期权交易的相关规则和风险，请您确认是否需要开通股票期权全真模拟交易账户，如无异议请点击继续申请开通</p>',
                hasCancel: true,
                confirmBtn: '继续申请开通',
                cancelBtn: '取消',
                confirm: () => {
                  addClientCritMark({
                    flowToken: sessionStorage.getItem('TKFlowToken'),
                    markType: '19',
                    markContent:
                      '尊敬的客户：您现在申请办理的是股票期权全真模拟账户开户业务，若您参与期权全真模拟交易不会发生资金的实质性盈利或者损失，但您也需要知晓期权交易的相关规则和风险，请您确认是否需要开通股票期权全真模拟交易账户，如无异议请点击继续申请开通',
                    confirmFlag: '1'
                  }).then((res) => {});
                  this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
                    verif_mobile_tel: this.mobileTel,
                    client_name: this.clientName,
                    fund_account: this.fundAccount
                  });
                },
                cancel: () => {}
              });
            }
          });
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    },
    SMSCodeCallback(flag) {
      console.log(flag);
      if (!flag) {
        this.uuid = '';
      }
    },
    changeMobile() {
      // 跳转至个人资料
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010004', initJumpMode: '0' });
      });
    },
    smsVoice() {
      if (!this.uuid) {
        this.$TAlert({
          title: '温馨提示',
          tips: '请先发送短信验证码'
        });
        return false;
      }
      _hvueConfirm({
        title: '温馨提示',
        mes: '亲，您可以使用输入的手机号，拨打95310-按3-按1，收听语音验证码。',
        opts: [
          {
            txt: '取消',
            color: '#333333'
          },
          {
            txt: '立即拨打',
            callback: this.callTelphone
          }
        ]
      });
    },
    callTelphone() {
      if (hmosUtil.checkHM) {
        hmosUtil.callPhone('95310');
      } else if ($hvue.platform === '0') {
        window.location.href = 'tel:95310';
      } else {
        let reqParams = {
          funcNo: '50220',
          telNo: '95310',
          callType: '0'
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      }
    }
  }
};
</script>

<style scoped>
.code_btn2 {
  background: #f2f4f8;
}
.qq_hm_form .ce_btn .p_button.disabled {
  box-shadow: none;
}
input.t1[readonly] {
  color: #333333;
}
</style>
