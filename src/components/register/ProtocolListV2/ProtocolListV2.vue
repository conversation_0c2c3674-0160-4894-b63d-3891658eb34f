<template>
  <fragment>
    <div class="com_box">
      <ul class="protocol_list">
        <li
          v-for="({ agreementName, agreementId }, index) in agreeList"
          :key="index"
          :class="{
            readed: readList.includes(agreementId),
            unread: !readList.includes(agreementId)
          }"
          @click="openAgree(index)"
        >
          <a>《{{ agreementName }}》</a>
          <span class="state">{{
            readList.includes(agreementId) ? '已读' : '未读'
          }}</span>
        </li>
      </ul>
    </div>
    <div class="rule_check">
      <span
        class="icon_check"
        :class="{ checked: isChecked }"
        @click="selectAgree"
      ></span
      >本人已详细阅读并完全理解以上合同及协议，同意签署。
    </div>
    <protocol-detail
      v-if="showAgreeDetail"
      v-model="agreeIndex"
      :agree-list="agreeList"
      :is-count="!isCount"
      @callback="agreeCallBack"
    />
  </fragment>
</template>

<script>
import { getJwtToken, queryAgreement } from '@/service/service';
import ProtocolDetail from '@/components/ProtocolDetail';
import { EVENT_NAME } from '@/common/formEnum';
import { signAgree } from '@/common/util';

export default {
  name: 'ProtocolListV2',
  inject: ['tkFlowInfo', 'clearKeepAlive', 'eventMessage'],
  components: {
    ProtocolDetail
  },
  props: {
    advAgeAgreeParam: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showAgreeDetail: false,
      isChecked: false,
      agreeIndex: 0,
      readList: [],
      agreeList: []
    };
  },
  computed: {
    isCount() {
      return this.readList.includes(this.agreeId);
    },
    agreeId() {
      return this.agreeList[this.agreeIndex]?.agreementId;
    },
    allReadFlag() {
      return this.agreeList.every((a) => {
        return this.readList.includes(a.agreementId);
      });
    },
    isAdvAge() {
      return this.$attrs.clientAge >= '70';
    }
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    this._queryAgreement();
    this.clearKeepAlive();
  },
  methods: {
    getProtocolParam() {
      let param = {
        groupId: [],
        contractType: []
      };
      try {
        let protocolParam = JSON.parse(this.$attrs.protocolParam);
        protocolParam.forEach(({ groupId, contractType }) => {
          param.groupId.push(groupId);
          param.contractType.push(contractType);
        });
        return param;
      } catch (e) {
        console.log(e);
        return param;
      }
    },

    async _queryAgreement() {
      const { flowNodeNo, inProperty } = this.tkFlowInfo();
      let param = this.getProtocolParam();
      const tokenRes = await getJwtToken({
        flowNo: flowNodeNo,
        businessType: inProperty.bizType
      });
      $h.setSession('jwtToken', tokenRes.data);
      let reqParams = {
        flowToken: sessionStorage.getItem('TKFlowToken'),
        bizType: inProperty.bizType,
        contractType: param.contractType.join(','),
        groupId: param.groupId.join(',')
        // agreementId: agreementIds
      };
      if (this.bankId) reqParams.agreementSubtype = this.bankId;
      if (this.isAdvAge && this.advAgeAgreeParam !== '') {
        // ·······················
        let advAgeAgreeQueryParam = {
          groupId: [],
          contractType: []
        };
        let advAgeAgreeQueryList = JSON.parse(this.advAgeAgreeParam);
        advAgeAgreeQueryList.forEach(({ groupId, contractType }) => {
          advAgeAgreeQueryParam.groupId.push(groupId);
          advAgeAgreeQueryParam.contractType.push(contractType);
        });
        reqParams.contractType = `${param.contractType.join(
          ','
        )},${advAgeAgreeQueryParam.contractType.join(',')}`;
        reqParams.groupId = `${param.groupId.join(
          ','
        )},${advAgeAgreeQueryParam.groupId.join(',')}`;
      }
      queryAgreement(reqParams)
        .then((res) => {
          this.agreeList = res.data;
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 2,
            data: this.submitForm
          });
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    },
    openAgree(i) {
      this.showAgreeDetail = true;
      this.agreeIndex = i;
    },
    agreeCallBack(flag) {
      if (flag) {
        if (!this.isCount && !this.readList.includes(this.agreeId)) {
          this.readList.push(this.agreeId);
        }
        this.isChecked = this.allReadFlag;
        if (this.allReadFlag || this.agreeIndex + 1 === this.agreeList.length)
          this.showAgreeDetail = false;
      } else {
        this.showAgreeDetail = false;
      }
    },
    selectAgree() {
      let firstKey = this.agreeList.filter((item) => {
        return !this.readList.includes(item.agreementId);
      })[0];
      let firstIndex = 0;
      if (firstKey) {
        firstIndex = this.agreeList.findIndex((item) => {
          return item.agreementId === firstKey.agreementId;
        });
      }
      if (!this.allReadFlag) {
        this.showAgreeDetail = true;
        this.agreeIndex = firstIndex;
        return;
      }
      this.isChecked = !this.isChecked;
    },
    submitForm() {
      if (!this.isChecked) {
        _hvueToast({
          mes: '请阅读并勾选协议'
        });
        return;
      }
      const tkFlowInfo = this.tkFlowInfo();
      signAgree(tkFlowInfo, this.agreeList)
        .then((epaperSignJson) => {
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, { epaperSignJson });
        })
        .catch((error) => {
          _hvueToast({
            mes: error
          });
        });
    }
  }
};
</script>

<style scoped></style>
