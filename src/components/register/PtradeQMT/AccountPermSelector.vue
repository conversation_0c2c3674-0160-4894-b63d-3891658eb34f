<template>
  <fragment>
    <template v-if="mainFundAccNormalFlag === true">
      <div class="com_title">
        <h5>普通资金账户：{{ accountInfo.fundAccount }}</h5>
      </div>
      <div class="acct_status_item">
        <ul class="acct_list">
          <li
            v-for="(
              { permissionCode, desc, checkFlag, checked, subPermission }, k
            ) in accountInfo.permissionList"
            :key="k"
            @click="select(k)"
          >
            <span
              class="icon_check"
              :class="{ disabled: checkFlag === '1', checked }"
              >{{ desc }}</span
            >
            <span class="state" v-show="checkFlag === '1'">已开通</span>
            <div class="ellipsis_cont" v-if="permissionCode === '01'">
              <div class="txt ellipsis">
                策略交易权限主要包含以下功能：<br />策略编写、策略回测、策略交易。根据监管有关要求...
              </div>
              <a class="com_link" @click.stop="showMore(permissionCode)"
                >查看更多</a
              >
            </div>
            <div v-for="(it, i) in subPermission" :key="i">
              <div class="sub_gl_item" @click.stop="selectChild(k, i)">
                <span
                  class="icon_check"
                  :class="{
                    disabled: it.checkFlag === '1',
                    checked: it.checked
                  }"
                  >{{ it.desc }}</span
                >
                <span class="state" v-show="it.checkFlag === '1'">已开通</span>
              </div>
            </div>
            <div class="ellipsis_cont" v-if="permissionCode === '00'">
              <div class="txt ellipsis" v-if="sysNo === SYS_CONFIG.PTRADE">
                PTrade默认权限包括不限于：<br />闪电下单（行情界面双击盘口，弹出闪电下单窗口...
              </div>
              <div class="txt ellipsis" v-else>
                QMT默认权限包括不限于：<br />联动下单（行情板块和下单板块联动、多券监控、多交易...
              </div>
              <a class="com_link" @click.stop="showMore(permissionCode)"
                >查看更多</a
              >
            </div>
          </li>
        </ul>
      </div>
      <div
        class="acct_txt_tips pb0"
        v-show="!dpEnhanceAuthOpen && accountInfo.ldpFlag === '0'"
      >
        <span
          class="icon_check"
          :class="{ checked: accountInfo.ldpFlagChecked === '1' }"
          @click.stop="
            accountInfo.ldpFlagChecked === '1'
              ? (accountInfo.ldpFlagChecked = '0')
              : (accountInfo.ldpFlagChecked = '1')
          "
        >
          开通LDP极速柜台
        </span>
      </div>
      <div class="acct_txt_tips">
        <p>温馨提示:</p>
        <p>
          投资有风险，入市需谨慎。如开通权限后长期未入金或未通过{{
            sysNo
          }}交易，权限可能会被冻结，如需重新启用可与您客服经理联系。
        </p>
      </div>
      <!-- <div class="com_title" v-show="accountInfo.creditFundAccount">
      <h5>信用资金账户：{{ accountInfo.creditFundAccount }}</h5>
    </div>
    <div class="acct_status_item" v-show="accountInfo.creditFundAccount">
      <div class="tit">
        <h5>与普通资金账户(主)保持一致</h5>
        <div class="switch">
          <input
            type="checkbox"
            v-model="checkBoxInput"
            :disabled="accountInfo.disabled"
          />
          <div class="switch-inner">
            <div class="switch-arrow"></div>
          </div>
        </div>
      </div>
      <div class="acct_txt_tips">
        <p>温馨提示:</p>
        <p>
          1、开关打开，信用账户开通权限与普通账户保持一致(不含ETF趋势交易)；
        </p>
        <p>2、权限开通后线上不支持关闭；</p>
        <p>如有疑问，可联系客服95310咨询。</p>
      </div>
    </div> -->
    </template>
    <template v-else-if="mainFundAccNormalFlag === false">
      <div class="no_data_content">
        <img src="@/assets/images/result_none.png" class="no_data_icon" />
        <div class="none">
          您当前资金账号异常，暂无法进行权限开通，如有疑问可联系服务人员或咨询客服95310。
        </div>
      </div>
    </template>
  </fragment>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
import { SYS_CONFIG, ASSET_PROP } from '@/common/enumeration';
import {
  accountMenuPermQuery,
  fundAccountListQry,
  sysOpenPreCheck
} from '@/service/service';

export default {
  name: 'AccountPermSelector',
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {
    //系统编号
    sysNo: {
      type: String,
      default: ''
    },
    //是否专业投资者标识
    profFlag: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      accountInfo: {
        ldpFlag: '', //是否开通LDP标识 "1":已开通；"0":未开通
        ldpFlagChecked: '0',
        fundAccount: '', //资金账号
        creditFundAccount: '', //信用资金账号
        creditPermFlag: '', //信用权限标识 "1":一致；"0":不一致
        permissionList: [] //权限信息
      },
      SYS_CONFIG,
      dpEnhanceAuthOpen: false, //有否仓增强策略权限
      mainFundAccNormalFlag: null // 主资金账户状态是否正常标识
    };
  },
  computed: {
    checkBoxInput: {
      set(v) {
        if (v) {
          this.accountInfo.creditPermFlag = '1';
        } else {
          this.accountInfo.creditPermFlag = '0';
        }
      },
      get() {
        return this.accountInfo.creditPermFlag === '1';
      }
    },
    //下一步标识 0不可点击，1可以下一步，3隐藏，4返回首页
    nextFlag() {
      if (!this.mainFundAccNormalFlag) return '3';
      if (this.accountInfo.ldpFlagChecked === '1') return '1';
      if (this.allOpenFlag) return '4';
      return this.accountInfo.permissionList.some(
        ({ checked = false, subPermission = [] }) => {
          return checked || subPermission.some((it) => !!it.checked);
        }
      )
        ? '1'
        : '0';
    },
    allOpenFlag() {
      return this.accountInfo.permissionList.every(
        ({ checkFlag = '', subPermission = [] }) => {
          return (
            checkFlag === '1' &&
            subPermission.every((it) => it.checkFlag === '1')
          );
        }
      );
    }
  },
  watch: {
    nextFlag: {
      handler(nv) {
        this.$store.commit('flow/setWhiteBg', false);
        if (nv === '4') {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            text: '返回首页',
            display: true,
            btnStatus: 2,
            data: () => {
              this.eventMessage(this, EVENT_NAME.TO_INDEX);
            }
          });
        } else if (nv === '3') {
          this.$store.commit('flow/setWhiteBg', true);
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
          return;
        }
        if (nv === '1') {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 2,
            data: this.submit
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.init();
    });
  },
  methods: {
    init() {
      fundAccountListQry({})
        .then(({ code, msg, data }) => {
          if (code === 0) {
            //判断主资金账号状态是否正常
            this.mainFundAccNormalFlag = data.fundAccountList.some(
              ({ mainFlag, fundAccountStatus, assetProp }) =>
                mainFlag === '1' &&
                fundAccountStatus === '0' &&
                assetProp === ASSET_PROP.ORDINARY_ACCOUNT
            );
            // 查询是否存在底仓增强策略权限
            return sysOpenPreCheck({
              flowToken: sessionStorage.getItem('TKFlowToken'),
              sysNo: SYS_CONFIG.DCZQCL
            });
          } else {
            return Promise.reject(new Error(msg));
          }
        })
        .then(({ data }) => {
          if (Object.keys(data).length === 0 || data.title === null) {
            this.dpEnhanceAuthOpen = false;
          } else {
            this.dpEnhanceAuthOpen = true;
          }
          return accountMenuPermQuery({
            sysNo: this.sysNo
          });
        })
        .then(({ data }) => {
          this.$set(
            this,
            'accountInfo',
            Object.assign(this.accountInfo, { ...data })
          );
          /**
           * @description:
           * 1.客户同时具备正常状态的普通和信用资金账户，则需展提示语，且信用账户开通权限需与普通账户保持一致，开关默认打开.可以单击关闭开关，再次进入可以再次打开，但一经设置为打开状态，则线上不可再更改
           * 2.基础交易权限是否为已开通状态，如否，则自动勾选基础交易权限，并不可取消，点击取消则toast提示"基础交易权限不可取消"
           */
          /* if (this.accountInfo.creditFundAccount) {
            if (this.accountInfo.creditPermFlag === '1') {
              this.$set(this.accountInfo, 'disabled', true);
            } else {
              this.accountInfo.creditPermFlag = '1';
            }
          } */
          this.accountInfo.permissionList = this.accountInfo.permissionList.map(
            (it) => {
              if (it.checkFlag !== '1')
                this.$set(it, 'checked', it.permissionCode === '00');
              return it;
            }
          );
          /**
           *  @description
           * 如果客户已经开了底仓增强策略权限/已开通极速柜台的，后续权限开通默认就是开极速柜台
           */
          if (this.dpEnhanceAuthOpen && this.accountInfo.ldpFlag === '0') {
            this.$set(this.accountInfo, 'ldpFlagChecked', '1');
          }
        })
        .catch((err) => {
          this.$TAlert({
            title: '温馨提示',
            tips: err
          });
        });
    },
    showMore(permissionCode) {
      let title,
        tipsHtml = '';
      if (permissionCode === '00') {
        title =
          this.sysNo === SYS_CONFIG.PTRADE
            ? 'Ptrade基础交易权限说明'
            : 'QMT基础交易权限说明';
        tipsHtml =
          this.sysNo === SYS_CONFIG.PTRADE
            ? '<div class="qx_exp_box">\
                <p>PTrade默认权限包括不限于：</p>\
                <h5 class="tit">特色功能：</h5>\
                <ul class="list">\
                  <li>闪电下单（行情界面双击盘口，弹出闪电下单窗口）</li>\
                  <li>一键清仓（查询界面-持仓-清仓）</li>\
                  <li>一键全撤（撤全部、撤勾选、买全撤、卖全撤、撤最新）</li>\
                  <li>一键申购（今日新股、今日新债）</li>\
                  <li>篮子交易（一键买卖、调仓）</li>\
                  <li>拆单策略（数量递减、区间随机、固定数量）</li>\
                  <li>B/S 分时主图显示</li>\
                </ul>\
                <h5 class="tit">个性化工具：</h5>\
                <ul class="list">\
                  <li>条件单（定时埋单、价格埋单、移动止盈策略等）</li>\
                  <li>拐点交易（拐点买入、拐点卖出）</li>\
                  <li>抢单交易、追涨停工具</li>\
                  <li>网格交易</li>\
                  <li>盘口扫单</li>\
                  <li>可转债交易</li>\
                  <li>定时国债逆回购</li>\
                  <li>交易快手</li>\
                  <li>手工日内交易（键盘交易、鼠标交易、盘口交易等）</li>\
                  <li>策略交易模拟权限（策略编写、回测、模拟交易）</li>\
                </ul>\
            </div>'
            : '<div class="qx_exp_box">\
                <h5 class="tit">QMT默认权限包括不限于：</h5>\
                <ul class="list">\
                  <li>联动下单（行情板块和下单板块联动、多券监控、多交易界面）</li>\
                  <li>算法交易（支持参数控制）</li>\
                  <li>随机量交易</li>\
                  <li>条件单（触价单）</li>\
                  <li>下单类型选择（支持绝对数量、绝对金额、持仓数量比例、可用金额比例、总资产比例、[调整]持仓数量、[调整]持仓市值占账号总资产比例）</li>\
                  <li>篮子交易（组合交易）</li>\
                  <li>一键调仓</li>\
                  <li>一键清仓</li>\
                  <li>策略交易模拟权限（策略编写、回测、模拟交易）</li>\
                </ul>\
              </div>';
      } else {
        title = '策略交易权限说明';
        tipsHtml =
          '<div class="qx_exp_box">\
            <h5 class="tit">策略交易权限主要包含以下功能：</h5>\
            <ul class="list">策略编写、策略回测、策略交易。根据监管有关要求，投资者应进行程序化交易报告并签署相关协议，方可开通此权限。</ul>\
          </div>';
      }
      this.$TAlert({
        title,
        tipsHtml
      });
    },
    select(i) {
      let permissinonInfo = this.accountInfo.permissionList[i];
      if (permissinonInfo.checkFlag === '1') return; //已开通，无需勾选
      if (permissinonInfo.checked && permissinonInfo.permissionCode === '00') {
        _hvueToast({
          mes: '基础交易权限不可取消'
        });
        return;
      }
      if (
        permissinonInfo.checked &&
        permissinonInfo?.subPermission?.length > 0
      ) {
        const f = permissinonInfo.subPermission.some(({ checked }) => checked);
        if (f) return;
      }
      this.$set(permissinonInfo, 'checked', !permissinonInfo.checked);
    },
    selectChild(i, subIndex) {
      let permissinonInfo = this.accountInfo.permissionList[i];
      let subPermissionInfo = permissinonInfo.subPermission[subIndex];
      if (subPermissionInfo.checkFlag === '1') return; //已开通，无需勾选
      //勾选没有勾选父级权限，子权限不能单独勾选
      if (!permissinonInfo.checked && permissinonInfo.checkFlag !== '1') {
        this.$TAlert({
          title: '温馨提示',
          tips: `启用了${subPermissionInfo.desc}，必须同时启用${permissinonInfo.desc}`
        });
        return;
      }
      this.$set(subPermissionInfo, 'checked', !subPermissionInfo.checked);
    },
    toBizPage() {
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010042', initJumpMode: '0' });
      });
    },
    submit() {
      const profPremCodeCheck = ['03', '04']; // 仅针对专业投资者
      const profCheck = this.accountInfo.permissionList.some(
        ({ permissionCode, checked }) =>
          checked && profPremCodeCheck.includes(permissionCode)
      );
      if (profCheck && this.profFlag === '0') {
        this.$TAlert({
          title: '温馨提示',
          tips: '您启用了特殊交易权限，此权限仅针对专业投资者进行开放，请先进行专业投资者认定',
          hasCancel: true,
          confirmBtn: '立即认定',
          cancelBtn: '退出认定',
          confirm: this.toBizPage
        });
        return;
      }
      let permissionData = this.cloneDeep(this.accountInfo);
      permissionData.permissionList = permissionData.permissionList.map(
        (it) => {
          it.checked = it.checked ? '1' : '0'; //字段值转换，1已勾选 0没有勾选
          if (it.subPermission && it.subPermission.length !== 0) {
            it.subPermission = it.subPermission.map((a) => {
              a.checked = a.checked ? '1' : '0';
              return a;
            });
          }
          return it;
        }
      );
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        selectedAccountsData: JSON.stringify({
          permissionData
        })
      });
    }
  }
};
</script>

<style scoped lang="less">
.pb0 {
  padding-bottom: 0rem;
}
.no_data_content {
  text-align: center;
  color: var(--typography-333, #333);
  font-size: 0.16rem;
  line-height: 0.24rem;
  .no_data_icon {
    width: 1.8rem;
    height: 1.54rem;
    margin-top: 0.5rem;
    margin-bottom: 0.2rem;
  }
}
</style>
