<template>
  <fragment>
    <div class="com_title flex">
      <h5>请选择可转债策略类型</h5>
      <a class="com_link" @click="showMore">策略类型说明</a>
    </div>
    <div class="com_box">
      <div class="input_form form_tit_right">
        <div class="input_text text" @click="popupShow = true">
          <span class="tit active">可转债策略</span>
          <div class="dropdown" placeholder="请选择">
            {{ selectData.desc }}
          </div>
        </div>
      </div>
    </div>
    <div
      class="cm_sele_wrap pb16"
      v-show="selectData.strategyList.length !== 0"
    >
      <ul class="cm_sele_list right">
        <li
          v-for="(it, i) in selectData.strategyList"
          :key="i"
          @click="select(it)"
        >
          <div class="layout">
            <span class="icon_check" :class="{ checked: it.checked }"></span>
            <p>{{ it.desc }}</p>
          </div>
        </li>
      </ul>
      <div class="notes_input" v-show="otherFlag">
        <textarea
          v-model="otherContent"
          placeholder="请简要解释其他策略主要内容"
          maxlength="200"
        ></textarea>
      </div>
    </div>
    <div class="main fixed" v-show="popupShow">
      <van-popup v-model="popupShow" round position="bottom">
        <v-picker
          v-model="selectData.value"
          :columns="strategyList"
          @onConfirm="pickerCallback"
          @onCancel="popupShow = false"
        />
      </van-popup>
    </div>
  </fragment>
</template>

<script>
import VPicker from '@/components/VPicker';
import { EVENT_NAME } from '@/common/formEnum';
import { accountStrategyQuery } from '@/service/service';

export default {
  name: 'CBondStrategySelect',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {
    VPicker
  },
  props: {
    //系统编号
    sysNo: {
      type: String,
      default: ''
    },
    //此前选择的账户信息
    selectedAccountsData: {
      type: String,
      default: ''
    }
  },
  computed: {
    otherFlag() {
      return this.selectData?.strategyList?.some(
        ({ checked, desc }) => checked && desc === '其它'
      );
    },
    nextFlag() {
      return this.selectData?.strategyType !== '';
    }
  },
  watch: {
    //策略类选择其他时，输入1~200位字符（1~100个汉字），支持输入中英文及特殊字符
    otherContent(val) {
      const inputValue = val;
      const chineseCount = (inputValue.match(/[\u4e00-\u9fa5]/g) || []).length;
      const charCount = inputValue.length;
      // 判断是否含有emoji表情
      const iconRule =
        /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi;
      if (iconRule.test(inputValue)) {
        this.otherContent = inputValue.replace(iconRule, '');
        return;
      }
      if (chineseCount > 100 || charCount > 200) {
        this.otherContent = inputValue.slice(0, -1);
      }
    },
    nextFlag: {
      handler(nv) {
        if (nv) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 2,
            data: this.submit
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      popupShow: false,
      selectData: {
        strategyType: '',
        desc: '',
        checkFlag: '',
        strategyList: []
      },
      otherContent: '',
      strategyList: [] //策略信息集合
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      accountStrategyQuery({})
        .then(({ data: { strategyList } }) => {
          this.strategyList = strategyList.map((it) => {
            return {
              label: it.desc,
              value: it.strategyType,
              strategyList: it.strategyList || [],
              ...it
            };
          });
          const checkData = this.strategyList
            .filter(({ checkFlag }) => checkFlag === '1')
            .map(({ checked, ...it }) => {
              it.checked = '1';
              return it;
            })[0];
          if (checkData) this.selectData = { ...checkData };
        })
        .catch((err) => {
          this.$TAlert({
            title: '温馨提示',
            tips: err
          });
        });
    },
    select(it) {
      this.$set(it, 'checked', !it.checked);
    },
    //如选择策略类，默认选中基本面分析策略
    pickerCallback({ ...it }) {
      if (it.strategyType === '02') {
        it.strategyList = it.strategyList.map((h) => {
          if (h.desc.includes('基本面分析策略')) this.$set(h, 'checked', true);
          return h;
        });
      }
      this.selectData = { ...it };
    },
    showMore() {
      this.$TAlert({
        title: '可转债策略类型说明',
        tipsHtml:
          '<div class="qx_exp_box">\
            <p>主要分为执行类和策略类。</p>\
            <h5 class="tit">执行类：</h5>\
            <p>指算法交易，即策略以人工判断为主，程序化主要用于订单执行层面，包括时间加权平均价格算法（TWAP）、成交量加权平均价格算法（VWAP）、比例成交算法（POV）等算法交易；</p>\
            <h5 class="tit">策略类：</h5>\
            <p>指策略判断层面和订单执行层面均以程序化为主的交易，主要分为基本面分析策略（Alpha策略）、日内回转策略、套利类策略、趋势类策略、做市类策略和其它。</p>\
            <p>如选择策略类，需在以下选项中进一步选择填写具体策略类型（可多选），选择“其它”的需简要解释策略主要内容：</p>\
            <p>1、基本面分析策略（Alpha策略）。精选行业、个股、产品，寻找被市场低估的产品，以获取超越大盘收益的策略。</p>\
            <p>2、日内回转策略。指日内回转交易，获取日内波动价差的策略。</p>\
            <p>3、套利类策略。指利用一个或多个市场存在的价格差异，或市场价格与理论价格存在差异获利的策略。</p>\
            <p>4、趋势类策略。指利用量价等微观信息预测未来短期价格走势，并根据走势生成相应交易指令的策略。</p>\
            <p>5、做市类策略。指为市场提供流动性，撮合买卖报价赚取盘口价差的策略。</p>\
            <p>6、其它。简要解释策略主要内容。</p>\
          </div>'
      });
    },
    submit() {
      if (
        this.selectData.strategyList.length !== 0 &&
        !this.selectData.strategyList.some(({ checked }) => checked)
      ) {
        this.$TAlert({
          title: '温馨提示',
          tips: '请至少选择一项具体策略类型'
        });
        return;
      }
      if (this.otherFlag) {
        if (this.otherContent === '') {
          this.$TAlert({
            title: '温馨提示',
            tips: '请简要解释其他策略的主要内容'
          });
          return;
        } else {
          this.selectData.strategyList = this.selectData.strategyList.map(
            (it) => {
              if (it.checked && it.desc === '其它') {
                it.other = this.otherContent;
                return it;
              } else {
                return it;
              }
            }
          );
        }
      } else {
        this.selectData.strategyList = this.selectData.strategyList.map(
          ({ other, ...it }) => it
        );
      }

      let strategyData = this.cloneDeep(this.selectData);
      strategyData.strategyList = strategyData.strategyList.map((it) => {
        it.checked = it.checked ? '1' : '0'; //字段值转换，1已勾选 0没有勾选
        return it;
      });
      const prevSelectData = JSON.parse(this.selectedAccountsData);
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        selectedAccountsData: JSON.stringify({
          strategyData,
          ...prevSelectData
        })
      });
    }
  }
};
</script>

<style></style>
