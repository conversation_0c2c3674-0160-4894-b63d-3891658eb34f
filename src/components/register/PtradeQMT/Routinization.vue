<template>
  <PTQuestionnaire :sys-no="sysNo" @success="success" />
</template>

<script>
import PTQuestionnaire from '@/components/routinization/PTQuestionnaire.vue';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'Routinization',
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {
    //系统编号
    sysNo: {
      type: String,
      default: ''
    }
  },
  components: {
    PTQuestionnaire
  },
  data() {
    return {};
  },
  mounted() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
  },
  methods: {
    success() {
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, { validFlag: '1' });
    }
  }
};
</script>

<style></style>
