<template>
  <fragment>
    <div class="com_title">
      <h5>请选择要开通的系统</h5>
    </div>
    <ul class="type_navlist">
      <li
        v-for="({ sysCode, desc, title }, k) in viewList"
        :key="k"
        @click="select(sysCode)"
      >
        <h5>{{ title }}</h5>
        <p>{{ desc }}</p>
      </li>
    </ul>
  </fragment>
</template>

<script>
import { querySysConfig, sysOpenPreCheck } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import { DICT_TYPE } from '@/common/enumeration';

export default {
  name: 'SysOpenChoice',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      viewList: []
    };
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      const configKey = DICT_TYPE.PTRADEQMT_SYS_CONFIG;
      querySysConfig({ configKey })
        .then(({ data, code, msg }) => {
          if (code === 0) {
            try {
              this.viewList = JSON.parse(data[configKey].configValue);
            } catch (error) {
              console.error(error);
              this.viewList = [];
            }
          } else {
            return Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            mes: err
          });
        });
    },
    async select(sysNo) {
      try {
        if (sysNo === 'QMT') sysNo = 'Qmt';
        const { data } = await sysOpenPreCheck({
          flowToken: sessionStorage.getItem('TKFlowToken'),
          sysNo
        });
        if (Object.keys(data).length === 0 || data.title === null) {
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
            sysNo,
            selectedAccountsData: ''
          });
        } else {
          throw { title: data.title, tips: data.tips };
        }
      } catch ({ tips = '', ...e }) {
        const alertConfig =
          tips === ''
            ? {
                title: '温馨提示',
                tips: e.message || e.msg || '系统异常'
              }
            : { tips, ...e };

        this.$TAlert(alertConfig);
      }
    }
  }
};
</script>

<style></style>
