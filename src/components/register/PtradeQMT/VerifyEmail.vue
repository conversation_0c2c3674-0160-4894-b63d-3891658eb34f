<template>
  <div class="info_compage">
    <div class="com_title">
      <h5>请输入您的电子邮箱</h5>
    </div>
    <div class="input_form">
      <div class="input_text text">
        <span class="tit active">电子邮箱</span>
        <input
          class="t1"
          type="text"
          v-model="targetEmail"
          placeholder="请输入电子邮箱"
          maxlength="64"
        />
      </div>
    </div>
    <div class="acct_list_tips">
      <p>
        为了您后续能正常接收权限开通后的客户端下载链接与账户相关信息，请完整填写正确的电子邮箱内容。
      </p>
    </div>
  </div>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
import { rules } from '@/common/rule';
import { clientInfoQry } from '@/service/service';

export default {
  name: 'VerifyEmail',
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {
    // 电子邮箱
    email: {
      type: String,
      default: ''
    }
  },
  computed: {
    nextFlag() {
      return this.targetEmail !== '';
    }
  },
  watch: {
    nextFlag: {
      handler(nv) {
        if (nv) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 2,
            data: this.submit
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      targetEmail: ''
    };
  },
  mounted() {
    this.$nextTick(() => {
      if (this.email !== '') {
        this.targetEmail = this.email;
      } else {
        clientInfoQry({})
          .then(({ data }) => {
            this.targetEmail = data.email;
          })
          .catch((err) => {
            this.$TAlert({
              title: '温馨提示',
              tips: err
            });
          });
      }
    });
  },
  methods: {
    submit() {
      const emailRules = rules.email;
      if (!emailRules.validate(this.targetEmail)) {
        _hvueToast({
          mes: emailRules.getMessage()
        });
        return false;
      }
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        email: this.targetEmail
      });
    }
  }
};
</script>

<style></style>
