<template>
  <section
    class="main fixed qq_bg"
    data-page="home"
    style="position: fixed; z-index: 200"
  >
    <header class="header fixed_header">
      <div class="header_inner">
        <a class="icon_back" @click="back"></a>
      </div>
    </header>
    <article class="content">
      <div class="qq_hm_page">
        <div class="qq_bannerbox">
          <img src="@/assets/images/qq_banner02.png" />
        </div>
        <div class="qq_hm_cont">
          <div class="qq_hm_title">
            <h5><i></i><span>填写期权预约信息</span><i></i></h5>
          </div>
          <div class="qq_hm_form">
            <div class="s_input_item">
              <div class="tit">姓名</div>
              <div class="ct">
                <input
                  class="t1"
                  type="text"
                  placeholder="请输入"
                  v-model="clientName"
                  readonly
                />
              </div>
            </div>
            <div class="s_input_item">
              <div class="tit">预约手机号</div>
              <div class="ct">
                <input
                  class="t1"
                  type="tel"
                  maxlength="11"
                  placeholder="请输入"
                  v-model="phoneNumInput"
                  readonly
                />
                <a class="code_btn" @click="changeMobile">修改</a>
              </div>
            </div>
            <div class="s_input_item">
              <div class="tit">验证码</div>
              <div class="ct">
                <input
                  v-model="smsCode"
                  @input="smsCode = smsCode.replace(/[^\d]/g, '')"
                  class="t1"
                  type="tel"
                  maxlength="6"
                  placeholder="请输入"
                  autocomplete="off"
                />
                <sms-code-btn
                  v-model="uuid"
                  :need-img-code="false"
                  :mobile-no="mobileTel"
                  @send-result="SMSCodeCallback"
                />
              </div>
            </div>
            <div class="s_input_item">
              <div class="tit">营业部</div>
              <div class="ct">
                <div
                  class="dropdown"
                  placeholder="请选择"
                  @click="showSelect = true"
                >
                  {{ selectData ? selectData.label : '' }}
                </div>
              </div>
            </div>
            <div class="s_input_item">
              <div class="tit">资金账号</div>
              <div class="ct">
                <input
                  class="t1"
                  type="text"
                  placeholder="请输入"
                  v-model="fundAccount"
                  readonly
                />
              </div>
            </div>
            <div class="s_input_item">
              <div class="tit">开户推荐人</div>
              <div class="ct">
                <input
                  class="t1"
                  type="text"
                  placeholder="选填"
                  maxlength="60"
                  v-model.trim="recommendNo"
                />
              </div>
            </div>
            <div class="s_form_tips">
              短信验证码收不到？试试<a class="com_link" @click="smsVoice"
                >语音验证码</a
              >吧！
            </div>
            <div class="ce_btn">
              <a class="p_button" :class="{ disabled }" @click="submit"
                >申请开通</a
              >
            </div>
          </div>
          <div class="qq_hm_tips">
            <div class="txt">
              <p>办理小贴士：</p>
              <p>
                1、投资者风险承受能力为C3及以上且有效期一年以上(一级交易权限:C3及以上或者专业投资者;二、三级交易权限均需为C4及以上或者专业投资者);
              </p>
              <p>
                2、申请开户前20个交易日日均托管在公司的证券市值与资金账户可用余额(不含通过融资融券交易融入的资金或证券)，合计不低于人民币50万元;
              </p>
              <p>
                3、在证券公司开户6个月以上并具备融资融券参与资格或者金融期货交易经历;
              </p>
              <p>4、具备期权基础知识，通过交易所认可的相应等级知识测试；</p>
              <p>5、具有交易所认可的期权模拟交易经历;</p>
              <p>
                6、不存在严重不良诚信记录，不存在法律、法规、规章和交易所业务规则禁止或者限制从事期权交易的情形;
              </p>
              <p>
                已开立正常状态的沪市衍生品合约账户、风险承受能力C3及以上且有效期在一年以上、满足上述第6项条件的个人投资者，开立深市期权账户时视同符合本条要求。
              </p>
            </div>
            <div class="txt">
              <p>温馨提示:</p>
              <p>1、提交手机号码即同意国金证券通过该手机号码为您提供后续服务</p>
              <p>2、提交信息后，会有专人与您联系，协助您办理期权业务。</p>
            </div>
            <div>
              <p>
                客服热线：<span class="com_link" @click="callTelphone"
                  >95310</span
                >
              </p>
              <p>投资有风险，入市需谨慎</p>
            </div>
          </div>
        </div>
      </div>
    </article>
    <sel-box
      v-model="showSelect"
      title="请选择"
      :initData="branchList"
      @selCallback="selCallback"
    />
  </section>
</template>

<script>
import selBox from '@/components/selBox';
import SmsCodeBtn from '@/components/SmsCodeBtn.vue';
import {
  smsCodeVerification,
  businessAcceptInfoQry,
  getSysBranchInfo,
  clientInfoQryV2
} from '@/service/service';
import { formatMobileNo } from '@/common/filter';
import { EVENT_NAME } from '@/common/formEnum';
import HmosUtil from '@/common/HmosUtil';
const hmosUtil = new HmosUtil({});

export default {
  name: 'ReserveSHOptAccount',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {
    SmsCodeBtn,
    selBox
  },
  data() {
    return {
      clientName: '',
      mobileTel: '',
      smsCode: '',
      fundAccount: '',
      uuid: '',
      recommendNo: '',
      branchNo: '',
      branchList: [],
      showSelect: false,
      selectData: null
    };
  },
  computed: {
    phoneNumInput() {
      return formatMobileNo(this.mobileTel);
    },
    disabled() {
      return this.smsCode === '' || this.uuid === '';
    }
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      const { inProperty } = this.tkFlowInfo();
      const { fundAccount } = inProperty;
      clientInfoQryV2()
        .then(({ data }) => {
          this.clientName = data.clientName;
          this.mobileTel = data.mobileTel;
          this.fundAccount = fundAccount;
          return getSysBranchInfo({});
        })
        .then(({ data }) => {
          this.branchList = data.map((it) => {
            return {
              label: it.branchName,
              value: it.branchNo,
              ...it
            };
          });
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    },
    back() {
      this.eventMessage(this, EVENT_NAME.PREV_FLOW);
    },
    submit() {
      if (this.disabled) return;
      if (!this.uuid) {
        this.$TAlert({
          title: '温馨提示',
          tips: '请先发送短信验证码'
        });
        return false;
      }
      if (this.smsCode.length !== 6) {
        this.$TAlert({
          title: '温馨提示',
          tips: '短信验证码不匹配'
        });
        return false;
      }
      if (this.branchNo === '') {
        this.$TAlert({
          title: '温馨提示',
          tips: '请先请选择营业部'
        });
        return false;
      }
      if (
        this.recommendNo !== '' &&
        (this.recommendNo.length < 2 || this.recommendNo.length > 60)
      ) {
        this.$TAlert({
          title: '温馨提示',
          tips: '请填写推荐人信息'
        });
        return false;
      }
      smsCodeVerification({
        mobile: this.mobileTel,
        captchaCode: this.smsCode,
        serialNumber: this.uuid
      })
        .then((res) => {
          if (res.data.verificationvFlag !== '1') {
            this.$TAlert({
              title: '温馨提示',
              tips: '输入的验证码有误，请重新输入'
            });
            this.smsCode = '';
            return;
          }
          const { inProperty } = this.tkFlowInfo();
          const { bizType } = inProperty;
          businessAcceptInfoQry({
            flowToken: sessionStorage.getItem('TKFlowToken'),
            bizType,
            queryStatus: '1,3'
          }).then(({ data }) => {
            const { flowInsList } = data;
            if (flowInsList.length !== 0) {
              this.$TAlert({
                title: '温馨提示',
                tips: '您近期已完成了预约，请及时前往营业部进行办理。如有疑问可拨打客服95310进行咨询。',
                confirmBtn: '我知道了'
              });
            } else {
              this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
                verif_mobile_tel: this.mobileTel,
                client_name: this.clientName,
                fund_account: this.fundAccount,
                branch_no: this.branchNo,
                recommend_no: this.recommendNo
              });
            }
          });
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    },
    SMSCodeCallback(flag) {
      console.log(flag);
      if (!flag) {
        this.uuid = '';
      }
    },
    changeMobile() {
      // 跳转至个人资料
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010004', initJumpMode: '0' });
      });
    },
    smsVoice() {
      if (!this.uuid) {
        this.$TAlert({
          title: '温馨提示',
          tips: '请先发送短信验证码'
        });
        return false;
      }
      _hvueConfirm({
        title: '温馨提示',
        mes: '亲，您可以使用输入的手机号，拨打95310-按3-按1，收听语音验证码。',
        opts: [
          {
            txt: '取消',
            color: '#333333'
          },
          {
            txt: '立即拨打',
            callback: this.callTelphone
          }
        ]
      });
    },
    callTelphone() {
      if (hmosUtil.checkHM) {
        hmosUtil.callPhone('95310');
      } else if ($hvue.platform === '0') {
        window.location.href = 'tel:95310';
      } else {
        let reqParams = {
          funcNo: '50220',
          telNo: '95310',
          callType: '0'
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      }
    },
    selCallback({ data }) {
      this.branchNo = data.value;
      this.selectData = data;
    }
  }
};
</script>

<style scoped>
.code_btn2 {
  background: #f2f4f8;
}
.qq_hm_form .ce_btn .p_button.disabled {
  box-shadow: none;
}
input.t1[readonly] {
  color: #333333;
}
</style>
