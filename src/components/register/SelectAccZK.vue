<template>
  <section class="main fixed" data-page="home" style="position: fixed">
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" href="javascript:void(0);"></a>
        <h1 class="title">账户查询</h1>
      </div>
    </header>
    <article class="content">
      <div class="com_title">
        <h5>请选择需要开通的账户</h5>
      </div>
      <div
        v-for="(
          { isDisabled, isChecked, tip, accountTypeName, stockAccList }, index
        ) in accountInfoList"
        :key="index"
        class="acct_status_item"
      >
        <div class="tit">
          <span
            class="icon_check"
            :class="{ checked: isChecked, disabled: isDisabled }"
            @click.stop="selectAcc(index)"
            >{{ accountTypeName }}</span
          >
        </div>
        <ul class="acct_list">
          <li
            v-for="({ stockAccount, holderStatusDesc }, i) in stockAccList"
            :key="i"
          >
            <p>{{ stockAccount }}</p>
            <span class="state">{{ holderStatusDesc }}</span>
          </li>
        </ul>
        <div v-if="isDisabled" class="acct_imptips">
          <span>{{ tip }}</span>
        </div>
      </div>
      <agreement-sign
        v-if="getProtocolParam.groupId.length > 0"
        v-model="isCheckedAgree"
        :is-adv-age="isAdvAge"
        :adv-age-param="advAgeParam"
        :group-id="getProtocolParam.groupId.join(',')"
        :contract-type="getProtocolParam.contractType.join(',')"
        @agree-list="agreeCallback"
      />
      <!-- <div class="ce_btn mt20">
      <a v-if="allDisabled" class="p_button" @click="toIndex">返回首页</a>
      <a v-else class="p_button" @click="submitForm">下一步</a>
    </div> -->
    </article>
    <footer class="footer">
      <div class="ce_btn mt20">
        <a v-if="allDisabled" class="p_button" @click="toIndex">返回首页</a>
        <a v-else class="p_button" @click="submitForm">下一步</a>
      </div>
    </footer>
  </section>
</template>

<script>
import { HOLDER_STATUS, IS_OPEN_RIGHTS } from '@/common/enumeration';
import agreementSign from '@/components/agreementSign';
import { stockAccountQry } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import { signAgree } from '@/common/util';
export default {
  name: 'SelectAccZK',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {
    agreementSign
  },
  props: {
    protocolParam: {
      type: String,
      default: ''
    },
    advAgeAgreeParam: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      accountInfoList: [],
      acodeAccount: '', // 一码通账号
      agreeList: [],
      isCheckedAgree: false,
      IS_OPEN_RIGHTS
    };
  },
  computed: {
    allDisabled() {
      return this.accountInfoList.every(({ isDisabled }) => isDisabled);
    },
    advAgeParam() {
      try {
        return JSON.parse(this.advAgeAgreeParam);
      } catch (e) {
        return {};
      }
    },
    getProtocolParam() {
      let param = {
        groupId: [],
        contractType: []
      };
      try {
        let protocolParam = JSON.parse(this.protocolParam);
        let checkList = protocolParam.filter((a) => {
          return (
            (!a.exchangeType && !a.holderKind) ||
            this.openAccList.some((b) => b.exchangeType === a.exchangeType) ||
            this.openAccList.some((b) => b.holderKind === a.holderKind)
          );
        });
        checkList.forEach(({ groupId, contractType }) => {
          param.groupId.push(groupId);
          param.contractType.push(contractType);
        });
        return param;
      } catch (e) {
        console.log(e);
        return param;
      }
    },
    isAdvAge() {
      return this.$attrs.clientAge >= '70';
    },
    openAccList() {
      let arr = [];
      //assetProp 资产属性 0普通
      for (let { isChecked, exchangeType, assetProp = '0', holderKind } of this
        .accountInfoList) {
        if (isChecked) arr.push({ exchangeType, assetProp, holderKind });
      }
      return arr;
    }
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    this.renderingView();
  },
  methods: {
    async renderingView() {
      const { inProperty } = this.tkFlowInfo();
      const { clientId, bizType, branchNo } = inProperty;
      const { capitalAccountKind } = this.$attrs;
      stockAccountQry({
        clientId,
        bizType,
        branchNo,
        capitalAccountKind,
        isQryZd: '1', // 是否查询中登：1 是
        qryType: '1' // 查询类型：0 默认；1 增开；2 加挂
      })
        .then(({ data }) => {
          /*
           * "canIncrease": 0,  // 是否能增开：1 可以，0 不能
           * "canAttach": 0,    // 是否能加挂：1 可以，0 不能
           * */
          this.accountInfoList = data.accountInfoList.map((item) => {
            item.isDisabled = item.canIncrease !== 1;
            return item;
          });
          this.acodeAccount = data.acodeAccount || '';
        })
        .catch((err) => {
          this.$TAlert({
            tips: err,
            confirm: () => {
              this.eventMessage(this, EVENT_NAME.TO_INDEX);
            }
          });
        });
    },

    agreeCallback(data) {
      this.agreeList = data;
    },

    selectAcc(index) {
      let item = this.accountInfoList[index];
      if (item.isDisabled) return;
      if (item.isChecked) {
        this.$set(item, 'isChecked', false);
      } else {
        this.$set(item, 'isChecked', true);
      }
    },

    openFlag({ holderStatus, isOpenRights }) {
      return (
        holderStatus === HOLDER_STATUS.NORMAL &&
        isOpenRights !== IS_OPEN_RIGHTS.possess
      );
    },

    submitForm() {
      if (this.openAccList.length === 0) {
        _hvueToast({
          mes: '请选择需要开通的账户'
        });
        return;
      }
      if (!this.isCheckedAgree) {
        _hvueToast({
          mes: '请阅读并勾选协议'
        });
        return;
      }
      const tkFlowInfo = this.tkFlowInfo();
      signAgree(tkFlowInfo, this.agreeList)
        .then((epaperSignJson) => {
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
            acodeAccount: this.acodeAccount,
            selectedAccountsData: JSON.stringify(this.openAccList),
            epaperSignJson
          });
        })
        .catch((error) => {
          _hvueToast({
            mes: error
          });
        });
    },
    toIndex() {
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    }
  }
};
</script>

<style scoped></style>
