<template>
  <footer class="footer">
    <slot>
      <div class="ce_btn">
        <a
          class="p_button"
          :class="{ disabled: disabled }"
          @click="toNextBtn"
          >{{ buttonTxt || '下一步' }}</a
        >
      </div>
    </slot>
  </footer>
</template>

<script>
export default {
  name: 'TFooter',
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    buttonTxt: {
      type: String,
      default: '下一步'
    }
  },
  data() {
    return {};
  },
  methods: {
    toNextBtn() {
      this.$emit('triggerEvent');
    }
  }
};
</script>

<style scoped></style>
