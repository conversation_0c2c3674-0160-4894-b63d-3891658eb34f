<template>
  <header class="header" :class="fixedHeader ? 'fixed_header' : ''">
    <div class="top_height"></div>
    <div class="header_inner">
      <a v-if="showBack" class="icon_back" @click="back"></a>
      <h1 v-if="showTitle" class="title">{{ title || $route.meta.title }}</h1>
      <slot></slot>
      <a v-if="showClose" class="icon_close" @click="logout"></a>
      <a v-if="showHomeBtn" class="icon_home" @click="toIndex"></a>
    </div>
    <div v-if="showProgressBar" class="step_box">
      <div class="item">
        <b :style="{ width: percentCount + '%' }">
          <span>{{ percentCount + '%' }}</span>
        </b>
      </div>
    </div>
  </header>
</template>

<script>
import { setPageTitle } from 'thinkive-hvue';
export default {
  name: 'THeader',
  props: {
    title: {
      type: String,
      default: ''
    },
    showTitle: {
      type: Boolean,
      default: true
    },
    showBack: {
      type: Boolean,
      default: true
    },
    showProgressBar: {
      type: Boolean,
      default: false
    },
    barTitle: {
      type: String,
      default: ''
    },
    percent: {
      type: Number,
      default: 0
    },
    isNode: {
      type: Boolean,
      default: false
    },
    showHomeBtn: {
      type: Boolean,
      default: false
    },
    fixedHeader: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showClose: false,
      percentCount: 0
    };
  },
  watch: {
    title: {
      handler(newTitle = '') {
        if (newTitle) {
          // document.title = newTitle;
          this.$route.meta.title = newTitle;
          console.info('页面标题', this.title || this.$route.meta.title);
          setPageTitle(this.title || this.$route.meta.title);
          if ($h.getSession('channelType') === '2005000000000') {
            callNativeHandler(
              'JSWangTingEvent',
              {
                action: 'SetWebVCTitle',
                param: {
                  title: this.title || this.$route.meta.title
                }
              },
              function () {}
            );
            return;
          }
        }
      },
      immediate: true
    }
  },
  created() {},
  methods: {
    back(e) {
      this.$emit('back', e);
    },
    logout() {},
    toIndex() {
      this.$emit('toIndex');
    }
  }
};
</script>
