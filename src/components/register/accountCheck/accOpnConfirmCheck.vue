<template>
  <div v-if="loading" class="acct_nodata">
    <div class="icon">
      <img :src="require('@/assets/images/noData2.svg')" />
    </div>
    <h5>没有查到您的开户确认单信息</h5>
  </div>
</template>

<script>
import { accountConfirmFormCheck } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'AccOpnConfirmCheck',
  inject: ['eventMessage'],
  data() {
    return {
      loading: false
    };
  },
  created() {
    this.$store.commit('flow/setWhiteBg', true);
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
  },
  mounted() {
    this.renderingView();
  },
  destroyed() {},
  methods: {
    renderingView() {
      accountConfirmFormCheck({})
        .then(({ data }) => {
          if (data?.checkStatus === '1') {
            this.eventMessage(this, EVENT_NAME.NEXT_STEP);
          } else {
            this.loading = true;
          }
        })
        .catch((err) => {
          this.$TAlert({
            title: '温馨提示',
            tips: err
          });
        });
    }
  }
};
</script>
