<template>
  <section
    v-if="!loading"
    class="main fixed white_bg"
    data-page="home"
    style="position: fixed"
  >
    <t-header></t-header>
    <article class="content">
      <div v-if="pdfSrc === ''" class="acct_nodata">
        <div class="icon">
          <img :src="require('@/assets/images/noData2.svg')" />
        </div>
        <h5>没有查到您的开户确认单信息</h5>
      </div>
      <div v-else class="vertical_layout" style="width: 100%; height: 100%">
        <div
          v-if="pdfSrc !== ''"
          class="file_scrollbox"
          style="width: 100%; height: 100%"
        >
          <tk-pdf :base64="pdfSrc" />
        </div>
        <div class="file_sendform">
          <div class="input_form spel form_tit_right">
            <div class="input_text text">
              <span class="tit active">接收手机号</span>
              <div class="input_ct">
                <div class="t1_layout">
                  <input
                    v-model="mobileTel"
                    class="t1 disabled"
                    type="text"
                    disabled="disabled"
                  />
                  <a class="com_link" @click.stop="jumpPage">{{
                    !emptyMobile ? (!errorMobile ? '修改' : '去修改') : '去完善'
                  }}</a>
                </div>
              </div>
            </div>
          </div>
          <div class="tip_txtbox">
            <p v-if="!errorMobile">您当天最多可提交{{ count }}次申请!</p>
            <p v-else-if="emptyMobile">
              未查询到您的手机号，请点击去完善进行补充后再提交申请。
            </p>
            <p v-else>查询到您的手机号格式不正确，请点击去修改后再提交申请。</p>
          </div>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn block">
        <a
          class="p_button"
          :class="{ disabled: errorMobile || emptyMobile }"
          @click.stop="sendMessage"
          >提交申请</a
        >
      </div>
    </footer>
  </section>
</template>

<script>
import {
  flowSubmit,
  generateAccountConfirmForm,
  clientInfoQry
} from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import { formatMobileNo } from '@/common/filter';
import tkPdf from '@/plugins/vue-pdf/tkPdf.vue';

export default {
  name: 'AccOpnConfirmView',
  inject: ['eventMessage'],
  components: {
    tkPdf
  },
  props: {
    branchNo: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      pdfSrc: '',
      pdfDoc: null,
      loading: true,
      numPages: 0,
      mobile: '',
      count: ''
    };
  },
  computed: {
    mobileTel() {
      return formatMobileNo(this.mobile);
    },
    errorMobile() {
      return !/1[3-9][\d]{9}/.test(this.mobile);
    },
    emptyMobile() {
      return this.mobile === '';
    }
  },
  watch: {
    branchNo: {
      handler(newVal) {
        if (newVal) {
          this.renderingView();
        }
      },
      immediate: true
    }
  },
  created() {
    window.viewShowCallBack = this.renderingView;
    this.$store.commit('flow/setWhiteBg', true);
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
      display: false
    });
  },
  methods: {
    formatMobileNo,
    renderingView() {
      this.pdfSrc = '';
      this.mobile = '';
      this.loading = true;
      clientInfoQry()
        .then((res) => {
          if (res.code === 0) {
            this.mobile = res.data.mobileTel.trim();
            return generateAccountConfirmForm({
              branchNo: this.branchNo,
              flowToken: sessionStorage.getItem('TKFlowToken')
            });
          } else {
            return Promise.reject(res.msg);
          }
        })

        .then(({ data }) => {
          if (data && data.base64) {
            this.pdfSrc = data.base64;
            this.count = data.khqrdSubmit;
            /* let blob = this.base64ToBlob(data.base64);
             let fileUrl = '';
             if (window.createObjectURL !== undefined) {
               fileUrl = window.createObjectURL(blob);
             } else if (window.webkitURL !== undefined) {
               // webkit or chrome
               try {
                 fileUrl = window.webkitURL.createObjectURL(blob);
               } catch (error) {
                 console.log(error);
               }
             } else if (window.URL !== undefined) {
               // mozilla(firefox)
               try {
                 fileUrl = window.URL.createObjectURL(blob);
               } catch (error) {
                 console.log(error);
               }
             }
             this.pdfSrc = fileUrl;*/
          }
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
          this.$TAlert({
            title: '温馨提示',
            tips: err
          });
        });
    },
    base64ToBlob(code) {
      code = code.replace(/[\n\r]/g, '');
      // atob() 方法用于解码使用 base-64 编码的字符串。
      const raw = window.atob(code);
      const rawLength = raw.length;
      const uInt8Array = new Uint8Array(rawLength);
      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i);
      }
      return new Blob([uInt8Array], { type: 'application/pdf' });
    },
    sendMessage() {
      if (this.errorMobile || this.emptyMobile) return;
      const flowToken = sessionStorage.getItem('TKFlowToken');
      const object = {
        mobileTel: this.mobile,
        clientId: this.$attrs.clientId,
        branchNo: this.branchNo
      };
      flowSubmit({ flowToken, object })
        .then(() => {
          _hvueToast({
            mes: '提交成功',
            callback: () => {
              this.eventMessage(this, EVENT_NAME.TO_INDEX);
            }
          });
        })
        .catch((err) => {
          this.$TAlert({
            title: '温馨提示',
            tips: err
          });
        });
    },
    jumpPage() {
      // 跳转至个人资料
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010004', initJumpMode: '0' });
      });
    }
  }
};
</script>
<style scoped>
.protocol_pdf {
  width: 100%;
}
</style>
