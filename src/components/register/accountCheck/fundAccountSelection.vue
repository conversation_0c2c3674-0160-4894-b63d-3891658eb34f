<template>
  <div v-if="!loading" class="info_compage">
    <div class="com_title">
      <h5>
        您有如下账号，申请密码重置会对所有账号有效，本地重置密码账号如下：
      </h5>
    </div>
    <ul class="com_infolist">
      <li
        v-for="(
          { assetPropName, mainFlagName, fundAccount }, i
        ) in fundAccountList"
        :key="i"
      >
        <span class="tit">{{ assetPropName }}（{{ mainFlagName }}）</span>
        <p>{{ fundAccount }}</p>
      </li>
    </ul>
  </div>
</template>

<script>
import { fundAccountListQry } from '@/service/service';
import { ASSET_PROP } from '@/common/enumeration';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'FundAccountSelection',
  inject: ['eventMessage', 'setPropsByForm'],
  data() {
    return {
      loading: true,
      fundAccountList: [],
      ASSET_PROP
    };
  },
  created() {
    this.renderingView();
  },
  destroyed() {},
  methods: {
    getMainFlagMap(v) {
      let getMap = new Map();
      getMap.set('1', '主');
      getMap.set('0', '辅');
      return getMap.get(v) || '';
    },
    getAssetPropMap(v) {
      let getMap = new Map();
      getMap.set(ASSET_PROP.DERIVATIVES_ACCOUNT, '期权');
      getMap.set(ASSET_PROP.ORDINARY_ACCOUNT, '普通');
      getMap.set(ASSET_PROP.CREDIT_ACCOUNT, '信用');
      getMap.set(ASSET_PROP.OPTIONS_ACCOUNT, '期权');
      getMap.set(ASSET_PROP.FUND_ACCOUNT, '基金');
      return getMap.get(v) || '';
    },
    renderingView() {
      fundAccountListQry({})
        .then(({ data }) => {
          this.loading = false;
          this.fundAccountList = data.fundAccountList
            .map((item) => {
              item.mainFlagName = this.getMainFlagMap(item.mainFlag);
              item.assetPropName = `${this.getAssetPropMap(
                item.assetProp
              )}资金账户`;
              return item;
            })
            .filter(({ fundAccountStatus }) => fundAccountStatus === '0');
          const fundAccountData = data.fundAccountList
            .filter(({ fundAccountStatus }) => fundAccountStatus === '0')
            .map(({ assetProp, fundAccount, specialFlag }) => {
              return {
                asset_prop: assetProp, //资产属性
                fund_account: fundAccount, //资金账号
                pwd_linkage_flag: specialFlag //密码联动标识：0 公共；1 独立；
              };
            });
          const { loginFundAccount = '' } = this.$store.state.user.userInfo;
          let formData = {
            fundAccount: loginFundAccount,
            fundAccountData: JSON.stringify(fundAccountData)
          };
          if (
            this.fundAccountList.length === 1 &&
            this.fundAccountList[0].mainFlag === '1'
          ) {
            this.eventMessage(this, EVENT_NAME.NEXT_STEP, formData);
          } else {
            this.$emit('change', formData);
          }
        })
        .catch((err) => {
          this.$TAlert({
            title: '温馨提示',
            tips: err
          });
        });
    }
  }
};
</script>
