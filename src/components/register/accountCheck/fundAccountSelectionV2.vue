<template>
  <article v-if="!loading" class="content" style="background: #f5f6fa">
    <template v-if="fundAccountList.length > 0">
      <div class="com_title">
        <h5>信用资金账户</h5>
      </div>
      <div class="acct_status_item">
        <ul class="acct_list">
          <li
            v-for="(
              {
                fundAccount,
                assetProp,
                specialFlag,
                fundAccountStatusDesc,
                isChecked,
                available,
                isOpenRights
              },
              i
            ) in fundAccountList"
            :key="i"
            @click.stop="
              selectAcc({
                fundAccount,
                assetProp,
                specialFlag,
                isChecked,
                available,
                i
              })
            "
          >
            <span
              class="icon_check"
              :class="{ checked: isChecked, disabled: !available }"
            >
              {{ fundAccount }}
              <em class="acct_s_tag">{{ fundAccountStatusDesc }} </em>
            </span>
            <span class="state" v-show="!$attrs.needCancel && isOpenRights === '已开通'">
              {{ isOpenRights }}
            </span>
          </li>
        </ul>
      </div>
      <div
        v-if="fundAccountList[0].isOpen == '1' && !$attrs.needCancel"
        class="tip_txtbox spel"
      >
        <p>
          您需要取消权限，请点击
          <a class="link_right_arrow" @click="toBizType($attrs.cancelBiztype)"
            >取消权限</a
          >
        </p>
      </div>
      <div v-if="tips" class="tip_txtbox spel" v-html="tips" />
    </template>
    <div
      v-else-if="creditFundAccountExist === '0'"
      style="height: 100vh; background: #ffffff; text-align: center"
    >
      <div class="acct_nodata">
        <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
        <h5>
          您当前暂未开通信用账户，请前往开通信用账户，如有疑问可咨询客服95310。
        </h5>
      </div>
      <div class="bus_txtbox">
        <p>
          如何开通信用账户?
          <a class="link_right_arrow" @click="jumpBusiness('010174')">前往开通</a>
        </p>
      </div>
    </div>
    <div
      v-else-if="creditBankAccountExist === '0'"
      style="height: 100vh; background: #ffffff; text-align: center"
    >
      <div class="acct_nodata">
        <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
        <h5>
          您当前暂未开通信用三方存管，请先开通信用三方存管后再进行业务办理，如有疑问可咨询客服95310。
        </h5>
      </div>
      <div class="bus_txtbox">
        <p>
          <a class="link_right_arrow" @click="jumpBusiness('010294')"
            >前往开通信用三方存管</a
          >
        </p>
      </div>
    </div>
    <div v-else style="height: 100vh; background: #ffffff; text-align: center">
      <div class="acct_nodata">
        <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
        <h5>
          您当前信用账户异常，暂无法进行业务办理，如有疑问可咨询客服95310。
        </h5>
      </div>
    </div>
  </article>
</template>

<script>
import {
  fundAccountLrhyQry,
  creditAndFundPwdCheck,
  queryCreditFundAccount
} from '@/service/service';
import { creditAccountAndBankQueryV2 } from '@/service/lrService';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'FundAccountSelectionV2',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      bizType: '',
      fundAccountList: [],
      loading: true,
      btmTips: '',
      tips: this.$attrs.tips,
      creditFundAccountExist: '', //信用资金账户是否存在：1 存在；0 不存在
      creditFundAccountStatus: '', //信用资金账户状态：1 正常 0 异常
      creditBankAccountExist: '' //信用存管账户是否存在：1 存在；0 不存在
    };
  },
  computed: {
    openAccList() {
      let arr = [];
      for (let { fundAccount, assetProp, specialFlag, isChecked } of this
        .fundAccountList) {
        if (isChecked) {
          arr.push({
            asset_prop: assetProp,
            fund_account: fundAccount,
            pwd_linkage_flag: specialFlag
          });
        }
      }
      return arr;
    }
  },
  watch: {
    openAccList: {
      handler(list) {
        if (!this.fundAccountList.length) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
        } else {
          if (list.length > 0) {
            this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
              text: '下一步',
              display: true,
              btnStatus: 2,
              data: () => {
                creditAndFundPwdCheck().then((res) => {
                  let specialFlag = res.data.specialFlag || '0';
                  if (this.$attrs.needCancel) {
                    this.needCancelConfirm(specialFlag);
                  } else {
                    this.submitFlowData(specialFlag);
                  }
                });
              }
            });
          } else {
            this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
          }
        }
      }
    }
  },
  created() {
    window.viewShowCallBack = this.viewShowCallBack;
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  mounted() {
    this.viewShowCallBack();
  },
  methods: {
    toBizType(bizType) {
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType, initJumpMode: '0' });
      });
    },

    needCancelConfirm(flag = '') {
      const { bizName } = this.tkFlowInfo();
      this.$TAlert({
        title: '温馨提示',
        tips: `请确认是否需要${bizName}`,
        hasCancel: true,
        confirmBtn: '确认',
        confirm: () => {
          this.submitFlowData(flag);
        }
      });
    },

    submitFlowData(creditTPwdSameFlag = '') {
      const selectedAccountsData = this.openAccList;
      if (selectedAccountsData?.length > 0) {
        this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
          fundAccountData: JSON.stringify(selectedAccountsData),
          credit_t_pwd_same_flag: creditTPwdSameFlag
        });
      }
    },

    viewShowCallBack() {
      this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      this.bizType = bizType;
      creditAccountAndBankQueryV2({})
        .then(({ data = {}, code, msg }) => {
          if (code === 0) {
            const {
              creditFundAccountExist = '0', //信用资金账户是否存在：1 存在；0 不存在
              creditFundAccountStatus = '0', //信用资金账户状态：1 正常 0 异常
              creditBankAccountExist = '0' //信用存管账户是否存在：1 存在；0 不存在
            } = data;
            const checkArray = [
              creditFundAccountExist,
              creditFundAccountStatus,
              creditBankAccountExist
            ].some((a) => a !== '1');
            if (checkArray) {
              this.creditFundAccountExist = creditFundAccountExist;
              this.creditFundAccountStatus = creditFundAccountStatus;
              this.creditBankAccountExist = creditBankAccountExist;
              this.loading = false;
              this.fundAccountList = [];
              this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
            } else {
              this.renderingView();
            }
          } else {
            this.loading = false;
            this.$TAlert({
              tips: msg
            });
          }
        })
        .catch((err) => {
          this.loading = false;
          this.$TAlert({
            tips: err
          });
        });
    },
    renderingView() {
      fundAccountLrhyQry({ bizType: this.bizType })
        .then((res) => {
          this.loading = false;
          if (res.code == 0) {
            let list =
              (res.data?.fundAccountList || []).filter(
                (item) => item.fundAccountStatus == '0'
              ) || [];
            if (!list.length) {
              this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
            } else {
              if (list[0].isOpen == '1' && !this.$attrs.needCancel) {
                this.$TAlert({
                  tips: '您当前所有账户均已开通当前业务权限。'
                });
              }
            }
            list.map((item) => {
              item.isChecked = false;
            });
            this.fundAccountList = list;
          } else {
            this.$TAlert({
              title: '温馨提示',
              tips: res.msg
            });
          }
        })
        .catch((err) => {
          this.$TAlert({
            title: '温馨提示',
            tips: err
          });
        });
    },
    jumpBusiness(bizType = '') {
      if (bizType === '') throw new Error('bizType 不能为空');
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, {
          bizType,
          initJumpMode: '0'
        });
      });
    },
    selectAcc(item) {
      const { isChecked, available, i } = item;
      if (!available) return;
      this.$set(this.fundAccountList[i], 'isChecked', !isChecked);
      // this.$emit('change', {
      //   fundAccountData: JSON.stringify(this.openAccList)
      // });
    }
  }
};
</script>
<style scoped>
.acct_nodata h5 {
  text-align: left;
  font-size: 0.15rem;
  color: #333333;
}
</style>
