<template>
  <section class="main fixed" data-page="home" style="position: fixed">
    <t-header />
    <article v-if="pageStep === 1" class="content" />
    <template v-else>
      <article class="content">
        <div class="limit_infobox">
          <p>根据您的账户情况，您的信用额度可调整为</p>
          <!-- 修改这里，添加编辑模式切换 -->
          <div class="num" v-if="!isEditing" @click="startEditing">
            <span class="icon"></span>
            <span class="amount-text">{{
              Math.floor(applyCreditQuota / 10000) | formatMoneyV2
            }}</span>
            <span class="unit-text">万元</span>
            <span class="edit-text">修改</span>
            <!-- <img src="@/assets/images/edit-icon.png" class="edit-icon" /> -->
          </div>
          <!-- 直接在原位置编辑 -->
          <div class="num editing" v-else>
            <div class="input-wrapper">
              <span class="icon"></span>
              <input
                type="number"
                v-model="inputValue"
                class="inline-number-input"
                placeholder="请输入金额"
                @input="validateInput"
                @blur="confirmInput"
                @keyup.enter="confirmInput"
                ref="amountInput"
                autocomplete="off"
              />
              <span class="unit-label">万元</span>
            </div>
          </div>
          <div class="info">
            <span class="limit_tag"
              >（当前额度:
              {{
                Math.floor(currentCreditQuota / 10000) | formatMoneyV2
              }}万元）</span
            >
          </div>
          <!-- 优化滑块控制 -->
          <div class="slider-container">
            <div class="slider-track">
              <div
                class="slider-progress"
                :style="{ width: progressWidth + '%' }"
              ></div>
              <input
                type="range"
                v-model="sliderValue"
                :min="minCreditQuota"
                :max="maxSliderValue"
                step="10000"
                class="slider"
                @input="updateApplyCreditQuota"
              />
            </div>
            <div class="slider-labels">
              <span>0万元</span>
              <span
                >{{
                  Math.floor(maxSliderValue / 10000) | formatMoneyV2
                }}万元</span
              >
            </div>
          </div>
        </div>
        <div class="tip_txtbox spel" v-if="warmReminder">
          <p>温馨提示:</p>
          <p>
            根据您账户情况，您的信用额度已超过最高限额，线上仅支持额度下调或最高调整至{{
              Math.floor(maxSliderValue / 10000) | formatMoneyV2
            }}万，如您需要更高额度，请联系您的客服经理或咨询热线95310。
          </p>
        </div>
        <div class="tip_txtbox spel" v-if="deepCredit">
          <p>温馨提示:</p>
          <p>
            您的当前额度已超过最高限额，线上仅支持额度下调或最高调整至{{
              Math.floor(maxSliderValue / 10000) | formatMoneyV2
            }}万，如您需要更高额度，请点击<a
              class="com_link"
              @click="deepCreditRecordQuery"
              >申请深度征信</a
            >
            或联系您的客服经理或咨询热线95310。
          </p>
        </div>
        <div class="ce_btn block mt20">
          <a class="p_button" @click="nextClick">立即调整</a>
          <a class="p_button border-2" @click="invalidFlowIns">放弃调整</a>
        </div>
      </article>
    </template>
  </section>
</template>

<script>
import {
  querySysConfig,
  queryCreditFundAccount,
  creditQuotaQuery,
  calculateCreditQuota,
  deepCreditRecordQuery,
  deepCreditSubmit,
  invalidFlowIns,
  clientCreditAnswerSubmission
} from '@/service/service.js';
import { EVENT_NAME } from '@/common/formEnum';
import { formatMoney } from '@/common/filter';
import BigNumber from 'bignumber.js';

export default {
  name: 'AdjustCreditQuotaV2',
  inject: ['tkFlowInfo', 'clearKeepAlive', 'eventMessage'],
  data() {
    return {
      // 当前步骤页面
      pageStep: 1,
      // 信用账户
      fundAccount: '',
      // 一码通账户
      acodeAccount: '',
      // 账户列表
      accountList: [],
      // 中台系统参数配置的最大信用额度
      maxCreditQuota: '',
      // 客户当前的信用额度
      currentCreditQuota: '',
      // 客户的测算信用额度
      csCreditQuota: '',
      // 融资额度上限
      finCreditQuota: '',
      // 融券额度上限
      sloCreditQuota: '',
      // 客户申请的信用额度
      applyCreditQuota: '',
      // 满足调额，客户当前额度<最大额度且测算额度≥最大额度，提示中台系统参数配置的文案
      warmReminder: false,
      // 客户是否已深度征信
      deepCredit: false,
      // 页面状态及提示信息
      pageTips: {
        title: '',
        tips: ''
      },
      // 征信测评问卷答案
      paperAnswer: '',
      // 征信测评问卷分数
      paperScore: '',

      // 新增滑块相关数据
      sliderValue: 0,
      minCreditQuota: 0,
      maxSliderValue: null, // 单位为元
      inputValue: '',
      // 进度条相关
      progressWidth: 100, // 默认进度条宽度为100%
      // 添加编辑状态
      isEditing: false
    };
  },
  computed: {},
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    this.renderingView();
    this.clearKeepAlive();
  },
  mounted() {},
  destroyed() {},
  methods: {
    // 查询信用资金账号
    async renderingView() {
      const { code, data, msg } = await this.fetchData(
        queryCreditFundAccount()
      );
      if (code != 0) return _hvueToast({ mes: msg });
      if (!data) {
        return this.$TAlert({
          title: '温馨提示',
          tips: '信用资金账号查询异常！',
          confirm: () => {
            this.backShowCreditQuota();
          }
        });
      }
      this.fundAccount = data.fundAccount;
      this.queryMaxCreditQuota();
    },
    // 查询中台配置的最大信用额度
    async queryMaxCreditQuota() {
      const maxCreditQuota = 'bc.business.maxCreditQuota';
      const { code, data, msg } = await this.fetchData(
        querySysConfig({
          configKey: maxCreditQuota
        })
      );
      if (code != 0) return _hvueToast({ mes: msg });
      this.maxCreditQuota = data[maxCreditQuota].configValue;
      this.creditQuotaQuery();
    },
    // 查询中台配置的温馨提示文案
    async queryWarmReminder() {
      const exceedMaxCreditQuota = 'bc.business.exceedMaxCreditQuota.tips';
      const { code, data, msg } = await this.fetchData(
        querySysConfig({
          configKey: exceedMaxCreditQuota
        })
      );
      if (code != 0) return _hvueToast({ mes: msg });
      this.warmReminder = data[exceedMaxCreditQuota].configValue;
    },
    // 查询客户的当前信用额度
    async creditQuotaQuery() {
      const { code, data, msg } = await this.fetchData(
        creditQuotaQuery({
          fundAccount: this.fundAccount
        })
      );
      if (code != 0) return _hvueToast({ mes: msg });
      console.log(data, '信用额度');
      this.currentCreditQuota = data.finContractQuota;
      // this.finCreditQuota = data.finContractQuota;
      // this.sloCreditQuota = data.sloContractQuota;
      this.calculateCreditQuota();
    },
    // 查询客户的测算信用额度
    async calculateCreditQuota() {
      const { code, data, msg } = await this.fetchData(
        calculateCreditQuota({
          fundAccount: this.fundAccount
        })
      );
      if (code != 0) return _hvueToast({ mes: msg });
      console.log(data, '测算额度');
      // 试算额度按万元向下取整
      this.csCreditQuota =
        !data.csTotalMaxQuota || parseFloat(data.csTotalMaxQuota) == 0
          ? '0'
          : String(
              Math.floor(parseFloat(data.csTotalMaxQuota) / 10000) * 10000
            );
      let max = BigNumber(this.maxCreditQuota * 10000);
      let current = BigNumber(this.currentCreditQuota);
      // let currentPositve20 = BigNumber(this.currentCreditQuota).times(1.2);
      // let currentNegative20 = BigNumber(this.currentCreditQuota).times(0.8);
      let calculate = BigNumber(this.csCreditQuota);
      this.paperAnswer = data.paperAnswer;
      this.paperScore = data.paperScore;

      // 初始化滑块最小值
      this.minCreditQuota = 0;

      if (
        [-1].includes(current.comparedTo(max)) &&
        [1].includes(calculate.comparedTo(max))
      ) {
        //客户当前额度＜3000万 ，新额度＞3000万 ，并支持客户调额区间为0-3000万
        // this.queryWarmReminder();
        this.warmReminder = true;
        this.applyCreditQuota = max;
        this.maxSliderValue = this.applyCreditQuota;
        this.sliderValue = Number(max); // 初始化滑块值
        this.pageStep = 2;
      } else if (
        [0, 1].includes(current.comparedTo(max)) &&
        [1].includes(calculate.comparedTo(current))
      ) {
        //客户当前额度≥3000万，新额度＞当前额度，支持客户调额区间为0-3000万，如果客户还需调高，客户可以选择深度征信，等业务老师审核是否可调整（业务老师手动触发短信通知
        this.deepCredit = true;
        this.applyCreditQuota = max;
        this.maxSliderValue = this.applyCreditQuota;
        this.sliderValue = Number(max); // 初始化滑块值
        this.pageStep = 2;
      } else {
        //其他场景：支持客户调额区间为0-客户新试算额度。
        this.applyCreditQuota = calculate;
        this.maxSliderValue = this.applyCreditQuota;
        this.sliderValue = Number(calculate); // 初始化滑块值
        this.pageStep = 2;
      }
      this.updateApplyCreditQuota();
    },
    // 查询客户的深度征信次数
    async deepCreditRecordQuery() {
      let tkFlowInfo = this.tkFlowInfo();
      const { code, data, msg } = await this.fetchData(
        deepCreditRecordQuery({
          fundAccount: this.fundAccount,
          bizType: tkFlowInfo.bizType,
          flowToken: sessionStorage.getItem('TKFlowToken')
        })
      );
      if (code != 0) return _hvueToast({ mes: msg });
      if (data.submitCount < this.$attrs.limit_count) {
        this.deepCreditSubmit(tkFlowInfo.bizType);
      } else {
        this.$TAlert({
          title: '申请成功',
          tips: '我司会为您线下处理。请耐心等待，为确保服务质量，请保证预留在我司的联络方式真实有效，方便您的服务人员与您联系，详细咨询热线95310。',
          confirm: this.backShowCreditQuota
        });
      }
    },
    // 提交深度信用次数
    async deepCreditSubmit(bizType) {
      const { code, msg } = await this.fetchData(
        deepCreditSubmit({
          fundAccount: this.fundAccount,
          bizType,
          creditQuota: this.currentCreditQuota,
          csTotalMaxQuota: this.csCreditQuota,
          totalMaxQuota: this.maxCreditQuota,
          remark: '客户当前额度≥最大额度，测算额度＞当前额度',
          flowToken: sessionStorage.getItem('TKFlowToken')
        })
      );
      if (code != 0) return _hvueToast({ mes: msg });
      this.clientCreditAnswerSubmission();
    },
    // 提交深度征信结果(提交柜台)
    async clientCreditAnswerSubmission() {
      const { code, msg } = await this.fetchData(
        clientCreditAnswerSubmission({
          fundAccount: this.fundAccount,
          creditQuota: this.currentCreditQuota,
          csTotalMaxQuota: this.csCreditQuota,
          totalMaxQuota: this.maxCreditQuota,
          remark: '客户当前额度≥最大额度，测算额度＞当前额度',
          flowToken: sessionStorage.getItem('TKFlowToken')
        })
      );
      if (code != 0) return _hvueToast({ mes: msg });
      this.$TAlert({
        title: '申请成功',
        tips: '我司会为您线下处理。请耐心等待，为确保服务质量，请保证预留在我司的联络方式真实有效，方便您的服务人员与您联系，详细咨询热线95310。',
        confirm: this.backShowCreditQuota
      });
    },
    // 跳转至联系客服
    toOther() {
      const targetUrl =
        $hvue.customConfig.targetUrl +
        '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=onlineServiceForWeb';
      if ($hvue.platform == 0) {
        window.location.href = targetUrl;
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          params: {
            url: targetUrl,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        const res = $h.callMessageNative(reqParams);
      }
    },

    // 滑块值变化时更新申请额度
    updateApplyCreditQuota() {
      // 将滑块值转换为万元单位，然后再转回元单位，确保千位以下为0
      const valueInWan = Math.floor(this.sliderValue / 10000);
      this.applyCreditQuota = valueInWan * 10000;
      // 计算进度条宽度
      this.progressWidth = (this.sliderValue / this.maxSliderValue) * 100;
    },

    // 优化显示数字输入弹窗
    showNumberInput() {
      this.inputValue = this.applyCreditQuota;
    },

    // 开始编辑模式
    startEditing() {
      this.isEditing = true;
      this.inputValue = Math.floor(this.applyCreditQuota / 10000);
      this.$nextTick(() => {
        this.$refs.amountInput.focus();
      });
    },

    // 优化验证输入值是否在有效范围内
    validateInput() {
      // 确保输入为正整数
      this.inputValue = this.inputValue.replace(/\D/g, '');

      const value = Number(this.inputValue);
      // 确保值大于0且不超过最大值
      if (value <= 0) {
        this.inputValue = ''; // 最小值为1万元
      } else if (value > this.maxSliderValue) {
        this.inputValue = String(this.maxSliderValue);
      }
    },

    // 优化确认输入的数值
    confirmInput() {
      const value = Number(this.inputValue);
      if (value > Math.floor(this.maxSliderValue / 10000)) {
        _hvueToast({
          mes: `申请额度不可超出${Math.floor(this.maxSliderValue / 10000)}万元`
        });
        this.inputValue = String(Math.floor(this.maxSliderValue / 10000));
      }
      this.applyCreditQuota = Number(this.inputValue) * 10000; // 转为元
      this.sliderValue = this.applyCreditQuota;
      this.progressWidth = (this.sliderValue / this.maxSliderValue) * 100;
      // 退出编辑模式
      this.isEditing = false;
    },

    // 下一步操作
    nextClick() {
      const currentCreditQuota = Math.floor(this.currentCreditQuota);
      const applyCreditQuota = Math.floor(this.applyCreditQuota);
      const maxSliderValue = Math.floor(this.maxSliderValue);
      const inputValue = Number(this.inputValue);

      // 新增校验逻辑
      if (currentCreditQuota === applyCreditQuota) {
        return this.$TAlert({
          title: '温馨提示',
          tips: '您申请的信用额度与当前额度一致，无需提交，请确认后操作'
        });
      }

      if (maxSliderValue < applyCreditQuota || maxSliderValue < inputValue) {
        return this.$TAlert({
          title: '温馨提示',
          tips: `申请额度不可超出${Math.floor(this.maxSliderValue / 10000)}万元`
        });
      }

      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        selected_accounts_data: JSON.stringify([
          {
            stockType: '信用额度调整',
            stockAccount: formatMoney(this.applyCreditQuota) + '元'
          }
        ]),
        curr_total_quota: this.currentCreditQuota,
        cs_total_max_quota: this.csCreditQuota,
        total_max_quota: this.maxCreditQuota,
        fin_apply_quota: this.applyCreditQuota,
        slo_apply_quota: this.applyCreditQuota,
        total_apply_quota: this.applyCreditQuota,
        paper_answer: this.paperAnswer,
        paper_score: this.paperScore
      });
    },
    // 放弃调整
    async invalidFlowIns() {
      const { code, msg } = await this.fetchData(
        invalidFlowIns({
          flowToken: sessionStorage.getItem('TKFlowToken')
        })
      );
      if (code != 0) return _hvueToast({ mes: msg });
      this.backShowCreditQuota();
    },
    // 返回查看信用额度页面
    backShowCreditQuota() {
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    },
    async fetchData(promise) {
      try {
        const data = await promise;
        return data;
      } catch (error) {
        return {
          code: -1,
          msg: error
        };
      }
    }
  }
};
</script>
<style scoped>
.com_btn {
  padding: 0;
  background: none;
}

/* 滑块样式 */
.slider-container {
  margin-top: 0.2rem;
  width: 100%;
  padding: 0 0.1rem;
}

.slider-track {
  position: relative;
  height: 0.12rem;
  background: #fff0f0;
  border-radius: 0.06rem;
  margin: 0.15rem 0;
}

.slider-progress {
  position: absolute;
  height: 0.12rem;
  background: #ff4d4f;
  border-radius: 0.03rem;
  z-index: 1;
  pointer-events: none;
}

.slider {
  -webkit-appearance: none;
  appearance: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  background: transparent;
  z-index: 2;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 0.24rem; /* 24px */
  height: 0.24rem; /* 24px */
  border-radius: 50%;
  background: #ffa3a5; /* 内圈圆的颜色 */
  cursor: pointer;
  position: relative;
  z-index: 3;
  border: 0.08rem solid #fff; /* 使用白色边框创建内圈效果 */
  box-shadow: 0 0 0 0.005rem #ffa3a5; /* 0.5px 的外边框 */
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  color: #55555e;
  font-size: 0.14rem;
  font-style: normal;
  font-weight: 400;
  line-height: 0.14rem;
}

/* 数字编辑图标 */
.edit-icon {
  font-size: 0.14rem;
  margin-left: 0.05rem;
  color: #999;
}

/* 数字输入弹窗样式 */
.number-input-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  width: 80%;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.modal-header {
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
}

.close-btn {
  font-size: 20px;
  cursor: pointer;
}

.modal-body {
  padding: 20px 15px;
}

.number-input {
  width: 100%;
  height: 40px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0 10px;
  font-size: 16px;
  text-align: center;
}

.input-hint {
  margin-top: 10px;
  font-size: 12px;
  color: #999;
}

.modal-footer {
  display: flex;
  border-top: 1px solid #eee;
}

.modal-footer button {
  flex: 1;
  height: 44px;
  border: none;
  font-size: 16px;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333;
}

.confirm-btn {
  background-color: #ff4d4f;
  color: #fff;
}

/* 额度显示样式优化 */
.num {
  font-size: 0.28rem;
  font-weight: bold;
  margin: 0.15rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #0f0f1b;
}

.amount-text {
  font-size: 0.44rem;
  font-weight: 600;
  color: #0f0f1b;
  padding-left: 0.04rem;
}

/* 内联编辑样式 */
.num.editing {
  padding: 0.1rem 0;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.currency-symbol {
  font-size: 0.3rem;
  margin-right: 0.05rem;
  color: #0f0f1b;
}

.inline-number-input {
  width: 60%;
  height: 0.6rem;
  border: none;
  border-bottom: 0.01rem solid #ddd;
  font-size: 0.44rem;
  font-weight: 600;
  color: #0f0f1b;
  text-align: center;
  background: transparent;
}

/* 添加 placeholder 样式 */
.inline-number-input::placeholder {
  font-size: 0.3rem;
  color: #87878d;
}

.inline-number-input:focus {
  outline: none;
  border-bottom: 0.02rem solid #ff4d4f;
}

.unit-label {
  font-size: 0.16rem;
  margin-left: 0.05rem;
  color: #87878d;
}

.limit_tag {
  background-color: #ffffff;
  padding: 0.04rem 0.08rem;
  border-radius: 0.04rem;
  font-size: 0.14rem;
  color: #87878d;
  font-style: normal;
  font-weight: 400;
}

/* 编辑图标 */
.edit-icon {
  width: 0.14rem;
  height: 0.14rem;
  margin-left: 0.05rem;
  vertical-align: middle;
}
div.num .icon {
  display: inline-block;
  width: 0.14rem;
  height: 0.2rem;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTkiIHZpZXdCb3g9IjAgMCAxNCAxOSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE0IDBMOC45ODExMyA4LjUxODc4SDEzLjMzOTZWMTAuMDM1Mkg4LjAxMjU4VjEyLjQ0MzdIMTMuMzgzNlYxMy45NjAxSDguMDEyNThWMTlINi4wMzE0NVYxMy45NjAxSDAuNzA0NDAyVjEyLjQ0MzdINi4wMzE0NVYxMC4wMzUySDAuNzA0NDAyVjguNTE4NzhINS4wNjI4OUwwIDBIMi4yMDEyNkw3LjA0NDAzIDguMzg0OThMMTEuODQyOCAwSDE0WiIgZmlsbD0iIzBGMEYxQiIvPgo8L3N2Zz4K')
    no-repeat center;
  background-size: 100% auto;
  position: relative;
  top: 0.06rem;
}
.edit-text {
  font-size: 0.16rem;
  color: #2b73ff;
  margin-left: 0.1rem;
  font-style: normal;
  font-weight: 400;
  position: relative;
  top: 0.06rem;
}
.unit-text {
  font-size: 0.14rem;
  color: #0F1826;
  margin-left: 0.05rem;
  font-style: normal;
  font-weight: 400;
  position: relative;
  top: 0.06rem;
}
</style>
