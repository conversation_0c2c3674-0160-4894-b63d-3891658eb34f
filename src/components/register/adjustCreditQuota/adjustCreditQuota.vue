<template>
  <section class="main fixed" data-page="home" style="position: fixed">
    <t-header />
    <article v-if="pageStep === 1" class="content" />
    <template v-else-if="pageStep === 2">
      <article class="content">
        <div class="limit_infobox">
          <p>根据您的账户情况，您的信用额度可调整为(元)</p>
          <div class="num">{{ applyCreditQuota | formatMoney }}</div>
          <div class="info">
            <span class="limit_tag"
              >当前额度: {{ currentCreditQuota | formatMoney }}元</span
            >
          </div>
        </div>
        <div class="tip_txtbox spel" v-if="warmReminder">
          <p>温馨提示:</p>
          <p>
            {{ warmReminder }}
          </p>
        </div>
        <div class="ce_btn block mt20">
          <a class="p_button" @click="nextClick">立即调整</a>
          <a class="p_button border-2" @click="invalidFlowIns">放弃调整</a>
        </div>
      </article>
    </template>
    <template v-else>
      <article class="content">
        <div class="result_page">
          <div class="result_tips">
            <div class="icon fail"></div>
            <h5>{{ pageTips.title }}</h5>
            <p style="text-align: left">{{ pageTips.tips }}</p>
            <!-- <p v-if="deepCredit">
                若您需要帮助，请<a class="com_btn" @click="toOther">联系客服</a>
              </p> -->
          </div>
        </div>
      </article>
      <footer class="footer">
        <div class="ce_btn black">
          <a class="p_button border" @click="backShowCreditQuota">返回</a>
        </div>
      </footer>
    </template>
  </section>
</template>

<script>
import {
  querySysConfig,
  queryCreditFundAccount,
  creditQuotaQuery,
  calculateCreditQuota,
  deepCreditRecordQuery,
  deepCreditSubmit,
  invalidFlowIns,
  clientCreditAnswerSubmission
} from '@/service/service.js';
import { EVENT_NAME } from '@/common/formEnum';
import { formatMoney } from '@/common/filter';
import BigNumber from 'bignumber.js';

export default {
  name: 'adjustCreditQuota',
  inject: ['tkFlowInfo', 'clearKeepAlive', 'eventMessage'],
  data() {
    return {
      // 当前步骤页面
      pageStep: 1,
      // 信用账户
      fundAccount: '',
      // 一码通账户
      acodeAccount: '',
      // 账户列表
      accountList: [],
      // 中台系统参数配置的最大信用额度
      maxCreditQuota: '',
      // 客户当前的信用额度
      currentCreditQuota: '',
      // 客户的测算信用额度
      csCreditQuota: '',
      // 融资额度上限
      finCreditQuota: '',
      // 融券额度上限
      sloCreditQuota: '',
      // 客户申请的信用额度
      applyCreditQuota: '',
      // 满足调额，客户当前额度<最大额度且测算额度≥最大额度，提示中台系统参数配置的文案
      warmReminder: '',
      // 客户是否已深度征信
      deepCredit: false,
      // 页面状态及提示信息
      pageTips: {
        title: '',
        tips: ''
      },
      // 征信测评问卷答案
      paperAnswer: '',
      // 征信测评问卷分数
      paperScore: ''
    };
  },
  computed: {},
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    this.renderingView();
    this.clearKeepAlive();
  },
  mounted() {},
  destroyed() {},
  methods: {
    // 查询信用资金账号
    async renderingView() {
      const { code, data, msg } = await this.fetchData(
        queryCreditFundAccount()
      );
      if (code != 0) return _hvueToast({ mes: msg });
      if (!data) {
        return this.$TAlert({
          title: '温馨提示',
          tips: '信用资金账号查询异常！',
          confirm: () => {
            this.backShowCreditQuota();
          }
        });
      }
      this.fundAccount = data.fundAccount;
      this.queryMaxCreditQuota();
    },
    // 查询中台配置的最大信用额度
    async queryMaxCreditQuota() {
      const maxCreditQuota = 'bc.business.maxCreditQuota';
      const { code, data, msg } = await this.fetchData(
        querySysConfig({
          configKey: maxCreditQuota
        })
      );
      if (code != 0) return _hvueToast({ mes: msg });
      this.maxCreditQuota = data[maxCreditQuota].configValue;
      this.creditQuotaQuery();
    },
    // 查询中台配置的温馨提示文案
    async queryWarmReminder() {
      const exceedMaxCreditQuota = 'bc.business.exceedMaxCreditQuota.tips';
      const { code, data, msg } = await this.fetchData(
        querySysConfig({
          configKey: exceedMaxCreditQuota
        })
      );
      if (code != 0) return _hvueToast({ mes: msg });
      this.warmReminder = data[exceedMaxCreditQuota].configValue;
    },
    // 查询客户的当前信用额度
    async creditQuotaQuery() {
      const { code, data, msg } = await this.fetchData(
        creditQuotaQuery({
          fundAccount: this.fundAccount
        })
      );
      if (code != 0) return _hvueToast({ mes: msg });
      console.log(data, '信用额度');
      this.currentCreditQuota = data.finContractQuota;
      // this.finCreditQuota = data.finContractQuota;
      // this.sloCreditQuota = data.sloContractQuota;
      this.calculateCreditQuota();
    },
    // 查询客户的测算信用额度
    async calculateCreditQuota() {
      const { code, data, msg } = await this.fetchData(
        calculateCreditQuota({
          fundAccount: this.fundAccount
        })
      );
      if (code != 0) return _hvueToast({ mes: msg });
      console.log(data, '测算额度');
      this.csCreditQuota =
        !data.csTotalMaxQuota || parseFloat(data.csTotalMaxQuota) == 0
          ? '0'
          : data.csTotalMaxQuota;
      let max = BigNumber(this.maxCreditQuota * 10000);
      let current = BigNumber(this.currentCreditQuota);
      let currentPositve20 = BigNumber(this.currentCreditQuota).times(1.2);
      let currentNegative20 = BigNumber(this.currentCreditQuota).times(0.8);
      let calculate = BigNumber(this.csCreditQuota);
      this.paperAnswer = data.paperAnswer;
      this.paperScore = data.paperScore;
      // 客户的测算额度与原额度差异<±20%，无需调整
      // 客户原额度为0，测算额度也为0的时候，无需调整
      if (
        ([-1].includes(calculate.comparedTo(currentPositve20)) &&
          [1].includes(calculate.comparedTo(currentNegative20))) ||
        (BigNumber(0).isEqualTo(current) && BigNumber(0).isEqualTo(calculate))
      ) {
        this.pageTips.title = '无需调整';
        this.pageTips.tips =
          '根据您的资信情况，您的信用额度暂无变化，目前不需要调整。';
        this.pageStep = 3;
      } else {
        // 客户的测算额度与原额度差异≥±20%，满足调额
        if (
          [0, 1].includes(current.comparedTo(max)) &&
          calculate.comparedTo(current) === 1
        ) {
          // 客户当前额度≥最大额度，测算额度>当前额度，不可调整
          // 查询客户是否已触发深度征信流程（大于部件属性中的limit_count次数，默认3次），如无则添加征信次数
          this.deepCreditRecordQuery();
        } else if (
          current.comparedTo(max) === -1 &&
          calculate.comparedTo(max) === 1
        ) {
          // 客户当前额度<最大额度，测算额度>最大额度，客户可以调整至最大额度
          this.applyCreditQuota = max;
          this.queryWarmReminder();
          this.pageStep = 2;
        } else if (
          (current.comparedTo(max) === -1 &&
            [-1, 0].includes(calculate.comparedTo(max))) ||
          ([0, 1].includes(current.comparedTo(max)) &&
            calculate.comparedTo(current) === -1)
        ) {
          // 客户当前额度<最大额度，测算额度≤最大额度，客户可以调整至测算额度
          // 客户当前额度≥最大额度，测算额度<当前额度，客户可以调整至测算额度
          this.applyCreditQuota = this.csCreditQuota;
          this.pageStep = 2;
        }
      }
    },
    // 查询客户的深度征信次数
    async deepCreditRecordQuery() {
      let tkFlowInfo = this.tkFlowInfo();
      const { code, data, msg } = await this.fetchData(
        deepCreditRecordQuery({
          fundAccount: this.fundAccount,
          bizType: tkFlowInfo.bizType,
          flowToken: sessionStorage.getItem('TKFlowToken')
        })
      );
      if (code != 0) return _hvueToast({ mes: msg });
      // 需求调整。KFSBYWBLCG-7127
      this.pageTips.title = '暂不支持线上调额';
      this.pageTips.tips =
        '您的当前额度已超过最高限额，暂不支持线上调额，我司会为您线下处理。为确保服务质量，请保证预留在我司的联络方式真实有效，方便您的服务人员与您联系，详细咨询热线95310。';

      if (data.submitCount >= this.$attrs.limit_count) {
        /* this.deepCredit = true;
        this.pageTips.title = '亲，申请提交失败了！';
        this.pageTips.tips =
          '温馨提醒：申请提交后，我司将尽快完成审核和自动调整，请耐心等待，在此期间，您不需要重复发起申请哦！若您需要帮助，请拨打客服 95310。';
        this.clientCreditAnswerSubmission(); */
        // this.clientCreditAnswerSubmission();
      } else {
        this.deepCreditSubmit(tkFlowInfo.bizType);
      }
      this.pageStep = 3;
    },
    // 提交深度信用次数
    async deepCreditSubmit(bizType) {
      const { code, msg } = await this.fetchData(
        deepCreditSubmit({
          fundAccount: this.fundAccount,
          bizType,
          creditQuota: this.currentCreditQuota,
          csTotalMaxQuota: this.csCreditQuota,
          totalMaxQuota: this.maxCreditQuota,
          remark: '客户当前额度≥最大额度，测算额度＞当前额度',
          flowToken: sessionStorage.getItem('TKFlowToken')
        })
      );
      if (code != 0) return _hvueToast({ mes: msg });

      this.clientCreditAnswerSubmission();
    },
    // 提交深度征信结果(提交柜台)
    async clientCreditAnswerSubmission() {
      const { code, msg } = await this.fetchData(
        clientCreditAnswerSubmission({
          fundAccount: this.fundAccount,
          creditQuota: this.currentCreditQuota,
          csTotalMaxQuota: this.csCreditQuota,
          totalMaxQuota: this.maxCreditQuota,
          remark: '客户当前额度≥最大额度，测算额度＞当前额度',
          flowToken: sessionStorage.getItem('TKFlowToken')
        })
      );
      if (code != 0) return _hvueToast({ mes: msg });
    },
    // 跳转至联系客服
    toOther() {
      const targetUrl =
        $hvue.customConfig.targetUrl +
        '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=onlineServiceForWeb';
      if ($hvue.platform == 0) {
        window.location.href = targetUrl;
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          params: {
            url: targetUrl,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        const res = $h.callMessageNative(reqParams);
      }
    },
    // 下一步操作
    nextClick() {
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        selected_accounts_data: JSON.stringify([
          {
            stockType: '信用额度调整',
            stockAccount: formatMoney(this.applyCreditQuota) + '元'
          }
        ]),
        curr_total_quota: this.currentCreditQuota,
        cs_total_max_quota: this.csCreditQuota,
        total_max_quota: this.maxCreditQuota,
        fin_apply_quota: this.applyCreditQuota,
        slo_apply_quota: this.applyCreditQuota,
        total_apply_quota: this.applyCreditQuota,
        paper_answer: this.paperAnswer,
        paper_score: this.paperScore
      });
    },
    // 放弃调整
    async invalidFlowIns() {
      const { code, msg } = await this.fetchData(
        invalidFlowIns({
          flowToken: sessionStorage.getItem('TKFlowToken')
        })
      );
      if (code != 0) return _hvueToast({ mes: msg });
      this.backShowCreditQuota();
    },
    // 返回查看信用额度页面
    backShowCreditQuota() {
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    },
    async fetchData(promise) {
      try {
        const data = await promise;
        return data;
      } catch (error) {
        return {
          code: -1,
          msg: error
        };
      }
    }
  }
};
</script>
<style scoped>
.com_btn {
  padding: 0;
  background: none;
}
</style>
