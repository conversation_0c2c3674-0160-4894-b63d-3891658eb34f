<template>
  <div>
    <div v-if="showFlag" class="rule_check">
      <span
        class="icon_check"
        :class="{ checked: isChecked }"
        @click="selectAgree"
      ></span
      >本人已详细阅读并同意签署以下协议<a @click="openAgree"
        >《{{ agreeDetail.agreementName }}》</a
      >
    </div>
    <agreementDetail
      :show="showAgreeDetail"
      :info="agreeDetail"
      :is-count="!isCount"
      @callback="agreeCallBack"
    />
  </div>
</template>

<script>
import agreementDetail from '@/components/agreementDetail';
import { getJwtToken, queryAgreement } from '@/service/service';
export default {
  name: 'AgreementSign',
  inject: ['tkFlowInfo', 'setPropsByForm'],
  components: {
    agreementDetail
  },
  data() {
    return {
      showAgreeDetail: false,
      isChecked: false,
      readList: [],
      agreeDetail: {}
    };
  },
  computed: {
    isCount() {
      return this.readList.includes(this.$attrs.bankId);
    },
    showFlag() {
      return this.$attrs.isShow === '1';
    }
  },
  watch: {
    $attrs: {
      handler({ bankId }) {
        if (bankId) this._queryAgreement(bankId);
      },
      deep: true,
      immediate: false
    }
  },
  created() {},
  methods: {
    async _queryAgreement(agreementSubtype) {
      const { flowNodeNo, inProperty } = this.tkFlowInfo();
      const { groupId, contractType } = this.$attrs;
      const tokenRes = await getJwtToken({
        flowNo: flowNodeNo,
        businessType: inProperty.bizType
      });
      $h.setSession('jwtToken', tokenRes.data);
      queryAgreement({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        bizType: inProperty.bizType,
        groupId,
        contractType,
        agreementSubtype
      })
        .then((res) => {
          this.agreeDetail = res.data[0];
          this.agreeDetail.bankId = agreementSubtype;
          this.isChecked = false;
        })
        .catch((err) => {
          this.setPropsByForm(this.$attrs.propKey, 'isShow', '0');
          _hvueToast({
            mes: err
          });
        });
    },
    openAgree() {
      this.showAgreeDetail = true;
    },
    selectAgree() {
      if (!this.readList.includes(this.$attrs.bankId)) {
        _hvueToast({
          mes: '请先阅读协议'
        });
        return;
      }
      this.isChecked = !this.isChecked;
    },
    agreeCallBack(flag) {
      this.showAgreeDetail = false;
      if (flag) {
        if (!this.isCount) {
          this.readList.push(this.$attrs.bankId);
        }
        this.isChecked = true;
      }
    }
  }
};
</script>

<style scoped></style>
