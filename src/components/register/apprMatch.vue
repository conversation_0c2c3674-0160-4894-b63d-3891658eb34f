<template>
  <fragment>
    <div v-if="suitObject" class="appro_page white_bg">
      <div class="appro_result_tips">
        <h3 v-if="isMatch">您的适当性评估匹配<i class="appro_ic_ok"></i></h3>
        <h3 v-else>您的适当性评估不匹配<i class="appro_ic_error"></i></h3>
      </div>
      <div class="appro_info">
        <div class="item">
          <span class="tit">个人风险承受能力等级</span>
          <div class="ct">
            <h5>{{ suitObject.corpRiskLevelName }}</h5>
          </div>
        </div>
        <div class="item">
          <span class="tit">拟接受服务</span>
          <div class="ct">
            <h5>{{ suitObject.bizName }}</h5>
          </div>
        </div>
        <div class="item">
          <span class="tit">服务风险等级</span>
          <div class="ct">
            <h5>{{ suitObject.bizRiskLevelName }}</h5>
          </div>
        </div>
        <!-- <div class="item">
          <span class="tit">所属投资品种</span>
          <div class="ct">
            <h5>{{ suitObject.businessInvestmentVarietiesName }}</h5>
          </div>
        </div> -->
      </div>
      <div v-if="isMatch || allowNextStep" class="appro_tips">
        <p>
          本人在此确认自身风险承受能力等级该金融服务风险等级
          <span v-if="isMatch" class="state_span_ok">匹配</span>
          <span v-else class="state_span_error">不匹配</span>
          。本人投资该项产品或接受该项服务的决定，系本人独立、自主、真实的意思表示，与贵公司及相关从业人员无关。
        </p>
      </div>
      <agreement-sign
        v-if="isShowAgree && (isMatch || allowNextStep)"
        v-model="ruleChecked"
        :group-id="groupId"
        :contract-type="contractType"
        @agree-list="agreeCallback"
      />
    </div>
  </fragment>
</template>

<script>
import { investProInfoQry } from '@/service/service';
import agreementSign from '@/components/agreementSign';
import { EVENT_NAME } from '@/common/formEnum';
import { signAgree } from '@/common/util';

export default {
  name: 'ApprMatch',
  components: {
    agreementSign
  },
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      suitObject: null, // 适当性匹配数据
      ruleChecked: false,
      showAgreeDetail: false,
      agreeList: [],
      epaper_sign_json: '',
      isShowAgree: false,
      groupId: this.$attrs.groupId,
      contractType: this.$attrs.contractType,
      allowNextStep: true //能否下一步强弱匹配
    };
  },
  computed: {
    isMatch() {
      return this.suitObject.matchingResult === '1';
    },
    exitBusiness() {
      return (
        this.suitObject?.lowestCorpRiskLevelFlag === '1' &&
        this.suitObject?.ageRiskFlag === '1'
      );
    }
  },
  created() {
    this.renderingView();
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
  },
  methods: {
    async renderingView() {
      const { inProperty } = this.tkFlowInfo();
      const bizType = inProperty.bizType;
      const suitRes = await investProInfoQry({
        bizType,
        flowToken: sessionStorage.getItem('TKFlowToken')
      });
      this.suitObject = suitRes.data;
      this.allowNextStep = suitRes.data.allowNextStep;
      this.contractType = this.isMatch
        ? this.contractType.split('|')[0]
        : this.contractType.split('|')[1];
      this.isShowAgree = true;
      console.log(this.allowNextStep);
      if (!this.isMatch && !this.allowNextStep) {
        // 换成返回首页
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '返回首页',
          display: true,
          btnStatus: 2,
          data: () => {
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
      } else {
        if (this.exitBusiness) {
          this.eventMessage(this, EVENT_NAME.INDEX_BTN);
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 2,
            data: this.triggerEvent
          });
        }
      }
    },

    agreeCallback(data) {
      this.agreeList = data;
    },

    toIndex() {
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    },

    triggerEvent() {
      if (!this.ruleChecked) {
        _hvueToast({
          mes: '请阅读并勾选协议'
        });
        return;
      }
      const tkFlowInfo = this.tkFlowInfo();
      signAgree(tkFlowInfo, this.agreeList)
        .then((epaperSignJson) => {
          const {
            matchingResult,
            corpRiskLevel,
            investmentVarieties,
            bizRiskLevel,
            businessInvestmentVarieties,
            businessInvestmentTerm,
            investmentTerm
          } = this.suitObject;
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
            matchingResult,
            corpRiskLevel,
            enInvestKind: investmentVarieties,
            enInvestTerm: investmentTerm,
            prInvestTerm: businessInvestmentTerm,
            prInvestKind: businessInvestmentVarieties,
            busSuitabilityGrade: bizRiskLevel,
            epaperSignJson
          });
        })
        .catch((error) => {
          _hvueToast({
            mes: error
          });
        });
    }
  }
};
</script>

<style scoped>
div.agree_fixed {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  -webkit-flex-direction: column;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: fixed;
  left: 0;
  top: 0;
}

div.agree_fixed article.content {
  -moz-box-flex: 1;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  height: 100%;
  -webkit-overflow-scrolling: auto;
  position: relative;
}

div.agree_fixed article.content::-webkit-scrollbar {
  width: 0;
  display: none;
}
</style>
