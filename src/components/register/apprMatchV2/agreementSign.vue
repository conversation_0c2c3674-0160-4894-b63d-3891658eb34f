<template>
  <fragment>
    <div class="rule_check" style="padding-left: 0.15rem">
      <!-- <span
        class="icon_check"
        :class="{ checked: isChecked }"
        @click="selectAgree"
      ></span
      > -->
      本人已详细阅读并同意签署以下协议
      <a
        v-for="({ agreementName, agreementId }, index) in agreeList"
        :key="index"
        :class="{ read: readList.includes(agreementId) }"
        @click="openAgree(index)"
      >
        《{{ agreementName }}》
      </a>
    </div>
    <agreementDetail
      v-if="showAgreeDetail"
      :show="showAgreeDetail"
      :info="agreeList[agreeIndex]"
      :is-count="!isCount"
      @callback="agreeCallBack"
    />
  </fragment>
</template>

<script>
import agreementDetail from '@/components/agreementDetail';
import { getJwtToken, queryAgreementExt } from '@/service/service';
export default {
  name: 'AgreementSign',
  inject: ['tkFlowInfo'],
  components: {
    agreementDetail
  },
  model: {
    prop: 'isChecked',
    event: 'change'
  },
  props: {
    isChecked: {
      type: Boolean,
      default: false
    },
    agreementNodeNo: {
      type: String,
      default: ''
    },
    agreementExt: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showAgreeDetail: false,
      agreeIndex: 1,
      readList: [],
      agreeList: []
    };
  },
  computed: {
    isCount() {
      return this.readList.includes(this.agreeId);
    },
    agreeId() {
      return this.agreeList[this.agreeIndex]?.agreementId;
    },
    allReadFlag() {
      return this.agreeList.every((a) => {
        return this.readList.includes(a.agreementId);
      });
    }
  },
  watch: {
    bankId(bankId) {
      if (bankId) this._queryAgreement();
    },
    groupId() {
      this._queryAgreement();
    }
  },
  created() {
    this._queryAgreement();
  },
  methods: {
    async _queryAgreement() {
      const { flowNodeNo, inProperty } = this.tkFlowInfo();
      const { groupId, contractType } = this;
      // const tokenRes = await getJwtToken({
      //   flowNo: flowNodeNo,
      //   businessType: inProperty.bizType
      // });
      // $h.setSession('jwtToken', tokenRes.data);
      let reqParams = {
        flowToken: sessionStorage.getItem('TKFlowToken'),
        // bizType: inProperty.bizType,
        // groupId
        // contractType
        agreementBizType: this.agreementNodeNo.split(':')[0],
        agreementNodeNo: this.agreementNodeNo.split(':')[1],
        agreementExt: this.agreementExt
      };
      queryAgreementExt(reqParams)
        .then((res) => {
          this.agreeList = res.data;
          this.checkEvent(this.allReadFlag);
          this.$emit('agree-list', this.agreeList);
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    },
    openAgree(i) {
      this.showAgreeDetail = true;
      this.agreeIndex = i;
    },
    selectAgree() {
      // if (!this.allReadFlag) {
      //   _hvueToast({
      //     mes: '请先阅读协议'
      //   });
      //   return;
      // }
      this.checkEvent(!this.isChecked);
    },
    agreeCallBack(flag) {
      this.showAgreeDetail = false;
      if (flag) {
        if (!this.isCount && !this.readList.includes(this.agreeId)) {
          this.readList.push(this.agreeId);
        }
        this.checkEvent(this.allReadFlag);
      }
    },
    checkEvent(checkFlag) {
      this.$emit('change', checkFlag);
    }
  }
};
</script>

<style scoped>
a.read {
  color: #999999;
}
</style>
