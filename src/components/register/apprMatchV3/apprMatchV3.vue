<template>
  <fragment>
    <article v-if="suitObject" class="content">
      <div class="appro_page">
        <div class="appro_result_tips">
          <h3 v-if="isMatch"><i class="appro_ic_ok" />您的适当性评估匹配</h3>
          <h3 v-else><i class="appro_ic_error" />您的适当性评估不匹配</h3>
        </div>
        <div class="appro_detail_info">
          <p>
            您的风险测评结果与您拟投资的产品或拟接受的服务的适当性信息如下：
          </p>
          <dl>
            <dt>
              风险等级：<span
                v-if="$attrs.check_elig_type.includes('1')"
                class="appro_span_ok"
                >{{ suitObject.riskLevelMatchResult }}</span
              >
            </dt>
            <dd v-if="$attrs.view_biz_elig_elements.includes('1')">
              产品所属的风险等级为：<strong>{{
                suitObject.bizRiskLevelName
              }}</strong>
            </dd>
            <dd v-if="$attrs.view_client_elig_elements.includes('1')">
              您的风险承受能力等级为：<strong>{{
                suitObject.corpRiskLevelName
              }}</strong>
            </dd>
          </dl>
          <dl
            v-if="
              $attrs.check_elig_type.includes('5') ||
              $attrs.view_biz_elig_elements.includes('5')
            "
          >
            <dt>
              投资期限：<span
                v-if="$attrs.check_elig_type.includes('5')"
                class="appro_span_ok"
                >{{ suitObject.investTermMatchResult }}</span
              >
            </dt>
            <dd v-if="$attrs.view_biz_elig_elements.includes('5')">
              产品所属的投资期限为：<strong>{{
                suitObject.businessInvestmentTermName
              }}</strong>
            </dd>
            <dd v-if="$attrs.view_client_elig_elements.includes('5')">
              您的拟投资期限为：<strong>{{
                suitObject.investmentTermName
              }}</strong>
            </dd>
          </dl>
          <dl
            v-if="
              $attrs.check_elig_type.includes('3') ||
              $attrs.view_biz_elig_elements.includes('3')
            "
          >
            <dt>
              投资品种：<span
                v-if="$attrs.check_elig_type.includes('3')"
                class="appro_span_ok"
                >{{ suitObject.investTypeMatchResult }}</span
              >
            </dt>
            <dd v-if="$attrs.view_biz_elig_elements.includes('3')">
              产品所属的投资品种为：<strong>{{
                suitObject.businessInvestmentVarietiesName
              }}</strong>
            </dd>
            <dd v-if="$attrs.view_client_elig_elements.includes('3')">
              您的拟投资品种为：<strong>{{
                suitObject.investmentVarietiesName
              }}</strong>
            </dd>
          </dl>

          <!-- <dl v-if="$attrs.investDestTxt">
            <dt>投资目标</dt>
            <dd>
              产品所属的投资目标为：<strong>{{ $attrs.investDestTxt }}</strong>
            </dd>
          </dl> -->
          <dl>
            <dt>拟开通业务</dt>
            <dd>
              您拟申请开通的业务为：<strong>{{ flowName }}</strong>
            </dd>
          </dl>
        </div>
        <div v-if="isMatch || allowNextStep" class="appro_tips">
          <strong>
            您的风险承受能力等级与该业务风险等级{{
              isMatch ? '相匹配' : '不匹配'
            }}
          </strong>
          <p>
            <strong
              >本公司已经向您充分揭示了该产品或业务服务的风险。您的风险承受能力等级与该业务风险等级{{
                isMatch ? '相匹配' : '不匹配'
              }}。</strong
            >本次适配意见不能取代您本人/贵单位的投资判断，不会降低国金证券销售或提供的产品或服务的固有风险，您将自行承担依据适当性匹配意见做出的投资决策的一切后果，自行承担履约责任以及相关费用。
          </p>
          <p>
            本公司就上述适当性匹配意见与您进行确认，并建议您审慎考察该业务的特征及风险，进行充分风险评估，自行做出投资决策。
          </p>
          <strong>客户确认</strong>
          <p>
            本人/本机构已认真阅读了贵司关于 {{ flowName }}
            业务的风险揭示书，并已充分了解该产品或服务的特征和风险，签署了风险揭示书。本人/本机构确认已充分理解和接受国金证券的告知警示内容。
          </p>
          <p>
            <strong
              >本人/本机构在此确认自身风险承受能力等级与该业务的风险等级相匹配。</strong
            >
          </p>
          <p>
            <strong
              >本人/本机构投资该项产品或接受该项服务的决定，系本人/本机构独立、自主、真实的意思表示，与贵营业部及相关从业人员无关。</strong
            >
          </p>
        </div>
        <agreement-sign
          v-if="isShowAgree && (isMatch || allowNextStep) && hasSaved"
          v-model="ruleChecked"
          :agreement-node-no="agreementNodeNo"
          :agreement-ext="agreementExt"
          @agree-list="agreeCallback"
        />
      </div>
    </article>
  </fragment>
</template>

<script>
import { investProInfoQryV2, updateFlowForm, permissionTypeNameQry } from '@/service/service';
import agreementSign from './components/AgreementSign.vue';
import { EVENT_NAME } from '@/common/formEnum';
import { signAgreeV2 } from '@/common/util';

export default {
  name: 'ApprMatchV3',
  components: {
    agreementSign
  },
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      matchingResult: null,
      flowName: '',
      suitObject: null, // 适当性匹配数据
      ruleChecked: false,
      showAgreeDetail: false,
      agreeList: [],
      epaper_sign_json: '',
      isShowAgree: false,
      agreementNodeNo: this.$attrs.agreementNodeNo,
      agreementExt: this.$attrs.agreementExt,
      allowNextStep: true, //能否下一步强弱匹配
      hasSaved: false
    };
  },
  computed: {
    isMatch() {
      return this.suitObject.matchingResult === '1';
    },
    exitBusiness() {
      return (
        this.suitObject?.lowestCorpRiskLevelFlag === '1' &&
        this.suitObject?.ageRiskFlag === '1'
      );
    }
  },
  created() {
    this.renderingView();
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
  },
  methods: {
    agreeCallback(data) {
      this.agreeList = data;
    },

    toIndex() {
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    },

    async renderingView() {
      const { inProperty, flowName } = this.tkFlowInfo();
      const bizType = inProperty.bizType;
      try {
        const typeData = await permissionTypeNameQry({ bizType });
        this.flowName = typeData?.data?.permissionTypeName;
      } catch (error) {
        console.error(error);
      }

      const suitRes = await investProInfoQryV2({
        bizType,
        flowToken: sessionStorage.getItem('TKFlowToken'),
        checkEligType: this.$attrs.check_elig_type
      });
      this.suitObject = suitRes.data;
      this.allowNextStep = suitRes.data.allowNextStep;
      this.isShowAgree = true;
      const {
        matchingResult,
        corpRiskLevel,
        investmentVarieties,
        bizRiskLevel,
        businessInvestmentVarieties,
        businessInvestmentTerm,
        investmentTerm
      } = this.suitObject;
      updateFlowForm({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        bizType: bizType,
        source: `${flowName}-初始化`,
        formParam: {
          matching_result: matchingResult,
          corp_risk_level: corpRiskLevel,
          en_invest_kind: investmentVarieties.split(';').join(','),
          en_invest_term: investmentTerm.split(';').join(','),
          pr_invest_term: businessInvestmentTerm,
          pr_invest_kind: businessInvestmentVarieties,
          bus_suitability_grade: bizRiskLevel,
          invest_dest: this.$attrs.investDestTxt
        }
      })
        .then((res) => {
          // 查询协议
          this.hasSaved = true;
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
      if (!this.isMatch && !this.allowNextStep) {
        // 换成返回首页
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '返回首页',
          display: true,
          btnStatus: 2,
          data: () => {
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
      } else {
        if (this.exitBusiness) {
          this.eventMessage(this, EVENT_NAME.INDEX_BTN);
        } else {
          // this.eventMessage(this, EVENT_NAME.NEXT_STEP);
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 2,
            data: this.triggerEvent
          });
        }
      }
    },

    triggerEvent() {
      // if (!this.ruleChecked) {
      //   _hvueToast({
      //     mes: '请阅读并勾选协议'
      //   });
      //   return;
      // }
      const tkFlowInfo = this.tkFlowInfo();
      signAgreeV2(tkFlowInfo, this.agreeList, this.$attrs)
        .then((epaperSignJson) => {
          const {
            matchingResult,
            corpRiskLevel,
            investmentVarieties,
            bizRiskLevel,
            businessInvestmentVarieties,
            businessInvestmentTerm,
            investmentTerm
          } = this.suitObject;
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
            matchingResult,
            corpRiskLevel,
            enInvestKind: investmentVarieties.split(';').join(','),
            enInvestTerm: investmentTerm.split(';').join(','),
            prInvestTerm: businessInvestmentTerm,
            prInvestKind: businessInvestmentVarieties,
            busSuitabilityGrade: bizRiskLevel,
            epaperSignJson
          });
        })
        .catch((error) => {
          _hvueToast({
            mes: error
          });
        });
    }
  }
};
</script>

<style scoped>
.appro_tips > strong {
  color: #333333;
}
.appro_tips > p strong {
  color: #333333;
}
</style>
