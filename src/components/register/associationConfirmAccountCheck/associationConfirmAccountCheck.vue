<template>
  <section class="main fixed white_bg" data-page="home">
    <div class="content" v-if="accountList.length == 0">
      <div class="no_data_content">
        <img src="@/assets/images/no_data.png" class="no_data_icon" />
        <div class="no_data_tip">当前暂无证券账户需进行关联关系确认</div>
      </div>
    </div>
    <div class="content" v-if="accountList.length > 0">
      <div class="title">您有以下账户需要进行关联关系确认</div>
      <div class="account_list">
        <div
          class="account_item"
          v-for="(item, index) in accountList"
          :key="index"
        >
          <div class="left">
            <span>{{ item.csdcHolderName }}</span> &nbsp
            <span>{{ item.stockAccount }}</span>
          </div>
          <div class="right">
            {{ item.needHandleAtTheCounter ? '需临柜办理' : '' }}
          </div>
        </div>
      </div>
      <div class="tip" v-if="needUploadNoPosition == 1">
        <p>温馨提示：</p>
        <p>
          1、证券账户关联关系指投资者名下各子证券账户与投资者一码通账户的对应关系，如名下子证券账户未确认关联关系，将会影响投资者办理证券账户开立、关键信息(名称、证件类型、证件号码)变更、休眠账户激活业务;
        </p>
        <p>
          2、请您提前准备无持仓证明与身份证<br />
          您可在中国结算APP首页-电子凭证-证券持有余额查询截止目前的证券持有余额并上传查询电子凭证截图作为无证券持有余额证明;
        </p>
        <p>3、如您需临柜办理,请提前准备未确认券商出具的资产权属证明。</p>
      </div>
      <div class="tip" v-else>
        <p>温馨提示：</p>
        <p>
          1、证券账户关联关系指投资者名下各子证券账户与投资者一码通账户的对应关系，如名下子证券账户未确认关联关系，将会影响投资者办理证券账户开立、关键信息(名称、证件类型、证件号码)变更、休眠账户激活业务;
        </p>
        <p>2、请您提前准备身份证</p>
        <p>3、如您需临柜办理,请提前准备未确认券商出具的资产权属证明。</p>
      </div>
    </div>
  </section>
</template>

<script>
import { exitApp, getPwdEncryption } from '@/common/util';
import { EVENT_NAME } from '@/common/formEnum';
import { incidenceRelationInfo } from '@/service/service.js';

export default {
  name: 'associationConfirmAccountCheck',
  inject: ['tkFlowInfo', 'eventMessage'],

  data() {
    return {
      accountList: [],
      needUploadNoPosition: '0',
      needHandleAtTheCounter: false,
      needHandleOnLine: false,
      incidenceOnline: [],
      acodeAccountSize:''
    };
  },
  computed: {
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo;
    }
  },
  watch: {
    ssoLoginFlag: {
      handler: function (userInfo) {
        console.log('userInfo', userInfo);
      },
      immediate: true
    },
    needHandleOnLine: function (value) {
      console.log('value', value);
      if (value) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '下一步',
          display: true,
          btnStatus: 2,
          data: () => {
            this.toNext();
          }
        });
      }
    }
  },
  created() {},
  mounted() {
    this.pageInit();
  },
  methods: {
    pageInit() {
      console.log(' this.flowOutputInfo.inProperty', this.flowOutputInfo);
      console.log(' this.tkFlowInfo', this.tkFlowInfo());
      console.log(' this.$attrs', this.$attrs);
      console.log('  this.$route', this.$route);
      console.log(
        ' this.$store.state.user?.userInfo',
        this.$store.state.user?.userInfo
      );
      let { branchNo, clientId, fundAccount } =
        this.$store.state.user?.userInfo;
      let { opStation } = this.tkFlowInfo().inProperty;
      let params = {
        branchNo: branchNo,
        clientId: clientId,
        fundAccount: fundAccount,
        opStation: opStation,
      };
      incidenceRelationInfo(params).then((res) => {
        console.log('res =======>', res);
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '返回',
          display: true,
          btnStatus: 2,
          data: () => {
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
        if (res.code == 0) {
          this.acodeAccountSize = res.data.acodeAccountSize
          this.needUploadNoPosition = res.data.incidenceOnline.some(
            (item) => item.needUploadNoPostition === '1'
          ) ? '1' : '0'; 
          this.needHandleAtTheCounter = res.data.incidenceOffline.length > 0;
          this.needHandleOnLine = res.data.incidenceOnline.length > 0;
          if (res.data.incidenceOffline.length > 0) {
            res.data.incidenceOffline.forEach((item) => {
              item.needHandleAtTheCounter = true;
            });
          }
          this.accountList = [
            ...res.data.incidenceOnline,
            ...res.data.incidenceOffline
          ];
          this.incidenceOnline = res.data.incidenceOnline.map(item => {
            item.fundAccount = fundAccount
            item.stockType = item.csdcHolderName
            return item
          });
        }
      });
    },
    back() {
      $h.clearSession('fromIntroduce');
      if ($hvue.platform === '0') {
        this.$router.back();
      } else {
        if ($h.getSession('introduceNeedBack')) {
          this.$router.back();
        } else {
          exitApp();
        }
      }
    },
    toNext() {
      console.log('去下一步');
      if(this.acodeAccountSize > 1){
        _hvueAlert({ mes: '您当前存在多个一码通，暂不可办理账户类业务，请先进行一码通规范。如有问题可咨询客服95310。' });
        return
      }
      let filterAccountList = this.accountList.filter((item) => {
        return item.needUploadNoPostition === '1';
      });
      console.log('filterAccountList', filterAccountList);
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        needUploadNoPosition: this.needUploadNoPosition,
        selectAccountsData: JSON.stringify(this.incidenceOnline)
      });
    }
  }
};
</script>
<style scoped>
.content {
  background-color: #fff;
}
.title {
  padding: 0.15rem 0.16rem;
  color: #2a2b30;
  font-size: 0.14rem;
  background: #f4f4f4;
}
.account_list {
  padding: 0 0.16rem;
}
.account_item {
  padding: 0.15rem 0;
  color: #333;
  font-size: 0.16rem;
  border-bottom: 0.01rem solid #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.right {
  color: #999;
  font-size: 0.14rem;
}
.tip {
  padding: 0.16rem 0.16rem 0;
  color: #999;
  font-size: 0.14rem;
  line-height: 20px;
}
.no_data_content {
  text-align: center;
  color: #333;
  font-size: 0.16rem;
  line-height: 0.24rem;
}
.no_data_icon {
  width: 1.8rem;
  height: 1.54rem;
  margin-top: 0.5rem;
  margin-bottom: 0.2rem;
}
</style>
