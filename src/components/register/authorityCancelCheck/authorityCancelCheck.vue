<template>
  <article class="content">
    <div class="com_title">
      <h5>办理此业务需要满足以下条件</h5>
    </div>
    <div class="com_box">
      <ul class="cond_list">
        <template v-for="(item, index) in viewList">
          <li
            v-if="
              item.ruleResult !== '1' && JSON.parse(item.ruleResultDesc).title
            "
            :key="index"
            :class="
              item.userInfo && item.userInfo.warning === '1'
                ? 'warning'
                : item.ruleResult === '1'
                ? 'ok'
                : 'error'
            "
          >
            <div class="tit">
              <h5>
                {{ JSON.parse(item.ruleResultDesc).title }}
                <div class="cond_info_layout" style="justify-content: flex-end">
                  <span v-if="item.userInfo.markText" class="num">{{
                    item.userInfo.markText
                  }}</span>
                  <a
                    v-if="item.userInfo.confirmLabel"
                    class="btn"
                    @click="toQuestionSave(item.factorNo)"
                    >{{ item.userInfo.confirmLabel }}</a
                  >
                  <a
                    v-if="item.userInfo.targetType && item.userInfo.targetLabel"
                    class="btn"
                    @click="
                      toUrl(
                        item.userInfo.targetUrl,
                        item.userInfo.targetType,
                        item.userInfo
                      )
                    "
                    >{{ item.userInfo.targetLabel }}</a
                  >
                </div>
              </h5>
              <p v-if="JSON.parse(item.ruleResultDesc).tips">
                <span v-html="JSON.parse(item.ruleResultDesc).tips"></span>
              </p>
            </div>
          </li>
        </template>
      </ul>
    </div>
  </article>
</template>

<script>
import {
  businessEgliCheck,
  accountConditionCheck,
  questionSave
} from '@/service/service.js';
import { RULE_RESULT } from '@/common/enumeration';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'AuthorityCancelCheck',
  inject: ['clearKeepAlive', 'eventMessage'],
  data() {
    return {
      viewList: [],
      bizName: '',
      allPass: false,
      RULE_RESULT,
      pageShow: false
    };
  },
  created() {
    // this.eventMessage(this, EVENT_NAME.NEXT_STEP);
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    this.renderingView();
    this.clearKeepAlive();
    window.viewShowCallBack = this.viewShowCallBack;
    let _this = this;
    document.addEventListener(
      'resume',
      function () {
        console.log('resumed');
        _this.renderingView();
      },
      false
    );
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  /*activated() {
    this.renderingView();
  },*/
  // deactivated() {
  //   this.clearKeepAlive();
  // },
  methods: {
    viewShowCallBack() {
      this.renderingView();
    },
    toQuestionSave(factorNo) {
      let questionnaireType = 'ageNotMatchPro';
      if (factorNo === 'oftenAddressInfo') {
        questionnaireType = 'addressDoubt';
      }
      questionSave({
        questionnaireType
      }).then((res) => {
        this.renderingView();
      });
    },
    async toUrl(url, type, { bizType, targetLabel }) {
      // 准入跳转业务新跳转方式必须使用push
      if (targetLabel === '重新测评' || targetLabel === '更新身份证' || targetLabel === '修改个人资料') {
        let biz = '';
        if (targetLabel === '重新测评') {
          biz = '010013';
        }
        if (targetLabel === '更新身份证') {
          biz = '010006';
        }
        if (targetLabel === '修改个人资料') {
          biz = '010004';
        }
        import('@/common/flowMixinV2.js').then((a) => {
          a.initFlow.call(this, { bizType: biz, initJumpMode: '0'  });
        });
        return;
        // let configKey = 'bc.opt.urlback.010013';
        // let { data } = await getConfigMap({
        //   configKey
        // });
        // if (data[configKey].configValue === '0') {
        //   $h.setSession('initJumpMode', '0');
        //   import('@/common/flowMixin.js').then((a) => {
        //     a.initFlow.call(this, '010013');
        //   });
        //   return;
        // }
      }
      if (type === '2') {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '11',
          // targetModule: 'open',
          params: {
            type: 20,
            tradeType: 1
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
        return;
      }
      let targetUrl = '';
      if (url.includes('http')) {
        targetUrl = url;
      } else {
        $h.setSession('store', JSON.stringify(this.$store.state));
        targetUrl = $hvue.customConfig.targetUrl + url;
      }
      if ($hvue.platform == 0) {
        if ($h.getSession('channelType') === '2095000000000') {
          targetUrl = targetUrl + '&sd_token=' + $h.getSession('authorization');
          // 支付宝渠道
          AlipayJSBridge.call('pushWindow', {
            url: targetUrl,
            param: {
              readTitle: true
            }
          });
        } else {
          window.location.href = targetUrl;
        }
      } else {
        // let params = {
        //   actionType: '6',
        //   url: targetUrl
        //   // leftType: 1,
        //   // rightType: 99,
        //   // rightText: ''
        // };
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          // targetModule: 'open',
          params: {
            url: targetUrl,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      }
    },

    renderingView() {
      let _this = this;
      const flowToken = sessionStorage.getItem('TKFlowToken');
      businessEgliCheck({
        flowToken
      })
        .then((data) => {
          _hvueLoading.close();
          _this.pageShow = true;
          _this.viewList = data.data.result;
          const strategyResult = data.data.strategyResult;
          if (strategyResult === RULE_RESULT.pass) {
            _this.allPass = true;
            if (this.$attrs.isAutoNext) {
              this.eventMessage(this, EVENT_NAME.NEXT_STEP);
            }
            this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: true });
          } else {
            if ($hvue.platform == 0 && $h.getSession('from') === 'pc') {
              this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
              return;
            }
            this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
              text: '返回首页',
              display: true,
              btnStatus: 2,
              data: () => {
                this.eventMessage(this, EVENT_NAME.TO_INDEX);
              }
            });
          }

          // accountConditionCheck({
          //   flowToken: sessionStorage.getItem('TKFlowToken')
          // }).then((res) => {
          //   _hvueLoading.close();
          //   _this.pageShow = true;
          //   _this.viewList = data.data.result;
          //   const strategyResult = data.data.strategyResult;
          //   let arr = res.data.map((item) => {
          //     return {
          //       ruleResult: item.result ? '1' : '0',
          //       ruleResultDesc: JSON.stringify({
          //         title: item.title,
          //         tips: item?.tips
          //       }),
          //       userInfo: { markText: '' }
          //     };
          //   });
          //   let arrFlag =
          //     JSON.parse(JSON.stringify(arr)).filter(
          //       (item) => item.ruleResult === '0'
          //     ).length > 0
          //       ? false
          //       : true;
          //   _this.viewList = _this.viewList.concat(arr);
          //   console.log(_this.viewList);
          //   if (strategyResult === RULE_RESULT.pass && arrFlag) {
          //     _this.allPass = true;
          //     if (this.$attrs.isAutoNext) {
          //       this.eventMessage(this, EVENT_NAME.NEXT_STEP);
          //     }
          //     this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: true });
          //   } else {
          //     this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          //       text: '返回首页',
          //       display: true,
          //       btnStatus: 2,
          //       data: () => {
          //         this.eventMessage(this, EVENT_NAME.TO_INDEX);
          //       }
          //     });
          //   }
          // });
        })
        .catch((err) => {
          _hvueLoading.close();
          _hvueToast({ mes: err });
        });
    }
  }
};
</script>

<style></style>
