<template>
  <fragment>
    <div class="com_title">
      <h5>请绑定持卡人本人的银行借记卡（不能是信用卡）</h5>
    </div>
    <div class="com_box">
      <div class="input_form spel">
        <div class="input_text text">
          <span class="tit active">持卡人</span>
          <input
            v-model="$attrs.clientName"
            class="t1 disabled"
            type="text"
            disabled="disabled"
          />
        </div>
        <div v-show="ocrBankImage !== ''" class="tpbank_img">
          <img :src="ocrBankImage" />
        </div>
        <div class="input_text text">
          <span class="tit">卡号</span>
          <input
            v-model="bkaccountNew"
            class="t1"
            type="tel"
            placeholder="输入银行卡号"
            maxlength="23"
            @input="bankCardInputFormat"
            @blur="isShowBigBankNo = false"
          />
          <a class="icon_photo" @click="ocrParseBankCard"></a>
          <div v-show="isShowBigBankNo" class="num_layer">
            {{ bkaccountNew }}
          </div>
        </div>
        <div class="input_text text">
          <span class="tit active">所属银行</span>
          <div class="dropdown" placeholder="请选择" @click="showPicker = true">
            <div v-if="selectBankData.bankName">
              <img :src="`${fileUrl}${selectBankData.bankLogo}`" />{{
                selectBankData.bankName
              }}
            </div>
          </div>
        </div>
        <div v-if="needPassword" class="input_text">
          <input
            v-model="bkPasswordNew"
            class="t1"
            :type="!isShowPwd ? 'password' : 'tel'"
            maxlength="6"
            placeholder="请输入银行卡交易密码"
          />
          <a
            class="icon_eye"
            :class="{ show: isShowPwd }"
            @click="isShowPwd = !isShowPwd"
          ></a>
        </div>
        <div v-if="needPassword" class="input_text">
          <input
            v-model="bkPasswordNewConfirm"
            class="t1"
            :type="!isShowPwd ? 'password' : 'tel'"
            maxlength="6"
            placeholder="请确认银行卡交易密码"
          />
        </div>
      </div>
    </div>
    <agreement-sign
      v-if="selectBankData.bankNo"
      v-model="isAgreeChecked"
      :group-id="$attrs.groupId"
      :bank-id="selectBankData.bankNo"
      :contract-type="$attrs.contractType"
      @agree-list="agreeCallback"
    />
    <bank-view-list></bank-view-list>
    <div class="ce_btn mt15">
      <a class="p_button" @click="triggerEvent">下一步</a>
    </div>
    <van-popup v-model="showPicker" round position="bottom">
      <t-picker
        v-model="selectBankData"
        type="bank"
        :columns="columns"
        @cancel="showPicker = false"
      />
    </van-popup>
    <get-img-box-browser
      ref="getImgBoxBrowser"
      :scan="true"
      @getImgCallBack="getImgCallBack"
    />
  </fragment>
</template>

<script>
import bankViewList from '@/components/register/bankInfo/bankViewList';
import TPicker from '@/components/TPicker';
import getImgBoxBrowser from '@/components/getImg_browser';
import { uploadFile, getPwdEncryption, signAgree } from '@/common/util';
import { getBank } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import agreementSign from '@/components/agreementSign';
export default {
  name: 'BankInput',
  components: {
    bankViewList,
    getImgBoxBrowser,
    agreementSign,
    TPicker
  },
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      fileUrl: $hvue.customConfig.fileUrl,
      selectBankData: {},
      ocrBankImage: '',
      columns: [],
      showPicker: false,
      showAgreeDetail: false,
      isAgreeChecked: true,
      readList: [],
      agreeDetail: {},
      bkaccountNew: '',
      bankNoNew: '',
      bkPasswordNew: '',
      bkPasswordNewConfirm: '',
      isShowPwd: false,
      isShowBigBankNo: false
    };
  },
  computed: {
    isCount() {
      return this.readList.includes(this.selectBankData.bankNo);
    },
    needPassword() {
      return this.selectBankData.needPassword === '1';
    }
  },
  /*watch: {
    'selectBankData.bankNo'(bankNo) {
      if (bankNo) this._queryAgreement(bankNo);
    }
  },*/
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    this.getBankList();
  },
  methods: {
    selectAgree() {
      this.isAgreeChecked = !this.isAgreeChecked;
    },
    triggerEvent() {
      const { clientName, bankNo } = this.$attrs;
      let errorMessage = '';
      let errorTitle = '温馨提示';
      let bankNoNew = this.selectBankData.bankNo;
      let {
        bkaccountNew,
        bkPasswordNew,
        isAgreeChecked,
        bkPasswordNewConfirm
      } = this;
      let oldBankNo = bankNo;
      if (oldBankNo && oldBankNo === bankNoNew) {
        this.$TAlert({
          title: errorTitle,
          tips: '暂不能修改与现有银行相同的银行，如要修改卡号请前往该存管银行营业点或通过银行APP修改卡号',
          confirmBtn: '重新输入'
        });
        return;
      }
      bkaccountNew = bkaccountNew.replace(/\s+/g, '');
      const bankRegExp = /^[\d\s]{13,19}$/;
      const pwdRegExp = /^[\d\s]{6}$/;
      if (bkaccountNew === '') {
        errorMessage = '请输入银行卡号';
      } else if (!bankRegExp.test(bkaccountNew)) {
        errorMessage = '银行卡号格式不正确';
      } else if (!bankNoNew) {
        errorMessage = '请选择所属银行';
      } else if (this.needPassword) {
        if (bkPasswordNew === '') {
          errorMessage = '请输入密码';
        } else if (bkPasswordNewConfirm !== bkPasswordNew) {
          errorTitle = '两次输入的密码不一致';
          errorMessage = '您输入的银行卡交易密码不一致，请重新输入';
        } else if (!pwdRegExp.test(bkPasswordNew)) {
          errorMessage = '银行卡密码格式不正确';
        }
      } else if (!isAgreeChecked) {
        errorMessage = '请阅读并勾选协议';
      }
      if (errorMessage) {
        this.$TAlert({
          title: errorTitle,
          tips: errorMessage,
          confirmBtn: '重新输入'
        });
        return;
      }
      const tkFlowInfo = this.tkFlowInfo();
      signAgree(tkFlowInfo, [{ ...this.agreeDetail }])
        .then((epaperSignJson) => {
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
            clientName,
            bkaccountNew,
            bankNoNew,
            epaperSignJson,
            bkPasswordNew: getPwdEncryption(bkPasswordNew)
          });
        })
        .catch((error) => {
          _hvueToast({
            mes: error
          });
        });
    },
    getBankList() {
      getBank({ allowRepeat: true }).then((res) => {
        this.columns = res.data;
      });
    },
    agreeCallback(data) {
      this.agreeDetail = data[0];
    },
    ocrParseBankCard() {
      if ($hvue.platform == 0) {
        this.$refs.getImgBoxBrowser.getImg();
      } else {
        window.bankOcrCallBack = this.bankOcrCallBack;
        // 调用功能号60016获取
        let config = {
          funcNo: '60016',
          isAlbum: '0', //是否显示相册 1表示显示相册，0或其他表示不显示 默认隐藏
          isTake: '1', //是否显示拍照按钮
          mainColor: $hvue.customConfig.mainColor,
          compressSize: 200, //原生压缩大小 不传默认200k
          moduleName: $hvue.customConfig.moduleName // 必须为open
        };
        console.log(config);
        let result = $h.callMessageNative(config);
        if (result.error_no !== '0') {
          console.log({ mes: result.error_info });
        }
        console.log(result);
      }
    },
    bankCardInputFormat(e) {
      let obj = e.target;
      obj.prevValue = obj.prevValue ? obj.prevValue : obj.value;
      let beforeSelectionIndex = obj.selectionStart;
      obj.value = obj.value.replace(/ /g, '').replace(/(\d{4})+?/g, '$& ');
      // 判断是输入还是退格
      obj.prevValue.length < obj.value.length
        ? beforeSelectionIndex % 5 === 0 && ++beforeSelectionIndex // 输入
        : beforeSelectionIndex % 5 === 0 && --beforeSelectionIndex; // 退格
      //webkit内核老版本问题 setSelectionRange失效   https://bugs.chromium.org/p/chromium/issues/detail?id=32865
      setTimeout(function () {
        obj.setSelectionRange(beforeSelectionIndex, beforeSelectionIndex);
      }, 0);
      this.bkaccountNew = obj.value;
      obj.prevValue = obj.value;
      this.isShowBigBankNo = this.bkaccountNew.length > 0;
    },
    bankOcrCallBack(data) {
      if (typeof data === 'string') {
        data = JSON.parse(data);
      }
      const { cardNumber, bankName } = data;
      let selBankData = this.columns.filter((item) => {
        return bankName.includes(item.bankName);
      })[0];
      if (selBankData) this.selectBankData = selBankData;
      this.bkaccountNew = cardNumber;
    },
    getImgCallBack(imgInfo) {
      // _hvueLoading.open();
      uploadFile(
        $hvue.customConfig.serverUrl + '/client/ocrParseBankCard',
        imgInfo.base64,
        {
          success: async (data) => {
            // _hvueLoading.close();
            if (data.code === 0) {
              const { cardNumber, bankId } = data.data;
              if (cardNumber && cardNumber !== '') {
                let selBankData = this.columns.filter((item) => {
                  return item.bankId === bankId;
                })[0];
                if (selBankData) this.selectBankData = selBankData;
                this.bkaccountNew = cardNumber;
                this.ocrBankImage = `${this.fileUrl}${data.data.path}`;
              }
            } else {
              _hvueAlert({ mes: data.msg });
            }
          },
          progress: (count) => {
            console.log(count);
          },
          error: (e) => {
            _hvueLoading.close();
            console.log(e);
          }
        },
        {}
      );
    }
  }
};
</script>
