<template>
  <fragment>
    <van-field
      readonly
      clickable
      :label="$attrs.label"
      :value="value"
      :placeholder="$attrs.placeholder"
      @click="showPicker = true"
    />
    <van-popup v-model="showPicker" round position="bottom">
      <van-picker
        show-toolbar
        value-key="bankName"
        :columns="columns"
        @cancel="showPicker = false"
        @confirm="onConfirm"
      />
    </van-popup>
  </fragment>
</template>

<script>
import { getBank } from '@/service/service';

export default {
  name: 'BankSelect',
  inject: ['setPropsByForm'],
  data() {
    return {
      value: '',
      columns: [],
      showPicker: false
    };
  },
  watch: {
    '$attrs.bankName': {
      handler(newVal) {
        this.value = newVal;
        for (let item of this.columns) {
          if (item.bankName === newVal) {
            this.setPropsByForm(this.$attrs.propKey, 'bankId', item.bankId);
            this.$emit('change', item.bankNo);
            break;
          }
        }
      },
      deep: false,
      immediate: false
    }
  },
  created() {
    this.getBankList();
  },
  methods: {
    getBankList() {
      getBank({ allowRepeat: true }).then((res) => {
        this.columns = res.data;
      });
    },
    onConfirm(data) {
      this.value = data.bankName;
      this.showPicker = false;
      this.setPropsByForm(this.$attrs.propKey, 'bankName', data.bankName);
    }
  }
};
</script>

<style scoped></style>
