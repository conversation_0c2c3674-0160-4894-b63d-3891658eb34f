<template>
  <div class="tpbank_zcbox">
    <h5 class="title">我们支持的银行借记卡如下：</h5>
    <ul class="available_bklist">
      <li v-for="(item, index) in bankList" :key.camel="index">
        <img :src="`${fileUrl}${item.bankLogo}`" />
        <span>{{ item.bankName }}</span>
      </li>
    </ul>
  </div>
</template>

<script>
import { getBank } from '@/service/service';

export default {
  name: 'BankViewList',
  data() {
    return {
      fileUrl: $hvue.customConfig.fileUrl,
      bankList: []
    };
  },
  created() {
    this.getBankList();
  },
  methods: {
    getBankList() {
      getBank({ allowRepeat: true }).then((res) => {
        this.bankList = res.data;
      });
    }
  }
};
</script>

<style scoped></style>
