<template>
  <div v-if="bankList">
    <div v-if="mainCardData">
      <div class="tp_mid_title">
        <h5>主资金账户存管银行</h5>
      </div>
      <div class="p_tpbank_list">
        <div class="p_tpbank_card zhu">
          <div class="tp_bank_info">
            <div class="pic">
              <img :src="`${fileUrl}${mainCardData.bankLogo}`" />
            </div>
            <div class="cont">
              <h5>{{ mainCardData.bankName }}<i class="tp_tag zhu"></i></h5>
              <div class="num">
                {{ mainCardData.bankCardNo | formatBankCardNo }}
              </div>
            </div>
            <a class="link" @click="changeBankCard">更换</a>
          </div>
        </div>
      </div>
      <div class="p_tpbank_list">
        <div class="p_tpbank_card">
          <div
            v-for="({ bankLogo, bankCardNo }, index) in bankList"
            :key="index"
            class="tp_bank_info"
          >
            <div class="pic">
              <img :src="`${fileUrl}${bankLogo}`" />
            </div>
            <div class="cont">
              <h5>{{ item.bankName }}<i class="tp_tag"></i></h5>
              <div class="num">
                {{ formatBankCardNo(bankCardNo) }}
              </div>
            </div>
            <!--            <a class="link">更换</a>-->
          </div>
        </div>
      </div>
    </div>
    <div v-else class="nodata_box">
      <div class="icon">
        <img :src="require('@/assets/images/noData.svg')" />
      </div>
      <p>没有绑定三方存管</p>
    </div>
  </div>
</template>

<script>
import { bindedBankQry } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'GetUserBank',
  inject: ['eventMessage'],
  data() {
    return {
      fileUrl: $hvue.customConfig.fileUrl,
      mainCardData: null,
      bankList: null
    };
  },
  created() {
    this._bindedBankQry();
  },
  methods: {
    _bindedBankQry() {
      bindedBankQry({}).then(({ data }) => {
        this.mainCardData = data.filter(
          ({ isMainCard }) => isMainCard === '1'
        )[0];
        this.bankList = data.filter(({ isMainCard }) => isMainCard !== '1');
        if (this.mainCardData) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
        } else {
          this.$emit('change', {
            isChangeBank: 0
          });
        }
      });
    },
    changeBankCard() {
      const { bankCardNo, bankId } = this.mainCardData;
      const data = {
        isChangeBank: 1,
        bankNo: bankId,
        bankAccount: bankCardNo
      };
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, data);
    }
  }
};
</script>

<style scoped></style>
