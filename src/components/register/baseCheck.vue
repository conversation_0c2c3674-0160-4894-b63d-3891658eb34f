<template>
  <div>
    <template v-if="show">
      <div class="dialog_overlay"></div>
      <div class="dialog_box">
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>{{ tips.title }}</h3>
          <div>
            <p>{{ tips.tips }}</p>
          </div>
        </div>
        <div class="dialog_btn">
          <a v-if="!tips.bizType" @click="toIndex">我知道了</a>
          <a v-if="tips.bizType" @click="toBizType">前往完善</a>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { baseInfoCheck } from '@/service/service.js';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'BaseCheck',
  inject: ['eventMessage'],
  props: {
    api: {
      type: String,
      default: ''
    },
    bizType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: false,
      tips: {}
    };
  },
  mounted() {
    this.baseckeck();
  },
  activated() {
    this.baseckeck();
  },
  // deactivated() {
  //   this.clearKeepAlive();
  // },
  methods: {
    toIndex() {
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    },

    toBizType() {
      this.eventMessage(this, 'toBizType', { bizType: this.tips.bizType });
    },

    baseckeck() {
      baseInfoCheck({
        flowToken: sessionStorage.getItem('TKFlowToken')
      }).then((res) => {
        if (res.data.tips) {
          this.show = true;
          this.tips = JSON.parse(res.data.tips);
        }
        if (res.data.strategyResult === '1') {
          this.eventMessage(this, 'nextStep');
        }
      });
    }
  }
};
</script>

<style lang="less" scope></style>
