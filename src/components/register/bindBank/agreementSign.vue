<template>
  <fragment>
    <div class="rule_check">
      <span
        class="icon_check"
        :class="{ checked: isChecked }"
        @click="selectAgree"
      ></span
      >本人已详细阅读并同意签署以下协议
      <a
        v-for="({ agreementName, agreementId }, index) in agreeList"
        :key="index"
        :class="{ read: readList.includes(agreementId) }"
        @click="openAgree(index)"
      >
        《{{ agreementName }}》
      </a>
    </div>
    <agreementDetail
      v-if="showAgreeDetail"
      :show="showAgreeDetail"
      :info="agreeList[agreeIndex]"
      :is-count="false"
      @callback="agreeCallBack"
    />
  </fragment>
</template>

<script>
import agreementDetail from '@/components/agreementDetail';
import {
  getJwtToken,
  queryAgreementExt,
  updateFlowForm
} from '@/service/service';

export default {
  name: 'AgreementSign',
  inject: ['tkFlowInfo'],
  components: {
    agreementDetail
  },
  model: {
    prop: 'isChecked',
    event: 'change'
  },
  props: {
    isChecked: {
      type: Boolean,
      default: false
    },
    groupId: {
      type: String,
      default: ''
    },
    bankId: {
      type: String,
      default: ''
    },
    contractType: {
      type: String,
      default: ''
    },
    selectBankData: {
      type: Object,
      default: () => {}
    },
    isCredit: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      showAgreeDetail: false,
      agreeIndex: 1,
      readList: [],
      agreeList: []
    };
  },
  computed: {
    isCount() {
      return this.readList.includes(this.agreeId);
    },
    agreeId() {
      return this.agreeList[this.agreeIndex]?.agreementId;
    },
    allReadFlag() {
      return this.agreeList.every((a) => {
        return this.readList.includes(a.agreementId);
      });
    }
  },
  watch: {
    bankId(bankId) {
      if (bankId) this._queryAgreement();
    },
    groupId() {
      this._queryAgreement();
    }
  },
  created() {
    this._queryAgreement();
  },
  methods: {
    async _queryAgreement() {
      const { flowName } = this.tkFlowInfo();
      const { selectBankData } = this;
      updateFlowForm({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        source: `${flowName}-初始化`,
        formParam: {
          bankNo: selectBankData.bankNo
        }
      }).then((res) => {
        let reqParams = {
          flowToken: sessionStorage.getItem('TKFlowToken'),
          agreementBizType: this.isCredit? 'PBN00041':'PBN00024',
          agreementNodeNo: this.isCredit? 'BNN00071':'BNN00035',
          agreementExt: 'bankNo,eleSignAgr'
        };
        if (this.bankId) reqParams.agreementSubtype = this.bankId;
        queryAgreementExt(reqParams)
          .then((res) => {
            this.agreeList = res.data;
            if (reqParams.agreementSubtype);
            if(this.agreeList.length > 0){
              this.agreeList[0].bankId = reqParams.agreementSubtype;
            }
            this.checkEvent(this.allReadFlag);
            this.$emit('agree-list', this.agreeList);
          })
          .catch((err) => {
            _hvueToast({
              mes: err
            });
          });
      });
    },
    openAgree(i) {
      this.showAgreeDetail = true;
      this.agreeIndex = i;
    },
    selectAgree() {
      this.checkEvent(!this.isChecked);
    },
    agreeCallBack(flag) {
      console.log('flag=====', flag);
      this.showAgreeDetail = false;
      if (flag) {
        if (!this.isCount && !this.readList.includes(this.agreeId)) {
          console.log('this.agreeId', this.agreeId);
          this.readList.push(this.agreeId);
        }
        this.checkEvent(this.allReadFlag);
      }
    },
    checkEvent(checkFlag) {
      this.$emit('change', checkFlag);
    }
  }
};
</script>

<style scoped>
a.read {
  color: #999999;
}
</style>
