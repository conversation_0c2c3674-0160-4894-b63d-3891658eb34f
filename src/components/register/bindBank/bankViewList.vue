<template>
  <div class="tpbank_zcbox">
    <h5 class="title">我们支持的银行借记卡如下：</h5>
    <ul class="available_bklist">
      <li v-for="(item, index) in bankList" :key.camel="index">
        <div class="bank-item">
          <img :src="`data:image/jpeg;base64,${item.bankLogo}`" />
          <span>{{ item.bankName }}</span>
          <span v-show="showTag(item)" class="transfer-tag">7*24h转账</span>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    bankList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {};
  },
  methods: {
    showTag({ support7x24Flag = '', counterKind = '' }) {
      // counterKind 0普通柜台 1信用柜台
      // support7x24Flag 0不支持 1普通柜台支持 2信用柜台支持
      if (!support7x24Flag) return false;
      const support7x24FlagList = support7x24Flag.split(',');
      const counterKindList = counterKind.split(',');
      return counterKindList.some((kind) => {
        const expectedFlag = String(Number(kind) + 1);
        return support7x24FlagList.includes(expectedFlag);
      });
    }
  }
};
</script>

<style scoped>
.bank-item {
  position: relative;
  display: flex;
  align-items: center;
  top: 0.04rem;
}

.transfer-tag {
  position: absolute;
  top: -0.12rem;
  right: 0;
  background-color: #fff2e3;
  color: #ff7015;
  font-size: 0.09rem;
  line-height: 0.14rem;
  padding: 0 0.06rem;
  border-radius: 0.1rem 0.1rem 0.1rem 0;
  border: 0.005rem solid #ff7015;
  font-family: 'PingFang SC';
  font-weight: 400;
}
</style>
