<template>
  <section class="main fixed container white_bg" data-page="home">
    <div class="title">请填写本人名下养老金银行卡</div>
    <div class="input_form spel">
      <div class="input_text text">
        <span class="tit">银行卡号</span>
        <input v-model="pensionBank.bankAccount" class="t1" type="tel" placeholder="输入银行卡号" maxlength="19"
          @input="inputFn" />
        <!-- <a class="icon_photo" @click="ocrParseBankCard" /> -->
      </div>
      <div class="input_text text">
        <span class="tit active">所属银行</span>
        <div class="dropdown" placeholder="请选择签约银行" @click="showPickerFn">
          <div v-if="pensionBank.bankName">
            <img :src="`data:image/jpeg;base64,${selectBankData.bankLogo}`" />
            <span>
              {{ pensionBank.bankName }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="warm_tip">
      温馨提示：请输入您在银行开通的养老金银行卡信息，具体信息可在银行APP内查看。
    </div>
    <van-popup v-model="showPicker" round position="bottom">
      <t-picker v-model="selectBankData" type="bank" :columns="columns" @cancel="showPicker = false" />
    </van-popup>
    <getImgBoxApp ref="getImgBoxApp" @getImgCallBack="getImgCallBack" />
    <get-img-box-browser ref="getImgBoxBrowser" :scan="true" @getImgCallBack="getImgCallBack" />
  </section>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
import TPicker from '@/components/TPicker';
import getImgBoxBrowser from '@/components/getImg_browser';
import getImgBoxApp from './getImg_app';
import { uploadFile } from '@/common/util';
import { csdcBankQry, binquery } from '@/service/service';

export default {
  name: 'bindBankForPension',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {
    getImgBoxBrowser,
    TPicker,
    getImgBoxApp
  },
  data() {
    return {
      columns: [], // 银行卡下拉列表
      showPicker: false, //是否展示银行列表
      selectBankData: {}, // 选中的银行
      cardBinResult: {},
      pensionBank: {
        bankAccount: '',
        bankName: '',
        dcType: '', //银行卡类型
        bankNo: ''
      }
    };
  },
  computed: {
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo?.fundAccount;
    },
    watchObj() {
      let { pensionBank, selectBankData } = this;
      return {
        bankAccount: pensionBank.bankAccount,
        selectBankData
      };
    }
  },
  watch: {
    ssoLoginFlag: {
      handler: function (fundAccount) {
        console.log(fundAccount);
        if (fundAccount) {
          this.renderingView();
        }
      },
      immediate: true
    },
    selectBankData: function (val, oldVal) {
      this.pensionBank.bankName = this.selectBankData.bankName;
      this.pensionBank.bankNo = this.selectBankData.bankNo;
    },
    watchObj: function (val, oldVal) {
      let { bankAccount, selectBankData } = val;
      if (bankAccount && selectBankData.bankNo) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          btnStatus: 2,
          data: () => {
            this.goNext();
          }
        });
      } else {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          btnStatus: 0
        });
      }
    }
  },
  created() {
    console.log('test=================');
    window.viewShowCallBack = this.viewShowCallBack;
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
      btnStatus: 0
    });
  },

  destroyed() {
    window.viewShowCallBack = null;
  },
  methods: {
    viewShowCallBack() {
      this.renderingView();
    },
    renderingView() {
      csdcBankQry().then((res) => {
        console.log('获取银行列表', res);
        const { code, data } = res;
        if (code == '0') {
          this.columns = data;
        }
      });
    },
    inputFn(e) {
      this.pensionBank.bankAccount = e.target.value.replace(/\s/g, '');
      if (
        this.pensionBank.bankAccount.length >= 16 &&
        this.pensionBank.bankAccount.length <= 19
      ) {
        this.queryBin();
      }
    }, // 展示银行列表
    showPickerFn() {
      this.showPicker = true;
    },
    // 卡bin 查询
    queryBin() {
      console.log('卡bin查询');
      let params = {
        cardNo: this.pensionBank.bankAccount
      };
      binquery(params).then((res) => {
        console.log('res =====', res);
        if (res.code == 0) {
          const { cardIssuerName, dcType } = res.data;
          this.cardBinResult = res.data;
          this.pensionBank.dcType = dcType;
          let selectBankData = this.columns.filter((item) => {
            return cardIssuerName.includes(item.bankName);
          })[0];
          if (selectBankData) {
            this.selectBankData = selectBankData;
          } else {
            this.selectBankData = {};
          }
        } else {
          this.selectBankData = {};
        }
      });
    },
    // 银行卡ocr识别
    ocrParseBankCard() {
      if ($hvue.platform == 0) {
        this.$refs.getImgBoxBrowser.getImg();
      } else {
        this.$refs.getImgBoxApp.getImg();
      }
    },
    // ocr识别回调
    getImgCallBack(imgInfo) {
      if (!imgInfo.base64) {
        return;
      }
      _hvueLoading.open();
      uploadFile(
        $hvue.customConfig.serverUrl + '/pension/ocrParseBankCard',
        imgInfo.base64,
        {
          success: async (res) => {
            _hvueLoading.close();
            if (res.code === 0) {
              const { cardNumber, bankNo, type } = res.data;
              this.pensionBank.bankAccount = cardNumber.replace(/\s/g, '');
              this.pensionBank.dcType = type;
              if (bankNo && bankNo != '') {
                let selectBankData = this.columns.filter((item) => {
                  return item.bankNo === bankNo;
                })[0];
                if (selectBankData) {
                  this.selectBankData = selectBankData;
                } else {
                  this.selectBankData = {};
                }
              } else {
                this.selectBankData = {};
              }
            } else {
              _hvueAlert({ mes: res.msg });
            }
          },
          progress: (count) => {
            console.log(count);
          },
          error: (e) => {
            _hvueLoading.close();
            console.log(e);
          }
        },
        {}
      );
    },
    goNext() {
      // binquery({
      //   cardNo: this.pensionBank.bankAccount
      // }).then((res) => {
      //   this.cardBinResult = res.data;
      //   if (this.cardBinResult.dcType === '1') {
      //     this.$TAlert({
      //       tips: '该银行卡为信用卡，请输入您本人的借记卡进行三方存管绑定。'
      //     });
      //     return;
      //   }
      //   let regBankNum = /^\d{16,19}$/;
      //   if (!regBankNum.test(this.pensionBank.bankAccount.replace(/\s/g, ''))) {
      //     _hvueToast({ mes: '请输入正确格式银行卡卡号' });
      //     return;
      //   }
      //   if (
      //     this.cardBinResult?.cardIssuerName &&
      //     this.pensionBank.bankName != this.cardBinResult?.cardIssuerName
      //   ) {
      //     this.$TAlert({
      //       tips: '您输入的银行卡号和所选银行可能不匹配，请确认您输入的信息正确，才能成功办理哦。',
      //       hasCancel: true,
      //       confirmBtn: '继续办理',
      //       confirm: () => {
      //         this.nextStep();
      //       },
      //       cancel: () => {}
      //     });
      //   } else {
      //     this.nextStep();
      //   }
      // });
      let regBankNum = /^\d{16,19}$/;
      if (!regBankNum.test(this.pensionBank.bankAccount.replace(/\s/g, ''))) {
        _hvueToast({ mes: '请输入正确格式银行卡卡号' });
        return;
      }
      this.nextStep();
    },
    nextStep() {
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        bankAccount: this.pensionBank.bankAccount,
        bankNo: this.pensionBank.bankNo
      });
    }
  }
};
</script>

<style scoped lang="less">
.title {
  padding: 0.15rem 0.16rem;
  color: #0f0f1b;
  font-size: 14px;
  font-weight: 500;
  line-height: 18px;
}

.bank_info {
  background-color: #fff;
  padding: 0.16rem;
}

.warm_tip {
  color: #55555e;
  font-size: 14px;
  line-height: 22px;
  margin-top: 0.2rem;
  padding: 0 0.16rem;
}
</style>
