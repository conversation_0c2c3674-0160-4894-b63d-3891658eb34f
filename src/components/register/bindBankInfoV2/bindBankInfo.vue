<template>
  <section
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <t-header title="绑定存管银行" @back="back"></t-header>
    <article class="content">
      <div class="com_title">
        <h5>请绑定持卡人本人的银行借记卡（不能是信用卡）</h5>
      </div>
      <div class="com_box">
        <div class="input_form spel">
          <div class="input_text text">
            <span class="tit">卡号</span>
            <!--  e.value.replace(/[^0-9]/g, '') -->
            <input
              class="t1"
              type="tel"
              placeholder="输入银行卡号"
              v-model="bankCardNumber"
              :maxlength="19"
              @input="bankCardInput"
              @blur="checkBank"
            />
            <a class="icon_photo" @click="ocrParseBankCard"></a>
          </div>
          <div class="input_text text">
            <span class="tit active">所属银行</span>
            <div
              class="dropdown"
              placeholder="请选择"
              @click="showSelect = true"
            >
              <img
                v-if="selectedBank && selectedBank.bankName"
                :src="`data:image/jpeg;base64,${selectedBank.bankLogo}`"
              />{{ selectedBank.bankName }}
              <span v-if="!selectedBank.bankName" style="color: #999999"
                >请选择所属银行</span
              >
            </div>
          </div>
        </div>
      </div>
      <div v-if="agreeList.length > 0" class="rule_check">
        <span
          class="icon_check"
          :class="{ checked: signed }"
          @click="signed = !signed"
        ></span
        >本人已详细阅读并同意签署以下协议<a @click="toDetail(0)">{{
          agreeList[0].agreementName
        }}</a>
      </div>
      <div class="tpbank_zcbox">
        <h5 class="title">我们支持的银行借记卡如下：</h5>
        <ul class="available_bklist">
          <li v-for="(item, index) in bankList" :key="index">
            <!-- <img :src="`${fileUrl}${item.bankLogo}`" /><span>{{
              item.bankName
            }}</span> -->
            <div class="bank-item">
              <img :src="`data:image/jpeg;base64,${item.bankLogo}`" /><span>{{
                item.bankName
              }}</span>
              <span v-show="showTag(item)" class="transfer-tag">7*24h转账</span>
            </div>
          </li>
        </ul>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" :class="{ disabled: !DisabledBtn }" @click="toNext"
          >下一步</a
        >
      </div>
    </footer>

    <!-- <div v-if="showSelect">
      <div class="dialog_overlay"></div>
      <div class="layer_box">
        <div class="layer_tit">
          <h3>所属银行</h3>
          <a class="close" @click="showSelect = false"></a>
        </div>
        <div class="layer_cont">
          <ul class="select_list">
            <li
              v-for="(item, index) in bankList"
              :key="index"
              @click="selectBank(item)"
              :class="{ active: item.bankNo == selectedBank.bankNo }"
            >
              <span
                ><img :src="`data:image/jpeg;base64,${item.bankLogo}`" />{{
                  item.bankName
                }}</span
              >
            </li>
          </ul>
        </div>
      </div>
    </div> -->
    <getImgBoxApp ref="getImgBoxApp" @getImgCallBack="getImgCallBack" />
    <get-img-box-browser
      ref="getImgBoxBrowser"
      :scan="true"
      @getImgCallBack="getImgCallBack"
    />
    <protocol-detail
      v-if="showAgreeDetail"
      v-model="agreeIndex"
      :agree-list="agreeList"
      :read-list="readList"
      :is-count="!isCount"
      @callback="agreeCallBack"
      @signAgree="detailSignAgree"
    />
    <van-popup v-model="showSelect" round position="bottom">
      <bank-picker
        v-model="selectedBank"
        type="bank"
        :columns="bankList"
        @cancel="showSelect = false"
      />
    </van-popup>
  </section>
</template>

<script>
import {
  queryAgreementExtV3,
  updateFlowForm,
  addClientCritMark
} from '@/service/service';
import getImgBoxBrowser from '@/components/getImg_browser';
import getImgBoxApp from './components/getImg_app';
import { selectBcBankBybankNo, bankCardBINQuery } from '@/service/lrService';
import { EVENT_NAME } from '@/common/formEnum';
import { uploadFile } from '@/common/util';
import ProtocolDetail from './components/ProtocolDetail';
import { signAgreeV2 } from '@/common/util';
import BankPicker from '@/components/BankPicker';

export default {
  name: 'bindBankInfoV2',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {
    getImgBoxBrowser,
    ProtocolDetail,
    getImgBoxApp,
    BankPicker
  },
  data() {
    return {
      showSelect: false,
      fileUrl: $hvue.customConfig.fileUrl,
      bankList: [],
      agreeList: [],
      readList: [],
      bankCardNumber: '',
      selectedBank: {},
      showAgreeDetail: false,
      agreeIndex: 0,
      signed: false
    };
  },
  watch: {
    selectedBank: {
      handler(data = {}) {
        if (data) {
          this.selectBank(data);
        }
      },
      deep: true
    }
  },
  computed: {
    isCount() {
      return this.readList.includes(this.agreeId);
    },
    DisabledBtn() {
      console.info(this.bankCardNumber, this.selectedBank.bankName);
      return this.bankCardNumber && this.selectedBank.bankName && this.signed;
    }
  },
  mounted() {
    this.$TAlert({
      title: '',
      tips: '该业务办理需采集您的银行卡信息（视银行规定），用于存管银行与资金账户的绑定。请确认同意提交以上信息。',
      hasCancel: true,
      confirmBtn: '同意',
      confirm: () => {
        addClientCritMark({
          flowToken: sessionStorage.getItem('TKFlowToken'),
          markType: '17',
          markContent:
            '该业务办理需采集您的银行卡信息（视银行规定），用于存管银行与资金账户的绑定。请确认同意提交以上信息。',
          confirmFlag: '1'
        });
      },
      cancel: () => {
        this.eventMessage(this, EVENT_NAME.PREV_FLOW);
      }
    });
    selectBcBankBybankNo().then((res) => {
      this.bankList = res.data;
    });
  },
  methods: {
    showTag({ support7x24Flag = '', counterKind = '' }) {
      // counterKind 0普通柜台 1信用柜台
      // support7x24Flag 0不支持 1普通柜台支持 2信用柜台支持
      if (!support7x24Flag) return false;
      const support7x24FlagList = support7x24Flag.split(',');
      const counterKindList = counterKind.split(',');
      return counterKindList.some((kind) => {
        const expectedFlag = String(Number(kind) + 1);
        return support7x24FlagList.includes(expectedFlag);
      });
    },
    back() {
      this.eventMessage(this, EVENT_NAME.PREV_FLOW);
    },
    bankCardInput() {
      if (!String(this.bankCardNumber).trim()) return;
      this.bankCardNumber = String(this.bankCardNumber).replace(/[^0-9]/g, '');
    },
    async toNext() {
      if (this.bankCardNumber === '') {
        _hvueToast({
          mes: '请输入银行卡号'
        });
        return;
      }
      if (this.bankNo === '') {
        _hvueToast({
          mes: '请选择所属银行'
        });
        return;
      }
      if (!/^(\d{16}|\d{17}|\d{18}|\d{19})$/.test(this.bankCardNumber)) {
        _hvueToast({
          mes: '银行卡号格式不正确'
        });
        return;
      }
      this._queryAgreement();
      if (this.agreeList.length > 0) {
        if (!this.signed && this.agreeList.length > 0) {
          _hvueToast({
            mes: '请阅读并勾选协议'
          });
          return;
        }
        let haseEpaperESigned = this.agreeList.length;
        const tkFlowInfo = this.tkFlowInfo();
        signAgreeV2(tkFlowInfo, this.agreeList, this.$attrs)
          .then((epaperSignJson) => {
            this.toSubmit(epaperSignJson, haseEpaperESigned);
          })
          .catch((error) => {
            _hvueToast({ mes: error });
          });
      } else {
        this.toSubmit();
      }
    },

    toSubmit(epaperSignJson, haseEpaperESigned) {
      bankCardBINQuery({
        cardNo: this.bankCardNumber
      })
        .then((res) => {
          if (res.data.dcType == '1') {
            return this.$TAlert({
              title: '',
              tips: '该银行卡为信用卡，请输入您本人的借记卡进行三方存管绑定',
              confirmBtn: '我知道了'
            });
          }
          if (this.selectedBank.bankNo != res.data.bankNo) {
            return this.$TAlert({
              title: '',
              tips: '您输入的银行卡号和所选银行可能不匹配，请确认您输入的信息正确，才能成功办理哦。',
              hasCancel: true,
              confirmBtn: '继续办理',
              confirm: () => {
                this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
                  bankNo: this.selectBank.bankNo,
                  bankAccount: this.bankCardNumber,
                  epaperSignJson
                });
              }
            });
          }
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
            bankNo: this.selectBank.bankNo,
            bankAccount: this.bankCardNumber,
            epaperSignJson
          });
        })
        .catch((data) => {
          console.info(data);
          return this.$TAlert({
            title: '',
            tips: data,
            confirmBtn: '我知道了'
          });
        });
    },

    checkBank() {
      if (!this.bankCardNumber) return;
      bankCardBINQuery({
        cardNo: this.bankCardNumber
      }).then((res) => {
        if (res.code != 0) return;
        this.selectedBank =
          this.bankList.filter((item) => item.bankNo === res.data.bankNo)[0] ||
          {};
        if (!this.selectedBank) return;
        updateFlowForm({
          flowToken: sessionStorage.getItem('TKFlowToken'),
          bizType: '010174',
          source: `绑定存管银行-初始化`,
          formParam: {
            bank_no: this.selectedBank.bankNo
          }
        }).then((res) => {
          this._queryAgreement();
        });
      });
    },

    selectBank(item) {
      this.selectedBank = item;
      updateFlowForm({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        bizType: '010174',
        source: `绑定存管银行-初始化`,
        formParam: {
          bank_no: item.bankNo
        }
      }).then((res) => {
        this._queryAgreement();
        this.showSelect = false;
      });
    },

    toDetail(index) {
      this.showAgreeDetail = true;
      console.log(123);
      this.agreeIndex = index;
    },

    agreeCallBack() {
      this.showAgreeDetail = false;
    },

    detailSignAgree() {},

    async _queryAgreement() {
      let param = {
        agreementBizType: this.$attrs.agreementNodeNo.split(':')[0],
        agreementNodeNo: this.$attrs.agreementNodeNo.split(':')[1],
        agreementExt: this.$attrs.agreementExt
      };
      let reqParams = {
        flowToken: sessionStorage.getItem('TKFlowToken'),
        ...param
      };
      queryAgreementExtV3(reqParams)
        .then((res) => {
          const agreementList = res.data.agreementList;
          this.agreeList = agreementList.map((it, i) => {
            it.agreementBizType = param.agreementBizType;
            return it;
          });
        })
        .catch((err) => {
          _hvueToast({ mes: err });
        });
    },

    ocrParseBankCard() {
      if ($hvue.platform == 0) {
        this.$refs.getImgBoxBrowser.getImg();
      } else {
        this.$refs.getImgBoxApp.getImg();
      }
      // this.$refs.getImgBoxBrowser.getImg();
      // if ($hvue.platform == 0) {
      //   this.$refs.getImgBoxBrowser.getImg();
      // } else {
      //   window.bankOcrCallBack = this.bankOcrCallBack;
      //   // 调用功能号60016获取
      //   let config = {
      //     funcNo: '60016',
      //     isAlbum: '0', //是否显示相册 1表示显示相册，0或其他表示不显示 默认隐藏
      //     isTake: '1', //是否显示拍照按钮
      //     mainColor: $hvue.customConfig.mainColor,
      //     compressSize: 200, //原生压缩大小 不传默认200k
      //     moduleName: 'open' // 必须为open
      //   };
      //   console.log(config);
      //   let result = $h.callMessageNative(config);
      //   if (result.error_no !== '0') {
      //     console.log({ mes: result.error_info });
      //   }
      //   console.log(result);
      // }
    },

    getImgCallBack(imgInfo) {
      _hvueLoading.open();
      uploadFile(
        $hvue.customConfig.serverUrl + '/credit/ocrParseBankCard',
        imgInfo.base64,
        {
          success: async (data) => {
            _hvueLoading.close();
            if (data.code === 0) {
              const { cardNumber, bankNo } = data.data;
              if (cardNumber && cardNumber !== '') {
                this.bankCardNumber = cardNumber.replace(/\s/g, '');
                // this.checkBank();
                const selectedBank = this.bankList.filter((item) => {
                  return item.bankNo === bankNo;
                })[0];
                selectedBank && this.selectBank(selectedBank);
              }
            } else if (data.code != 11) {
              _hvueAlert({ mes: data.msg });
            }
          },
          progress: (count) => {
            console.log(count);
          },
          error: (e) => {
            _hvueLoading.close();
            console.log(e);
          }
        },
        {}
      );
    }
  }
};
</script>

<style scoped>
.bank-item {
  position: relative;
  display: flex;
  align-items: center;
  top: 0.04rem;
}
.transfer-tag {
  position: absolute;
  top: -0.12rem;
  right: 0;
  background-color: #fff2e3;
  color: #ff7015;
  font-size: 0.09rem;
  line-height: 0.14rem;
  padding: 0 0.06rem;
  border-radius: 0.1rem 0.1rem 0.1rem 0;
  border: 0.005rem solid #ff7015;
  font-family: 'PingFang SC';
  font-weight: 400;
}
</style>
