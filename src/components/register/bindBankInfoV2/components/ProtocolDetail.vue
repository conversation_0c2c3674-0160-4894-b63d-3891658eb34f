<template>
  <div class="agree_fixed" style="z-index: 1400">
    <t-header
      :title="`协议签署（${agreeIndex + 1}/${agreeList.length}）`"
      @back="back"
    />
    <article
      ref="protocolCont"
      class="content"
      style="background: #ffffff; -webkit-overflow-scrolling: touch"
    >
      <div
        v-if="!info.agreementPath"
        class="protocol_cont"
        v-html="info.agreementContent"
      ></div>
      <template v-if="info.agreementPath">
        <pdf
          v-for="i in numPages"
          :key="i"
          class="protocol_pdf"
          :src="pdfSrc"
          :page="i"
        ></pdf>
      </template>
    </article>
    <footer class="footer" style="background: #ffffff">
      <!-- <div v-if="agreeIndex === agreeList.length - 1" class="rule_check">
        <span
          class="icon_check"
          :class="{ checked: isChecked }"
          @click="selectAgree"
        ></span
        >本人已详细阅读并完全理解以上合同及协议，同意签署。
      </div> -->
      <div class="ce_btn">
        <!-- <a
          v-show="agreeIndex !== 0"
          class="p_button border"
          @click="toPrevAgree"
          >上一协议</a
        >
        <a v-if="countdown && isCount" class="p_button disabled"
          >请阅读{{ countdown }}</a
        >
        <a
          v-else-if="isCount && info.enableReadfull === '1' && !isBottom"
          class="p_button disabled"
          >请阅读完协议后确认</a
        > -->
        <a class="p_button" @click="hasRead">
          <!-- <span v-if="agreeList.length !== agreeIndex + 1">下一协议</span> -->
          <span>我已阅读并同意</span>
        </a>
      </div>
    </footer>
  </div>
</template>

<script>
import pdf from 'vue-pdf';
import { getProtocolDetail } from '@/common/utils/protocols.util';
let timer;

export default {
  components: {
    pdf
  },
  model: {
    event: 'change',
    prop: 'agreeIndex'
  },
  props: {
    isCount: {
      type: Boolean,
      default: true
    },
    agreeList: {
      type: Array,
      default: () => []
    },
    readList: {
      type: Array,
      default: () => []
    },
    agreeIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      count: 0,
      pdfSrc: '',
      numPages: null,
      containerScrollFn: null,
      isBottom: false,
      isChecked: false
    };
  },
  computed: {
    info() {
      return this.agreeList[this.agreeIndex];
    },
    countdown() {
      if (this.count <= 0) {
        return false;
      } else {
        return `（${this.count} 秒）`;
      }
    }
  },
  watch: {
    agreeIndex: {
      handler() {
        this.updateData();
      },
      immediate: true
    }
  },
  mounted() {
    if (this.readList.length === this.agreeList.length) {
      this.isChecked = true;
    }
  },
  methods: {
    back() {
      clearInterval(timer);
      this.removeEventListen();
      timer = null;
      this.$emit('callback', false);
    },
    selectAgree() {
      if (this.readList.length <= this.agreeList.length) {
        if (this.readList.length == this.agreeList.length - 1) {
          if (!this.countdown) {
            if (this.info.enableReadfull === '1') {
              if (!this.isBottom) {
                _hvueToast({
                  mes: '请先完整阅读协议'
                });
                return false;
              }
            }
          } else {
            _hvueToast({
              mes: '请先完整阅读协议'
            });
            return false;
          }
          this.$emit('callback', true, false);
          this.isChecked = !this.isChecked;
        } else {
          if (this.readList.length == this.agreeList.length) {
            this.isChecked = !this.isChecked;
            return true;
          }
          _hvueToast({
            mes: '请返回协议列表，阅读完全部协议后再进行下一步'
          });
          return false;
        }
      } else {
        _hvueToast({
          mes: '请先完整阅读所有协议'
        });
        return false;
      }
    },
    updateData() {
      let { enableRead, readTime, enableReadfull } = this.info;
      this.pdfSrc = '';
      this.numPages = '';
      getProtocolDetail({ ...this.info })
        .then((protocolInfo) => {
          if (protocolInfo.agreementPDF) {
            const loadingTask = pdf.createLoadingTask(
              protocolInfo.agreementPDF,
              {
                withCredentials: false
              }
            );
            this.pdfSrc = loadingTask;
            loadingTask.promise.then((pdf) => {
              this.numPages = pdf.numPages;
            });
            this.$once('hook:deactivated', () => {
              if (this.pdfSrc) URL.revokeObjectURL(this.pdfSrc);
            });
          } else if (protocolInfo.agreementHtml) {
            this.$nextTick(() => {
              this.$refs.protocalContent.innerHTML = protocolInfo.agreementHtml;
            });
          }
          if (this.isCount) {
            /** 设置协议阅读倒计时 start */
            /** 协议字段备注
             * enableRead: 是否强制阅读
             * enableReadfull: 是否强制阅读全文
             * enableMustsign: 是否每次要签署
             * enableOnlinesign: 是否支持线上签
             * */
            if (enableRead !== '0') {
              this.count = readTime;
            } else {
              this.count = 0;
            }
            timer = setInterval(() => {
              if (this.count <= 0) {
                clearInterval(timer);
              } else {
                this.count--;
              }
            }, 1000);
            this.$once('hook:deactivated', () => {
              clearInterval(timer);
              this.removeEventListen();
            });
            if (enableReadfull === '1') {
              this.$nextTick(() => {
                const $protocolCont = this.$refs.protocolCont;
                this.containerScrollFn = this.throttle(
                  this.isScrollBottom,
                  200
                );
                $protocolCont.addEventListener(
                  'scroll',
                  this.containerScrollFn
                );
                const scrollTop = $protocolCont.scrollTop;
                const windowHeight = $protocolCont.clientHeight;
                const scrollHeight = $protocolCont.scrollHeight;
                let isBottomHeight = scrollHeight - scrollTop - windowHeight;
                console.log(isBottomHeight);
                if (isBottomHeight <= 10) {
                  this.isBottom = true;
                } else {
                  this.isBottom = false;
                }
              });
            }
            /** 设置协议阅读倒计时 end */
          }
        })
        .catch((e) => {
          this.$TAlert({
            title: '温馨提示',
            tips: e
          });
        });
    },
    hasRead() {
      clearInterval(timer);
      this.removeEventListen();
      this.$emit('callback', true);
      // if (this.agreeList.length !== this.agreeIndex + 1) {
      //   this.$emit('callback', true);
      //   this.toNextAgree();
      // } else {
      //   // 签署协议下一步
      //   // if (!this.isChecked) {
      //   //   _hvueToast({
      //   //     mes: '请阅读并勾选协议'
      //   //   });
      //   //   return;
      //   // }
      //   this.selectAgree();
      //   if (this.selectAgree()) {
      //     this.$emit('signAgree');
      //   }
      // }
    },
    toNextAgree() {
      this.resetScroll();
      this.$emit('change', this.agreeIndex + 1);
    },
    toPrevAgree() {
      this.resetScroll();
      this.$emit('change', this.agreeIndex - 1);
    },
    resetScroll() {
      const $protocolCont = this.$refs.protocolCont;
      $protocolCont.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
      clearInterval(timer);
    },
    throttle(fn, wait) {
      let t = null;
      return function (...args) {
        let context = this;
        if (t) {
          clearTimeout(t);
          t = null;
        }
        t = setTimeout(function () {
          fn.apply(context, args);
        }, wait);
      };
    },

    isScrollBottom() {
      const $protocolCont = this.$refs.protocolCont;
      const scrollTop = $protocolCont.scrollTop;
      const windowHeight = $protocolCont.clientHeight;
      const scrollHeight = $protocolCont.scrollHeight;
      let isBottomHeight = scrollHeight - scrollTop - windowHeight;
      console.log(isBottomHeight);
      if (isBottomHeight <= 10) {
        this.isBottom = true;
      } else {
        this.isBottom = false;
      }
      console.log('isBottom=' + this.isBottom);
      if (this.isBottom) this.removeEventListen();
    },

    removeEventListen() {
      this.$refs.protocolCont.removeEventListener(
        'scroll',
        this.containerScrollFn
      );
    }
  }
};
</script>

<style scoped>
.protocol_cont {
  padding: 0.15rem;
  font-size: 0.16rem;
  line-height: 0.26rem;
}
.protocol_cont p {
  padding: 0.05rem 0;
}
.protocol_cont h1 {
  font-size: 0.18rem;
}
.protocol_pdf {
  width: 100%;
}
div.agree_fixed {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  -webkit-flex-direction: column;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: fixed;
  left: 0;
  top: 0;
}
div.agree_fixed article.content {
  -moz-box-flex: 1;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  height: 100%;
  -webkit-overflow-scrolling: auto;
  position: relative;
}
div.agree_fixed article.content::-webkit-scrollbar {
  width: 0;
  display: none;
}
/* table 样式 */
table {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
}
table td,
table th {
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
  padding: 3px 5px;
}
table th {
  border-bottom: 2px solid #ccc;
  text-align: center;
}

/* blockquote 样式 */
blockquote {
  display: block;
  border-left: 8px solid #d0e5f2;
  padding: 5px 10px;
  margin: 10px 0;
  line-height: 1.4;
  font-size: 100%;
  background-color: #f1f1f1;
}

/* code 样式 */
code {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  background-color: #f1f1f1;
  border-radius: 3px;
  padding: 3px 5px;
  margin: 0 3px;
}
pre code {
  display: block;
}

/* ul ol 样式 */
ul,
ol {
  margin: 10px 0 10px 20px;
}

.protocol_cont >>> table {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
}
.protocol_cont >>> table > tbody > tr > td {
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
  padding: 3px 5px;
}
.protocol_cont >>> table > tbody > tr > td > p {
  word-wrap: break-word;
  word-break: break-all;
}
</style>
