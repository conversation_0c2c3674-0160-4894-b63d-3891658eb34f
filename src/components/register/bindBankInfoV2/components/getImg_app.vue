<template>
  <div v-show="showSelImgBox">
    <!-- 遮罩层 -->
    <div class="dialog_overlay" style="display: block" @click="close" />
    <!-- 弹出层 -->
    <div class="upload_select show">
      <h5>请选择上传方式</h5>
      <ul>
        <li @click="selImg('2')">
          <a>拍照</a>
        </li>
        <li @click="selImg('1')">
          <a>从相册上传</a>
        </li>
      </ul>
      <a class="cancel" @click="close">取消</a>
    </div>
  </div>
</template>

<script>
import '@/nativeShell/nativeCallH5';

export default {
  name: 'GetImgApp',
  data() {
    return {
      showSelImgBox: false
    };
  },

  methods: {
    getImg() {
      this.showSelImgBox = true;
    },
    close() {
      this.showSelImgBox = false;
    },
    premissionCallBack(data, mode) {
      console.info(data, data.isGrant == '1');
      if (data.isGrant == '1') {
        // 打开相册获取银行卡照片base64
        console.info('获取结果', this.getImgCallBackFn);
        window.imgCallBack = this.getImgCallBackFn;
        console.info('获取姐u共');
        let callMessageNativeParams = {
          funcNo: '50273',
          moduleName: $hvue.customConfig.moduleName, // 必须为open
          mode: mode,
          cutFlag: '0', // 是否裁剪 0不裁剪 1裁剪
          cameraFlag: '1', // 拍照摄像头（0:前置摄像头，1:后置摄像头）
          isAlbum: '0',
          compressSize: '600',
          width: '595',
          height: '842',
          cutFlag: '0'
        };
        console.log('上传参数', callMessageNativeParams);
        let result = $h.callMessageNative(callMessageNativeParams);
        console.log('返回结果', result);
        this.close();
        if (result.error_no !== '0') {
          console.log({ mes: result.error_info });
        }
      } else {
        _hvueToast({ mes: '当前无相机或相册权限。' });
      }
    },
    selImg(mode) {
      let { android } = $hvue.iBrowser;
      if (android) {
        const callParam = {
          funcNo: '60046',
          moduleName: $hvue.customConfig.moduleName,
          permissions: 'android.permission.CAMERA'
        };
        window.premissionCallBack = (data) =>
          this.premissionCallBack(data, mode);
        let result = $h.callMessageNative(callParam);
      } else {
        // 打开相册获取银行卡照片base64
        window.imgCallBack = this.getImgCallBackFn;
        let callMessageNativeParams = {
          funcNo: '50273',
          moduleName: $hvue.customConfig.moduleName, // 必须为open
          mode: mode,
          cutFlag: '0', // 是否裁剪 0不裁剪 1裁剪
          cameraFlag: '1', // 拍照摄像头（0:前置摄像头，1:后置摄像头）
          isAlbum: '0',
          compressSize: '600',
          width: '595',
          height: '842',
          cutFlag: '0'
        };
        console.log('上传参数', callMessageNativeParams);
        let result = $h.callMessageNative(callMessageNativeParams);
        console.log('返回结果', result);
        this.close();
        if (result.error_no !== '0') {
          console.log({ mes: result.error_info });
        }
      }

      // }
    },
    getImgCallBackFn(data) {
      console.log('原生上传返回结果传递给getImgCallBackFn', data);
      this.$emit('getImgCallBack', {
        base64: data.base64Image
      });
    }
  }
};
</script>
