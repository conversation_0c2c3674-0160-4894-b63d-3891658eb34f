<template>
  <div class="tj_audio_page">
    <div class="com_title">
      <h5>请您完整播放音频文件并明确相关业务风险后进行信用北交所权限开通</h5>
    </div>
    <div class="audio_p_wrap">
      <div class="audio_player">
        <a
          v-if="!playerDetail.play"
          class="a_player_button a_player_play"
          @click="playAudio()"
        ></a>
        <a
          v-else
          class="a_player_button a_player_pause"
          @click="pauseAudio()"
        ></a>
        <div class="a_player_controller">
          <div class="a_player_bar">
            <div
              class="a_player_played"
              :style="{ width: `${playerDetail.progress}%` }"
            >
              <div class="a_player_thumb"></div>
            </div>
          </div>
          <div class="a_player_time">
            <span class="a_player_ptime">{{ playerDetail.startTime }}</span>
            <span class="a_player_dtime">{{ playerDetail.endTime }}</span>
          </div>
        </div>
      </div>
    </div>
    <audio ref="audioPlayer" @ended="handleAudioEnded">
      <source :src="mediaFileUrl" type="audio/wav" />
      Your browser does not support the audio element.
    </audio>
    <div class="tj_audio_txt" ref="markContext">
      <div>
        <p>尊敬的投资者：</p>
        <p>
          融资融券交易与普通证券交易不同，存在着较大的投资风险。投资者在开通融资融券交易相关权限之前，应充分阅读《融资融券交易风险揭示书》并详细了解融资融券交易中的各种风险。
        </p>
        <p>
          下面将为您讲解北京证券交易所融资融券业务的特有关注内容，请您仔细聆听：
        </p>
        <p>
          1、投资者应委托同一家证券公司开展北交所和深交所融资融券交易，且必须使用同一信用证券账户。投资者申请参与北交所融资融券交易的，
          证券公司在完成资质审核后为其深市信用证券账户新增北京市场账户标识。投资者信用证券账户不得申请撤销北京市场账户标识。带有北京市场账户标识的深市信用账户注销后，方可撤销对应普通证券账户的北京市场账户标识或注销对应普通证券账户。
        </p>
        <p>
          2、交易过程中，涉及证券终止上市或转板的，投资者在符合提取保证金可用余额相关维持担保比例要求的前提下，应及时申请将有关证券从客户信用交易担保证券账户划转到客户普通证券账户中。若在有关证券终止上市前，因客户不满足公司协助办理条件，导致终止上市或转板证券未从信用账户转出的，客户可能面临信用账户无法确权、转板上市后股票无法及时交易等风险。请您从风险承受能力等自身实际情况出发，审慎参与融资融券业务。
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import { addClientCritMark } from '@/service/service.js';
import { EVENT_NAME } from '@/common/formEnum';
export default {
  name: 'investEdu',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      playerDetail: {
        play: false,
        progress: 0,
        startTime: '00:00',
        endTime: '00:00'
      }
    };
  },
  props: {
    mediaFileUrl: {
      type: String,
      default: ''
    }
  },
  watch: {
    'playerDetail.play': function (nv) {
      const $audioPlayer = this.$refs.audioPlayer;
      if (nv) {
        $audioPlayer.play();
      } else {
        $audioPlayer.pause();
      }
    }
  },
  methods: {
    renderingView() {
      const $audioPlayer = this.$refs.audioPlayer;
      let _this = this;
      $audioPlayer.addEventListener('loadedmetadata', function () {
        // 获取音频的持续时间（以秒为单位）
        let durationInSeconds = $audioPlayer.duration;
        // 将持续时间转换为更友好的格式，如分钟和秒
        let minutes = Math.floor(durationInSeconds / 60);
        let seconds = Math.floor(durationInSeconds % 60);
        // 打印或使用持续时间
        console.log('音频持续时间：' + minutes + ' 分钟 ' + seconds + ' 秒');
        _this.playerDetail.endTime = `${minutes}:${seconds}`;
      });
      // 在音频播放时监听当前播放时间
      $audioPlayer.addEventListener('timeupdate', function () {
        // 获取当前播放时间（以秒为单位）
        const currentTimeInSeconds = $audioPlayer.currentTime;
        // 将当前播放时间转换为更友好的格式，如分钟和秒
        const currentMinutes = Math.floor(currentTimeInSeconds / 60);
        const currentSeconds = Math.floor(currentTimeInSeconds % 60);
        _this.playerDetail.progress =
          (currentTimeInSeconds / $audioPlayer.duration) * 100;
        // 更新当前播放时间的显示
        _this.playerDetail.startTime =
          currentMinutes +
          ':' +
          (currentSeconds < 10 ? '0' : '') +
          currentSeconds;
      });
    },
    playAudio() {
      this.playerDetail.play = true;
    },
    pauseAudio() {
      this.playerDetail.play = false;
    },
    handleAudioEnded() {
      console.log('音频播放完成');
      this.playerDetail.play = false;
      this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
        text: '我已知晓，下一步',
        btnStatus: 2,
        data: this.addMark
      });
    },
    addMark() {
      const { inProperty } = this.tkFlowInfo();
      let content = [inProperty.clientId];
      content.push(dayjs(new Date()).format('YYYY-MM-DD HH:MM:ss'));
      content.push(this.$refs.markContext.innerText);
      addClientCritMark({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        markContent: content.join(','),
        markType: '16',
        confirmFlag: '1'
      }).then((data) => {
        this.eventMessage(this, EVENT_NAME.NEXT_STEP);
      });
    }
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
      text: '我已知晓，下一步',
      btnStatus: 0
    });
  },
  mounted() {
    this.$nextTick(() => {
      this.renderingView();
    });
  }
};
</script>

<style></style>
