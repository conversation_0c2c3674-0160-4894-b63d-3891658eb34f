<template>
  <div v-show="loaded">
    <div v-if="!noAccount">
      <div class="com_title">
        <h5>特转A账户</h5>
      </div>
      <div class="acct_status_item">
        <ul
          v-for="(item, index) in stockAccList"
          :key="index"
          class="acct_list"
        >
          <li @click.stop="selectAcc(item, index)">
            <span
              class="icon_check"
              :class="{ checked: item.isChecked, disabled: !item.available }"
              >{{ item.stockAccount
              }}<em class="acct_s_tag">{{ item.holderStatusDesc }}</em></span
            >
          </li>
        </ul>
      </div>
      <div class="tip_txtbox spel">
        <p>温馨提示：</p>
        <p>该特转A账户将用于北交所融资融券担保品划转业务</p>
      </div>
    </div>
    <div v-else>
      <div class="acct_emptybox">
        <p>您还没有正常状态账户，请前往开通</p>
        <a class="r_link_arrow" @click="gotoOpen">去开通</a>
      </div>
      <div class="tip_txtbox">
        <p>您本地还没有可办理业务的账户，请先前往开通。</p>
      </div>
    </div>
  </div>
</template>

<script>
import { stockAccountSelect } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'selectedTfaBjs',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      loaded: false,
      stockAccList: []
    };
  },
  computed: {
    openAccList() {
      return this.stockAccList
        .filter(({ isChecked }) => isChecked)
        .map(
          ({
            exchangeType,
            fundAccount,
            stockAccount,
            assetProp,
            holderKind
          }) => {
            return {
              exchangeType,
              fundAccount,
              stockAccount,
              assetProp,
              holderKind
            };
          }
        );
    },
    noAccount() {
      return this.stockAccList.length === 0;
    }
  },
  watch: {
    openAccList(newList) {
      if (newList.length === 0) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
      } else {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 1 });
      }
    },
    noAccount: {
      handler(flag) {
        if (flag && this.loaded) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            text: '返回首页',
            btnStatus: 2,
            display: true,
            data: () => {
              this.eventMessage(this, EVENT_NAME.TO_INDEX);
            }
          });
        }
      },
      immediate: true
    }
  },
  methods: {
    renderingView() {
      stockAccountSelect({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        filterStockType: '0|9|0',
        filterHolderStatus: '0',
        filterHolderRights: '$,7,8',
        isShowOnlyAvailable: '1',
        needCancel: '1'
      })
        .then(({ data }) => {
          this.stockAccList = [];
          data.groupVoList.forEach((item) => {
            item.saccountQryVoList.forEach((it) => {
              this.stockAccList.push(it);
            });
          });
          if (this.stockAccList.length === 1) {
            let formParam = this.stockAccList.map(
              ({
                exchangeType,
                fundAccount,
                stockAccount,
                assetProp,
                holderKind
              }) => {
                return {
                  exchangeType,
                  fundAccount,
                  stockAccount,
                  assetProp,
                  holderKind
                };
              }
            );
            this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
              selectedTfaData: JSON.stringify(formParam)
            });
            return;
          }
          this.loaded = true;
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },
    selectAcc(item, i) {
      const { available, isDisabled, isChecked } = item;
      if (isDisabled || !available) return;
      this.stockAccList.forEach((it, index) => {
        if (index === i) {
          this.$set(it, 'isChecked', !isChecked);
        } else {
          this.$set(it, 'isChecked', false);
        }
      });
      this.$emit('change', {
        selectedTfaData: JSON.stringify(this.openAccList)
      });
    },
    gotoOpen() {}
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
    this.renderingView();
  }
};
</script>

<style></style>
