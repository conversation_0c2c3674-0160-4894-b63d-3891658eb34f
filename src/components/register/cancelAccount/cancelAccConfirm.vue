<template>
  <fragment>
    <div class="com_title">
      <h5>
        您在销户时与工作人员确认销户的账户号码如下，请确认。如需要修改，您可以在后续与见证人员录制视频时提出您需修改销户账户。
      </h5>
    </div>
    <div class="acct_status_item" v-show="fundAccountList.length > 0">
      <div class="tit" @click="fundAccOn = !fundAccOn">
        <h5>资金账户</h5>
        <i class="drop_arrow" :class="fundAccOn ? 'on' : 'off'"></i>
      </div>
      <ul class="acct_list xh" v-show="fundAccOn">
        <li v-for="(item, i) in fundAccountList" :key="i">
          <div
            class="icon_check"
            :class="{ checked: item.chooseFlag === 1 }"
            @click="selectFundAcc(item)"
          >
            <div class="small_tit">{{ item.name }}</div>
            <div>
              <span>{{ item.account }}</span
              ><!-- <em class="acct_s_tag">正常</em> -->
            </div>
          </div>
          <div v-if="item.accountType === 'CF8M1'" class="pension_tips">
            仅注销本券商的养老金账户，不会注销养老金银行卡
          </div>
        </li>
      </ul>
      <div class="imp_c_tips" v-show="fundAccOn">
        <p class="warn">
          <span class="imp">注销资金账户，将同步注销场外基金账户</span>
        </p>
      </div>
    </div>
    <div class="acct_status_item" v-show="stockholderList.length > 0">
      <div class="tit" @click="stockholderOn = !stockholderOn">
        <h5>证券账户</h5>
        <i class="drop_arrow" :class="stockholderOn ? 'on' : 'off'"></i>
      </div>
      <ul class="acct_list xh" v-show="stockholderOn">
        <li v-for="(item, i) in stockholderList" :key="i">
          <div
            class="icon_check"
            :class="{ checked: item.chooseFlag === 1 }"
            @click="item.chooseFlag = item.chooseFlag === 1 ? 0 : 1"
          >
            <div class="small_tit">{{ item.name }}</div>
            <div>
              <span>{{ item.account }}</span
              ><!-- <em class="acct_s_tag">正常</em> -->
            </div>
          </div>
        </li>
      </ul>
    </div>
    <div class="acct_status_item" v-show="financialAccList.length > 0">
      <div class="tit" @click="financialAccOn = !financialAccOn">
        <h5>理财账户</h5>
        <i class="drop_arrow" :class="financialAccOn ? 'on' : 'off'"></i>
      </div>
      <ul class="acct_list xh" v-show="financialAccOn">
        <li v-for="(item, i) in financialAccList" :key="i">
          <div
            class="icon_check"
            :class="{ checked: item.chooseFlag === 1 }"
            @click="selectFinanAcc(item)"
          >
            <div class="small_tit">{{ item.name }}</div>
            <div>
              <span>{{ item.account }}</span
              ><!-- <em class="acct_s_tag">正常</em> -->
            </div>
          </div>
        </li>
      </ul>
    </div>
    <div class="acct_status_item" v-show="creditAccList.length > 0">
      <div class="tit" @click="creditAccOn = !creditAccOn">
        <h5>信用账户</h5>
        <i class="drop_arrow" :class="creditAccOn ? 'on' : 'off'"></i>
      </div>
      <ul class="acct_list xh" v-show="creditAccOn">
        <li v-for="(item, i) in creditAccList" :key="i">
          <div
            class="icon_check"
            :class="{ checked: item.chooseFlag === 1 }"
            @click="item.chooseFlag = item.chooseFlag === 1 ? 0 : 1"
          >
            <div class="small_tit">{{ item.name }}</div>
            <div>
              <span>{{ item.account }}</span
              ><!-- <em class="acct_s_tag">正常</em> -->
            </div>
          </div>
        </li>
      </ul>
    </div>
    <div class="acct_status_item" v-show="optionAccList.length > 0">
      <div class="tit" @click="optionAccOn = !optionAccOn">
        <h5>期权账户</h5>
        <i class="drop_arrow" :class="optionAccOn ? 'on' : 'off'"></i>
      </div>
      <ul class="acct_list xh" v-show="optionAccOn">
        <li v-for="(item, i) in optionAccList" :key="i">
          <div
            class="icon_check"
            :class="{ checked: item.chooseFlag === 1 }"
            @click="item.chooseFlag = item.chooseFlag === 1 ? 0 : 1"
          >
            <div class="small_tit">{{ item.name }}</div>
            <div>
              <span>{{ item.account }}</span
              ><!-- <em class="acct_s_tag">正常</em> -->
            </div>
          </div>
        </li>
      </ul>
    </div>
  </fragment>
</template>

<script>
import {
  xhCancelAccountSelect,
  xhChooseAccountUpdate
} from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
export default {
  name: 'cancelAccConfirm',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      fundAccountList: [], //资金账户列表
      financialAccList: [], //理财账户列表
      creditAccList: [], //信用账户列表
      optionAccList: [], //期权账户列表
      stockholderList: [], //证券账户列表
      fundAccOn: true,
      financialAccOn: true,
      creditAccOn: true,
      optionAccOn: true,
      stockholderOn: true
    };
  },
  watch: {
    btnOkStatus: {
      handler(nv) {
        if (nv) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 2,
            data: this.submitInfo
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
        }
      },
      immediate: true
    }
  },
  computed: {
    btnOkStatus() {
      let fundCheckedFlag = false;
      let finaCheckedFlag = false;
      let creditCheckedFlag = false;
      let optionCheckedFlag = false;
      let stockholderCheckedFlag = false;
      if (this.fundAccountList.length > 0) {
        fundCheckedFlag = this.fundAccountList.some(
          ({ chooseFlag }) => chooseFlag === 1
        );
      }
      if (this.financialAccList.length > 0) {
        finaCheckedFlag = this.financialAccList.some(
          ({ chooseFlag }) => chooseFlag === 1
        );
      }
      if (this.creditAccList.length > 0) {
        creditCheckedFlag = this.creditAccList.some(
          ({ chooseFlag }) => chooseFlag === 1
        );
      }
      if (this.optionAccList.length > 0) {
        optionCheckedFlag = this.optionAccList.some(
          ({ chooseFlag }) => chooseFlag === 1
        );
      }
      if (this.stockholderList.length > 0) {
        stockholderCheckedFlag = this.stockholderList.some(
          ({ chooseFlag }) => chooseFlag === 1
        );
      }
      return (
        fundCheckedFlag ||
        finaCheckedFlag ||
        creditCheckedFlag ||
        optionCheckedFlag ||
        stockholderCheckedFlag
      );
    }
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      this.renderingView();
    });
  },
  methods: {
    renderingView() {
      const flowToken = sessionStorage.getItem('TKFlowToken');
      const preFlowInsId = this.$attrs.preFlowInsId;
      xhCancelAccountSelect({ flowToken, preFlowInsId })
        .then(({ data: { cancelAccountDataList } }) => {
          // 账号类别：1 资金账户；2证券账户；3信用账户；4 期权账户；5 理财账户；
          for (const { type, ...item } of cancelAccountDataList) {
            if (type === '1') {
              this.fundAccountList.push({ ...item });
            } else if (type === '2') {
              this.stockholderList.push({ ...item });
            } else if (type === '3') {
              this.creditAccList.push({ ...item });
            } else if (type === '4') {
              this.optionAccList.push({ ...item });
            } else if (type === '5') {
              this.financialAccList.push({ ...item });
            }
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },
    selectFundAcc(item) {
      item.chooseFlag = item.chooseFlag === 1 ? 0 : 1;
      /* if (item.accountType === 'C' && item.chooseFlag === 1) {
        this.financialAccList = this.financialAccList.map((it) => {
          it.chooseFlag = 1;
          return it;
        });
      } */
    },
    selectFinanAcc(item) {
      item.chooseFlag = item.chooseFlag === 1 ? 0 : 1;
      //如果选择了销资金账号，所有的场外基金不可以取消勾选
      /* const isChecked = this.fundAccountList.some(
        ({ accountType, chooseFlag }) => accountType === 'C' && chooseFlag === 1
      );
      if (!isChecked) {
        item.chooseFlag = item.chooseFlag === 1 ? 0 : 1;
      } */
    },
    submitInfo() {
      const preFlowInsId = this.$attrs.preFlowInsId;
      xhChooseAccountUpdate({
        preFlowInsId,
        cancelAccountDataList: JSON.stringify([
          ...this.fundAccountList,
          ...this.financialAccList, //理财账户列表
          ...this.creditAccList, //信用账户列表
          ...this.optionAccList, //期权账户列表
          ...this.stockholderList
        ])
      })
        .then(({ code, msg }) => {
          if (code === 0) {
            this.eventMessage(this, EVENT_NAME.NEXT_STEP);
          } else {
            Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    }
  }
};
</script>

<style scoped>
.xh_pword_input > .hui-keypanel {
  display: block;
  font-size: 0.16rem;
  color: #333333;
  border-radius: 0.04rem;
  border: 0 none;
  background: #f4f4f4;
  outline: none;
}
.drop_arrow.off {
  -webkit-transform: rotate(270deg);
  transform: rotate(270deg);
}
.pension_tips {
  color: #999797;
}
</style>
