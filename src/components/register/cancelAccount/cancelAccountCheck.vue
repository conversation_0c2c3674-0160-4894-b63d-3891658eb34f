<template>
  <article class="content" style="background: #f5f6fa">
    <div class="acct_status_item">
      <div class="tit">
        <h5>可注销账户</h5>
        <!-- <i class="drop_arrow on"></i> -->
      </div>
      <ul class="acct_list">
        <li v-for="(item, index) in validAccountList" :key="index">
          <div class="txt_p">
            <span>{{ item.name }}</span> {{ item.account }}
            <em class="acct_s_tag" :class="{ abnormal: item.status !== '0' }">{{
              item.statusDesc
            }}</em>
          </div>
        </li>
      </ul>
    </div>
    <div class="acct_status_item">
      <div class="tit">
        <h5>不可注销账户<span class="state_ic_error"></span></h5>
        <!-- <i class="drop_arrow on"></i> -->
      </div>
      <ul class="acct_list">
        <li v-for="(item, index) in invalidAccountList" :key="index">
          <div class="txt_p">
            <span>{{ item.name }}</span> {{ item.account
            }}<em
              class="acct_s_tag"
              :class="{ abnormal: item.status !== '0' }"
              >{{ item.statusDesc }}</em
            >
          </div>
          <div class="erro_cont">
            <div
              v-for="(it, idx) in item.unavailableDesc"
              :key="idx"
              class="item"
            >
              {{ it }}
            </div>
          </div>
        </li>
      </ul>
    </div>
  </article>
</template>

<script>
import { cancelAccCheck } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'CancelAccountCheck',
  inject: ['clearKeepAlive', 'eventMessage'],
  data() {
    return {
      checkFlag: '0',
      invalidAccountList: [],
      validAccountList: []
    };
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    this.renderingView();
    this.clearKeepAlive();
  },
  mounted() {},
  methods: {
    renderingView() {
      cancelAccCheck({
        flowToken: sessionStorage.getItem('TKFlowToken')
      }).then((res) => {
        this.checkFlag = res.data.checkFlag;
        this.invalidAccountList = res.data.invalidAccountList;
        this.validAccountList = res.data.validAccountList;
        console.log(this.invalidAccountList);
        if (this.checkFlag !== '0') {
          if (this.$attrs.isAutoNext) {
            this.eventMessage(this, EVENT_NAME.NEXT_STEP);
          }
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: true });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            text: '返回首页',
            display: true,
            btnStatus: 2,
            data: () => {
              this.eventMessage(this, EVENT_NAME.TO_INDEX);
            }
          });
        }
      });
    }
  }
};
</script>
