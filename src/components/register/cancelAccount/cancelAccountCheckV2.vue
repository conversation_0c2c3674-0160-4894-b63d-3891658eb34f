<template>
  <article class="content" style="background: #f5f6fa">
    <div
      class="acct_status_item mt10"
      v-for="(arr, key) in viewData"
      :key="key"
    >
      <div class="tit" v-if="arr.length > 0" @click="selectLi(key)">
        <h5>{{ arr[0].title }}</h5>
        <i class="drop_arrow" :class="listOff.includes(key) ? 'off' : 'on'"></i>
      </div>
      <ul class="acct_list" v-if="!listOff.includes(key) && arr.length > 0">
        <li v-for="(it, i) in arr" :key="i">
          <div class="txt_p">
            <div class="small_tit">{{ it.name }}</div>
            <span>{{ it.account }}</span>
            <!-- <em class="acct_s_tag" :class="{ abnormal: it.errMsg !== '' }">{{
              it.errMsg
            }}</em> -->
          </div>
          <div class="erro_cont" v-show="it.errMsg">
            <div class="item">{{ it.errMsg }}</div>
          </div>
        </li>
      </ul>
    </div>
  </article>
</template>

<script>
import { xhCancelAccountSelect, xhCancelAccCheck } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'CancelAccountCheckV2',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      checkFlag: '0',
      viewData: {
        1: [], //资金账户列表
        2: [], //证券账户列表
        3: [], //信用账户列表
        4: [], //期权账户列表
        5: [] //理财账户列表
      },
      listOff: []
    };
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      this.renderingView();
    });
  },
  methods: {
    renderingView() {
      const flowToken = sessionStorage.getItem('TKFlowToken');
      const preFlowInsId = this.$attrs.preFlowInsId;
      xhCancelAccountSelect({ flowToken, preFlowInsId })
        .then(({ data: { cancelAccountDataList } }) => {
          cancelAccountDataList = cancelAccountDataList.filter(
            ({ chooseFlag }) => chooseFlag === 1
          );
          return xhCancelAccCheck({
            preFlowInsId,
            cancelAccountDataList: JSON.stringify(cancelAccountDataList)
          });
        })
        .then(({ data, code, msg }) => {
          if (code === 0) {
            this.checkFlag = data.checkFlag; //1通过 0不通过
            const cancelAccountDataList = data.cancelAccountDataList;
            if (this.checkFlag !== '0') {
              this.eventMessage(this, EVENT_NAME.NEXT_STEP);
            } else {
              // type账号类别：1 资金账户；2证券账户；3信用账户；4 期权账户；5 理财账户；
              for (const item of cancelAccountDataList) {
                if (item.chooseFlag === 0) continue;
                this.viewData[item.type].push(item);
              }
            }
          } else {
            return Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },
    selectLi(k) {
      if (this.listOff.includes(k)) {
        this.listOff.splice(this.listOff.indexOf(k), 1);
      } else {
        this.listOff.push(k);
      }
    }
  }
};
</script>
<style scoped>
.drop_arrow.off {
  -webkit-transform: rotate(270deg);
  transform: rotate(270deg);
}
</style>
