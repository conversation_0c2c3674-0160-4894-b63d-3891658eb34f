<template>
  <fragment>
    <div class="com_title">
      <h5>您选择的账户存在以下问题，可能会影响您最终的办理结果</h5>
    </div>
    <div v-for="(item, index) in list" :key="index" class="acct_status_item">
      <div class="tit">
        <h5>{{ item.title }}</h5>
      </div>
      <ul class="acct_list">
        <li>
          <div class="txt_p">{{ item.name }}：{{ item.account }}<span v-if="item.positionHoldingFlag == '1'"
              :class="{ active: item.ztgChecked }" @click="item.ztgChecked = !item.ztgChecked"
              class="ztg_check">转托管</span></div>
          <div v-show="!item.ztgChecked" v-for="(msg, index2) in item.checkMsg" :key="index2" class="xh_erro_cont">
            <div class="item">{{ msg }}</div>
          </div>
          <div v-if="item.positionHoldingFlag == '1'" v-show="item.ztgChecked">
            <div class="ztg_form">
              <div class="ztg_input">
                <div class="label">转入券商</div>
                <input v-model="item.toChangeIntoBroker" class="t1" type="text" placeholder="请输入" maxlength="50">
              </div>
              <div class="ztg_input">
                <div class="label">席位号</div>
                <input v-model="item.brokerTypeSeatNumber" @input="handleInput(index, $event)" class="t1" type="text"
                  placeholder="请输入" maxlength="10">
              </div>
            </div>
            <div class="ztg_btn"><a class="com_link" @click="gotoTrade">查看持仓明细</a></div>
          </div>
        </li>
      </ul>
    </div>
  </fragment>
</template>

<script>
import { accountClosureCheck, recordChoiceOfAccountClosure, recordOfAccountClosure } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'CancelAccountCheckV3',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      cancelAccountDataList: [],
      list: []
    };
  },
  computed: {
    computedCheckFlag() {
      let flag = '1';
      for (let i = 0; i < this.list.length; i++) {
        const acc = this.list[i];
        if (acc.checkFlag == '0') {
          return '0';
        } else if (acc.checkFlag == '2') {
          flag = '2'
        }
      }
      return flag;
    },

    nextBtnStatus() {
      const flag = this.computedCheckFlag;
      if (flag == '2') {
        return this.list.filter(t => {
          // 如果状态是非强制校验，并且有持仓，必须填写 转入券商 和 席位号
          if (t.positionHoldingFlag == '1') {
            if (!t.ztgChecked || !t.brokerTypeSeatNumber || !t.toChangeIntoBroker) {
              return true;
            } else {
              return false;
            }
          }
          return false;
        }).length;
      }
      return false;
    }
  },
  watch: {
    nextBtnStatus(val) {
      if (val === false) return;
      if (val > 0) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '下一步',
          display: true,
          btnStatus: 0
        });
      } else {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '下一步',
          display: true,
          btnStatus: 2,
          data: () => {
            this.submitData();
          }
        });
      }
    }
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
      text: '下一步',
      display: true,
      btnStatus: 0
    });
  },
  mounted() {
    this.$nextTick(() => {
      this.renderingView();
    });
  },
  methods: {
    renderingView() {
      const preFlowInsId = this.$attrs.preFlowInsId;
      const flowToken = sessionStorage.getItem('TKFlowToken');
      let { branchNo, clientId } = this.$store.state.user?.userInfo;
      const { bizType } = this.tkFlowInfo().inProperty;
      recordChoiceOfAccountClosure({
        clientId,
        branchNo,
        bizType,
        flowToken,
        preFlowInsId,
      })
        .then(({ data: { cancelAccountDataList } }) => {
          this.cancelAccountDataList = cancelAccountDataList;
          return accountClosureCheck({
            clientId,
            branchNo,
            bizType,
            flowToken,
            preFlowInsId,
            cancelAccountDataList: JSON.stringify(cancelAccountDataList)
          });
        })
        .then(({ data, code, msg }) => {
          if (code === 0) {
            this.list = data.cancelAccountDataList.map(t => {
              return { ...t, ztgChecked: false, toChangeIntoBroker: undefined, brokerTypeSeatNumber: undefined }
            });
            const flag = this.computedCheckFlag;
            if (flag == '0') {
              this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
                text: '返回',
                display: true,
                btnStatus: 2,
                data: () => {
                  this.eventMessage(this, EVENT_NAME.PREV_FLOW);
                }
              });
            } else if (flag == '2') {
              // 走 nextBtnStatus 的逻辑
            } else {
              this.eventMessage(this, EVENT_NAME.NEXT_STEP);
            }
          } else {
            return Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },

    handleInput(index, event) {
      const inputValue = event.target.value;
      // 只允许输入数字和字母
      let validValue = inputValue.replace(/[^a-zA-Z0-9]/g, '');
      // 限制最多输入10个字符
      if (validValue.length > 10) {
        validValue = validValue.slice(0, 10);
      }
      // 更新对应索引的inputList中的值
      this.list[index].brokerTypeSeatNumber = validValue;
    },

    gotoTrade() {
      let reqParams = {
        funcNo: '60099',
        actionType: '13',
        params: {
          yjbFuncId: '4002',
          yjbFuncParams: {
            type: 27
          }
        }
      };
      console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
      const res = $h.callMessageNative(reqParams);
      console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      return;
    },

    submitData() {
      const accList = this.list.filter(t => t.checkFlag == '2' && t.positionHoldingFlag == '1');
      const newList = this.cancelAccountDataList.filter(cancel => accList.some(acc => acc.account === cancel.account))
        .map(cancel => ({ ...accList.find(acc => acc.account === cancel.account), ...cancel }));
      console.log('submitData', accList, newList);

      // checkFlag = 2，但是非转托管的其他情况只做提示，下一步操作直接跳转到下一个节点
      if (newList?.length == 0) {
        this.nextStep();
        return true;
      }

      const preFlowInsId = this.$attrs.preFlowInsId;
      const flowToken = sessionStorage.getItem('TKFlowToken');
      let { branchNo, clientId } = this.$store.state.user?.userInfo;
      const { bizType } = this.tkFlowInfo().inProperty;
      recordOfAccountClosure({
        clientId,
        branchNo,
        bizType,
        flowToken,
        preFlowInsId,
        cancelAccountDataList: JSON.stringify(newList),
      })
        .then(({ code, msg }) => {
          if (code === 0) {
            this.eventMessage(this, EVENT_NAME.NEXT_STEP)
          } else {
            Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },

    nextStep() {
      this.eventMessage(this, EVENT_NAME.NEXT_STEP);
    }
  }
};
</script>
<style scoped>
.drop_arrow.off {
  -webkit-transform: rotate(270deg);
  transform: rotate(270deg);
}
</style>
