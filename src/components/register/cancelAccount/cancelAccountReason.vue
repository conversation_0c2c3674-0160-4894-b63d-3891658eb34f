<template>
  <article class="content" style="background: #f5f6fa">
    <div class="set_pword_item">
      <div class="com_title">
        <h5>您的销户原因（可多选）</h5>
      </div>
    </div>
    <div class="input_form">
      <div
        v-for="(item, index) in reasonList"
        :key="index"
        class="input_text"
        style="display: flex; align-items: center"
        @click="ckeckReason(item)"
      >
        <van-checkbox v-model="item.isChecked" shape="square">{{
          item.dictLabel
        }}</van-checkbox>
      </div>
      <van-field
        v-if="showOther"
        v-model="reasonAccCancelOther"
        autosize
        rows="5"
        maxlength="50"
        type="textarea"
        placeholder="点击输入其他原因说明"
        show-word-limit
      />
    </div>
  </article>
</template>

<script>
// import { cancelAccountSelect, cancelAccCheck } from '@/service/service';
import { queryDictProps } from '@/common/util';

export default {
  name: 'CancelAccountReason',
  data() {
    return {
      reasonList: [],
      reasonAccCancelOther: '',
      reasonAccCancel: ''
    };
  },
  computed: {
    showOther() {
      if (this.reasonAccCancel.includes('99')) {
        return true;
      } else {
        return false;
      }
    }
  },
  watch: {
    reasonAccCancel() {
      this.$emit('change', {
        reasonAccCancel: this.reasonAccCancel,
        reasonAccCancelOther: this.reasonAccCancelOther
      });
    },

    reasonAccCancelOther() {
      this.$emit('change', {
        reasonAccCancel: this.reasonAccCancel,
        reasonAccCancelOther: this.reasonAccCancelOther
      });
    }
  },
  mounted() {
    queryDictProps('bc.common.reasonAccCancel').then((res) => {
      this.reasonList = res;
    });
    // cancelAccountSelect({
    //   flowToken: sessionStorage.getItem('TKFlowToken')
    // }).then((res) => {});
  },
  methods: {
    ckeckReason(item) {
      for (let it of this.reasonList) {
        if (item.dictValue === it.dictValue) {
          if (it.isChecked) {
            this.$set(it, 'isChecked', false);
          } else {
            this.$set(it, 'isChecked', true);
          }
        }
      }
      this.reasonAccCancel = this.reasonList
        .filter((item) => item.isChecked === true)
        .map((it) => it.dictValue)
        .join(',');
    }
  }
};
</script>
