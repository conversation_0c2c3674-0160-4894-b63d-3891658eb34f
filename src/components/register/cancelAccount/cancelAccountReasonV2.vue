<template>
  <fragment>
    <div class="com_title">
      <h5>
        感恩与您携手的美好时光,衷心希望您能告诉我们销户辞别的原因，让未来更好的国金与您再相逢。
      </h5>
    </div>
    <div class="cm_sele_wrap">
      <h5 class="title">
        <em class="page_num">1/2</em>请选择您转销户的原因(支持多选)：
      </h5>
      <ul class="cm_sele_list">
        <li
          v-for="(item, index) in reasonList"
          :key="index"
          @click="checkReason(item)"
        >
          <div class="layout">
            <span
              class="icon_check"
              :class="{ checked: item.isChecked }"
            ></span>
            <p>{{ item.dictLabel }}</p>
          </div>
        </li>
      </ul>
      <div class="notes_input" v-if="showOther">
        <textarea
          placeholder="请输入"
          maxlength="100"
          v-model="reasonAccCancelOther"
        ></textarea>
      </div>
    </div>
    <div class="cm_sele_wrap">
      <h5 class="title">
        <em class="page_num">2/2</em>请选择您预约办理的业务(支持多选)：
      </h5>
      <ul class="cm_sele_list">
        <li
          v-for="(item, index) in bookingBusiList"
          :key="index"
          @click="checkBooking(item)"
        >
          <div class="layout">
            <span
              class="icon_check"
              :class="{ checked: item.isChecked }"
            ></span>
            <p>{{ item.dictLabel }}</p>
          </div>
        </li>
      </ul>
    </div>
    <div class="tip_txtbox spel">
      <p>温馨提示:勾选项仅代表当前意愿,最终受理业务以人工确认结果为准。</p>
    </div>
  </fragment>
</template>

<script>
import { queryDictProps } from '@/common/util';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'cancelAccountReasonV2',
  inject: ['eventMessage'],
  data() {
    return {
      reasonList: [], //销户原因
      bookingBusiList: [], // 预约办理业务
      reasonAccCancelOther: ''
    };
  },
  watch: {
    reasonAccCancelOther: {
      handler(nv) {
        // 判断是否含有emoji表情
        const iconRule =
          /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi;
        if (iconRule.test(nv)) {
          this.reasonAccCancelOther = nv.replace(iconRule, '');
          console.log(nv);
        }
      }
    },
    nextBtnStatus: {
      handler(newV) {
        if (newV) {
          let formData = {
            reason_acc_cancel: this.reasonAccCancel,
            business_acc_cancel: this.bookingBiz
          };
          if (this.showOther) {
            formData.reason_acc_cancel_other = this.reasonAccCancelOther;
          }
          this.$emit('change', formData);
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 1
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 0
          });
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    nextBtnStatus() {
      return (
        this.reasonAccCancel !== '' &&
        this.bookingBiz !== '' &&
        (!this.showOther || this.reasonAccCancelOther !== '')
      );
    },
    reasonAccCancel() {
      return this.reasonList
        .filter((item) => item.isChecked === true)
        .map((it) => it.dictValue)
        .join(',');
    },
    bookingBiz() {
      return this.bookingBusiList
        .filter((item) => item.isChecked === true)
        .map((it) => it.dictValue)
        .join(',');
    },
    showOther() {
      if (this.reasonAccCancel.includes('17')) {
        return true;
      } else {
        return false;
      }
    }
  },
  created() {
    this.$store.commit('flow/setWhiteBg', true);
  },
  mounted() {
    queryDictProps('bc.common.reasonAccCancel')
      .then((res) => {
        this.reasonList = res;
        return queryDictProps('bc.common.typeAccCancel');
      })
      .then((res) => {
        this.bookingBusiList = res;
      })
      .catch((err) => {
        this.$TAlert({
          tips: err
        });
      });
  },
  methods: {
    checkReason(item) {
      for (let it of this.reasonList) {
        if (item.dictValue === it.dictValue) {
          if (it.isChecked) {
            this.$set(it, 'isChecked', false);
          } else {
            this.$set(it, 'isChecked', true);
          }
        }
      }
    },
    checkBooking(item) {
      for (let it of this.bookingBusiList) {
        if (item.dictValue === it.dictValue) {
          if (it.isChecked) {
            this.$set(it, 'isChecked', false);
          } else {
            this.$set(it, 'isChecked', true);
          }
        }
      }
    }
  }
};
</script>
<style scoped>
h5.title > .page_num {
  width: 0.35rem;
  height: 0.22rem;
  display: inline-flex;
  background-color: #fa443a;
  color: #ffffff;
  font-size: 0.14rem;
  padding-left: 0.05rem;
  margin-right: 0.1rem;
}
</style>
