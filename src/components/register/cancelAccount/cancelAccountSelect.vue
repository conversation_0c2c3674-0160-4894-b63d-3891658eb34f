<template>
  <article class="content" style="background: #f5f6fa">
    <div
      v-for="({ typeName, stockAccList }, index) in accountInfoList"
      :key="index"
      class="acct_status_item"
    >
      <div class="tit">
        <h5>{{ typeName }}</h5>
      </div>
      <ul class="acct_list">
        <li v-for="(item, i) in stockAccList" :key="i">
          <div
            class="icon_check"
            :class="{ checked: item.isChecked, disabled: !item.available }"
            @click.stop="selectAcc(item, typeName)"
          >
            <div>
              <span v-if="item.name">{{ item.name }}</span>
              {{ item.stockAccount
              }}<em
                v-if="item.holderStatusDesc"
                class="acct_s_tag"
                :class="{ abnormal: item.status !== '0' }"
                >{{ item.holderStatusDesc }}</em
              >
            </div>
          </div>
          <div
            v-if="typeName === '资金账户' && item.isChecked && item.bankAccount"
            class="input_form"
          >
            <div class="input_text text">
              <span class="tit">银行卡号</span>
              <div class="t1" type="text" placeholder="请输入">
                {{ item.bankAccount }}
              </div>
            </div>
            <div class="input_text text">
              <span class="tit">资金密码</span>
              <input
                v-model="item.fundPassword"
                class="t1"
                type="password"
                placeholder="请输入资金密码"
                @input="changePassword"
              />
            </div>
            <div class="input_text text">
              <span class="tit">银行密码</span>
              <input
                v-model="item.bankPassword"
                class="t1"
                type="password"
                placeholder="请输入银行密码"
                @input="changePassword"
              />
            </div>
          </div>
        </li>
      </ul>
      <van-field
        v-if="typeName === '证券账户'"
        label="同步中登销户"
        input-align="right"
      >
        <template #input>
          <van-switch
            v-model="csdcSynCancel"
            size="20"
            active-color="#EF4034"
          />
        </template>
      </van-field>
      <div v-if="typeName === '证券账户'" class="input_form">
        <div class="imp_c_tips" style="margin: 0">
          <p>
            <span class="imp"
              >开启同步中登系统销户，销户成功后将无法再使用该账户交易</span
            >
          </p>
        </div>
      </div>
    </div>
  </article>
</template>

<script>
import { cancelAccountSelect } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'CancelAccountSelect',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      accountInfoList: [],
      csdcSynCancel: false
    };
  },
  computed: {
    openAccList() {
      let arr = [];
      for (let { stockAccList, typeName } of this.accountInfoList) {
        if (typeName !== '客户号') {
          console.log(stockAccList);
          for (let item of stockAccList) {
            item = { ...item, account: item.stockAccount };
            if (item.isChecked) arr.push(item);
          }
        }
      }
      console.log(arr);
      if (arr.length > 0) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 1 });
      } else {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
      }
      console.log(arr);
      let bankNoPasswordArr = [];
      arr.forEach((item) => {
        if (item.bankAccount) {
          if (!item.fundPassword) {
            bankNoPasswordArr.push(
              `请填写${item.name}${item.stockAccount}资金密码`
            );
            return;
          }
          if (!item.bankPassword) {
            bankNoPasswordArr.push(
              `请填写${item.name}${item.stockAccount}银行密码`
            );
            return;
          }
        }
      });
      console.log(bankNoPasswordArr);
      if (bankNoPasswordArr.length > 0) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          display: true,
          btnStatus: 2,
          data: () => {
            _hvueToast({ mes: bankNoPasswordArr[0] });
          }
        });
      }
      return arr;
    }
  },
  mounted() {
    cancelAccountSelect({
      flowToken: sessionStorage.getItem('TKFlowToken')
    }).then((res) => {
      this.accountInfoList.push({
        typeName: '客户号',
        stockAccList: [{ stockAccount: res.data.clientId, available: true }]
      });
      this.accountInfoList.push({
        typeName: '资金账户',
        stockAccList: res.data.fundAccountList.map((item) => {
          return {
            stockAccount: item.account,
            mainFlag: item.mainFlag,
            bankAccount: item.bankAccount,
            name: item.name,
            holderStatusDesc: item.statusDesc,
            status: item.status,
            type: item.type,
            available: true,
            isChecked: false
          };
        })
      });
      this.accountInfoList.push({
        typeName: '证券账户',
        stockAccList: res.data.stockAccountList.map((item) => {
          return {
            stockAccount: item.account,
            name: item.name,
            holderStatusDesc: item.statusDesc || '',
            assetProp: item.assetProp,
            exchangeType: item.exchangeType,
            holderKind: item.holderKind,
            status: item.status,
            type: item.type,
            available: true,
            isChecked: false
          };
        })
      });
      this.accountInfoList.push({
        typeName: 'OTC账户',
        stockAccList: []
      });
      this.accountInfoList.push({
        typeName: '基金账户',
        stockAccList: res.data.openFundCompanyList.map((item) => {
          return {
            stockAccount: item.account,
            name: item.name,
            holderStatusDesc: item.statusDesc || '',
            type: item.type,
            status: item.status,
            available: true,
            isChecked: false
          };
        })
      });
    });
  },
  methods: {
    selectAcc(accountInfo, typeName) {
      if (typeName === '客户号' && !accountInfo.type) {
        // 勾选客户号，其他账户会自动勾选，并且不可取消
        for (let { stockAccList } of this.accountInfoList) {
          for (let item of stockAccList) {
            if (accountInfo.stockAccount === item.stockAccount && !item.type) {
              if (item.isChecked) {
                this.$set(item, 'isChecked', false);
              } else {
                this.$set(item, 'isChecked', true);
              }
            }
          }
        }
        let accountInfoChecked = this.accountInfoList
          .filter((item) => item.typeName === typeName)[0]
          .stockAccList.filter(
            (it) => it.stockAccount === accountInfo.stockAccount
          )[0].isChecked;
        for (let { stockAccList } of this.accountInfoList) {
          for (let item of stockAccList) {
            if (item.type) {
              if (accountInfoChecked) {
                this.$set(item, 'isChecked', true);
                this.$set(item, 'available', false);
              } else {
                this.$set(item, 'isChecked', false);
                this.$set(item, 'available', true);
              }
            }
          }
        }
      } else if (
        typeName === '资金账户' &&
        accountInfo.mainFlag === '1' &&
        accountInfo.available
      ) {
        // 勾选主资金账户会把除了客户号以外的其他账户全部勾选、并且不可取消。
        for (let { stockAccList } of this.accountInfoList) {
          for (let item of stockAccList) {
            if (
              accountInfo.stockAccount === item.stockAccount &&
              item.type === '1'
            ) {
              if (item.isChecked) {
                this.$set(item, 'isChecked', false);
              } else {
                this.$set(item, 'isChecked', true);
              }
            }
          }
        }
        let accountInfoChecked = this.accountInfoList
          .filter((item) => item.typeName === typeName)[0]
          .stockAccList.filter(
            (it) => it.stockAccount === accountInfo.stockAccount
          )[0].isChecked;
        for (let { stockAccList } of this.accountInfoList) {
          for (let item of stockAccList) {
            if (item.type && item.mainFlag !== '1') {
              if (accountInfoChecked) {
                this.$set(item, 'isChecked', true);
                this.$set(item, 'available', false);
              } else {
                this.$set(item, 'isChecked', false);
                this.$set(item, 'available', true);
              }
            }
          }
        }
      } else {
        if (!accountInfo.available) {
          return;
        }
        for (let { stockAccList } of this.accountInfoList) {
          for (let item of stockAccList) {
            if (
              item.stockAccount === accountInfo.stockAccount &&
              item.type === accountInfo.type &&
              item.name === accountInfo.name
            ) {
              if (item.isChecked) {
                this.$set(item, 'isChecked', false);
              } else {
                this.$set(item, 'isChecked', true);
              }
              break;
            }
          }
        }
        let accountInfoChecked = this.accountInfoList
          .filter((item) => item.typeName === typeName)[0]
          .stockAccList.filter(
            (it) =>
              it.stockAccount === accountInfo.stockAccount &&
              it.name === accountInfo.name
          )[0];
        if (
          accountInfo.exchangeType === '2' &&
          accountInfo.available &&
          typeName === '证券账户'
        ) {
          // 若勾选深A账户，与其相关的京A和股转A账户需全勾选
          for (let { stockAccList } of this.accountInfoList) {
            for (let item of stockAccList) {
              if (
                item.type === '2' &&
                item.stockAccount === accountInfoChecked.stockAccount &&
                (item.exchangeType === '9' || item.exchangeType === '3')
              ) {
                if (accountInfoChecked.isChecked) {
                  this.$set(item, 'isChecked', true);
                }
              }
            }
          }
        }

        // 除客户号以外的其他账户全部勾选会自动勾选主资金账户
        let allChickList = [];
        this.accountInfoList.forEach((item) => {
          if (item.typeName !== '客户号') {
            item.stockAccList.forEach((it) => {
              if (it.mainFlag !== '1') {
                allChickList.push(it.isChecked);
              }
            });
          }
        });
        if (!allChickList.includes(false)) {
          for (let { stockAccList } of this.accountInfoList) {
            for (let item of stockAccList) {
              if (item.mainFlag === '1') {
                console.log(item);
                this.$set(item, 'isChecked', true);
                break;
              }
            }
          }
        }
      }

      this.$emit('change', {
        cancelAccountsData: JSON.stringify(this.openAccList),
        csdcSynCancel: this.csdcSynCancel ? '1' : '0'
      });
    },

    changePassword() {
      this.$emit('change', {
        cancelAccountsData: JSON.stringify(this.openAccList),
        csdcSynCancel: this.csdcSynCancel ? '1' : '0'
      });
    }
  }
};
</script>
