<template>
  <fragment>
    <div v-if="agreeList.length" class="protocol_cont" ref="protocalContent" v-html="agreeList[0].agreementContent"></div>
  </fragment>
</template>

<script>
import { queryAgreementExt } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import { signAgreeV2 } from '@/common/util';

export default {
  name: 'CancelTips',
  inject: ['tkFlowInfo', 'clearKeepAlive', 'eventMessage'],
  components: {
  },
  data() {
    return {
      onlyShowOne: false,
      loadingEd: false,
      showAgreeDetail: false,
      isChecked: false,
      agreeIndex: 0,
      readList: [],
      agreeList: []
    };
  },
  computed: {
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
      btnStatus: 0,
      text: '我已理解并同意',
    });
    this._queryAgreement();
    this.clearKeepAlive();
  },
  methods: {
    getProtocolParam() {
      let param = {
        agreementBizType: this.$attrs.agreementNodeNo.split(':')[0],
        agreementNodeNo: this.$attrs.agreementNodeNo.split(':')[1],
        agreementExt: this.$attrs.agreementExt
      };
      try {
        // let protocolParam = JSON.parse(this.$attrs.protocolParam);
        // protocolParam.forEach(({ groupId, contractType }) => {
        //   param.groupId.push(groupId);
        //   param.contractType.push(contractType);
        // });
        return param;
      } catch (e) {
        console.log(e);
        return param;
      }
    },

    async _queryAgreement() {
      const { inProperty } = this.tkFlowInfo();
      let param = this.getProtocolParam();
      let reqParams = {
        flowToken: sessionStorage.getItem('TKFlowToken'),
        ...param
        // bizType: '013455',
        // contractType: param.contractType.join(','),
        // groupId: param.groupId.join(',')
        // agreementNo: agreementNos
      };
      queryAgreementExt(reqParams)
        .then((res) => {
          this.agreeList = res.data;
          if (this.agreeList.length === 0) {
            this.eventMessage(this, EVENT_NAME.NEXT_STEP)
            return
          }
          this.loadingEd = true;
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 2,
            text: '我已理解并同意',
            data: this.submitForm
          });
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    },
    submitForm() {
      const tkFlowInfo = this.tkFlowInfo();
      signAgreeV2(tkFlowInfo, this.agreeList, this.$attrs)
        .then((epaperSignJson) => {
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
            epaperSignJson
          });
        })
        .catch((error) => {
          _hvueToast({
            mes: error
          });
        });
    }
  }
};
</script>

<style scoped>
.protocol_cont {
  padding: 0.15rem;
  font-size: 0.16rem;
  line-height: 0.26rem;
}
.protocol_cont p {
  padding: 0.05rem 0;
}
.protocol_cont h1 {
  font-size: 0.18rem;
}
</style>
