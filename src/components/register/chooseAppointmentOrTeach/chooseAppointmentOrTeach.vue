<template>
  <article class="content">
    <div class="com_title">
      <h5>您需完成以下两项操作，方可预约开户</h5>
    </div>
    <ul class="yy_must_list">
      <li class="ok" @click="toAppoint">
        <div class="icon"><img src="@/assets/images/yy_nav_01.png" /></div>
        <div class="cont">
          <h5>填写开户信息</h5>
          <p>业务办理时间：交易日 09：00-16：00</p>
        </div>
      </li>
      <li class="ok">
        <div class="icon"><img src="@/assets/images/yy_nav_02.png" /></div>
        <div class="cont">
          <h5>观看投教视频</h5>
          <p>可观看时间：7*24小时</p>
        </div>
      </li>
    </ul>
  </article>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'ChooseAppointmentOrTeach',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {};
  },
  mounted() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
      text: '去预约'
    });
  },
  methods: {
    toAppoint() {}
  }
};
</script>
