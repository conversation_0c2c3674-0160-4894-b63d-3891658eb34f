<template>
  <article class="content" style="background: #f5f6fa">
    <div class="com_title">
      <h5>请选择需要开通的股转市场交易权限：</h5>
    </div>
    <ul class="select_levellist">
      <li @click="chooseRights('7', classOneOptional)">
        <div class="tit">
          <span
            class="icon_check"
            :class="[
              chooseHolderRights === '7' ? 'checked' : '',
              classOneOptional === '0' ? 'disabled' : ''
            ]"
          ></span>
          <h5>一类股转合格投资者</h5>
          <span class="state">{{ classOneAuthorityTips }}</span>
        </div>
        <div class="cont">
          <p>可交易北交所公司、创新层、基础层股票</p>
          <p>日均资产200万元以上（前10个交易日）</p>
        </div>
      </li>
      <li @click="chooseRights('8', classTwoOptional)">
        <div class="tit">
          <span
            class="icon_check"
            :class="[
              chooseHolderRights === '8' ? 'checked' : '',
              classTwoOptional === '0' ? 'disabled' : ''
            ]"
          ></span>
          <h5>二类股转合格投资者</h5>
          <span class="state">{{ classTwoAuthorityTips }}</span>
        </div>
        <div class="cont">
          <p>可交易北交所公司、创新层股票</p>
          <p>日均资产100万元以上（前10个交易日）</p>
        </div>
      </li>
    </ul>
  </article>
</template>

<script>
import { accPermissionSelect } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'chooseHolderRightsGZ',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      chooseHolderRights: '',
      classOneAuthorityOpen: '0', // 是否已开通
      classOneOptional: '0', // 是否可选
      classTwoAuthorityOpen: '0',
      classTwoOptional: '0'
    };
  },
  computed: {
    classOneAuthorityTips() {
      if (this.classOneAuthorityOpen === '0' && this.classOneOptional === '0') {
        return '不满足';
      } else {
        return this.classOneAuthorityOpen === '0' ? '未开通' : '已开通';
      }
    },

    classTwoAuthorityTips() {
      if (this.classTwoAuthorityOpen === '0' && this.classTwoOptional === '0') {
        return '不满足';
      } else {
        return this.classTwoAuthorityOpen === '0' ? '未开通' : '已开通';
      }
    }
  },
  mounted() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
    this.renderingView();
  },
  methods: {
    renderingView() {
      accPermissionSelect({
        flowToken: sessionStorage.getItem('TKFlowToken')
      }).then((res) => {
        this.classOneAuthorityOpen = res.data.classOneAuthorityOpen;
        this.classOneOptional = res.data.classOneOptional;
        this.classTwoAuthorityOpen = res.data.classTwoAuthorityOpen;
        this.classTwoOptional = res.data.classTwoOptional;
      });
    },

    chooseRights(val, disabled) {
      if (disabled === '0') {
        return;
      }
      this.chooseHolderRights = val;
      if (this.chooseHolderRights) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 1 });
        this.$emit('change', {
          choose_holder_rights: this.chooseHolderRights
        });
      } else {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
      }
    }
  }
};
</script>
