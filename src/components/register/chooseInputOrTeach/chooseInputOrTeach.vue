<template>
  <section class="main fixed white_bg" data-page="home" style="position: fixed">
    <t-header></t-header>
    <article class="content">
      <div class="com_title">
        <h5>您需完成以下两项操作，方可预约开户</h5>
      </div>
      <ul class="tj_videolist">
        <li v-for="(item, index) in videoList" :key="index">
          <div
            class="pic"
            @click="playVideo(item.videoUrl, item.pointTime, item.videoName)"
          >
            <img :src="previewBase64" /><a class="play_btn"></a>
          </div>
          <div class="cont">
            <h5>{{ item.videoName }}</h5>
            <p>{{ item.videoName }}</p>
            <p v-if="item.completeTime">完成时间：{{ item.completeTime }}</p>
            <div class="info">
              <div class="row_01">
                <span v-if="item.stateLabel === '未完成'" class="state">{{
                  item.stateLabel
                }}</span>
                <span v-if="item.stateLabel === '已过期'" class="state error">{{
                  item.stateLabel
                }}</span>
                <span v-if="item.stateLabel === '已完成'" class="state ok">{{
                  item.stateLabel
                }}</span
                ><span v-if="item.expireTime"
                  ><b class="imp_span">{{ item.expireTime }}</b
                  >有效期</span
                >
              </div>
              <a
                class="btn"
                @click="
                  playVideo(item.videoUrl, item.pointTime, item.videoName)
                "
                >立即观看</a
              >
            </div>
          </div>
        </li>
      </ul>
      <div class="bottom_tips">
        <p>温馨提示:</p>
        <p>
          1、请确保观看融资融券投资者教育视频的为拟申请开立融资融券账户的投资者本人。
        </p>
        <p>
          2、请在观看完视频后{{
            expireDay
          }}个自然日之内前往临柜办理业务，否则视频观看状态会过期，需重新观看视频。
        </p>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" :class="{ disabled: !completeAll }" @click="toNext"
          >下一步</a
        >
      </div>
    </footer>
  </section>
</template>

<script>
import { videoWatchAdd, videoWatchList, videoList } from '@/service/service.js';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'ChooseInputOrTeach',
  inject: ['eventMessage'],
  data() {
    return {
      expireDay: 0,
      defaultVideoList: [],
      videoList: [],
      videoNameNow: '',
      isReView: 0, //是否点击了重新观看
      previewBase64: ''
    };
  },
  computed: {
    completeAll() {
      let allComplete = [];
      this.videoList.forEach((item) => {
        if (item.stateLabel === '已完成') {
          allComplete.push(item);
        }
      });
      if (allComplete.length === this.videoList.length) {
        return true;
      }
      return false;
    }
  },
  mounted() {
    // this.videoList = JSON.parse(this.$attrs.teachJson);
    videoList().then((vedio) => {
      this.defaultVideoList = vedio.data.videoList;
      this.videoList = vedio.data.videoList;
      const flowToken = sessionStorage.getItem('TKFlowToken');
      videoWatchList({ flowToken }).then((res) => {
        this.expireDay = res.data.expireDay;
        this.videoList = this.videoList.map((item) => {
          let data = res.data.recordList.filter(
            (it) => it.videoName === item.videoName
          )[0];
          let pointTime = '0';
          let expireTime = '';
          let completeTime = '';
          let isExpire = '0';
          let stateLabel = '未完成';
          if (data) {
            pointTime = data.pointTime;
            expireTime = data.expireTime;
            completeTime = data.completeTime;
            isExpire = data.isExpire;
            if (data.isComplete === '1') {
              stateLabel = isExpire === '0' ? '已完成' : '已过期';
            } else {
              stateLabel = '未完成';
            }
          }
          return {
            videoUrl: item.videoUrl,
            videoName: item.videoName,
            stateLabel: stateLabel,
            completeTime: completeTime, //视频完成时间
            expireTime: expireTime, // 视频过期时间
            pointTime: pointTime, //上次观看截止时间
            isExpire: isExpire //视频是否已过期
          };
        });
      });
    });
  },
  methods: {
    toNext() {
      if (this.completeAll) {
        this.eventMessage(this, EVENT_NAME.NEXT_STEP);
      }
    },

    formatSecends(value) {
      let secendTime = parseInt(value);
      let minuteTime = 0;
      let hourTime = 0;
      if (secendTime >= 60) {
        minuteTime = parseInt(secendTime / 60);
        secendTime = parseInt(secendTime % 60);
        if (minuteTime >= 60) {
          hourTime = parseInt(minuteTime / 60);
          minuteTime = parseInt(minuteTime % 60);
        }
      }
      hourTime = hourTime < 10 ? '0' + hourTime : hourTime;
      minuteTime = minuteTime < 10 ? '0' + minuteTime : minuteTime;
      secendTime = secendTime < 10 ? '0' + secendTime : secendTime;
      let res = hourTime + ':' + minuteTime + ':' + secendTime;
      return res;
    },

    playVideo(url, pointTime, videoName) {
      this.videoNameNow = videoName;
      this.isReView = 0;
      if (parseInt(pointTime) > 0) {
        let pointTimeTips = this.formatSecends(pointTime);
        _hvueConfirm({
          title: '请您确认',
          mes: `您上次观看到${pointTimeTips}，是否继续观看?`,
          opts: [
            {
              txt: '重新观看',
              callback: () => {
                this.isReView = 1;
                this.openVideoPlay(url, 0, videoName);
              }
            },
            {
              txt: '继续观看',
              callback: () => {
                this.isReView = 0;
                this.openVideoPlay(url, pointTime, videoName);
              }
            }
          ]
        });
      } else {
        this.openVideoPlay(url, pointTime, videoName);
      }
    },

    openVideoPlay(url, pointTime, videoName) {
      let param = {
        funcNo: '60087',
        videoUrl: url,
        seekToTime: pointTime, //视频从什么时间开始播放
        previewBase64: this.previewBase64, //视频预览图的base64
        moduleName: $hvue.customConfig.moduleName,
        mainColor: $hvue.customConfig.mainColor,
        isRestFull: '1',
        requestHeaders: { flowToken: sessionStorage.getItem('TKFlowToken') },
        requestParams: { flowToken: sessionStorage.getItem('TKFlowToken') },
        url: $hvue.customConfig.serverUrl + '/client/clientFace',
        faceDetectFuncNo: '10001',
        faceDetectCompositeFuncNo: '10000'
      };
      window.videoPlayCallBack = this.videoPlayCallBack;
      $h.callMessageNative(param);
    },

    videoPlayCallBack(data) {
      let pointTime = 0;
      let isComplete = 0;
      if (data.watch_time) {
        pointTime = data.watch_time;
      }
      if (data.error_no === 0) {
        pointTime = 0;
        isComplete = 1;
      }
      videoWatchAdd({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        isComplete: isComplete,
        isReView: this.isReView,
        pointTime: pointTime,
        videoName: this.videoNameNow,
        videoUrl: ''
      }).then(() => {
        videoWatchList({
          flowToken: sessionStorage.getItem('TKFlowToken')
        }).then((res) => {
          this.videoList = this.defaultVideoList;
          this.videoList = this.videoList.map((item) => {
            let data = res.data.recordList.filter(
              (it) => it.videoName === item.videoName
            )[0];
            let pointTime = '0';
            let expireTime = '';
            let completeTime = '';
            let isExpire = '0';
            let stateLabel = '未完成';
            if (data) {
              pointTime = data.pointTime;
              expireTime = data.expireTime;
              completeTime = data.completeTime;
              isExpire = data.isExpire;
              if (data.isComplete === '1') {
                stateLabel = isExpire === '0' ? '已完成' : '已过期';
              } else {
                stateLabel = '未完成';
              }
            }
            return {
              videoUrl: item.videoUrl,
              videoName: item.videoName,
              stateLabel: stateLabel,
              completeTime: completeTime, //视频完成时间
              expireTime: expireTime, // 视频过期时间
              pointTime: pointTime, //上次观看截止时间
              isExpire: isExpire //视频是否已过期
            };
          });
        });
      });
    }
  }
};
</script>
