<template>
  <article class="content">
    <van-form v-show="false" ref="formList">
      <!-- <div class="com_title">
        <h5>证件信息</h5>
      </div>
      <div class="com_box">
        <van-field
          :value="form.clientName"
          label="姓名"
          placeholder="请补充"
          input-align="right"
          readonly
        />
        <van-field
          :value="form.idNo"
          label="证件号码"
          placeholder="请补充"
          input-align="right"
          readonly
        />
        <van-field
          :value="form.idAddress"
          label="证件地址"
          placeholder="请补充"
          input-align="right"
          type="textarea"
          readonly
          autosize
        />
        <van-field
          :value="`${formatDate(form.idBegindate)}-${formatDate(
            form.idEnddate
          )}`"
          label="到期日"
          placeholder="请补充"
          input-align="right"
          readonly
        />
        <van-field
          :value="form.issuedDepart"
          label="签发机关"
          placeholder="请补充"
          input-align="right"
          readonly
        />
        <div class="opea_ctbox">
          <a class="add_btn_01" href="#">更新证件信息</a>
        </div>
      </div>
      <div class="com_title">
        <h5>风险测评</h5>
      </div>
      <div class="com_box">
        <van-field
          :value="
            riskInfo.corpRiskLevel === '99'
              ? '专业投资者'
              : riskInfo.riskLevelName
          "
          label="风险评级"
          placeholder="请补充"
          input-align="right"
          readonly
        />
        <van-field
          v-if="riskInfo.corpRiskLevel !== '99' && riskInfo.corpRiskLevel"
          :value="`${formatDate(riskInfo.corpBeginDate)}-${formatDate(
            riskInfo.corpEndDate
          )}`"
          label="有效期限"
          placeholder="请补充"
          input-align="right"
          readonly
        />
        <div
          v-if="riskInfo.corpRiskLevel !== '99' && riskInfo.corpRiskLevel"
          class="opea_ctbox"
        >
          <a class="add_btn_01" href="#">重新测评</a>
        </div>
      </div> -->
      <div class="com_title">
        <h5>基本信息</h5>
      </div>
      <div class="com_box">
        <van-field
          :value="form.mobileTel"
          label="手机号"
          placeholder="请补充"
          right-icon="arrow"
          input-align="right"
          type="textarea"
          rows="1"
          readonly
          autosize
          required
          error-message-align="right"
          @click="clickInput('mobileTel')"
        />
        <van-field
          :value="form.address"
          type="textarea"
          label="常住地址"
          placeholder="请补充"
          right-icon="arrow"
          input-align="right"
          rows="1"
          readonly
          autosize
          required
          error-message-align="right"
          @click="clickInput('address')"
        />
        <van-field
          :value="form.zipcode"
          label="邮政编码"
          placeholder="请补充"
          right-icon="arrow"
          input-align="right"
          type="textarea"
          rows="1"
          readonly
          autosize
          @click="clickInput('address')"
        />
        <van-field
          v-if="form.appendEmail"
          :value="form.appendEmail"
          label="追保邮箱"
          placeholder="请补充"
          input-align="right"
          type="textarea"
          rows="1"
          readonly
          autosize
        />
        <van-field
          :value="format(form.professionCode, 'professionCodeOptions')"
          label="职业"
          placeholder="请补充"
          right-icon="arrow"
          input-align="right"
          type="textarea"
          rows="1"
          readonly
          autosize
          required
          error-message-align="right"
          @click="showProfessionCode = true"
        />
        <van-popup v-model="showProfessionCode" round position="bottom">
          <div class="layer_tit">
            <h3>请选择</h3>
            <a class="close" @click="showProfessionCode = false"></a>
          </div>
          <div class="layer_cont">
            <ul class="select_list">
              <li
                v-for="item in professionCodeOptions"
                v-show="item.show"
                :key="item.name"
                :class="{ active: form.professionCode === item.name }"
                @click="onConfirmProfessionCode(item)"
              >
                <span>{{ item.label }}</span>
              </li>
            </ul>
          </div>
        </van-popup>
        <van-field
          :value="format(form.taxResidentPerson, 'taxResidentPersonOptions')"
          label="税收身份"
          placeholder="请补充"
          right-icon="arrow"
          input-align="right"
          type="textarea"
          rows="1"
          readonly
          autosize
          required
          @click="clickInput('taxResidentPerson')"
        />
        <van-field
          :value="form.phonecode"
          label="固定电话"
          placeholder="请补充"
          right-icon="arrow"
          input-align="right"
          type="textarea"
          rows="1"
          readonly
          autosize
          @click="clickInput('phonecode')"
        />
        <van-field
          :value="form.email"
          label="电子邮箱"
          placeholder="请补充"
          right-icon="arrow"
          input-align="right"
          type="textarea"
          rows="1"
          readonly
          autosize
          @click="clickInput('email')"
        />
      </div>
      <div class="com_title">
        <h5>其他信息</h5>
      </div>
      <div class="com_box">
        <van-field
          :value="format(form.controlPerson, 'controlPersonOption')"
          label="实际控制投资者自然人"
          placeholder="请补充"
          right-icon="arrow"
          input-align="right"
          type="textarea"
          rows="1"
          readonly
          autosize
          required
          @click="clickInput('controlPerson')"
        />
        <van-field
          :value="format(form.benefitPerson, 'benefitPersonOption')"
          label="交易的实际受益人"
          placeholder="请补充"
          right-icon="arrow"
          input-align="right"
          type="textarea"
          rows="1"
          readonly
          autosize
          required
          @click="clickInput('benefitPerson')"
        />
        <van-field
          :value="format(form.creditRecord, 'creditRecordOption')"
          label="诚信记录"
          placeholder="请补充"
          right-icon="arrow"
          input-align="right"
          type="textarea"
          rows="1"
          readonly
          autosize
          required
          @click="clickInput('creditRecord')"
        />
        <van-field
          :value="form.secRelationName"
          label="第二联系人"
          placeholder="请补充"
          right-icon="arrow"
          input-align="right"
          type="textarea"
          rows="1"
          readonly
          autosize
          @click="clickInput('secRelationName')"
        />
        <van-field
          label="开户营业部"
          placeholder="请补充"
          input-align="right"
          type="textarea"
          rows="1"
          readonly
          autosize
        /></div
    ></van-form>
    <changeMobile
      ref="mobileTel"
      :old-mobile="requestData.mobileTel"
      :default-value="form"
      @result="componentResult"
      v-if="modifyType == 'mobileTel'"
    />
    <changeAddress
      ref="address"
      :doubtAddress="doubtAddress"
      :default-value="form"
      @result="componentResult"
      v-if="modifyType == 'address'"
    />
    <changeProfessionCode
      ref="professionCode"
      :default-value="form"
      :options="professionCodeOptions"
      @result="componentResult"
      v-if="modifyType == 'professionCode'"
    />
    <changeTaxResidentPerson
      ref="taxResidentPerson"
      :default-value="form"
      @getdefault="getdefault"
      @result="componentResult"
      v-if="modifyType == 'taxResidentPerson'"
    />
    <changeAppendEmail
      ref="appendEmail"
      :default-value="form"
      @result="componentResult"
      v-if="modifyType == 'appendEmail'"
    />
    <changeEmail
      ref="email"
      :default-value="form"
      @result="componentResult"
      v-if="modifyType == 'email'"
    />
    <changeZipcode
      ref="zipcode"
      :default-value="form"
      @result="componentResult"
      v-if="modifyType == 'zipcode'"
    />
    <changePhonecode
      ref="phonecode"
      :default-value="form"
      @result="componentResult"
      v-if="modifyType == 'phonecode'"
    />
    <changeBenefitPerson
      ref="benefitPerson"
      :default-value="form"
      :default-client-name="form.clientName"
      :default-type="requestData.benefitIsMyself"
      @getdefault="getdefault"
      @result="componentResult"
      v-if="modifyType == 'benefitPerson'"
    />
    <changeControlPerson
      ref="controlPerson"
      :default-value="form"
      :default-client-name="form.clientName"
      :default-type="requestData.controlIsMyself"
      @getdefault="getdefault"
      @result="componentResult"
      v-if="modifyType == 'controlPerson'"
    />
    <changeCreditRecord
      ref="creditRecord"
      :default-value="form"
      @result="componentResult"
      v-if="modifyType == 'creditRecord'"
    />
    <changeSecRelationName
      ref="secRelationName"
      :default-value="form"
      @result="componentResult"
      v-if="modifyType == 'secRelationName'"
    />
  </article>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
import { clientInfoQry, riskQuery, getAdressTree } from '@/service/service';
import { updateFlowForm, flowSubmit } from '@/service/service';
import { queryDictProps } from '@/common/util';
import changeMobile from './components/changeMobile.vue';
import changeAddress from './components/changeAddress.vue';
import changeProfessionCode from './components/changeProfessionCode';
import changeTaxResidentPerson from './components/changeTaxResidentPerson.vue';
import changeAppendEmail from './components/changeAppendEmail.vue';
import changeBenefitPerson from './components/changeBenefitPerson.vue';
import changeControlPerson from './components/changeControlPerson.vue';
import changeEmail from './components/changeEmail.vue';
import changeZipcode from './components/changeZipcode.vue';
import changePhonecode from './components/changePhonecode.vue';
import changeCreditRecord from './components/changeCreditRecord.vue';
import changeSecRelationName from './components/changeSecRelationName.vue';
const changeLabel = [
  {
    label: '手机号',
    key: 'mobileTel'
  },
  {
    label: '常住地址',
    key: 'address'
  },
  {
    label: '职业',
    key: 'professionCode'
  },
  {
    label: '税收身份',
    key: 'taxResidentPerson'
  },
  {
    label: '固定电话',
    key: 'phonecode'
  },
  {
    label: '电子邮箱',
    key: 'email'
  },
  {
    label: '实际控制投资者自然人',
    key: 'controlPerson'
  },
  {
    label: '交易的实际受益人',
    key: 'benefitPerson'
  },
  {
    label: '诚信记录（含交易合规情况）',
    key: 'creditRecord'
  },
  {
    label: '第二联系人',
    key: 'secRelationName'
  }
];

/**
 * 对象中的key转换成下划线
 */
function objectHumpToLine(obj) {
  console.log(obj);
  var newObj = new Object();
  for (let key in obj) {
    newObj[key.replace(/([A-Z])/g, '_$1').toLowerCase()] = obj[key];
  }
  return newObj;
}

// 获取两个对象里值不同的key，对比修改前和修改后，有哪些key做了修改
function findDifferentKeys(obj1, obj2) {
  let differentKeys = [];
  for (let key in obj1) {
    if (obj1.hasOwnProperty(key) && obj2.hasOwnProperty(key)) {
      if (obj1[key] !== obj2[key]) {
        differentKeys.push(key);
      }
    }
  }
  return differentKeys;
}
export default {
  name: 'ClientInfoModify',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {
    changeMobile,
    changeAddress,
    changeTaxResidentPerson,
    changeProfessionCode,
    changePhonecode,
    changeEmail,
    changeBenefitPerson,
    changeControlPerson,
    changeZipcode,
    changeAppendEmail,
    changeCreditRecord,
    changeSecRelationName
  },
  data() {
    return {
      toTax: '',
      changeFormKey: '',
      doubtAddress: '',
      requestData: {}, //请求出来的defaule数据
      degreeCodeOption: [],
      professionCodeOptions: [], //职业下拉框
      taxResidentPersonOptions: [], //税务人下拉框
      benefitPersonOption: [],
      controlPersonOption: [],
      creditRecordOption: [],
      showProfessionCode: false,
      clientAge: '',
      degreeCode: '',
      modifyType: '',
      riskInfo: {},
      // 修改后表单中的数据
      form: {
        clientName: '',
        idNo: '',
        idAddress: '',
        issuedDepart: '',
        idBegindate: '',
        idEnddate: '',
        idKind: '',
        mobileTel: '',
        address: '',
        zipcode: '',
        appendEmail: '',
        professionCode: '',
        phonecode: '',
        email: '',
        creditRecord: '',
        secRelationName: '',
        secRelationPhone: '',
        socialralType: '',
        benefitPerson: '',
        controlPerson: '',
        benefitClientName: '',
        benefitIdNo: '',
        benefitIdBegindate: '',
        benefitIdEnddate: '',
        benefitAddress: '',
        benefitMobileTel: '',
        benefitEmail: '',
        benefitProfessionCode: '',
        benefitBirthday: '',
        ctrPersonClientName: '',
        ctrPersonIdNo: '',
        ctrPersonIdBegindate: '',
        ctrPersonIdEnddate: '',
        ctrPersonAddress: '',
        ctrPersonMobileTel: '',
        ctrPersonEmail: '',
        ctrPersonProfessionCode: '',
        ctrPersonBirthday: '',
        taxResidentPerson: '',
        birthday: '',
        clientSurname: '',
        engAddress: '',
        engCity: '',
        engCountry: '',
        engProvince: '',
        livingAddressEn: '',
        livingCity: '',
        livingCountry: '',
        livingProvince: '',
        personalName: '',
        taxResidentData: ''
      }
    };
  },
  watch: {
    clientAge() {
      if (this.clientAge <= 44 && this.clientAge >= 30) {
        if (
          this.degreeCode === '1' ||
          this.degreeCode === '2' ||
          this.degreeCode === '3'
        ) {
          this.professionCodeOptions = this.professionCodeOptions.filter(
            (item) => item.dictValue !== '9'
          );
        }
      }
    }
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    this.getProfessionCodeOpt();
    this.getDict();
  },
  mounted() {
    this.renderingView();
  },
  methods: {
    onFailed(errorInfo) {
      console.log('failed', errorInfo);
    },

    formatDate(dateString) {
      var year = dateString ? dateString.substring(0, 4) : '';
      var month = dateString ? dateString.substring(4, 6) : '';
      var day = dateString ? dateString.substring(6, 8) : '';
      return year + '.' + month + '.' + day;
    },

    format(val, key) {
      if (key === 'creditRecordOption') {
        if (val === '0') {
          return this[key].filter((item) => {
            return item.value === val;
          }).length > 0
            ? this[key].filter((item) => {
                return item.value === val;
              })[0].label
            : '';
        } else {
          if (val) {
            return '有不良诚信记录';
          } else {
            return '';
          }
        }
      }
      if (
        this[key].filter((item) => {
          return item.value === val;
        }).length > 0
      ) {
        return this[key].filter((item) => {
          return item.value === val;
        })[0].label;
      } else {
        return '';
      }
    },

    getDict() {
      queryDictProps('bc.common.taxResidentPerson').then((res) => {
        this.taxResidentPersonOptions = res.map((item) => {
          return {
            label: item.dictLabel.trim(),
            value: item.dictValue,
            name: item.dictValue
          };
        });
      });
      queryDictProps('bc.common.integrityRec').then((res) => {
        this.creditRecordOption = res.map((item) => {
          return {
            label: item.dictLabel.trim(),
            value: item.dictValue,
            name: item.dictValue
          };
        });
      });
      queryDictProps('bc.common.isMyself').then((res) => {
        this.benefitPersonOption = res.map((item) => {
          return {
            label: item.dictLabel.trim(),
            value: item.dictValue,
            name: item.dictValue
          };
        });
        this.controlPersonOption = res.map((item) => {
          return {
            label: item.dictLabel.trim(),
            value: item.dictValue,
            name: item.dictValue
          };
        });
      });
    },

    getProfessionCodeOpt() {
      queryDictProps('bc.common.professionCode').then((res) => {
        this.professionCodeOptions = res.map((item) => {
          let show = true;
          if (item.dictValue === '99') {
            show = false;
          }
          return {
            label: item.dictLabel.trim(),
            value: item.dictValue,
            name: item.dictValue,
            show: show
          };
        });
      });
    },

    // onConfirmProfessionCode(item) {
    //   if (this.clientAge <= 44 && this.clientAge >= 30) {
    //     // 年龄小于等于44大于30，选择学生时.9为学生
    //     if (item.value === '9') {
    //       if (
    //         this.degreeCode === '1' ||
    //         this.degreeCode === '2' ||
    //         this.degreeCode === '3'
    //       ) {
    //         this.showProfessionCode = false;
    //         // 职业选学生，学历为本，硕，博
    //         this.$TAlert({
    //           tips: '您选择的职业可能与您的年龄不符，请您确认是否属实？',
    //           confirmBtn: '确认',
    //           cancelBtn: '返回修改',
    //           hasCancel: true,
    //           cancel: () => {
    //             this.form.professionCode = '';
    //             this.showProfessionCode = true;
    //           },
    //           confirm: () => {
    //             // todo 留痕
    //             this.form.professionCode = item.value;
    //             this.showProfessionCode = false;
    //           }
    //         });
    //         return;
    //       }
    //     }
    //   }

    //   this.form.professionCode = item.value;
    //   this.showProfessionCode = false;
    // },

    clickInput(propType) {
      this.modifyType = propType;
      this.$nextTick(() => {
        this.$refs[this.modifyType].show();
      });
    },

    componentResult(key, data) {
      this.changeFormKey = key;
      if (key === 'address') {
        this.toTax = data.toTax;
      }
      if (
        key === 'benefitPerson' ||
        key === 'controlPerson' ||
        key === 'secRelationName' ||
        key === 'address' ||
        key === 'taxResidentPerson'
      ) {
        // Object.keys(this.form).forEach((key) => {
        //   this.form[key] = data.value[key] || this.form[key];
        // });
        Object.keys(data.value).forEach((key) => {
          this.form[key] = data.value[key];
        });
        this.submit();
        return;
      }
      this.form[key] = data.value;
      this.submit();
    },

    getdefault(key, value) {
      this.requestData = { ...this.requestData, ...value.value };
      this.form = { ...this.form, ...value.value };
    },

    renderingView() {
      getAdressTree(
        {},
        {
          cache: true
        }
      );
      riskQuery().then((res) => {
        this.riskInfo = res.data;
      });
      clientInfoQry().then((res) => {
        this.doubtAddress = res.data.doubtAddress;
        this.clientAge = res.data.clientAge;
        this.degreeCode = res.data.degreeCode;
        this.requestData = { ...this.requestData, ...res.data };
        const { inProperty } = this.tkFlowInfo();
        // Object.keys(this.form).forEach((key) => {
        //   this.form[key] = inProperty[key] || this.form[key];
        // });
        Object.keys(this.form).forEach((key) => {
          this.form[key] = res.data[key] || this.form[key];
        });
        this.form.benefitPerson = res.data.benefitIsMyself;
        this.form.controlPerson = res.data.controlIsMyself;

        this.modifyType = JSON.parse(
          inProperty.extInitParams || '{}'
        ).modifyType;
        console.info(this.modifyType, this.$refs);
        this.$nextTick(() => {
          this.$refs[this.modifyType].show();
        });
      });
    },

    submit() {
      let _this = this;
      this.requestData.benefitPerson = this.requestData.benefitIsMyself;
      this.requestData.controlPerson = this.requestData.controlIsMyself;
      this.$refs.formList
        .validate()
        .then(() => {
          if (this.controlPerson === '1') {
            // 本人
            this.form.ctrPersonClientName = this.form.ctrPersonClientName;
            this.form.ctrPersonIdNo = this.form.ctrPersonIdNo;
            this.form.ctrPersonIdBegindate = this.form.ctrPersonIdBegindate;
            this.form.ctrPersonIdEnddate = this.form.ctrPersonIdEnddate;
            this.form.ctrPersonAddress = this.form.ctrPersonAddress;
            this.form.ctrPersonMobileTel = this.form.ctrPersonMobileTel;
            this.form.ctrPersonEmail = this.form.ctrPersonEmail;
            this.form.ctrPersonProfessionCode =
              this.form.ctrPersonProfessionCode;
            this.form.ctrPersonIdKind = this.form.idKind;
            this.form.ctrPersonClientGender = this.form.clientGender;
            this.form.ctrPersonNationality = this.form.nationality;
            this.form.ctrPersonBirthday = this.form.ctrPersonBirthday;
          }
          if (this.benefitPerson === '1') {
            // 本人
            this.form.benefitClientName = this.form.clientName;
            this.form.benefitIdNo = this.form.idNo;
            this.form.benefitIdBegindate = this.form.idBegindate;
            this.form.benefitIdEnddate = this.form.idEnddate;
            this.form.benefitAddress = this.form.address;
            this.form.benefitMobileTel = this.form.mobileTel;
            this.form.benefitEmail = this.form.email;
            this.form.benefitProfessionCode = this.form.professionCode;
            this.form.benefitIdKind = this.form.idKind;
            this.form.benefitClientGender = this.form.clientGender;
            this.form.benefitNationality = this.form.nationality;
            this.form.benefitBirthday = this.form.benefitBirthday;
          }
          // 获取已修改字段的字段key，传给跑批，跑批只去提交已修改字段
          let changedClientInfoKey = '';
          if (this.changeFormKey === 'address') {
            // changedClientInfoKey = 'address';
            if (
              this.form.address !== this.requestData.address &&
              this.form.zipcode !== this.requestData.zipcode
            ) {
              changedClientInfoKey = 'address,zipcode';
            }
            if (
              this.form.address === this.requestData.address &&
              this.form.zipcode !== this.requestData.zipcode
            ) {
              changedClientInfoKey = 'zipcode';
            }
            if (
              this.form.address !== this.requestData.address &&
              this.form.zipcode === this.requestData.zipcode
            ) {
              changedClientInfoKey = 'address';
            }
            if (
              this.form.address === this.requestData.address &&
              this.form.zipcode === this.requestData.zipcode
            ) {
              changedClientInfoKey = 'address,zipcode';
            }
          } else if (this.changeFormKey === 'professionCode') {
            changedClientInfoKey = 'professionCode';
          } else if (this.changeFormKey === 'benefitPerson') {
            changedClientInfoKey = 'benefitPerson';
          } else if (this.changeFormKey === 'controlPerson') {
            changedClientInfoKey = 'controlPerson';
          } else {
            changedClientInfoKey = findDifferentKeys(
              this.requestData,
              this.form
            ).join(',');
          }
          if (!changedClientInfoKey) {
            this.$TAlert({
              title: '温馨提示',
              tips: '您当前未修改任何信息,如需更新该信息请修改后再提交',
              confirm: () => {}
            });
            return;
          }
          console.log(changedClientInfoKey);
          const { inProperty, flowName } = this.tkFlowInfo();
          let modifyType = JSON.parse(inProperty.extInitParams).modifyType;
          this.flowName = flowName;
          const bizType = inProperty.bizType;
          updateFlowForm({
            flowToken: sessionStorage.getItem('TKFlowToken'),
            bizType: bizType,
            source: `${flowName}-初始化`,
            formParam: {
              ...objectHumpToLine(this.form),
              changed_client_info_key: changedClientInfoKey
            }
          }).then(() => {
            let labelTips = '';
            if (
              changeLabel.filter((item) => item.key === modifyType)[0].label ===
              '常住地址'
            ) {
              if (changedClientInfoKey === 'address') {
                labelTips = '常住地址、邮政编码';
              } else if (changedClientInfoKey === 'zipcode') {
                labelTips = '常住地址、邮政编码';
              } else {
                labelTips = '常住地址、邮政编码';
              }
            } else {
              labelTips = changeLabel.filter(
                (item) => item.key === modifyType
              )[0].label;
            }
            flowSubmit({
              flowToken: sessionStorage.getItem('TKFlowToken')
            })
              .then((res) => {
                if (this.toTax) {
                  _this.$TAlert({
                    title: '温馨提示',
                    tips: `您的常住地址和邮编已更新。此更新可能会影响到您的税收身份。根据相关法律法规，请前往确认您的税收身份。`,
                    confirmBtn: '去确认',
                    confirm: () => {
                      // _this.eventMessage(this, EVENT_NAME.TO_INDEX);
                      import('@/common/flowMixinV2.js').then((a) => {
                        // this.$router.history.current.name = null;
                        a.initFlow.call(this, {
                          bizType: '010004',
                          flowNo: '0-3053',
                          isReset: null,
                          isJump: true,
                          initJumpMode: '1',
                          contextParam: JSON.stringify({
                            extInitParams: JSON.stringify({
                              modifyType: 'taxResidentPerson'
                            })
                          })
                        });
                      });
                    }
                  });
                } else {
                  _this.$TAlert({
                    title: '修改成功',
                    tips: `修改${labelTips}成功！`,
                    confirmBtn: '确定',
                    confirm: () => {
                      // _this.eventMessage(this, EVENT_NAME.TO_INDEX);
                      // if ($hvue.platform !== '0') {
                      //   this.$router.replace({
                      //     name: 'clientInfoModify'
                      //   });
                      // } else {
                      this.eventMessage(this, EVENT_NAME.PREV_FLOW);
                      // _this.eventMessage(this, EVENT_NAME.TO_INDEX);
                      // }
                      // this.$router.replace({
                      //   name: 'clientInfoModify'
                      // });
                    }
                  });
                }
              })
              .catch((err) => {
                _this.$TAlert({
                  title: '修改失败',
                  tips: `修改${labelTips}失败！请重新修改提交`,
                  confirmBtn: '确定',
                  confirm: () => {
                    // _this.eventMessage(this, EVENT_NAME.TO_INDEX);
                    // if ($hvue.platform !== '0') {
                    //   this.$router.replace({
                    //     name: 'clientInfoModify'
                    //   });
                    // } else {
                    this.eventMessage(this, EVENT_NAME.PREV_FLOW);
                    // _this.eventMessage(this, EVENT_NAME.TO_INDEX);
                    // }
                    // this.$router.replace({
                    //   name: 'clientInfoModify'
                    // });
                  }
                });
              });
          });
        })
        .catch((err) => {
          console.log('err');
          console.log(err);
        });
      // this.eventMessage(this, EVENT_NAME.NEXT_STEP, { ...this.form });
    }
  }
};
</script>

<style></style>
