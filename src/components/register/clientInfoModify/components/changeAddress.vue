<template>
  <section
    v-show="showPage"
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <t-header @back="back" title="常住地址"></t-header>
    <article class="content">
      <div
        v-if="doubtAddress === '1'"
        class="reject_box"
        style="position: fixed; z-index: 1000"
      >
        <div style="padding: 7px 15px 7px 15px">
          <p>
            根据监管关于金融机构客户身份信息识别相关要求，请您填写准确的常住地址。
          </p>
        </div>
      </div>
      <div v-if="doubtAddress === '1'" class="reject_box">
        <div style="padding: 7px 15px 7px 15px">
          <p>
            根据监管关于金融机构客户身份信息识别相关要求，请您填写准确的常住地址。
          </p>
        </div>
      </div>
      <div class="upload_infobox">
        <div class="input_form">
          <div class="input_text text">
            <span class="tit active">选择地区</span>
            <!-- <div class="dropdown">河北省 保定市 竞秀区</div> -->
            <div
              class="dropdown"
              placeholder="选择地区（省/市/县）"
              @click="openAreaSelect()"
            >
              <span v-if="areaSelValText">{{ areaSelValText }}</span>
            </div>
          </div>
          <div class="input_text text">
            <span class="tit active">详细地址</span>
            <!-- <div
              class="tarea1 needsclick"
              contenteditable="true"
              placeholder="请输入您的详细地址（xx路小区x单元xx室）"
            >
              阳光南街39号阳光新城2单元607室
            </div> -->
            <van-cell-group>
              <van-field
                v-model="addressDetail"
                class="tarea1 needsclick"
                type="textarea"
                row="3"
                :autosize="{ maxHeight: 150, minHeight: 60 }"
                :maxlength="120"
                placeholder="请输入您的详细地址"
                autocomplete="off"
              />
            </van-cell-group>

            <!-- <multLineInput
              v-model="addressDetail"
              class="tarea1 needsclick"
              :maxlength="120"
              placeholder="请输入您的详细地址"
              autocomplete="off"
            /> -->
          </div>
          <div class="input_text text">
            <span class="tit">邮政编码</span>
            <input
              v-model="zipcode"
              class="t1"
              type="tel"
              :maxlength="6"
              placeholder="请输入邮政编码"
            />
          </div>
          <div class="imp_c_tips">
            <p>
              地址填写样例<br />城市地址:XX小区XX栋XX门牌号(如是整栋需备注整栋/自建房/别墅)<br />乡镇地址:XX镇(乡)XX路XX号<br />农村地址:X镇(乡)XX村XX组(号、排)<br />公司地址:XX大厦XX公司XX部门或者XX大厦XX公司<br />商铺地址:XX路XX号XX店铺<br />学校地址:XX学校XX学院XX班级或XX职XX学校XX栋XX门牌号
            </p>
          </div>
        </div>
      </div>
      <div class="ce_btn mt20">
        <a class="p_button" :class="{ disabled: disabledForm }" @click="submit"
          >提交</a
        >
      </div>
    </article>
    <openAreaSelect
      v-if="showCitySelect"
      v-model="areaSelVal"
      @change="areaChangeCallback"
      @finish="areaCallback"
    ></openAreaSelect>
  </section>
</template>

<script>
import openAreaSelect from '@/components/openAreaSelect';
import multLineInput from '@/components/multLineInput';
import { EVENT_NAME } from '@/common/formEnum';
import {
  // sysAddressParse,
  // addressPreSubmit,
  addClientCritMark,
  questionSave
} from '@/service/service';
import {
  sysAddressParse,
  addressPreSubmit,
  postcode
  // questionSave
} from '@/service/modifyClientService';

export default {
  name: 'ChangeAddress',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {
    openAreaSelect,
    multLineInput
  },
  props: {
    defaultValue: {
      type: Object,
      default: () => {}
    },
    doubtAddress: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      taxResidentPerson: '',
      showCitySelect: false,
      showPage: false,
      message: '',
      defaultAddress: '',
      addressDetail: '',
      address: '',
      defaultZipcode: '',
      zipcode: '',
      areaSelVal: '',
      areaSelValText: '',
      oldAreaSelVal: ''
    };
  },
  watch: {
    areaSelVal() {
      this.address = this.areaSelValText + this.addressDetail;
    },
    addressDetail() {
      this.address = this.areaSelValText + this.addressDetail;
    },
    showPage(val) {
      if (val) {
        this.taxResidentPerson = this.defaultValue.taxResidentPerson;
        this.defaultAddress = this.defaultValue.address;
        this.address = this.defaultValue.address;
        this.zipcode = this.defaultValue.zipcode;
        this.defaultZipcode = this.defaultValue.zipcode;

        if (this.address.includes('香港特别行政区')) {
          this.areaSelVal = '810000';
          this.oldAreaSelVa = '810000';
          this.areaSelValText = '香港特别行政区';
          this.addressDetail = this.address.split('香港特别行政区')[1];
          return;
        }
        if (this.address.includes('澳门特别行政区')) {
          this.areaSelVal = '820000';
          this.oldAreaSelVa = '820000';
          this.areaSelValText = '澳门特别行政区';
          this.addressDetail = this.address.split('澳门特别行政区')[1];
          return;
        }
        if (this.address.includes('台湾省')) {
          this.areaSelVal = '830000';
          this.oldAreaSelVa = '830000';
          this.areaSelValText = '台湾省';
          this.addressDetail = this.address.split('台湾省')[1];
          return;
        }
        if (this.address.includes('海外')) {
          // this.areaSelVal = '999999';
          // this.oldAreaSelVa = '999999';
          // this.areaSelValText = '海外';
          // this.addressDetail = this.address.split('海外')[1];
          this.areaSelVal = '999999';
          this.oldAreaSelVa = '999999';
          this.areaSelValText = '海外';
          let a = this.address.split('海外');
          a.shift();
          this.addressDetail = a.join('海外');
          return;
        }
        sysAddressParse({
          address: this.address,
          tag: Math.random()
        }).then(({ data = {} }) => {
          this.addressDetail = data.streetAddress;
          this.areaSelVal = data.xzqyCode;
          this.oldAreaSelVal = data.xzqyCode;
          const { provinceName = '', cityName = '', xzqyName = '' } = data;
          this.areaSelValText = provinceName + cityName + xzqyName;
        });
      }
    }
  },
  computed: {
    disabledForm() {
      if (
        this.address === '' ||
        this.areaSelVal === '' ||
        this.addressDetail === '' ||
        this.zipcode === ''
      ) {
        return true;
      } else {
        return false;
      }
    }
  },
  methods: {
    show() {
      this.showPage = true;
    },

    back() {
      // this.showPage = false;
      // if ($hvue.platform !== '0') {
      //   this.$router.replace({
      //     name: 'clientInfoModify'
      //   });
      // } else {
      this.eventMessage(this, EVENT_NAME.PREV_FLOW);
      // }
      // this.eventMessage
      // this.$router.replace({
      //   name: 'clientInfoModify'
      // });
    },

    openAreaSelect() {
      this.showCitySelect = true;
    },

    areaChangeCallback(data) {
      console.log(data);
      if (data !== this.oldAreaSelVal) {
        this.addressDetail = '';
      }
      this.areaSelVal = data;
      if (['810000', '820000', '830000', '999999'].includes(data)) {
        this.zipcode = '';
      } else {
        postcode({
          areaCode: data
        }).then(({ data = {} }) => {
          this.zipcode = data.post;
        });
      }
    },

    areaCallback(data) {
      console.log(data);
      this.showCitySelect = false;
      if (!data) return;
      /* if (data.length === 1) {
        this.areaSelValText = data[0].text;
        return;
      }
      const [{ text: prov } = '', { text: city } = '', { text: area } = ''] = data;
      this.areaSelValText = prov + city + area; */
      const [{ text: prov }, { text: city } = '', { text: area } = ''] = data;
      this.areaSelValText = Array.from(new Set([prov, city, area])).join('');
    },

    submit() {
      if (this.disabledForm) {
        return;
      }
      if (!this.areaSelValText) {
        _hvueToast({
          mes: '请选择省市区'
        });
        return false;
      }
      if (!this.addressDetail) {
        _hvueToast({
          mes: '请输入您的详细地址'
        });
        return false;
      }
      if (this.areaSelVal === '999999') {
      } else {
        let regExp =
          /[`~!@#$^&*()=|{}':;',\[\].<>?~！@#￥……&*——|{}【】‘；：”“'。，、？%+_]/g;
        let reg = /^(?!.*(.)\1{4})[^\r]{0,}$/;
        // let reg1 = /(自治区|省|市|特别行政区)/;
        // let reg2 = /(镇|组|街|乡|弄|路|区|座|层|号|排|栋|幢|巷|村|队|室)/;
        // 判断条件修改
        let reg1 = /(自治区|省|市|特别行政区)/;
        let reg2 = /(组|座|层|号|栋|幢|室|厦|队)/;
        if (
          this.addressDetail
            .replace(/\s+/g, '')
            .replace(/[\u4e00-\u9fa5]/g, 'xx').length < 5 ||
          this.addressDetail
            .replace(/\s+/g, '')
            .replace(/[\u4e00-\u9fa5]/g, 'xx').length > 120 ||
          regExp.test(this.address)
        ) {
          _hvueToast({
            mes: '详细地址格式不正确'
          });
          return false;
        }
        if (
          !reg.test(this.address) ||
          !reg1.test(this.address) ||
          !reg2.test(this.addressDetail)
        ) {
          _hvueToast({
            mes: '详细地址格式不正确'
          });
          return false;
        }
        if (this.address.includes('海外') && this.address[0] !== '海') {
          _hvueToast({
            mes: '详细地址格式不正确'
          });
          return false;
        }
        if (!/[\u4e00-\u9fa5]/.test(this.address)) {
          _hvueToast({
            mes: '详细地址格式不正确'
          });
          return false;
        }
      }
      if (this.zipcode !== '' && !/^[0-9]\d{5}$/.test(this.zipcode)) {
        _hvueToast({
          mes: '邮政编码格式不正确'
        });
        return false;
      }
      addressPreSubmit({
        mobileTel: this.defaultValue.mobileTel,
        address: this.address
      }).then((res) => {
        if (res.data && res.data.strategyResult === '0') {
          let tips = JSON.parse(res.data.strategyResultMsg).tips;
          this.$TAlert({
            title: '温馨提示',
            tips,
            hasCancel: true,
            confirmBtn: '确定',
            cancelBtn: '返回修改',
            confirm: () => {
              this.toSubmit();
            },
            cancel: () => {}
          });
        } else {
          this.toSubmit();
        }
      });
      // this.showPage = false;
    },

    toSubmit() {
      // this.$emit('result', 'address', {
      //   value: {
      //     address: this.address,
      //     zipcode: this.zipcode
      //   }
      // });
      if (this.doubtAddress === '1') {
        // 常住地址存疑，需解禁
        questionSave({
          questionnaireType: 'addressDoubt',
          address: this.address
        }).then((res) => {
          this.toToSubmit();
          // this.$emit('result', 'address', {
          //   value: {
          //     address: this.address,
          //     zipcode: this.zipcode
          //   }
          // });
        });
      } else {
        this.toToSubmit();
        // this.$emit('result', 'address', {
        //   value: {
        //     address: this.address,
        //     zipcode: this.zipcode
        //   }
        // });
      }
    },

    toToSubmit() {
      // ['810000', '820000', '830000', '999999'].includes(this.areaSelVal)
      if (
        this.taxResidentPerson === '2' &&
        !['999999'].includes(this.areaSelVal)
      ) {
        // 仅为非居民且修改为内地/港澳台
        this.$emit('result', 'address', {
          value: {
            address: this.address,
            zipcode: this.zipcode
          },
          toTax: true
        });
      } else if (
        this.taxResidentPerson === '1' &&
        ['810000', '820000', '830000', '999999'].includes(this.areaSelVal)
      ) {
        // 仅为中国税收居民且修改为港澳台/海外
        this.$emit('result', 'address', {
          value: {
            address: this.address,
            zipcode: this.zipcode
          },
          toTax: true
        });
      } else {
        this.$emit('result', 'address', {
          value: {
            address: this.address,
            zipcode: this.zipcode
          },
          toTax: false
        });
      }
    }
  }
};
</script>

<style></style>
