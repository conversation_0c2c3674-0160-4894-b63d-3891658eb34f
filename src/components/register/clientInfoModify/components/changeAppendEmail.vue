<template>
  <section
    v-show="showPage"
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <t-header @back="back" title="追保邮箱"></t-header>
    <article class="content" style="background: #ffffff">
      <div class="zb_email_box">
        <div class="icon"><img src="@/assets/images/zb_email_img.png" /></div>
        <h5>您的信用账户追保邮箱</h5>
        <h4>{{ appendEmail }}</h4>
        <p>
          亲，信用交易过程中所有的通知均会发送邮件到您的融资融券专属邮箱，包括预警、追加保证金和平仓通知等，请保持关注！
        </p>
        <p>
          邮箱登录地址: <span class="com_link">rzrq.263.net</span>，初始密码为:
          gjzq@身份证后6位，长度为11位，请在信用账户开立的下一个交易日及时登录修改初始密码。
        </p>
      </div>
      <div class="ce_btn mt20">
        <a class="p_button" @click="submit">返回</a>
      </div>
    </article>
  </section>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'ChangeAppendEmail',
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {
    defaultValue: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      showPage: false,
      message: '',
      appendEmail: ''
    };
  },
  watch: {
    defaultValue(val) {
      if (val) {
        this.appendEmail = this.defaultValue.appendEmail;
      }
    },
    showPage(val) {
      if (val) {
        this.appendEmail = this.defaultValue.appendEmail;
      }
    }
  },
  methods: {
    show() {
      this.showPage = true;
    },

    back() {
      // this.showPage = false;
      // if ($hvue.platform !== '0') {
      //   this.$router.replace({
      //     name: 'clientInfoModify'
      //   });
      // } else {
      this.eventMessage(this, EVENT_NAME.PREV_FLOW);
      // }
      // this.$router.replace({
      //   name: 'clientInfoModify'
      // });
    },

    submit() {
      this.eventMessage(this, EVENT_NAME.PREV_FLOW);
      // this.$emit('result');
      // this.showPage = false;
    }
  }
};
</script>

<style lang="less" scoped>
.zb_email_box {
  padding: 0.2rem 0.16rem;

  .icon {
    width: 1.8rem;
    margin: 0 auto 0.2rem;

    img {
      display: block;
      width: 100%;
    }
  }
  h5 {
    font-size: 12px;
    line-height: 1.5;
    font-weight: 500;
    margin-bottom: 0.2rem;
    text-align: center;
  }
  h4 {
    text-align: center;
    color: #ff2840;
  }
  p {
    margin-top: 0.1rem;
    color: #666666;
  }
}
</style>
