<template>
  <section
    v-show="showPage"
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <t-header @back="back" title="实际控制人"></t-header>
    <article class="content">
      <div class="com_title">
        <h5>您的账户实际控制人是：</h5>
      </div>
      <div class="com_box">
        <ul class="check_cmlist">
          <li v-for="({ dictLabel }, index) in selectBox" :key="index">
            <span
              class="icon_radio"
              :class="{ checked: index === selectIndex }"
              @click="selectIndex = index"
              >{{ dictLabel }}</span
            >
          </li>
        </ul>
        <!-- <ul class="check_cmlist">
          <li><span class="icon_radio">本人</span></li>
          <li><span class="icon_radio checked">非本人</span></li>
        </ul> -->
      </div>
      <div v-if="selectIndex === 1" style="display: block">
        <div class="com_box mt10">
          <div class="input_form">
            <div class="input_text text">
              <span class="tit">姓名</span>
              <input
                v-model="form.ctrPersonClientName"
                class="t1"
                type="text"
                placeholder="请输入"
                value=""
              />
            </div>
            <div class="input_text text">
              <span class="tit">证件类型</span>
              <div
                class="dropdown"
                placeholder="请选择"
                @click="showIdKind = true"
              >
                <span v-if="form.ctrPersonIdKind">{{
                  format(form.ctrPersonIdKind, 'idKindOptions')
                }}</span>
              </div>
              <van-popup v-model="showIdKind" round position="bottom">
                <div class="layer_tit">
                  <h3>请选择</h3>
                  <a class="close" @click="showIdKind = false"></a>
                </div>
                <div class="layer_cont">
                  <ul class="select_list">
                    <li
                      v-for="item in idKindOptions"
                      v-show="item.show"
                      :key="item.name"
                      :class="{
                        active: form.ctrPersonIdKind === item.name
                      }"
                      @click="onConfirmIdKind(item)"
                    >
                      <span>{{ item.label }}</span>
                    </li>
                  </ul>
                </div>
              </van-popup>
            </div>
            <div class="input_text text">
              <span class="tit">证件号码</span>
              <input
                v-model="form.ctrPersonIdNo"
                class="t1"
                type="text"
                maxlength="18"
                placeholder="请输入"
                value=""
              />
            </div>
            <div class="input_text text">
              <span class="tit">开始日期</span>
              <h-datetime
                v-model="form.ctrPersonIdBegindate"
                class="dropdown"
                title="请选择开始日期"
                placeholder="请选择开始日期"
                type="date"
                start-year="1900"
                end-year="2100"
              ></h-datetime>
            </div>
            <div class="input_text text">
              <span class="tit">结束日期</span>
              <!-- <h-datetime
                v-model="form.ctrPersonIdEnddate"
                class="dropdown"
                title="请选择结束日期"
                placeholder="请选择结束日期"
                type="date"
                start-year="1900"
                end-year="2100"
              ></h-datetime> -->
              <h-datetime
                v-if="!longTimeChecked"
                v-model="form.ctrPersonIdEnddate"
                class="dropdown"
                title="请选择结束日期"
                placeholder="请选择结束日期"
                type="date"
                start-year="1900"
                end-year="2100"
              />
              <input
                v-else
                class="t1"
                type="text"
                maxlength="32"
                disabled
                value="3000-12-31"
              />
              <span
                :class="{ checked: longTimeChecked }"
                class="icon_check long_span"
                @click.stop="triggerLongTime()"
                >长期</span
              >
            </div>
            <div v-if="form.ctrPersonIdKind !== '0'" class="input_text text">
              <span class="tit">出生日期</span>
              <h-datetime
                v-model="form.ctrPersonBirthday"
                class="dropdown"
                title="请选择出生日期"
                placeholder="请选择开始日期"
                type="date"
                start-year="1900"
                end-year="2100"
              ></h-datetime>
            </div>
            <div v-if="form.ctrPersonIdKind !== '0'" class="input_text text">
              <span class="tit">性别</span>
              <div
                class="dropdown"
                placeholder="请选择性别"
                @click="showClientGender = true"
              >
                <span v-if="form.ctrPersonClientGender">{{
                  format(form.ctrPersonClientGender, 'GenderOption')
                }}</span>
              </div>
              <van-popup v-model="showClientGender" round position="bottom">
                <div class="layer_tit">
                  <h3>请选择</h3>
                  <a class="close" @click="showClientGender = false"></a>
                </div>
                <div class="layer_cont">
                  <ul class="select_list">
                    <li
                      v-for="item in GenderOption"
                      v-show="item.show"
                      :key="item.name"
                      :class="{
                        active: form.ctrPersonClientGender === item.name
                      }"
                      @click="onConfirmClientGender(item)"
                    >
                      <span>{{ item.label }}</span>
                    </li>
                  </ul>
                </div>
              </van-popup>
            </div>
          </div>
        </div>
        <div class="com_box mt10">
          <div class="input_form form_tit_right">
            <div class="input_text text">
              <span class="tit">常住地址</span>
              <!-- <div
                v-model="form.ctrPerson_address"
                class="dropdown"
                placeholder="请选择省市区"
              ></div> -->
              <div
                class="dropdown"
                placeholder="选择地区（省/市/县）"
                @click="openAreaSelect()"
              >
                <span v-if="areaSelValText">{{ areaSelValText }}</span>
              </div>
            </div>
            <div class="input_text text">
              <span class="tit">详细地址</span>
              <div class="input_text">
                <multLineInput
                  v-model="addressDetail"
                  class="tarea1 needsclick"
                  :maxlength="60"
                  placeholder="请输入您的详细地址"
                  autocomplete="off"
                />
              </div>
              <!-- <div
                class="tarea1 needsclick"
                contenteditable="true"
                placeholder="请输入街道、门牌号"
              ></div> -->
            </div>
            <div class="input_text text">
              <span class="tit">手机号码</span>
              <input
                v-model="form.ctrPersonMobileTel"
                class="t1"
                type="text"
                maxlength="11"
                placeholder="请输入"
                value=""
              />
            </div>
            <div class="input_text text">
              <span class="tit">电子邮箱</span>
              <input
                v-model="form.ctrPersonEmail"
                class="t1"
                type="text"
                :maxlength="64"
                placeholder="请输入"
                value=""
              />
            </div>
            <div class="input_text text">
              <span class="tit">职业</span>
              <div
                class="dropdown"
                placeholder="请选择"
                @click="showProfessionCode = true"
              >
                <span v-if="form.ctrPersonProfessionCode">{{
                  format(form.ctrPersonProfessionCode, 'professionCodeOptions')
                }}</span>
              </div>
              <van-popup v-model="showProfessionCode" round position="bottom">
                <div class="layer_tit">
                  <h3>请选择</h3>
                  <a class="close" @click="showProfessionCode = false"></a>
                </div>
                <div class="layer_cont">
                  <ul class="select_list">
                    <li
                      v-for="item in professionCodeOptions"
                      v-show="item.show"
                      :key="item.name"
                      :class="{
                        active: form.ctrPersonProfessionCode === item.name
                      }"
                      @click="onConfirmProfessionCode(item)"
                    >
                      <span>{{ item.label }}</span>
                    </li>
                  </ul>
                </div>
              </van-popup>
            </div>
          </div>
        </div>
      </div>
      <div class="ce_btn mt20">
        <a class="p_button" :class="{ disabled: disabledForm }" @click="submit"
          >提交</a
        >
      </div>
    </article>
    <openAreaSelect
      v-if="showCitySelect"
      v-model="areaSelVal"
      @change="areaChangeCallback"
      @finish="areaCallback"
    ></openAreaSelect>
  </section>
</template>

<script>
import openAreaSelect from '@/components/openAreaSelect';
import multLineInput from '@/components/multLineInput';
import { queryDictProps } from '@/common/util';
import {
  clientRelatedPersonInfoQty,
  securityAvatarQry
  // sysAddressParse
} from '@/service/service';
import {
  sysAddressParse,
  addressPreSubmit
  // questionSave
} from '@/service/modifyClientService';
import { EVENT_NAME } from '@/common/formEnum';
import { ID_KIND } from '@/common/enumeration';
import {
  checkHKLiveCard,
  checkHKPassCard,
  idCardToBirthday,
  computeGetYears
} from '@/common/util';

function formatDate(dateString) {
  var year = dateString.substring(0, 4);
  var month = dateString.substring(4, 6);
  var day = dateString.substring(6, 8);
  return year + '-' + month + '-' + day;
}
export default {
  name: 'ChangeControlPerson',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {
    openAreaSelect,
    multLineInput
  },
  props: {
    defaultValue: {
      type: Object,
      default: () => {}
    },
    defaultClientName: {
      type: String,
      default: ''
    },
    defaultType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      ID_KIND,
      longTimeChecked: false,
      showPage: false,
      showCitySelect: false,
      showClientGender: false,
      showIdKind: false,
      showProfessionCode: false,
      GenderOption: [
        {
          show: true,
          name: '0',
          label: '男',
          value: '0'
        },
        {
          show: true,
          name: '1',
          label: '女',
          value: '1'
        },
        {
          show: true,
          name: '2',
          label: '非自然人',
          value: '2'
        }
      ],
      idKindOptions: [
        {
          show: true,
          name: '0',
          label: '身份证',
          value: '0'
        },
        // {
        //   show: true,
        //   name: 'i',
        //   label: '香港地区居民身份证',
        //   value: 'i'
        // },
        // {
        //   show: true,
        //   name: 'j',
        //   label: '澳门地区居民身份证',
        //   value: 'j'
        // },
        // {
        //   show: true,
        //   name: 'l',
        //   label: '港澳台居民居住证',
        //   value: 'l'
        // },
        {
          show: true,
          name: 'G',
          label: '港澳居民来往内地通行证',
          value: 'G'
        },
        {
          show: true,
          name: 'H',
          label: '台湾居民来往大陆通行证',
          value: 'H'
        }
        // {
        //   show: true,
        //   name: 'I',
        //   label: '外国人永久居留证',
        //   value: 'I'
        // }
      ],
      professionCodeOptions: [],
      message: '',
      areaSelVal: '',
      areaSelValText: '',
      addressDetail: '',
      selectBox: null,
      selectIndex: '',
      controlPerson: '',
      defaultDetail: {},
      form: {
        ctrPersonClientName: '',
        ctrPersonIdNo: '',
        ctrPersonIdBegindate: '',
        ctrPersonIdEnddate: '',
        ctrPersonAddress: '',
        ctrPersonMobileTel: '',
        ctrPersonEmail: '',
        ctrPersonProfessionCode: '',
        ctrPersonIdKind: '',
        ctrPersonClientGender: '',
        ctrPersonNationality: '',
        ctrPersonBirthday: ''
      }
    };
  },
  computed: {
    disabledForm() {
      if (this.selectIndex === 0) {
        return false;
      } else {
        console.log(this.form);
        if (
          this.form.ctrPersonClientName === '' ||
          this.form.ctrPersonIdNo === '' ||
          this.form.ctrPersonIdKind === '' ||
          this.form.ctrPersonIdBegindate === '' ||
          (this.form.ctrPersonIdEnddate === '' && !this.longTimeChecked) ||
          this.form.ctrPersonAddress === '' ||
          this.form.ctrPersonMobileTel === '' ||
          this.form.ctrPersonEmail === '' ||
          this.form.ctrPersonProfessionCode === '' ||
          this.addressDetail === '' ||
          (this.form.ctrPersonBirthday === '' &&
            this.form.ctrPersonIdKind !== '0')
        ) {
          return true;
        } else {
          return false;
        }
      }
    },

    hkMacTaiwanPass() {
      const { HK_MACAU_PASS, TAIWAN_PASS } = this.ID_KIND; // 港澳台通行证
      return [HK_MACAU_PASS, TAIWAN_PASS].includes(this.form.ctrPersonIdKind);
    },
    hkMacTaiwanId() {
      const { HK_ID, MACAU_ID, HK_MACAU_TAIWAN_ID } = this.ID_KIND; // 港澳台居民居住证
      return [HK_ID, MACAU_ID, HK_MACAU_TAIWAN_ID].includes(
        this.form.ctrPersonIdKind
      );
    },
    idKindeName() {
      if (this.hkMacTaiwanPass) {
        return '港澳台居民来往内地通行证';
      } else if (this.hkMacTaiwanId) {
        return '港澳台居民居住证';
      } else {
        return '二代居民身份证';
      }
    }
  },
  watch: {
    areaSelVal() {
      this.form.ctrPersonAddress = this.areaSelValText + this.addressDetail;
    },
    addressDetail() {
      this.form.ctrPersonAddress = this.areaSelValText + this.addressDetail;
    },
    defaultClientName(val) {
      if (val) {
        this.toClientRelatedPersonInfoQty();
      }
    },
    showPage(val) {
      // if (val) {
      //   this.form.ctrPersonClientName = this.defaultValue.ctrPersonClientName;
      //   this.form.ctrPersonIdNo = this.defaultValue.ctrPersonIdNo;
      //   this.form.ctrPersonBirthday = this.defaultValue.ctrPersonBirthday;
      //   this.form.ctrPersonIdBegindate = this.defaultValue.ctrPersonIdBegindate;
      //   this.form.ctrPersonIdEnddate = this.defaultValue.ctrPersonIdEnddate;
      //   this.form.ctrPersonAddress = this.defaultValue.ctrPersonAddress;
      //   this.form.ctrPersonMobileTel = this.defaultValue.ctrPersonMobileTel;
      //   this.form.ctrPersonEmail = this.defaultValue.ctrPersonEmail;
      //   this.form.ctrPersonProfessionCode =
      //     this.defaultValue.ctrPersonProfessionCode;
      //   this.form.ctrPersonIdKind = this.defaultValue.idKind;
      //   this.form.ctrPersonClientGender = this.defaultValue.clientGender;
      //   this.form.ctrPersonNationality = this.defaultValue.nationality;
      //   this.controlPerson = this.defaultValue.controlPerson;
      //   if (this.controlPerson === '0') {
      //     this.selectIndex = 1;
      //   } else {
      //     this.selectIndex = 0;
      //   }
      //   sysAddressParse({
      //     address: this.defaultValue.ctrPersonAddress
      //   }).then((res) => {
      //     if (res.data.streetAddress) {
      //       this.addressDetail = res.data.streetAddress;
      //       this.areaSelVal = res.data.xzqyCode;
      //       this.areaSelValText =
      //         res.data.provinceName + res.data.cityName + res.data.xzqyName;
      //     }
      //   });
      // }
    }
  },
  mounted() {
    queryDictProps('bc.common.isMyself').then((res) => {
      this.selectBox = res;
    });
    this.getProfessionCodeOpt();
    this.toClientRelatedPersonInfoQty();
  },
  methods: {
    show() {
      this.showPage = true;
    },

    back() {
      // this.showPage = false;
      // if ($hvue.platform !== '0') {
      //   this.$router.replace({
      //     name: 'clientInfoModify'
      //   });
      // } else {
      this.eventMessage(this, EVENT_NAME.PREV_FLOW);
      // }
      // this.$router.replace({
      //   name: 'clientInfoModify'
      // });
    },

    triggerLongTime() {
      this.longTimeChecked = !this.longTimeChecked;
    },

    format(val, key) {
      if (
        this[key].filter((item) => {
          return item.value === val;
        }).length > 0
      ) {
        return this[key].filter((item) => {
          return item.value === val;
        })[0].label;
      } else {
        return '';
      }
    },

    changeType(index) {
      this.selectIndex = index;
    },

    toClientRelatedPersonInfoQty() {
      clientRelatedPersonInfoQty({
        personrelatedType: '1'
      }).then((res) => {
        let data = res.data;
        // data.address = '澳门特别行政区海外上海外滩101号';
        if (
          data.address.includes('海外') &&
          data.address.split('海外').length === 2 &&
          data.address[0] !== '海'
        ) {
          data.address = '海外' + data.address.split('海外')[1];
        }
        if (
          data.address.includes('海外') &&
          data.address.split('海外').length > 2 &&
          data.address[0] !== '海'
        ) {
          let a = data.address.split('海外');
          a.shift();
          console.log('海外' + a.join('海外'));
          data.address = '海外' + a.join('海外');
        }
        this.defaultDetail = {
          ctrPersonClientName: data.clientName,
          ctrPersonIdNo: data.idNo,
          ctrPersonIdBegindate: formatDate(data.idBegindate),
          ctrPersonIdEnddate: formatDate(data.idEnddate),
          ctrPersonAddress: data.address,
          ctrPersonMobileTel: data.phonecode,
          ctrPersonEmail: data.email,
          ctrPersonProfessionCode: data.professionCode,
          ctrPersonIdKind: data.idKind,
          ctrPersonClientGender: data.clientGender,
          ctrPersonNationality: data.nationality,
          ctrPersonBirthday: formatDate(data.birthday)
        };
        if (this.defaultType === '1') {
          this.controlPerson = '1';
          this.selectIndex = 0;
        } else if (this.defaultType === '') {
          this.selectIndex = '';
          this.$emit('getdefault', 'benefitPerson', {
            value: this.defaultDetail
          });
        } else {
          this.controlPerson = '0';
          this.selectIndex = 1;
          this.form.ctrPersonClientName = data.clientName;
          this.form.ctrPersonIdNo = data.idNo;
          this.form.ctrPersonIdBegindate = formatDate(data.idBegindate);
          this.form.ctrPersonIdEnddate = formatDate(data.idEnddate);
          this.form.ctrPersonBirthday = formatDate(data.birthday);
          this.form.ctrPersonAddress = data.address;
          this.form.ctrPersonMobileTel = data.phonecode;
          this.form.ctrPersonEmail = data.email;
          this.form.ctrPersonProfessionCode = data.professionCode;
          this.form.ctrPersonIdKind = data.idKind;
          this.form.ctrPersonClientGender = data.clientGender;
          this.form.ctrPersonNationality = data.nationality;
          this.$emit('getdefault', 'benefitPerson', {
            value: this.defaultDetail
          });
          if (
            data.idEnddate === '3000-12-31' ||
            data.idEnddate === '3000' ||
            data.idEnddate === '30001231'
          ) {
            this.longTimeChecked = true;
          }
          const ctrPersonAddr = this.defaultDetail.ctrPersonAddress;
          if (ctrPersonAddr.includes('香港特别行政区')) {
            this.areaSelVal = '810000';
            this.oldAreaSelVa = '810000';
            this.areaSelValText = '香港特别行政区';
            this.addressDetail =
              ctrPersonAddr.split('香港特别行政区')[
                ctrPersonAddr.split('香港特别行政区').length - 1
              ];
            return;
          }
          if (ctrPersonAddr.includes('澳门特别行政区')) {
            this.areaSelVal = '820000';
            this.oldAreaSelVa = '820000';
            this.areaSelValText = '澳门特别行政区';
            this.addressDetail =
              ctrPersonAddr.split('澳门特别行政区')[
                ctrPersonAddr.split('澳门特别行政区').length - 1
              ];
            return;
          }
          if (ctrPersonAddr.includes('台湾省')) {
            this.areaSelVal = '830000';
            this.oldAreaSelVa = '830000';
            this.areaSelValText = '台湾省';
            this.addressDetail =
              ctrPersonAddr.split('台湾省')[
                ctrPersonAddr.split('台湾省').length - 1
              ];
            return;
          }
          if (ctrPersonAddr.includes('海外')) {
            this.areaSelVal = '999999';
            this.oldAreaSelVa = '999999';
            this.areaSelValText = '海外';
            // this.addressDetail =
            //   ctrPersonAddr.split('海外')[1];
            let a = ctrPersonAddr.split('海外');
            a.shift();
            this.addressDetail = a.join('海外');
            return;
          }

          sysAddressParse({
            address: ctrPersonAddr,
            tag: Math.random()
          }).then((addressRes) => {
            if (addressRes.data && addressRes.data.streetAddress) {
              this.addressDetail = addressRes.data.streetAddress;
              this.areaSelVal = addressRes.data.xzqyCode;
              /* this.areaSelValText =
                res.data.provinceName + res.data.cityName + res.data.xzqyName; */
                const { provinceName = '', cityName = '', xzqyName = '' } = addressRes.data;
                this.areaSelValText = provinceName + cityName + xzqyName;
            } else {
              this.addressDetail = ctrPersonAddr;
            }
          });
        }
        // this.$emit('getdefault', 'controlPerson', {
        //   value: this.defaultDetail
        // });
      });
    },

    onConfirmClientGender(item) {
      this.form.ctrPersonClientGender = item.value;
      this.showClientGender = false;
    },

    onConfirmIdKind(item) {
      this.form.ctrPersonIdKind = item.value;
      this.showIdKind = false;
    },

    onConfirmProfessionCode(item) {
      this.form.ctrPersonProfessionCode = item.value;
      this.showProfessionCode = false;
    },

    openAreaSelect() {
      this.showCitySelect = true;
    },

    getProfessionCodeOpt() {
      queryDictProps('bc.common.ctrlProfessionCode').then((res) => {
        this.professionCodeOptions = res.map((item) => {
          let show = true;
          // if (item.dictValue === '99') {
          //   show = false;
          // }
          return {
            label: item.dictLabel.trim(),
            value: item.dictValue,
            name: item.dictValue,
            show: show
          };
        });
      });
    },

    areaChangeCallback(data) {
      // console.log(data);
      // this.areaSelVal = data;
      console.log(data);
      if (data !== this.oldAreaSelVal) {
        this.addressDetail = '';
      }
      this.areaSelVal = data;
      if (['810000', '820000', '830000', '999999'].includes(data)) {
        this.zipcode = '';
      } else {
        this.zipcode = data;
      }
    },

    areaCallback(data) {
      // this.showCitySelect = false;
      // if (!data) return;
      // const [{ text: prov }, { text: city }, { text: area }] = data;
      // this.areaSelValText = prov + city + area;
      console.log(data);
      this.showCitySelect = false;
      if (!data) return;
      if (data.length === 1) {
        this.areaSelValText = data[0].text;
        return;
      }
      const [{ text: prov }, { text: city } = '', { text: area } = ''] = data;
      this.areaSelValText = Array.from(new Set([prov, city, area])).join('');
    },

    submit() {
      if (this.disabledForm) {
        return;
      }
      this.controlPerson = this.selectBox[this.selectIndex].dictValue;
      if (this.controlPerson === '1') {
        if (
          this.defaultValue.clientName === '' ||
          this.defaultValue.idNo === '' ||
          this.defaultValue.idKind === ''
        ) {
          this.$TAlert({
            title: '温馨提示',
            tips: '您的证件信息不完整,请前往就近营业部更新您的证件信息后再来办理.详询客服热线95310.'
          });
          return;
        }
        this.$emit('result', 'controlPerson', {
          value: { controlPerson: this.controlPerson, ...this.form }
        });
      } else {
        let flag = this.checkInput();
        console.log(flag);
        if (flag) {
          securityAvatarQry(
            {
              clientName: this.form.ctrPersonClientName,
              idKind: this.form.ctrPersonIdKind,
              idNo: this.form.ctrPersonIdNo,
              idEnddate: this.defaultDetail.ctrPersonIdEnddate,
              clientGender: this.defaultDetail.ctrPersonClientGender === '0' ? '1' : '2',
              birthday: this.defaultDetail.ctrPersonBirthday,
              citizenship: this.defaultDetail.ctrPersonNationality
            },
            { filter: true }
          )
            .then(({ data, code }) => {
              let publicSecurityAvatar;
              let pubilcSecPicFlag;
              if (code === 0) {
                pubilcSecPicFlag = data.flag;
                publicSecurityAvatar =
                  data.securityAvatarPic?.length > 0
                    ? data.securityAvatarPic
                    : '0';
              } else {
                // 公安校验接口异常情况，默认通过（针对港澳台）
                if (this.hkMacTaiwanId || this.hkMacTaiwanPass) {
                  pubilcSecPicFlag = '1';
                } else {
                  pubilcSecPicFlag = '0';
                }
                publicSecurityAvatar = '0';
              }
              if (pubilcSecPicFlag !== '1') {
                let errorTips =
                  '您的身份信息未通过公安校验检查，请核实并填写正确的证件信息。';
                if (this.hkMacTaiwanId || this.hkMacTaiwanPass)
                  errorTips =
                    '您的身份信息未通过出入境检查，请核实并填写正确的证件信息。';
                this.$TAlert({
                  title: '温馨提示',
                  tips: errorTips,
                  confirmBtn: '我知道了'
                });
              } else {
                if (this.longTimeChecked) {
                  this.form.ctrPersonIdEnddate = '3000-12-31';
                }
                this.$emit('result', 'controlPerson', {
                  value: { controlPerson: this.controlPerson, ...this.form }
                });
              }
            })
            .catch((err) => {
              this.$TAlert({
                title: '温馨提示',
                tips: err
              });
            });
        }
      }
      // this.$emit('result', 'controlPerson', {
      //   value: { controlPerson: this.controlPerson, ...this.form }
      // });
      // this.showPage = false;
    },

    checkInput() {
      // const { clientName, idNo } = this.formData;
      let clientName = this.form.ctrPersonClientName;
      let idNo = this.form.ctrPersonIdNo;
      let matchFlag = true;
      // if (idNo === '' || clientName === '') {
      //   this.$TAlert({
      //     title: '温馨提示',
      //     tips: '请补充身份信息后再提交变更申请。'
      //   });
      //   return false;
      // }
      let idNoRegex = /^([\d]{17}[\dXx]|[\d]{15})$/;
      let idNoLenTest = idNo.length === 18;
      let idNoTest = idNoRegex.test(idNo);
      let idNoTestErrMsg = '请输入正确的证件号码';
      if (this.hkMacTaiwanId) {
        idNoTest = checkHKLiveCard(idNo);
        idNoTestErrMsg = '请输入正确的证件号码';
        if (idNo.length !== 18) {
          idNoLenTest = false;
          idNoTestErrMsg = '请输入正确的证件号码';
        } else {
          idNoLenTest = true;
        }
      } else if (this.hkMacTaiwanPass) {
        idNoTest = checkHKPassCard(idNo);
        idNoTestErrMsg = '请输入正确的证件号码';
        if (
          this.form.ctrPersonIdKind === this.ID_KIND.HK_MACAU_PASS &&
          idNo.length !== 9
        ) {
          idNoTestErrMsg = '请输入正确的证件号码';
          idNoLenTest = false;
        } else if (
          this.form.ctrPersonIdKind === this.ID_KIND.TAIWAN_PASS &&
          idNo.length !== 8
        ) {
          idNoTestErrMsg = '请输入正确的证件号码';
          idNoLenTest = false;
        } else {
          idNoLenTest = true;
        }
      }
      if (!idNoTest || !idNoLenTest) {
        // this.$TAlert({
        //   title: '温馨提示',
        //   tips: idNoTestErrMsg
        // });
        _hvueToast({
          mes: idNoTestErrMsg
        });
        return false;
      }

      let beginDate = this.form.ctrPersonIdBegindate;
      let endDate = this.longTimeChecked
        ? '3000-12-31'
        : this.form.ctrPersonIdEnddate;
      let nowDate = new Date().format('yyyy-MM-dd');
      let dateExp = /\d{4}-\d{2}-\d{2}$/;
      // 判断有效期格式是否正确
      if (
        !dateExp.test(beginDate) ||
        !dateExp.test(endDate) ||
        Date.parse(beginDate.replace(/\./g, '-')) > Date.now() ||
        beginDate === endDate ||
        computeGetYears(endDate, nowDate, 0)
      ) {
        // this.$TAlert({
        //   title: '温馨提示',
        //   tips: '请输入正确的证件有效期'
        // });
        _hvueToast({
          mes: '请输入正确的证件有效期'
        });
        return false;
      }
      // 判断身份证是否过期
      if (
        !this.longTimeChecked &&
        Date.parse(endDate.replace(/\./g, '-')) < Date.now()
      ) {
        // this.$TAlert({
        //   title: '温馨提示',
        //   tips: '请输入正确的证件有效期'
        // });
        _hvueToast({
          mes: '请输入正确的证件有效期'
        });
        return false;
      }
      if (this.hkMacTaiwanId) {
        /** 规则:通行证有效期非5年，提示：请设置正确的证件有效期
         * 截止日期只能与开始日期相同或者前一天，比如起始日期是20121212，截止日期可以是20221212或者20221211，但不能是1213或其他日期
         */
        if (!computeGetYears(beginDate, endDate, 5)) {
          // matchFlag = {
          //   alert: '港澳台居民居住证的有效期应为五年，请核实',
          //   markType: MARK_TYPE.HK_MAC_TAIWAN_CARD_DATE_CHECK
          // };
          // this.$TAlert({
          //   title: '温馨提示',
          //   tips: '请输入在有效期内的证件信息'
          // });
          _hvueToast({
            mes: '请输入在有效期内的证件信息'
          });
          return false;
        }
      } else if (this.hkMacTaiwanPass) {
        /** 规则:通行证有效期非5年或者10年，提示：请设置正确的证件有效期
         * 截止日期只能与开始日期相同或者前一天，比如起始日期是20121212，截止日期可以是20221212或者20221211，但不能是1213或其他日期
         */
        if (
          this.form.ctrPersonIdKind === this.ID_KIND.HK_MACAU_PASS &&
          !computeGetYears(beginDate, endDate, 10)
        ) {
          // matchFlag = {
          //   alert: '港澳居民来往内地通行证有效期应为十年，请核实',
          //   markType: MARK_TYPE.HK_MAC_TAIWAN_CARD_DATE_CHECK
          // };
          // this.$TAlert({
          //   title: '温馨提示',
          //   tips: '请输入在有效期内的证件信息'
          // });
          _hvueToast({
            mes: '请输入在有效期内的证件信息'
          });
          return false;
        } else if (
          this.form.ctrPersonIdKind === this.ID_KIND.TAIWAN_PASS &&
          !computeGetYears(beginDate, endDate, 5)
        ) {
          // matchFlag = {
          //   alert: '台湾居民来往内地通行证有效期应为五年，请核实',
          //   markType: MARK_TYPE.HK_MAC_TAIWAN_CARD_DATE_CHECK
          // };
          // this.$TAlert({
          //   title: '温馨提示',
          //   tips: '请输入在有效期内的证件信息'
          // });
          _hvueToast({
            mes: '请输入在有效期内的证件信息'
          });
          return false;
        }
      } else {
        //16 周岁至 25 周岁,有效期 为 10 年;26 周岁至 45 周岁有效期为 20 年;46 周岁以上的公民,有效期为长期。小 于 16 周岁的情况下申领身份证，有效期 为 5 年
        let birthday = idCardToBirthday(idNo);
        let birthdayTime = new Date(birthday.replace(/\-/g, '/'));
        let beginDateTime = new Date(beginDate.replace(/\-/g, '/'));
        //办理身份证时的年龄
        let handleAge =
          beginDateTime.getFullYear() -
          birthdayTime.getFullYear() -
          (beginDateTime.getMonth() < birthdayTime.getMonth() ||
          (beginDateTime.getMonth() == birthdayTime.getMonth() &&
            beginDateTime.getDate() < birthdayTime.getDate())
            ? 1
            : 0);
        /* let beginYear = beginDateTime.getFullYear();
        if (
          ((beginYear % 4 == 0 && beginYear % 100 != 0) ||
            beginYear % 400 == 0) &&
          beginDate.replace(/\./g, '-').substring(5) == '02-29'
        ) {
          //闰年2月29办理
          if (
            (endDate.replace(/\./g, '-').substring(5) != '02-29' &&
              handleAge >= 26 &&
              handleAge < 46) ||
            (handleAge < 26 &&
              endDate.replace(/\./g, '-').substring(5) != '02-28')
          ) {
            // matchFlag = {
            //   alert: '请确认您上传的证件正反面为同一张',
            //   markType: MARK_TYPE.ID_CARD_CONFIRM
            // };
            // this.$TAlert({
            //   title: '温馨提示',
            //   tips: '请输入在有效期内的证件信息'
            // });
            _hvueToast({
              mes: '请输入在有效期内的证件信息'
            });
            return false;
          }
        } */
        if (
          (handleAge < 16 &&
            handleAge >= 0 &&
            !computeGetYears(beginDate, endDate, 5)) ||
          (handleAge >= 16 &&
            handleAge < 26 &&
            !computeGetYears(beginDate, endDate, 10)) ||
          (handleAge >= 26 &&
            handleAge < 46 &&
            !computeGetYears(beginDate, endDate, 20)) ||
          (handleAge >= 46 && endDate != '3000-12-31')
        ) {
          _hvueToast({
            mes: '请输入在有效期内的证件信息'
          });
          return false;
        }
      }
      // 判断地址
      if (this.areaSelVal === '999999') {
      } else {
        let regExp =
          /[`~!@#$^&*()=|{}':;',\[\].<>?~！@#￥……&*——|{}【】‘；：”“'。，、？%+_]/g;
        let reg = /^(?!.*(.)\1{4})[^\r]{0,}$/;
        /* let reg1 = /(自治区|省|市|特别行政区)/;
        let reg2 = /(镇|组|街|乡|弄|路|区|座|层|号|排|栋|幢|巷|村|队|室)/; */
        let reg1 = /(自治区|省|市|特别行政区)/;
        let reg2 = /(组|座|层|号|栋|幢|室|厦|队)/;
        if (
          this.addressDetail
            .replace(/\s+/g, '')
            .replace(/[\u4e00-\u9fa5]/g, 'xx').length < 5 ||
          this.addressDetail
            .replace(/\s+/g, '')
            .replace(/[\u4e00-\u9fa5]/g, 'xx').length > 120 ||
          regExp.test(this.form.ctrPersonAddress)
        ) {
          _hvueToast({
            mes: '详细地址格式不正确'
          });
          return false;
        }
        if (
          !reg.test(this.form.ctrPersonAddress) ||
          !reg1.test(this.form.ctrPersonAddress) ||
          !reg2.test(this.addressDetail)
        ) {
          _hvueToast({
            mes: '详细地址格式不正确'
          });
          return false;
        }
        if (
          this.form.ctrPersonAddress.includes('海外') &&
          this.form.ctrPersonAddress[0] !== '海'
        ) {
          _hvueToast({
            mes: '详细地址格式不正确'
          });
          return false;
        }
        if (!/[\u4e00-\u9fa5]/.test(this.form.ctrPersonAddress)) {
          _hvueToast({
            mes: '详细地址格式不正确'
          });
          return false;
        }
      }
      // let regExp =
      //   /[`~!@#$^&*()=|{}':;',\[\].<>?~！@#￥……&*——|{}【】‘；：”“'。，、？%+_]/g;
      // let reg = /^(?!.*(.)\1{4})[^\n\r]{0,}$/;
      // let reg1 = /(自治区|省|市|特别行政区)/;
      // let reg2 = /(镇|组|街|乡|弄|路|区|座|层|号|排|栋|幢|巷|村|队|室)/;
      // if (
      //   this.form.ctrPersonAddress
      //     .replace(/\s+/g, '')
      //     .replace(/[\u4e00-\u9fa5]/g, 'xx').length < 5 ||
      //   this.form.ctrPersonAddress
      //     .replace(/\s+/g, '')
      //     .replace(/[\u4e00-\u9fa5]/g, 'xx').length > 120 ||
      //   regExp.test(this.form.ctrPersonAddress)
      // ) {
      //   _hvueToast({
      //     mes: '详细地址格式不正确'
      //   });
      //   return false;
      // }
      // if (
      //   !reg.test(this.form.ctrPersonAddress) ||
      //   !reg1.test(this.form.ctrPersonAddress) ||
      //   !reg2.test(this.addressDetail)
      // ) {
      //   _hvueToast({
      //     mes: '详细地址格式不正确'
      //   });
      //   return false;
      // }
      if (
        !/1[3-9][\d]{9}/.test(this.form.ctrPersonMobileTel) &&
        this.form.ctrPersonMobileTel.length > 11
      ) {
        _hvueToast({
          mes: '手机号码格式不正确'
        });
        return false;
      }
      // 校验邮编格式是否正确
      if (
        !/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(this.form.ctrPersonEmail)
      ) {
        _hvueToast({
          mes: '电子邮箱格式不正确'
        });
        return false;
      }
      return true;
    }
  }
};
</script>

<style scoped>
.form_tit_right .input_text.text .t1:focus,
.form_tit_right .input_text.text .tarea1:focus,
.form_tit_right .input_text.text .tarea1.focus {
  text-align: right;
  padding-right: 0;
  padding-left: 0.88rem;
}
</style>
