<template>
  <section
    v-show="showPage"
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <t-header @back="back" title="诚信记录"></t-header>
    <article class="content">
      <div class="com_title">
        <h5>请选择您的诚信记录</h5>
      </div>
      <div class="com_box">
        <ul class="check_cmlist">
          <li @click="clickCreditRecord('0')">
            <span class="icon_radio" :class="{ checked: creditRecord === '0' }"
              >无不良诚信记录</span
            >
          </li>
          <li @click="clickCreditRecord('')">
            <span class="icon_radio" :class="{ checked: creditRecord !== '0' }"
              >有不良诚信记录</span
            >
          </li>
        </ul>
      </div>
      <div v-if="creditRecord !== '0'" class="com_box mt10">
        <ul class="check_cmlist">
          <li
            v-for="({ dictLabel, dictValue, isChecked }, index) in options"
            :key="index"
            @click="chooseCreditRecord(dictValue)"
          >
            <span class="icon_check" :class="{ checked: isChecked }">{{
              dictLabel
            }}</span>
          </li>
        </ul>
      </div>
      <div class="ce_btn mt20">
        <a class="p_button" :class="{ disabled: !creditRecord }" @click="submit"
          >提交</a
        >
      </div>
    </article>
  </section>
</template>

<script>
import { queryDictProps } from '@/common/util';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'ChangeCreditRecord',
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {
    defaultValue: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      showPage: false,
      message: '',
      options: [],
      creditRecord: ''
    };
  },
  watch: {
    defaultValue(val) {
      if (val) {
        this.creditRecord = this.defaultValue.creditRecord;
        if (this.defaultValue.creditRecord !== '0') {
          this.options.forEach((item) => {
            if (this.defaultValue.creditRecord.includes(item.dictValue)) {
              this.$set(item, 'isChecked', true);
            }
          });
        }
      }
    },
    showPage(val) {
      if (val) {
        this.creditRecord = this.defaultValue.creditRecord;
        if (this.defaultValue.creditRecord !== '0') {
          this.options.forEach((item) => {
            if (this.defaultValue.creditRecord.includes(item.dictValue)) {
              this.$set(item, 'isChecked', true);
            }
          });
        }
      }
    }
  },
  mounted() {
    queryDictProps('bc.common.integrityRec').then((res) => {
      this.options = res.filter((item) => item.dictValue !== '0');
      if (this.defaultValue) {
        this.creditRecord = this.defaultValue.creditRecord;
        if (this.defaultValue.creditRecord !== '0') {
          this.options.forEach((item) => {
            if (this.defaultValue.creditRecord.includes(item.dictValue)) {
              this.$set(item, 'isChecked', true);
            }
          });
        }
      }
    });
  },
  methods: {
    show() {
      this.showPage = true;
    },

    back() {
      // this.showPage = false;
      // if ($hvue.platform !== '0') {
      //   this.$router.replace({
      //     name: 'clientInfoModify'
      //   });
      // } else {
      this.eventMessage(this, EVENT_NAME.PREV_FLOW);
      // }
      // this.$router.replace({
      //   name: 'clientInfoModify'
      // });
    },

    clickCreditRecord(val) {
      this.creditRecord = val;
      if (val === '0') {
        for (let item of this.options) {
          this.$set(item, 'isChecked', false);
        }
      }
    },

    chooseCreditRecord(val) {
      for (let item of this.options) {
        if (item.dictValue === val) {
          if (item.isChecked) {
            this.$set(item, 'isChecked', false);
          } else {
            this.$set(item, 'isChecked', true);
          }
        }
      }
      this.creditRecord = this.options
        .filter((item) => item.isChecked)
        .map((item) => item.dictValue)
        .join(',');
    },

    submit() {
      if (!this.creditRecord) {
        // _hvueToast({
        //   mes: '请选择您的诚信记录'
        // });
        return false;
      }
      console.log(this.creditRecord);
      this.$emit('result', 'creditRecord', { value: this.creditRecord });
    }
  }
};
</script>

<style></style>
