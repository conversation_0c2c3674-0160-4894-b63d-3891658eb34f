<template>
  <section
    v-show="showPage"
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <t-header @back="back" title="电子邮箱"></t-header>
    <article class="content">
      <div class="com_title">
        <h5>请输入您的电子邮箱</h5>
      </div>
      <div class="upload_infobox">
        <div class="input_form">
          <div class="input_text text">
            <span class="tit active">电子邮箱</span>
            <input
              v-model="email"
              class="t1"
              type="text"
              :maxlength="64"
              placeholder="请输入电子邮箱"
            />
          </div>
        </div>
      </div>
      <div class="ce_btn mt20">
        <a class="p_button" :class="{ disabled: !email }" @click="submit"
          >提交</a
        >
      </div>
    </article>
  </section>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'ChangeEmail',
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {
    defaultValue: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      showPage: false,
      message: '',
      email: ''
    };
  },
  watch: {
    defaultValue(val) {
      if (val) {
        this.email = this.defaultValue.email;
      }
    },
    showPage(val) {
      if (val) {
        this.email = this.defaultValue.email;
      }
    }
  },
  methods: {
    show() {
      this.showPage = true;
    },

    back() {
      // this.showPage = false;
      // if ($hvue.platform !== '0') {
      //   this.$router.replace({
      //     name: 'clientInfoModify'
      //   });
      // } else {
        this.eventMessage(this, EVENT_NAME.PREV_FLOW);
      // }
      // this.$router.replace({
      //   name: 'clientInfoModify'
      // });
    },

    submit() {
      if (!this.email) {
        return;
      }
      let reg = /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/;
      // 校验电子邮箱格式是否正确
      if (!reg.test(this.email)) {
        _hvueToast({
          mes: '电子邮箱格式不正确'
        });
        return false;
      }
      this.$emit('result', 'email', { value: this.email });
      // this.showPage = false;
    }
  }
};
</script>

<style></style>
