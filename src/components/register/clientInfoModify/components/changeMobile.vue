<template>
  <section
    v-show="showPage"
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <t-header @back="back" title="手机号码修改"></t-header>
    <article class="content">
      <div class="phone_num_page white_bg">
        <div class="phone_info">
          <div class="icon"></div>
          <p>请填写您的新手机号码，我们将为您发送验证码</p>
          <div class="num">
            原手机号码：<span>{{ format(oldMobile, 'mobileTel') }}</span>
          </div>
        </div>
        <div class="input_form">
          <div class="input_text text">
            <span class="tit">新手机号</span>
            <input
              v-model="mobileTel"
              class="t1"
              type="tel"
              maxlength="11"
              placeholder="请输入新手机号码"
            />
          </div>
          <!-- <div class="input_text text code">
            <span class="tit">图形码</span>
            <input
              v-model="captcha"
              class="t1"
              type="text"
              placeholder="请输入图形码"
            />
            <a class="code_img" @click="imgClick"><img :src="imgSrc" /></a>
          </div> -->
          <div class="input_text text code">
            <span class="tit">验证码</span>
            <input
              v-model="smsCode"
              class="t1"
              type="text"
              placeholder="请输入短信验证码"
            />
            <sms-code-btn
              v-model="uuid"
              :need-img-code="false"
              :secRelationPhone="secRelationPhone"
              :old-mobile-no="oldMobile"
              :mobile-no="mobileTel"
              :biz-type="'010004'"
              @send-result="SMSCodeCallback"
            />
          </div>
        </div>
        <div class="cond_tips">
          短信验证码收不到？试试<a @click="smsVoice" class="com_link"
            >语音验证码</a
          >吧！
        </div>
        <div class="warm_tips">
        温馨提示：如您已开通养老金账户但尚未更新对应银行卡的手机号，您的养老金账户手机号将会修改失败，但其余账户信息修改不受影响。
        </div>
        <div class="ce_btn">
          <a
            class="p_button"
            :class="{ disabled: disabledForm }"
            @click="submit"
            >提交</a
          >
        </div>
      </div>
    </article>
  </section>
</template>

<script>
import {
  getImgCode,
  smsCodeVerification
  // mobilePreSubmit
} from '@/service/service';
import { mobilePreSubmit } from '@/service/modifyClientService';
import SmsCodeBtn from './SmsCodeBtn.vue';
import { EVENT_NAME } from '@/common/formEnum';
import HmosUtil from '@/common/HmosUtil';
const hmosUtil = new HmosUtil({});

export default {
  name: 'ChangeMobile',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: { SmsCodeBtn },
  props: {
    oldMobile: {
      type: String,
      default: ''
    },
    defaultValue: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      showPage: false,
      secRelationPhone: '',
      mobileTel: '',
      captcha: '',
      captchaToken: '',
      uuid: '',
      imgSrc: '',
      smsCode: '',
      needImgCode: true
    };
  },
  computed: {
    disabledForm() {
      if (this.smsCode === '' || this.mobileTel === '') {
        return true;
      } else {
        return false;
      }
    }
  },
  watch: {
    defaultValue(val) {
      if (val) {
        this.secRelationPhone = this.defaultValue.secRelationPhone;
      }
    },
    showPage(val) {
      if (val) {
        this.secRelationPhone = this.defaultValue.secRelationPhone;
      }
    }
  },
  mounted() {
    this.captcha = '';
    this.captchaToken = '';
    this.uuid = '';
    this.imgClick();
  },
  methods: {
    submit() {
      if (this.disabledForm) {
        return;
      }
      if (!this.mobileTel) {
        _hvueToast({
          mes: '请输入手机号'
        });
        return false;
      }
      if (!/1[3-9][\d]{9}/.test(this.mobileTel)) {
        _hvueToast({
          mes: '手机号码格式不正确'
        });
        return false;
      }
      if (this.secRelationPhone === this.mobileTel) {
        _hvueToast({
          mes: '手机号码不能与第二联系人手机号码一致'
        });
        return false;
      }
      if (!this.uuid) {
        _hvueToast({
          mes: '请先发送短信验证码'
        });
        return false;
      }
      // if (!this.captcha) {
      //   _hvueToast({
      //     mes: '请输入图形验证码'
      //   });
      //   return false;
      // }
      if (!this.smsCode) {
        _hvueToast({
          mes: '请输入短信验证码'
        });
        return false;
      }
      mobilePreSubmit({
        mobileTel: this.mobileTel
      }).then((data) => {
        if (data.data.strategyResult === '0') {
          // _hvueToast({ mes: JSON.parse(data.data.strategyResultMsg).tips });
          this.$TAlert({
            title: '温馨提示',
            tips: JSON.parse(data.data.strategyResultMsg).tips
          });
          return;
        }
        smsCodeVerification({
          mobile: this.mobileTel,
          captchaCode: this.smsCode,
          serialNumber: this.uuid
        })
          .then((res) => {
            if (res.data.verificationvFlag !== '1') {
              _hvueToast({ mes: '输入的验证码有误，请重新输入' });
              this.smsCode = '';
              return;
            }
            this.$emit('result', 'mobileTel', { value: this.mobileTel });
          })
          .catch((err) => {
            _hvueToast({
              mes: err
            });
          });
      });
    },

    smsVoice() {
      if (!this.uuid) {
        this.$TAlert({
          title: '温馨提示',
          tips: '请先获取短信验证码'
        });
        return false;
      }
      _hvueConfirm({
        title: '温馨提示',
        mes: '亲，您可以使用输入的手机号，拨打95310-按3-按1，收听语音验证码。',
        opts: [
          {
            txt: '取消',
            color: '#333333'
          },
          {
            txt: '立即拨打',
            callback: () => {
              if (hmosUtil.checkHM) {
                hmosUtil.callPhone('95310');
              } else if ($hvue.platform === '0') {
                window.location.href = 'tel:95310';
              } else {
                let reqParams = {
                  funcNo: '50220',
                  telNo: '95310',
                  callType: '0'
                };
                console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
                const res = $h.callMessageNative(reqParams);
                console.log(`请求结果为: ~~${JSON.stringify(res)}`);
              }
            }
          }
        ]
      });
    },

    show() {
      this.showPage = true;
    },

    back() {
      // this.showPage = false;
      // if ($hvue.platform !== '0') {
      //   this.$router.replace({
      //     name: 'clientInfoModify'
      //   });
      // } else {
      this.eventMessage(this, EVENT_NAME.PREV_FLOW);
      // }
      // this.$router.replace({
      //   name: 'clientInfoModify'
      // });
    },

    imgClick() {
      getImgCode({}, { loading: false }).then((data) => {
        if (data.code === 0) {
          let results = data.data;
          this.imgSrc = results.image;
          this.captchaToken = results.token;
        }
      });
    },

    SMSCodeCallback(flag) {
      console.log(flag);
      if (!flag) {
        this.uuid = '';
      }
    },

    format(val, key) {
      if (key === 'creditRecordOption') {
        if (val === '0') {
          return this[key].filter((item) => {
            return item.value === val;
          }).length > 0
            ? this[key].filter((item) => {
                return item.value === val;
              })[0].label
            : '';
        } else {
          if (val) {
            return '有不良诚信记录';
          } else {
            return '';
          }
        }
      }
      if (
        [
          'clientName',
          'idNo',
          'idAddress',
          'mobileTel',
          'address',
          'appendEmail',
          'phonecode',
          'email',
          'secRelationName'
        ].includes(key)
      ) {
        if (key === 'clientName') {
          let str = '';
          for (let i = 0; i < val.length - 1; i++) {
            str += '*';
          }
          return val.substring(0, 1) + str;
        }
        if (key === 'idNo') {
          let str = '';
          for (let i = 0; i < val.length - 6; i++) {
            str += '*';
          }
          if (val.length > 7) {
            return val.substring(0, 3) + str + val.substring(val.length - 3);
          } else {
            return val ? val : '';
          }
        }
        if (key === 'idAddress') {
          let str = '';
          for (let i = 0; i < val.length - 6; i++) {
            str += '*';
          }
          return val ? val.substring(0, 6) + str : '';
        }
        if (key === 'mobileTel') {
          if (val.length > 7) {
            return val.substring(0, 3) + '****' + val.substring(7);
          } else {
            return val ? val : '';
          }
        }
        if (key === 'address') {
          let str = '';
          for (let i = 0; i < val.length - 6; i++) {
            str += '*';
          }
          return val ? val.substring(0, 6) + str : '';
        }
        if (key === 'appendEmail') {
          return val ? val.substring(0, 3) + '****@' + val.split('@')[1] : '';
        }
        if (key === 'phonecode') {
          if (val.length > 6) {
            return (
              val.substring(0, 3) + '*******' + val.substring(val.length - 3)
            );
          } else {
            return val ? val : '';
          }
        }
        if (key === 'email') {
          return val ? val.substring(0, 3) + '****@' + val.split('@')[1] : '';
        }
        if (key === 'secRelationName') {
          return val ? val : '';
        }
      }
      if (
        ![
          'clientName',
          'idNo',
          'idAddress',
          'mobileTel',
          'address',
          'appendEmail',
          'phonecode',
          'email',
          'secRelationName'
        ].includes(key) &&
        this[key].filter((item) => {
          return item.value === val;
        }).length > 0
      ) {
        return this[key].filter((item) => {
          return item.value === val;
        })[0].label;
      } else {
        return '';
      }
    }
  }
};
</script>

<style></style>
