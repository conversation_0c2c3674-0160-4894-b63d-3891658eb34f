<template>
  <section
    v-show="showPage"
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <t-header @back="back" title="固定电话"></t-header>
    <article class="content">
      <div class="com_title">
        <h5>请输入您在国内的固定电话</h5>
      </div>
      <div class="upload_infobox">
        <div class="input_form">
          <div class="input_text text">
            <span class="tit active">区号</span>
            <input
              v-model="qNumber"
              class="t1"
              type="tel"
              maxlength="4"
              placeholder="请输入区号"
            />
          </div>
          <div class="input_text text">
            <span class="tit active">电话号码</span>
            <input
              v-model="pNumber"
              class="t1"
              type="tel"
              maxlength="8"
              placeholder="请输入电话号码"
            />
          </div>
        </div>
      </div>
      <div class="ce_btn mt20">
        <a class="p_button" :class="{ disabled: disabledForm }" @click="submit"
          >提交</a
        >
      </div>
    </article>
  </section>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'ChangePhonecode',
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {
    defaultValue: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      showPage: false,
      message: '',
      qNumber: '',
      pNumber: '',
      phonecode: ''
    };
  },
  watch: {
    defaultValue(val) {
      if (val) {
        if (/[^\d-]/.test(this.defaultValue.phonecode)) {
          this.pNumber = '';
          this.qNumber = '';
        } else {
          if (
            this.defaultValue.phonecode.length === 11 &&
            !this.defaultValue.phonecode.includes('-')
          ) {
            this.pNumber = this.defaultValue.phonecode;
          } else {
            this.qNumber = this.defaultValue.phonecode
              ? this.defaultValue.phonecode.split('-')[0]
              : '';
            this.pNumber = this.defaultValue.phonecode
              ? this.defaultValue.phonecode.split('-')[1]
              : '';
          }
        }
      }
    },
    showPage(val) {
      if (val) {
        console.log(this.defaultValue.phonecode);
        if (/[^\d-]/.test(this.defaultValue.phonecode)) {
          this.pNumber = '';
          this.qNumber = '';
        } else {
          if (
            this.defaultValue.phonecode.length === 11 &&
            !this.defaultValue.phonecode.includes('-')
          ) {
            this.pNumber = this.defaultValue.phonecode;
          } else {
            this.qNumber = this.defaultValue.phonecode
              ? this.defaultValue.phonecode.split('-')[0]
              : '';
            this.pNumber = this.defaultValue.phonecode
              ? this.defaultValue.phonecode.split('-')[1]
              : '';
          }
        }
      }
    }
    // qNumber() {
    //   this.phonecode = this.qNumber + '-' + this.pNumber;
    // },
    // pNumber() {
    //   this.phonecode = this.qNumber + '-' + this.pNumber;
    // }
  },
  computed: {
    disabledForm() {
      if (this.qNumber === '' || this.pNumber === '') {
        return true;
      } else {
        return false;
      }
    }
  },
  methods: {
    show() {
      this.showPage = true;
    },

    back() {
      // this.showPage = false;
      // if ($hvue.platform !== '0') {
      //   this.$router.replace({
      //     name: 'clientInfoModify'
      //   });
      // } else {
        this.eventMessage(this, EVENT_NAME.PREV_FLOW);
      // }
      // this.$router.replace({
      //   name: 'clientInfoModify'
      // });
    },

    submit() {
      if (this.disabledForm) {
        return;
      }
      this.phonecode = this.qNumber + '-' + this.pNumber;
      let reg = /^(0\d{3,4}-|\d{3,4}\s)?\d{7,8}$/;
      // 校验电话格式是否正确
      if (!/^(0\d{2,3})$/.test(this.qNumber)) {
        _hvueToast({
          mes: '请输入正确的区号'
        });
        return false;
      }
      if (!/^\d{7,8}$/.test(this.pNumber)) {
        _hvueToast({
          mes: '请输入正确的电话号码'
        });
        return false;
      }
      // if (!reg.test(this.phonecode)) {
      //   _hvueToast({
      //     mes: '固定电话格式不正确'
      //   });
      //   return false;
      // }
      this.$emit('result', 'phonecode', { value: this.phonecode });
      // this.showPage = false;
    }
  }
};
</script>

<style></style>
