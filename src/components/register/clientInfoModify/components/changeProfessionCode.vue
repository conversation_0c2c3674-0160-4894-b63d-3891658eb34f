<template>
  <section
    v-show="showPage"
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <t-header @back="back" title="职业"></t-header>
    <article class="content">
      <div class="white_bg">
        <div class="top_searchbox">
          <i class="icon"></i>
          <input
            v-model="searchKey"
            class="t1"
            type="text"
            maxlength="25"
            style="padding: 0.08rem 0.15rem 0.08rem 0.4rem"
            placeholder="可直接搜索职业关键字，如“金融”"
            @keyup.enter="toSearch"
          />
          <a class="btn" @click="toSearch">搜索</a>
        </div>
      </div>
      <div class="com_title">
        <h5>请选择职业</h5>
      </div>
      <div class="fund_acct_scroll" style="-webkit-overflow-scrolling: touch">
        <div class="fund_acct_item">
          <ul class="select_list" style="background: #ffffff">
            <li
              v-for="(item, index) in showOptions"
              v-show="item.show && item.filterShow"
              :key="index"
              :class="{ active: item.value === professionCode }"
              @click="onConfirmProfessionCode(item)"
            >
              <span>{{ item.label }}</span>
            </li>
          </ul>
          <!-- <ul class="acct_list">
            <li
              v-for="(item, index) in options"
              v-show="item.show"
              :key="index"
            >
              <span class="icon_check">{{ item.label }}</span>
            </li>
          </ul> -->
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" :class="{ disabled: disabledForm }" @click="submit"
          >提交</a
        >
      </div>
    </footer>
  </section>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
import {
  // clientOccTip,
  questionSave
  // peofessionPreSubmit
} from '@/service/service';
import {
  clientOccTip,
  // addClientCritMark,
  peofessionPreSubmit
} from '@/service/modifyClientService';

export default {
  name: 'ChangeProfessionCode',
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {
    defaultValue: {
      type: Object,
      default: () => {}
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      recordKeys: '',
      searchKey: '',
      showPage: false,
      message: '',
      email: '',
      defaultProfessionCode: '',
      professionCode: '',
      strategyResult: '',
      showOptions: []
    };
  },
  watch: {
    showPage(val) {
      if (val) {
        this.defaultProfessionCode = this.defaultValue.professionCode;
        // this.professionCode = this.defaultValue.professionCode;
        this.renderingView();
      }
    }
  },
  computed: {
    disabledForm() {
      if (this.professionCode === '') {
        return true;
      } else {
        return false;
      }
    }
  },
  methods: {
    show() {
      this.showPage = true;
    },

    back() {
      // this.showPage = false;
      // if ($hvue.platform !== '0') {
      //   this.$router.replace({
      //     name: 'clientInfoModify'
      //   });
      // } else {
        this.eventMessage(this, EVENT_NAME.PREV_FLOW);
      // }
      // this.$router.replace({
      //   name: 'clientInfoModify'
      // });
    },

    renderingView() {
      clientOccTip().then((data) => {
        this.strategyResult = data.data.strategyResult;
        if (this.strategyResult === '0') {
          this.professionCode;
        } else {
          this.professionCode = this.defaultValue.professionCode;
        }
        peofessionPreSubmit().then((res) => {
          let filterKeys = JSON.parse(
            res.data.result[0].ruleResultDesc
          ).filterKeys;
          this.recordKeys = JSON.parse(
            res.data.result[0].ruleResultDesc
          ).recordKeys;
          this.showOptions = JSON.parse(JSON.stringify(this.options));
          this.showOptions.forEach((item) => {
            // item.filterShow = true;
            this.$set(item, 'filterShow', true);
          });
          this.showOptions.forEach((item) => {
            if (
              filterKeys.split(',').filter((it) => it === item.value).length > 0
            ) {
              // item.show = false;
              this.$set(item, 'show', false);
            }
            if (item.value === this.defaultProfessionCode) {
              this.$set(item, 'checked', true);
            }
          });
        });
      });
    },

    onConfirmProfessionCode(item) {
      this.showOptions.forEach((item) => {
        this.$set(item, 'checked', false);
      });
      this.showOptions.forEach((it) => {
        if (it.value === item.value) {
          this.$set(it, 'checked', true);
          this.professionCode = item.value;
        }
      });
    },

    toSearch() {
      if (this.searchKey) {
        this.showOptions.forEach((item) => {
          if (item.label.includes(this.searchKey)) {
            this.$set(item, 'filterShow', true);
          } else {
            this.$set(item, 'filterShow', false);
          }
        });
      } else {
        this.showOptions.forEach((item) => {
          this.$set(item, 'filterShow', true);
        });
      }
    },

    submit() {
      if (this.disabledForm) {
        return;
      }
      // if (this.recordKeys.includes(this.professionCode)) {
      if (
        this.recordKeys.split(',').filter((it) => it === this.professionCode)
          .length > 0
      ) {
        // 需留痕提交
        this.$TAlert({
          title: '温馨提示',
          tips: '您选择的职业与年龄可能不符，请您确认是否属实?',
          hasCancel: true,
          cancelBtn: '确认',
          confirmBtn: '返回修改',
          confirm: () => {
            // 清空当前选项
            this.professionCode = '';
          },
          cancel: () => {
            // addClientCritMark({
            //   flowToken: sessionStorage.getItem('TKFlowToken'),
            //   markType: '1',
            //   markContent: '职业与年龄不符',
            //   confirmFlag: '1'
            // }).then((res) => {
            //   this.$emit('result', 'professionCode', {
            //     value: this.professionCode
            //   });
            // });
            questionSave({
              questionnaireType: 'ageNotMatchPro',
              itemValue: this.professionCode
            }).then(() => {
              this.$emit('result', 'professionCode', {
                value: this.professionCode
              });
            });
          }
        });

        return;
      }
      this.$emit('result', 'professionCode', { value: this.professionCode });
    }
  }
};
</script>

<style></style>
