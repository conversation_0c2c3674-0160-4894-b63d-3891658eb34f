<template>
  <section
    v-show="showPage"
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <t-header @back="back" title="第二联系人"></t-header>
    <article class="content">
      <div class="upload_infobox">
        <div class="input_form">
          <div class="input_text text">
            <span class="tit active">姓名</span>
            <input
              v-model="secRelationName"
              class="t1"
              type="text"
              placeholder="请输入"
            />
          </div>
          <div class="input_text text">
            <span class="tit active">手机号码</span>
            <input
              v-model="secRelationPhone"
              class="t1"
              type="tel"
              maxlength="11"
              placeholder="请输入"
            />
          </div>
          <div class="input_text text">
            <span class="tit active">关系</span>
            <div
              class="dropdown"
              placeholder="请选择"
              @click="showoptions = true"
            >
              <span v-if="socialralType">{{
                format(socialralType, 'options')
              }}</span>
            </div>
          </div>
          <van-popup v-model="showoptions" round position="bottom">
            <div class="layer_tit">
              <h3>请选择</h3>
              <a class="close" @click="showoptions = false"></a>
            </div>
            <div class="layer_cont">
              <ul class="select_list">
                <li
                  v-for="item in options"
                  :key="item.name"
                  :class="{
                    active: socialralType === item.name
                  }"
                  @click="onConfirmSocialralType(item)"
                >
                  <span>{{ item.label }}</span>
                </li>
              </ul>
            </div>
          </van-popup>
        </div>
      </div>
      <div class="ce_btn mt20">
        <a class="p_button" :class="{ disabled: disabledForm }" @click="submit"
          >提交</a
        >
      </div>
    </article>
  </section>
</template>

<script>
import { queryDictProps } from '@/common/util';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'ChangeSecRelationName',
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {
    defaultValue: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      showPage: false,
      showoptions: false,
      options: [],
      message: '',
      secRelationName: '',
      secRelationPhone: '',
      socialralType: ''
    };
  },
  computed: {
    disabledForm() {
      if (
        this.secRelationName === '' ||
        this.secRelationPhone === '' ||
        this.socialralType === ''
      ) {
        return true;
      } else {
        return false;
      }
    }
  },
  watch: {
    defaultValue(val) {
      if (val) {
        this.secRelationName = this.defaultValue.secRelationName;
        this.secRelationPhone = this.defaultValue.secRelationPhone;
        this.socialralType = this.defaultValue.socialralType;
      }
    },
    showPage(val) {
      if (val) {
        this.secRelationName = this.defaultValue.secRelationName;
        this.secRelationPhone = this.defaultValue.secRelationPhone;
        this.socialralType = this.defaultValue.socialralType;
      }
    }
  },
  mounted() {
    queryDictProps('bc.common.socialRel').then((res) => {
      this.options = res.map((item) => {
        return {
          label: item.dictLabel.trim(),
          value: item.dictValue,
          name: item.dictValue
        };
      });
    });
  },
  methods: {
    show() {
      this.showPage = true;
    },

    back() {
      // this.showPage = false;
      // if ($hvue.platform !== '0') {
      //   this.$router.replace({
      //     name: 'clientInfoModify'
      //   });
      // } else {
        this.eventMessage(this, EVENT_NAME.PREV_FLOW);
      // }
      // this.$router.replace({
      //   name: 'clientInfoModify'
      // });
    },

    format(val, key) {
      if (
        this[key].filter((item) => {
          return item.value === val;
        }).length > 0
      ) {
        return this[key].filter((item) => {
          return item.value === val;
        })[0].label;
      } else {
        return '';
      }
    },

    onConfirmSocialralType(item) {
      this.socialralType = item.value;
      this.showoptions = false;
    },

    submit() {
      if (this.disabledForm) {
        return;
      }
      let secRelationNameLength = this.secRelationName
        .replace(/\s+/g, '')
        .replace(/[\u4e00-\u9fa5]/g, 'xx').length;
      if (secRelationNameLength <= 2 || secRelationNameLength > 60) {
        _hvueToast({
          mes: '姓名格式不正确'
        });
        return false;
      }
      if (!/1[3-9][\d]{9}/.test(this.secRelationPhone)) {
        _hvueToast({
          mes: '手机号码格式不正确'
        });
        return false;
      }
      if (this.secRelationName === this.defaultValue.clientName) {
        _hvueToast({
          mes: '第二联系人姓名不能是本人'
        });
        return false;
      }
      if (this.secRelationPhone === this.defaultValue.mobileTel) {
        _hvueToast({
          mes: '第二联系人手机号码不能是本人'
        });
        return false;
      }
      this.$emit('result', 'secRelationName', {
        value: {
          secRelationName: this.secRelationName,
          secRelationPhone: this.secRelationPhone,
          socialralType: this.socialralType
        }
      });
      // this.showPage = false;
    }
  }
};
</script>

<style></style>
