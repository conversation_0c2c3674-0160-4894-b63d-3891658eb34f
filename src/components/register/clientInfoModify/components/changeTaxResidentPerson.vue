<template>
  <section
    v-show="showPage"
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <t-header @back="back" title="纳税身份"></t-header>

    <article class="content">
      <div class="com_title">
        <h5>请选择您的纳税身份</h5>
      </div>
      <div class="com_box">
        <ul class="check_cmlist">
          <li v-for="({ dictLabel }, index) in selectBox" :key="index">
            <span
              class="icon_radio"
              :class="{ checked: index === selectIndex }"
              @click="selectIndex = index"
              >{{ dictLabel }}</span
            >
          </li>
        </ul>
      </div>
      <div v-if="showInput">
        <div class="com_title">
          <h5>请完善下列涉税信息</h5>
        </div>
        <div class="com_box special">
          <div class="input_form form_tit_right">
            <div class="input_text text">
              <span class="tit">姓(英文/拼音)</span>
              <input
                v-model="clientSurname"
                class="t1"
                type="text"
                maxlength="60"
                placeholder="请输入"
              />
            </div>
            <div class="input_text text">
              <span class="tit">名(英文/拼音)</span>
              <input
                v-model="personalName"
                class="t1"
                type="text"
                maxlength="60"
                placeholder="请输入"
              />
            </div>
            <!-- <div class="input_text text">
              <span class="tit">出生日期</span>
              <h-datetime
                v-model="birthday"
                class="dropdown"
                title="请选择日期"
                placeholder="请选择日期"
                type="date"
                start-year="1900"
                end-year="2100"
              ></h-datetime>
            </div> -->
          </div>
        </div>
        <div class="com_box mt10">
          <div class="input_form form_tit_right">
            <div class="input_text text">
              <span class="tit">出生地</span>
              <div
                class="dropdown"
                placeholder="请选择国家"
                @click="showPicker('engCountry')"
              >
                {{ engCountryStr }}
              </div>
            </div>
            <!-- <div v-if="engCountryStr === '中国'" class="input_text text">
              <span class="tit">选择地区</span>
              <div
                class="dropdown"
                placeholder="选择地区（省/市/县）"
                @click="openAreaSelect(areaSelKeyMap.birthplace)"
              >
                {{ engProvince + '' + engCity }}
              </div>
            </div> -->
            <div class="input_text">
              <multLineInput
                v-model="engAddress"
                class="tarea1 needsclick"
                :maxlength="255"
                placeholder="请输入您的详细地址（英文/拼音）"
                autocomplete="off"
              />
            </div>
            <div class="imp_c_tips">
              <p>
                常住地址需详细到
                <span class="imp"> 单元号、门牌号、楼层和房间号部门科室</span>
              </p>
            </div>
          </div>
        </div>
        <div class="com_box mt10">
          <div class="input_form form_tit_right">
            <div class="input_text text">
              <span class="tit">现居地</span>
              <div
                class="dropdown"
                placeholder="请选择国家"
                @click="showPicker('livingCountry')"
              >
                {{ livingCountryStr }}
              </div>
            </div>
            <div
              v-if="livingCountryStr === '中国（CHN）'"
              class="input_text text"
            >
              <span class="tit">选择地区</span>
              <div
                class="dropdown"
                placeholder="选择地区（省/市/县）"
                @click="openAreaSelect(areaSelKeyMap.livePlace)"
              >
                <span v-if="areaSelValText">{{ areaSelValText }}</span>
              </div>
            </div>
            <div class="input_text">
              <multLineInput
                v-model="livingAddressEn"
                class="tarea1 needsclick"
                :maxlength="255"
                placeholder="请输入您的详细地址（英文/拼音）"
                autocomplete="off"
              />
            </div>
            <div class="imp_c_tips">
              <p>
                常住地址需详细到
                <span class="imp"> 单元号、门牌号、楼层和房间号部门科室</span>
              </p>
            </div>
          </div>
        </div>
        <div>
          <div
            v-for="(taxResidentItem, index) in taxResViewBox"
            :key="index"
            class="com_box mt10"
          >
            <div class="input_form form_tit_right">
              <div class="input_text text">
                <span class="tit">税收居民国(地区）</span>
                <div
                  class="dropdown"
                  placeholder="请选择"
                  @click="openTaxResCountry(index)"
                >
                  {{ taxResidentItem.taxResidentCountryStr }}
                </div>
              </div>
              <div class="input_text text">
                <span class="tit">是否有纳税人识别号</span>
                <div
                  class="dropdown"
                  placeholder="请选择"
                  @click="
                    taxResidentItem.showTaxResNumPicker = true;
                    taxResViewIndex = index;
                  "
                >
                  {{ taxResidentItem.taxResNumFlag }}
                </div>
              </div>
              <div
                v-if="taxResidentItem.taxResNumFlag === '是'"
                class="input_text text"
              >
                <span class="tit active">纳税人识别号</span>
                <input
                  v-model="taxResidentItem.taxpayerIdentificationNumbe"
                  class="t1"
                  type="text"
                  placeholder="请输入纳税人识别号"
                />
              </div>
              <div
                v-else-if="taxResidentItem.taxResNumFlag === '否'"
                class="input_text text"
              >
                <span class="tit active">纳税人识别号</span>
                <div
                  class="dropdown"
                  placeholder="请选择"
                  style="
                    min-height: 0.56rem;
                    line-height: 1.5;
                    padding: 0.16rem 0.24rem 0.16rem 0;
                    font-size: 0.16rem;
                    color: #333333;
                    position: relative;
                    padding-left: 1.7rem;
                  "
                  @click="
                    taxResidentItem.showNoIdReasonPicker = true;
                    taxResViewIndex = index;
                  "
                >
                  {{ taxResidentItem.noIdentitynoReasonLabel }}
                </div>
              </div>
              <div
                v-if="taxResidentItem.noIdentitynoReason === '2'"
                class="input_text"
              >
                <input
                  v-model="taxResidentItem.nrfaNoIdentitynoRemark"
                  class="t1"
                  type="text"
                  maxlength="1500"
                  placeholder="请输入未取得纳税人识别号原因"
                />
              </div>
            </div>
            <!-- <div class="opea_ctbox">
              <a
                v-if="taxResViewBox.length > 1"
                class="delete_btn_01"
                @click="removeTaxResView(index)"
                >删除</a
              >
              <a
                v-if="taxResViewBox.length < 3"
                class="add_btn_01"
                @click="addTaxResView"
                >添加另一项纳税信息</a
              >
            </div> -->
            <van-popup
              v-model="taxResidentItem.showTaxResNumPicker"
              round
              position="bottom"
            >
              <v-picker
                v-model="taxResidentItem.taxResNumFlag"
                :columns="[
                  { label: '是', value: '是' },
                  { label: '否', value: '否' }
                ]"
                @onConfirm="taxResNumPickerCallback"
                @onCancel="taxResidentItem.showTaxResNumPicker = false"
              />
            </van-popup>
            <van-popup
              v-model="taxResidentItem.showNoIdReasonPicker"
              round
              position="bottom"
            >
              <v-picker
                v-model="taxResidentItem.noIdentitynoReason"
                :columns="noIdentitynoReasonData"
                @onConfirm="noIdReasonPickerCallback"
                @onCancel="taxResidentItem.showNoIdReasonPicker = false"
              />
            </van-popup>
            <van-popup
              v-model="taxResidentItem.showTaxResViewSelect"
              round
              position="bottom"
            >
              <v-picker
                v-model="taxResidentItem.taxResidentCountry"
                :columns="NationalityData"
                @onConfirm="taxResViewSelectCallback"
                @onCancel="taxResidentItem.showTaxResViewSelect = false"
              />
            </van-popup>
          </div>
        </div>
        <van-popup v-model="showEngCountrySelect" round position="bottom">
          <v-picker
            ref="engCountryPicker"
            :columns="nationalityData"
            @onConfirm="engCountryCallback"
            @onCancel="showEngCountrySelect = false"
          />
        </van-popup>
        <van-popup v-model="showLivingCountrySelect" round position="bottom">
          <v-picker
            :columns="nationalityData"
            @onConfirm="livingCountryCallback"
            @onCancel="showLivingCountrySelect = false"
          />
        </van-popup>
      </div>
      <!-- 设置单独的选中项 -->
      <openAreaSelect
        v-if="showCitySelect"
        v-model="areaSelVal"
        @change="areaChangeCallback"
        @finish="areaCallback"
      ></openAreaSelect>
      <div class="ce_btn mt20">
        <a class="p_button" @click="checkInput">提交</a>
      </div>

      <div class="appro_tips" style="background-color: #f5f6fa">
        <p>小贴士:</p>
        <p>Q:税收居民身份有哪几类?</p>
        <p>A:税收居民身份有三类︰</p>
        <p>(1)仅为中国税收居民</p>
        <p>
          中国税收居民指在中国境内有住所，或者无住所而境内居住满一年的个人。在中国境内有住所是指因户籍、家庭经济利益关系而在中国境内习惯性居住。在境内居住满一年，是指一个纳税年度中在中国境内居住365日。临时离境的，不扣减日数。临时离境，是指在一个纳税年度中一次不超过30日或者多次累计不超过90日的离境。
        </p>
        <p>(2)仅为非居民</p>
        <p>非居民是指中国税收居民以外的人。</p>
        <p>(3)既是中国税收居民又是其他国家(地区)税收居民</p>
        <p>
          其他国家(地区)税收居民身份认定规则及纳税人识别号相关信息请参见国家税务总局网站(http://www.chinatax.gov.cn/chinatax/aeoi_index.html)。
        </p>
      </div>
    </article>
  </section>
</template>

<script>
import openAreaSelect from '@/components/openAreaSelect';
import multLineInput from '@/components/multLineInput';
import { DICT_TYPE } from '@/common/enumeration';
import VPicker from '@/components/VPicker';
import { getDictData } from '@/service/service';
import { getRevenueResidentType } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'TaxResidentInfo',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {
    openAreaSelect,
    multLineInput,
    VPicker
  },
  data() {
    return {
      showPage: false,
      DICT_TYPE,
      selectBox: null,
      taxResidentMap: {
        isShow: false,
        showTaxResNumPicker: false,
        showNoIdReasonPicker: false,
        showTaxResViewSelect: false,
        taxResNumFlag: '', // 是否有纳税人识别号
        taxResidentCountry: '', // 税收居民国(字典)
        taxResidentCountryStr: '', // 税收居民国
        taxpayerIdentificationNumbe: '', // 纳税人识别号串
        noIdentitynoReason: '', // 无法提供纳税人识别号原因
        noIdentitynoReasonLabel: '',
        nrfaNoIdentitynoRemark: '' // 非居民未能取得税号原因
      },
      CityDisabled:[],
      taxResViewBox: [],
      taxResViewIndex: 0,
      nationalityData: null, // 国家数据字典信息
      noIdentitynoReasonData: null, // 无纳税人识别号原因数据字典信息
      selectIndex: 0,
      showEngCountrySelect: false,
      showLivingCountrySelect: false,
      showCitySelect: false,
      areaSelKeyMap: {
        birthplace: 'birthplace',
        livePlace: 'livePlace',
        taxResidentPlace: 'taxResidentPlace'
      },
      areaSelVal: '',
      areaSelKey: '',
      areaSelValText: '',
      clientSurname: '', // 姓氏
      personalName: '', // 名字
      // birthday: '', //	出生日期
      taxpayerIdentificationNumbe: '', //	纳税人识别号串
      engCountry: '', // 出生国(字典)
      engCountryStr: '', // 出生国
      engProvince: '', // 出生省
      engCity: '', // 出生市
      engArea: '',
      engAddress: '', // 出生地址
      livingCountry: '', // 现居国(字典)
      livingCountryStr: '', // 现居国
      livingProvince: '', // 现居省
      livingCity: '', //	现居市
      livingArea: '',
      livingAddressEn: '' // 现居地址（英）
    };
  },
  computed: {
    showInput() {
      return this.selectIndex !== 0;
    },
    isCount() {
      return this.readList.includes(this.agreeId);
    },
    NationalityData(){
      if(this.selectIndex == 1) {
        return this.nationalityData.filter(item => item.value != 'CHN' )
      }
      return this.nationalityData;
    },
  },
  mounted() {
    this._getDictData();
  },
  methods: {
    show() {
      this.showPage = true;
    },

    back() {
      // this.showPage = false;
      // if ($hvue.platform !== '0') {
      //   this.$router.replace({
      //     name: 'clientInfoModify'
      //   });
      // } else {
        this.eventMessage(this, EVENT_NAME.PREV_FLOW);
      // }
      // this.$router.replace({
      //   name: 'clientInfoModify'
      // });
    },

    getTaxResInfo() {
      getRevenueResidentType({})
        .then(({ data }) => {
          const { revenueResidentType, taxResidentData } = data;
          this.selectIndex = this.selectBox.findIndex(
            ({ dictValue }) => dictValue === revenueResidentType
          );
          if (this.showInput) {
            this.addTaxResView();

            // const taxResidentList = JSON.parse(taxResidentData);
            // let {
            //   engCountry,
            //   livingCountry,
            //   livingArea,
            //   livingAreaStr,
            //   livingCityStr,
            //   livingProvinceStr,
            //   ...res
            // } = data;
            // if (engCountry && engCountry !== ' ') {
            //   const i = this.nationalityData.findIndex(
            //     ({ value }) => value === engCountry
            //   );
            //   this.engCountryStr = this.nationalityData[i].label;
            //   this.engCountry = this.nationalityData[i].value;
            // }
            // if (livingCountry && livingCountry !== ' ') {
            //   const i = this.nationalityData.findIndex(
            //     ({ value }) => value === livingCountry
            //   );
            //   this.livingCountryStr = this.nationalityData[i].label;
            //   this.livingCountry = this.nationalityData[i].value;
            // }
            // if (livingArea && livingArea !== '') {
            //   this.areaSelVal = livingArea;
            // }
            // if (livingAreaStr && livingCityStr && livingProvinceStr) {
            //   this.areaSelValText =
            //     livingProvinceStr + livingCityStr + livingAreaStr;
            // }
            // let noTaxResidentList = false;
            // if (taxResidentList.length === 1) {
            //   if (
            //     taxResidentList[0].nothingTaxpayerIdentificationNumberMsg ===
            //       '' &&
            //     taxResidentList[0].taxpayerIdentificationNumbe === '' &&
            //     taxResidentList[0].taxResidentCountry === '' &&
            //     taxResidentList[0]
            //       .nothingTaxpayerIdentificationNumberSpecificMsg === ''
            //   ) {
            //     noTaxResidentList = true;
            //   } else {
            //     noTaxResidentList = false;
            //   }
            // }
            // if (taxResidentList.length === 0 && noTaxResidentList) {
            //   this.addTaxResView();
            // }
            // console.log(taxResidentList);
            // for (const {
            //   taxResidentCountry,
            //   taxpayerIdentificationNumbe,
            //   noIdentitynoReason,
            //   nrfaNoIdentitynoRemark
            // } of taxResidentList) {
            //   let taxResNumFlag = '否';
            //   let taxResidentCountryStr = '';
            //   let noIdentitynoReasonLabel = '';
            //   if (
            //     taxpayerIdentificationNumbe &&
            //     taxpayerIdentificationNumbe !== ''
            //   ) {
            //     taxResNumFlag = '是';
            //   }
            //   if (taxResidentCountry && taxResidentCountry !== ' ') {
            //     const natIndex = this.nationalityData.findIndex(
            //       ({ value }) => value === taxResidentCountry
            //     );
            //     taxResidentCountryStr = this.nationalityData[natIndex].label;
            //   }
            //   if (noIdentitynoReason && noIdentitynoReason !== ' ') {
            //     const reasonIndex = this.noIdentitynoReasonData.findIndex(
            //       ({ value }) => value === noIdentitynoReason
            //     );
            //     noIdentitynoReasonLabel =
            //       this.noIdentitynoReasonData[reasonIndex].label;
            //   }
            //   this.taxResViewBox.push({
            //     ...this.taxResidentMap,
            //     taxResidentCountry,
            //     taxResidentCountryStr,
            //     taxResNumFlag,
            //     taxpayerIdentificationNumbe,
            //     noIdentitynoReason,
            //     noIdentitynoReasonLabel,
            //     nrfaNoIdentitynoRemark
            //   });
            // }
            // console.log(this.taxResViewBox);
            // Object.assign(this, res);
            // this.engAddress = this.engCity;
          } else {
            this.addTaxResView();
          }

          console.log(data);
          let defaultData = {
            clientSurname: data.clientSurname, // 姓氏
            personalName: data.personalName, // 名字
            taxpayerIdentificationNumbe: data.taxpayerIdentificationNumbe, //	纳税人识别号串
            engCountry: data.engCountry, // 出生国(字典)
            // engCountryStr: data.engCountryStr, // 出生国
            engProvince: data.engProvince, // 出生省
            engCity: data.engCity, // 出生市
            // engArea: data.engArea,
            engAddress: data.engAddress, // 出生地址
            livingCountry: data.livingCountry, // 现居国(字典)
            livingCountryStr: data.livingCountryStr, // 现居国
            livingProvince: data.livingProvince, // 现居省
            livingCity: data.livingCity, //	现居市
            livingArea: data.livingArea,
            livingAddressEn: data.livingAddressEn, // 现居地址（英）
            taxResidentData: data.taxResidentData,
            taxResidentCountry: data.taxResidentData[0].taxResidentCountry,
            taxpayerIdentificationNumbe:
              data.taxResidentData[0].taxpayerIdentificationNumbe,
            noIdentitynoReason: data.taxResidentData[0].noIdentitynoReason,
            nrfaNoIdentitynoRemark:
              data.taxResidentData[0].nrfaNoIdentitynoRemark
          };
          this.$emit('getdefault', 'taxResidentPerson', {
            value: defaultData
          });
        })
        .catch((e) => {
          _hvueToast({
            mes: e
          });
        });
    },
    _getDictData() {
      getDictData({
        dictType: DICT_TYPE.TAX_RESIDENT_PERSON
      })
        .then(({ data }) => {
          this.selectBox = data[DICT_TYPE.TAX_RESIDENT_PERSON];
          return getDictData({
            dictType: DICT_TYPE.NO_IDENTITYNO_REASON
          });
        })
        .then(({ data }) => {
          const list = data[DICT_TYPE.NO_IDENTITYNO_REASON];
          this.noIdentitynoReasonData = list.map((d) => {
            d.label = d.dictLabel;
            d.value = d.dictValue;
            return d;
          });
          return getDictData({
            dictType: DICT_TYPE.NATIONALITY
          });
        })
        .then(({ data }) => {
          const list = data[DICT_TYPE.NATIONALITY];
          list.map((d, i) => {
            if (d.dictLabel.includes('中国')) {
              list.unshift(list.splice(i, 1)[0]);
            }
            d.label = d.dictLabel + `（${d.dictValue}）`;
            d.value = d.dictValue;
            return d;
          });
          this.nationalityData = [...list];
          return this.getTaxResInfo();
        })
        .catch((e) => {
          _hvueToast({ mes: e });
        });
    },
    addTaxResView() {
      this.taxResViewBox.push({ ...this.taxResidentMap });
    },
    removeTaxResView(i) {
      this.taxResViewBox.splice(i, 1);
    },
    showPicker(type) {
      if (type === 'engCountry') {
        this.showEngCountrySelect = true;
      } else {
        this.showLivingCountrySelect = true;
      }
    },
    openAreaSelect(key) {
      // if (this.areaSelKey !== key) {
      //   this.areaSelVal = '';
      // }
      // this.areaSelKey = `${key}`;
      this.showCitySelect = true;
    },
    areaChangeCallback(data) {
      this.areaSelVal = data;
    },

    areaCallback(data) {
      console.log(data);
      this.showCitySelect = false;
      if (!data) return;
      if (data.length === 1) {
        this.areaSelValText = data[0].text;
        this.engProvince = data[0].value;
        return;
      }
      /* const [{ text: prov }, { text: city }, { text: area }] = data;
      const [{ value: provVal }, { value: cityVal }, { value: areaVal }] = data;
      this.areaSelValText = prov + city + area; */
      const [{ text: prov }, { text: city } = '', { text: area } = ''] = data;
       const [{ value: provVal }, { value: cityVal } = '', { value: areaVal } = ''] = data;
      this.areaSelValText = Array.from(new Set([prov, city, area])).join('');
      this.livingProvince = provVal;
      this.livingCity = cityVal;
      this.livingArea = areaVal;
      // const [{ text: prov }, { text: city }, { text: area }] = data;
      // switch (this.areaSelKey) {
      //   case this.areaSelKeyMap.birthplace:
      //     this.engProvince = prov;
      //     this.engCity = city + area;
      //     this.areaSelValText = prov + city + area;
      //     break;
      //   case this.areaSelKeyMap.livePlace:
      //     this.livingProvince = prov;
      //     this.livingCity = city + area;
      //     this.areaSelValText = prov + city + area;
      //     break;
      // }
    },
    engCountryCallback({ label, value }) {
      this.showEngCountrySelect = false;
      if (this.engCountryStr !== label) {
        this.engProvince = '';
        this.engCity = '';
      }
      this.engCountry = value;
      this.engCountryStr = label;
    },
    livingCountryCallback({ label, value }) {
      this.showLivingCountrySelect = false;
      if (this.livingCountryStr !== label) {
        this.livingProvince = '';
        this.livingCity = '';
      }
      this.livingCountry = value;
      this.livingCountryStr = label;
    },
    openTaxResCountry(i) {
      this.taxResViewIndex = i;
      this.taxResViewBox[this.taxResViewIndex].showTaxResViewSelect = true;
    },
    taxResViewSelectCallback({ label }) {
      this.taxResViewBox[this.taxResViewIndex].showTaxResViewSelect = false;
      this.taxResViewBox[this.taxResViewIndex].taxResidentCountryStr = label;
    },
    taxResNumPickerCallback() {
      const flag = this.taxResViewBox[this.taxResViewIndex].taxResNumFlag;
      if (flag === '是') {
        this.taxResViewBox[this.taxResViewIndex].noIdentitynoReasonLabel = '';
        this.taxResViewBox[this.taxResViewIndex].noIdentitynoReason = '';
        this.taxResViewBox[this.taxResViewIndex].nrfaNoIdentitynoRemark = '';
      } else {
        if (
          this.taxResViewBox[this.taxResViewIndex]
            .taxpayerIdentificationNumbe !== ''
        ) {
          this.$TAlert({
            title: '温馨提示',
            tips: '是否需要删除已填写的纳税人识别号?点击确认将为您删除',
            hasCancel: true,
            confirmBtn: '确认',
            confirm: () => {
              this.taxResViewBox[
                this.taxResViewIndex
              ].taxpayerIdentificationNumbe = '';
            },
            cancel: () => {
              this.taxResViewBox[this.taxResViewIndex].taxResNumFlag = '是';
            }
          });
        } else {
          this.taxResViewBox[this.taxResViewIndex].taxpayerIdentificationNumbe =
            '';
        }
      }
      this.taxResViewBox[this.taxResViewIndex].showTaxResNumPicker = false;
    },
    noIdReasonPickerCallback({ label, value }) {
      console.log(value);
      if (value !== '2') {
        // this.taxResViewBox[this.taxResViewIndex].nrfaNoIdentitynoRemark = '';
      }
      this.taxResViewBox[this.taxResViewIndex].showNoIdReasonPicker = false;
      this.taxResViewBox[this.taxResViewIndex].noIdentitynoReasonLabel = label;
    },
    checkInput() {
      let errorMessage;
      const checkRule = [
        {
          name: '姓(英文/拼音)',
          key: 'clientSurname'
          // regExp: /^[a-zA-Z]{0,18}$/i
        },
        {
          name: '名(英文/拼音)',
          key: 'personalName'
          // regExp: /^[a-zA-Z]{0,18}$/i
        },
        // {
        //   name: '出生日期',
        //   key: 'birthday',
        //   regExp: /\d{4}-\d{2}-\d{2}/
        // },
        {
          name: '出生地国家',
          key: 'engCountry',
          method: () => {
            if (this.engCountryStr === '中国') {
              if (this.engProvince === '' || this.engCity === '') {
                return '请选择出生地区';
              }
            }
            return '';
          }
        },
        {
          name: '出生地详细地址',
          key: 'engAddress'
          // regExp: /^[a-zA-Z0-9]{0,64}$/i
        },
        {
          name: '现居地国家',
          key: 'livingCountry',
          method: () => {
            if (this.livingCountryStr === '中国') {
              if (this.livingProvince === '' || this.livingCity === '') {
                return '请选择现居地区';
              }
            }
            return '';
          }
        },
        {
          name: '现居地详细地址',
          key: 'livingAddressEn'
          // regExp: /^[a-zA-Z0-9]{0,64}$/i
        },
        {
          name: '纳税信息',
          method: this.checkTaxResInfo
        }
      ];
      if (this.selectIndex === 0) {
        this.submitForm();
      } else {
        for (const { key, name, regExp, method } of checkRule) {
          const value = this[key] && this[key].replace(/\s+/g, '');
          if (value === '') {
            errorMessage = `${name}不能为空`;
            break;
          } else if (regExp && !regExp.test(value)) {
            errorMessage = `${name}格式不正确`;
            break;
          } else if (method) {
            const res = method();
            if (res !== '') {
              errorMessage = res;
              break;
            }
          }
        }
        if (
          this.engAddress.replace(/\s+/g, '').replace(/[\u4e00-\u9fa5]/g, 'xx').length <
            5 ||
          this.engAddress.replace(/\s+/g, '').replace(/[\u4e00-\u9fa5]/g, 'xx').length > 255
        ) {
          errorMessage = '出生地详细地址格式不正确';
        }
        if (
          this.livingAddressEn
            .replace(/\s+/g, '')
            .replace(/[\u4e00-\u9fa5]/g, 'xx').length < 5 ||
          this.livingAddressEn
            .replace(/\s+/g, '')
            .replace(/[\u4e00-\u9fa5]/g, 'xx').length > 255
        ) {
          errorMessage = '现居地详细地址格式不正确';
        }
        if (errorMessage) {
          _hvueToast({ mes: errorMessage });
        } else {
          this.submitForm();
        }
      }
    },
    checkTaxResInfo() {
      let errorMessage;
      console.log(this.taxResViewBox);
      for (const {
        taxResidentCountry,
        taxResNumFlag,
        taxpayerIdentificationNumbe,
        noIdentitynoReason,
        nrfaNoIdentitynoRemark
      } of this.taxResViewBox) {
        if (taxResidentCountry === '') {
          errorMessage = '税收居民国(地区）不能为空';
          break;
        } else if (taxResNumFlag === '') {
          errorMessage = '请选择是否有纳税人识别号';
          break;
        } else if (
          taxResNumFlag === '是' &&
          taxpayerIdentificationNumbe === ''
        ) {
          errorMessage = '纳税人识别号不能为空';
          break;
        } else if (taxResNumFlag === '否') {
          if (noIdentitynoReason === '' || noIdentitynoReason === undefined) {
            errorMessage = '请选择无纳税人识别号原因';
            break;
          } else if (
            noIdentitynoReason === '2' &&
            (nrfaNoIdentitynoRemark === '' ||
              nrfaNoIdentitynoRemark === undefined)
          ) {
            errorMessage = '未取得纳税人识别号原因不能为空';
            break;
          }
        }
      }
      return errorMessage;
    },
    submitForm() {
      let taxResidentData = [];
      const {
        selectBox,
        selectIndex,
        clientSurname,
        personalName,
        // birthday,
        engCountry,
        engProvince,
        engCity,
        engArea,
        engAddress,
        livingCountry,
        livingProvince,
        livingCity,
        livingArea,
        livingAddressEn
      } = this;
      console.log(this);
      let nextParam = {
        taxResidentPerson: selectBox[selectIndex].dictValue
      };
      console.log(this.taxResViewBox);
      if (this.selectIndex !== 0) {
        taxResidentData = this.taxResViewBox.map(
          ({
            taxResidentCountry,
            taxpayerIdentificationNumbe,
            noIdentitynoReason,
            nrfaNoIdentitynoRemark
          }) => {
            return {
              taxResidentCountry,
              taxpayerIdentificationNumbe: taxpayerIdentificationNumbe.trim(),
              noIdentitynoReason:
                taxpayerIdentificationNumbe.trim() === ''
                  ? noIdentitynoReason
                  : '',
              nrfaNoIdentitynoRemark:
                noIdentitynoReason === '1' ? '' : nrfaNoIdentitynoRemark
            };
          }
        );
        try {
          taxResidentData = JSON.stringify(taxResidentData);
        } catch (mes) {
          _hvueToast({ mes });
          return;
        }
        nextParam = {
          ...nextParam,
          clientSurname,
          personalName,
          // birthday,
          engCountry,
          engProvince,
          engCity,
          engAddress,
          livingArea,
          livingCountry,
          livingProvince,
          livingCity,
          livingAddressEn,
          taxResidentData,
          taxResidentCountry: JSON.parse(taxResidentData)[0].taxResidentCountry,
          taxpayerIdentificationNumbe:
            JSON.parse(taxResidentData)[0].taxpayerIdentificationNumbe,
          noIdentitynoReason: JSON.parse(taxResidentData)[0].noIdentitynoReason,
          nrfaNoIdentitynoRemark:
            JSON.parse(taxResidentData)[0].nrfaNoIdentitynoRemark
        };
      }
      console.log(nextParam);
      let chnFlag = false;
      for (const {
        taxResidentCountry,
        taxResNumFlag,
        taxpayerIdentificationNumbe,
        noIdentitynoReason,
        nrfaNoIdentitynoRemark
      } of this.taxResViewBox) {
        if (taxResidentCountry === 'CHN') {
          chnFlag = true;
        }
      }
      if (this.selectIndex !== 0) {
        if (chnFlag) {
          this.$TAlert({
            title: '温馨提示',
            tips: '因您申报的税收居民身份不是仅为中国税收居民，请申报中国大陆以外的税收居民国家/地区的纳税信息。',
            confirmBtn: '确定'
          });
          return;
        }
        this.$TAlert({
          title: '温馨提示',
          tips: '本人确认上述信息的真实、准确和完整，且当这些信息发生变更时，将在30日内通知贵机构，否则本人承担由此造成的不利后果。',
          confirmBtn: '确定',
          hasCancel: true,
          cancelBtn: '取消',
          confirm: () => {
            this.$emit('result', 'taxResidentPerson', {
              value: { ...nextParam }
            });
          }
        });
      } else {
        this.$TAlert({
          title: '温馨提示',
          tips: '本人确认上述信息的真实、准确和完整，且当这些信息发生变更时，将在30日内通知贵机构，否则本人承担由此造成的不利后果。',
          confirmBtn: '确定',
          hasCancel: true,
          cancelBtn: '取消',
          confirm: () => {
            this.$emit('result', 'taxResidentPerson', {
              value: { ...nextParam }
            });
          }
        });
      }
      // this.showPage = false;
    }
  }
};
</script>

<style scoped>
.form_tit_right .input_text.text .t1:focus,
.form_tit_right .input_text.text .tarea1:focus,
.form_tit_right .input_text.text .tarea1.focus {
  text-align: right;
  padding-right: 0;
  padding-left: 0.88rem;
}
</style>
