<template>
  <section
    v-show="showPage"
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <t-header @back="back" title="邮政编码"></t-header>
    <article class="content">
      <div class="upload_infobox">
        <div class="input_form">
          <div class="input_text text">
            <span class="tit active">邮政编码</span>
            <input
              v-model="zipcode"
              class="t1"
              type="text"
              maxlength="6"
              placeholder="请输入邮政编码"
            />
          </div>
        </div>
      </div>
      <div class="ce_btn mt20">
        <a class="p_button" @click="submit">提交</a>
      </div>
    </article>
  </section>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'ChangeZipcode',
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {
    defaultValue: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      showPage: false,
      message: '',
      zipcode: ''
    };
  },
  watch: {
    defaultValue(val) {
      if (val) {
        this.zipcode = this.defaultValue.zipcode;
      }
    },
    showPage(val) {
      if (val) {
        this.zipcode = this.defaultValue.zipcode;
      }
    }
  },
  methods: {
    show() {
      this.showPage = true;
    },

    back() {
      // this.showPage = false;
      // if ($hvue.platform !== '0') {
      //   this.$router.replace({
      //     name: 'clientInfoModify'
      //   });
      // } else {
        this.eventMessage(this, EVENT_NAME.PREV_FLOW);
      // }
      // this.$router.replace({
      //   name: 'clientInfoModify'
      // });
    },

    submit() {
      let reg = /^[1-9][0-9]{5}$/;
      // 校验邮编格式是否正确
      if (!reg.test(this.zipcode)) {
        _hvueToast({
          mes: '邮政编码格式不正确'
        });
        return false;
      }
      this.$emit('result', 'zipcode', { value: this.zipcode });
      this.showPage = false;
    }
  }
};
</script>

<style></style>
