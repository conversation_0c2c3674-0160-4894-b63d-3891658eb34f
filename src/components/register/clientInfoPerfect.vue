<template>
  <fragment v-show="!loading">
    <div class="com_title">
      <h5>基本信息</h5>
    </div>
    <div class="com_box">
      <van-form ref="formBox">
        <van-field
          v-show="displayView.mobileTel"
          :value="form.mobileTel | formatMobileNo"
          label="手机号"
          placeholder="请完善"
          right-icon="arrow"
          input-align="right"
          type="textarea"
          rows="1"
          autosize
          readonly
          error-message-align="right"
          :rules="[{ validator: validatorMobileTel, message: validatorMsg }]"
          @click="selectInput('mobileTel')"
        />
        <van-field
          v-show="displayView.address"
          :value="form.address"
          type="textarea"
          label="常住地址"
          placeholder="请完善"
          right-icon="arrow"
          input-align="right"
          rows="1"
          autosize
          readonly
          error-message-align="right"
          :rules="[{ validator: validatorAddress, message: validatorMsg }]"
          @click="selectInput('address')"
        />
        <van-field
          v-show="displayView.zipcode"
          :value="form.zipcode"
          label="邮政编码"
          placeholder="请完善"
          right-icon="arrow"
          input-align="right"
          type="textarea"
          rows="1"
          autosize
          readonly
          error-message-align="right"
          :rules="[{ validator: validatorZipcode, message: validatorMsg }]"
          @click="selectInput('zipcode')"
        />
        <van-field
          v-show="displayView.professionCode"
          :value="format(form.professionCode, 'professionCodeOptions')"
          label="职业"
          placeholder="请完善"
          right-icon="arrow"
          input-align="right"
          type="textarea"
          rows="1"
          autosize
          readonly
          error-message-align="right"
          :rules="[
            {
              validator: validatorProfessionCode,
              message: validatorProfCodeMsg
            }
          ]"
          @click="showProfessionCode = true"
        />
      </van-form>
    </div>
    <van-popup v-model="showProfessionCode" round position="bottom">
      <div class="layer_tit">
        <h3>请选择</h3>
        <a class="close" @click="showProfessionCode = false"></a>
      </div>
      <div class="layer_cont">
        <ul class="select_list">
          <li
            v-for="item in professionCodeOptions"
            v-show="item.show"
            :key="item.name"
            :class="{ active: form.professionCode === item.name }"
            @click="onConfirmProfessionCode(item)"
          >
            <span>{{ item.label }}</span>
          </li>
        </ul>
      </div>
    </van-popup>
    <change-mobile
      ref="mobileTel"
      :old-mobile="requestData.mobileTel"
      :default-value="form"
      @result="componentResult"
    />
    <change-address
      ref="address"
      :default-value="form"
      @result="componentResult"
    />
    <change-zipcode
      ref="zipcode"
      :default-value="form"
      @result="componentResult"
    />
  </fragment>
</template>

<script>
import changeMobile from '@/components/common/changeMobile.vue';
import changeAddress from '@/components/common/changeAddress.vue';
import changeZipcode from '@/components/common/changeZipcode.vue';
import {
  clientInfoQry,
  businessStrategyCheck,
  addClientCritMark,
  critMarkQuery,
  mobileTelReport
} from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import { MARK_TYPE } from '@/common/enumeration';
import { queryDictProps } from '@/common/util';
import { rules } from '@/common/rule';

export default {
  name: 'ClientInfoPerfect',
  inject: ['eventMessage', 'setPropsByForm'],
  components: {
    changeMobile,
    changeAddress,
    changeZipcode
  },
  props: {
    changedClientInfoKey: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      loading: true,
      requestData: {}, //请求出来的defaule数据
      displayView: {
        mobileTel: false,
        address: false,
        zipcode: false,
        professionCode: false
      },
      form: {
        mobileTel: '',
        address: '',
        zipcode: '',
        professionCode: ''
      },
      professionCodeOptions: [], //职业下拉框
      showProfessionCode: false,
      doubtfulAddress: false, // 地址是否存疑
      profCodeError: false // 职业与年龄不符标识
    };
  },
  computed: {},
  created() {
    this.$store.commit('flow/setWhiteBg', true);
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
  },
  mounted() {
    this.$nextTick(() => {
      this.renderingView();
    });
  },
  destroyed() {},
  methods: {
    renderingView() {
      clientInfoQry({})
        .then(({ data }) => {
          this.requestData = data;
          this.form.mobileTel = this.requestData.mobileTel;
          this.form.address = this.requestData.address;
          this.form.zipcode = this.requestData.zipcode;
          this.form.professionCode = this.requestData.professionCode;
          if (this.form.professionCode === '99') this.form.professionCode = '';
          return this.getProfessionCodeOpt({});
        })
        .then(() => {
          return businessStrategyCheck({
            strategyNo: 'wt_often_addr_check',
            flowToken: sessionStorage.getItem('TKFlowToken')
          });
        })
        .then(({ data }) => {
          if (data.strategyResult === '1') {
            this.checkFormInput(true);
          } else {
            this.doubtfulAddress = true;
            this.checkFormInput();
          }
          this.displayView.mobileTel = !this.validatorMobileTel();
          this.displayView.address = !this.validatorAddress();
          this.displayView.zipcode =
            !this.validatorAddress() || !this.validatorZipcode();
          this.displayView.professionCode = !this.validatorProfessionCode();
        })
        .catch((err) => {
          this.$TAlert({
            title: '温馨提示',
            tips: err
          });
        });
    },
    componentResult(key, data) {
      if (key === 'address') {
        this.doubtfulAddress = false;
        Object.keys(data.value).forEach((key) => {
          this.form[key] = data.value[key].trim();
        });
      } else {
        this.form[key] = data.value.trim();
      }
      this.checkFormInput();
    },
    getProfessionCodeOpt() {
      return new Promise((resolve, reject) => {
        queryDictProps('bc.common.professionCode')
          .then((res) => {
            this.professionCodeOptions = res.map((item) => {
              let show = true;
              if (item.dictValue === '99') {
                show = false;
              }
              return {
                label: item.dictLabel.trim(),
                value: item.dictValue,
                name: item.dictValue,
                show: show
              };
            });
            this.profCodeError = this.professionCodeOptions.some(
              ({ value }) =>
                this.form.professionCode === value &&
                this.profCodeRule(value) !== true
            );
            this.professionCodeOptions = this.professionCodeOptions.filter(
              ({ value }) => this.profCodeRule(value) !== false
            );
            resolve();
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    onConfirmProfessionCode(item) {
      const ruleData = this.profCodeRule(item.value);
      if (ruleData !== true) {
        // 职业选学生，学历为本，硕，博
        this.$TAlert({
          tips: '您选择的职业可能与您的年龄不符，请您确认是否属实？',
          confirmBtn: '确认',
          cancelBtn: '返回修改',
          hasCancel: true,
          cancel: () => {
            this.form.professionCode = '';
            this.showProfessionCode = true;
            this.checkFormInput();
          },
          confirm: () => {
            addClientCritMark({
              flowToken: sessionStorage.getItem('TKFlowToken'),
              markType: MARK_TYPE.PROFESSION_CODE_CONFIRM,
              markContent: '您选择的职业可能与您的年龄不符，请您确认是否属实？',
              confirmFlag: '1'
            });
            this.form.professionCode = item.value;
            this.showProfessionCode = false;
            this.profCodeError = false;
            this.checkFormInput();
          }
        });
        return;
      }
      this.profCodeError = false;
      this.form.professionCode = item.value;
      this.showProfessionCode = false;
      this.checkFormInput();
    },
    //职业互斥规则
    profCodeRule(profCode = '') {
      const { clientAge, clientGender, degreeCode } = this.requestData;
      if (profCode === '') {
        profCode = this.form.professionCode;
      }
      if (clientAge <= 44 && clientAge >= 30) {
        if (profCode === '9') {
          if (degreeCode === '1' || degreeCode === '2' || degreeCode === '3') {
            return {
              msg: '您选择的职业可能与您的年龄不符，请您确认是否属实？'
            };
          }
          return false;
        }
      }
      if (clientAge >= 45 && profCode === '9') {
        return false;
      }
      if (clientAge <= 22 && ['2', 'A2', 'A3', '11'].includes(profCode)) {
        return false;
      }
      if (
        (clientAge >= 60 && clientGender < 70 && clientGender === '1') ||
        (clientAge >= 65 && clientGender < 70 && clientGender === '0')
      ) {
        return ['2', 'A2', 'A3', 'Aa', 'Ab', 'Ac', '8'].includes(profCode)
          ? {
              msg: '您选择的职业可能与您的年龄不符，请您确认是否属实？'
            }
          : true;
      }
      return true;
    },
    selectInput(propType) {
      this.$refs[propType].show();
    },
    validatorMsg(value) {
      if (value !== '') {
        return '格式不正确，请修改';
      } else {
        return '';
      }
    },
    validatorMobileTel() {
      return /1[3-9][\d]{9}/.test(this.form.mobileTel);
    },
    validatorProfCodeMsg(val) {
      if (this.profCodeError) {
        return '职业与您的年龄不符，请您确认是否属实？';
      } else if (val !== '') {
        return '格式不正确，请修改';
      } else {
        return '';
      }
    },
    validatorProfessionCode() {
      const checkFlag = this.professionCodeOptions.some(
        ({ value }) => this.form.professionCode === value
      );
      if (!checkFlag || this.profCodeError) {
        return false;
      } else {
        return true;
      }
    },
    validatorZipcode() {
      let reg = /[0-9]{6}$/;
      return reg.test(this.form.zipcode);
    },
    validatorAddress() {
      const addressRules = rules.client_address;
      if (
        !this.form.address ||
        this.doubtfulAddress ||
        !addressRules.validate(this.form.address)
      ) {
        return false;
      } else {
        return true;
      }
    },
    format(val, key) {
      if (
        this[key].filter((item) => {
          return item.value === val;
        }).length > 0
      ) {
        return this[key].filter((item) => {
          return item.value === val;
        })[0].label;
      } else {
        return '';
      }
    },
    checkFormInput(autoNext) {
      let changedClientInfoKey = '0';
      let formParams = {
        mobileTel: this.requestData.mobileTel,
        address: this.requestData.address,
        zipcode: this.requestData.zipcode,
        professionCode: this.requestData.professionCode
      };
      this.$refs.formBox
        .validate()
        .then((a) => {
          let excludeList = [
            'mobileTel',
            'address',
            'zipcode',
            'professionCode'
          ];
          for (const key in this.form) {
            if (
              excludeList.includes(key) &&
              this.form[key] !== this.requestData[key]
            ) {
              changedClientInfoKey = '1';
            }
          }
          if (changedClientInfoKey === '1') {
            if (formParams.mobileTel !== this.form.mobileTel) {
              formParams.newMobileTel = this.form.mobileTel;
            }
            if (formParams.address !== this.form.address) {
              formParams.newAddress = this.form.address;
            }
            if (formParams.zipcode !== this.form.zipcode) {
              formParams.newZipcode = this.form.zipcode;
            }
            if (formParams.professionCode !== this.form.professionCode) {
              formParams.newProfessionCode = this.form.professionCode;
            }
          }
          formParams.changedClientInfoKey =
            changedClientInfoKey === '0'
              ? this.changedClientInfoKey
              : changedClientInfoKey;
          if (autoNext) {
            this.eventMessage(this, EVENT_NAME.NEXT_STEP, formParams);
            return;
          } else {
            const _this = this;
            _this.eventMessage(_this, EVENT_NAME.NEXT_BTN, {
              btnStatus: 2,
              data: () => {
                if (!formParams.newMobileTel) {
                  _this.eventMessage(_this, EVENT_NAME.NEXT_STEP, formParams);
                  return;
                }
                mobileTelReport({
                  mobileTel: formParams.newMobileTel
                })
                  .then(({ data }) => {
                    _this.eventMessage(_this, EVENT_NAME.NEXT_STEP, formParams);
                  })
                  .catch((err) => {
                    this.$TAlert({
                      tips: err
                    });
                  });
              }
            });
          }
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
          console.error(err);
        });
    }
  }
};
</script>
