<template>
  <article class="content">
    <ul class="com_infolist">
      <li>
        <span class="tit">信用资金账户</span>
        <p class="txt_left">{{ creditFundAccount }}</p>
      </li>
    </ul>
    <div class="com_title">
      <h5>请输入信用交易密码进行校验</h5>
    </div>
    <div class="com_box">
      <div class="input_form spel">
        <div class="input_text pword">
          <!-- <input
            v-model="password"
            class="t1"
            type="password"
            placeholder="请输入6位交易数字密码"
            value=""
          /> -->
          <h-keypanel
            v-model="password"
            type="tel2"
            :mask="true"
            :is-head-icon="true"
            extra-parent-el=".hui-flexview"
            placeholder="请输入交易密码"
          >
            <div slot="head" class="safe-head">
              <img src="@/assets/images/logo.png" alt="" />
              <span>佣金宝安全输入</span>
            </div>
          </h-keypanel>
        </div>
      </div>
    </div>
  </article>
</template>

<script>
import { creditAndFundPwdCheck, creditPwdCheck } from '@/service/service';
import { getPwdEncryption } from '@/common/util';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  inject: ['tkFlowInfo', 'eventMessage'],
  name: 'CreditClientPwdCheck',
  data() {
    return {
      password: '',
      creditFundAccount: '', //信用资金账户
      ifExistCreditAccount: '0',
      specialFlag: '0'
    };
  },
  mounted() {
    this.renderingView();
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
      display: true,
      btnStatus: 2,
      data: () => {
        console.log(this.password);
        // this.$emit('change', {
        //   credit_t_pwd_same_flag: this.specialFlag
        // });
        if (!this.password) {
          _hvueToast({
            mes: '请输入密码'
          });
          return;
        }
        creditPwdCheck({
          flowToken: sessionStorage.getItem('TKFlowToken'),
          fundPassword: 'encrypt:' + getPwdEncryption(this.password)
        })
          .then((res) => {
            this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
              credit_t_pwd_same_flag: '1'
            });
          })
          .catch((err) => {
            this.$TAlert({
              title: '密码不正确',
              tips: '请重新输入密码',
              confirm: () => {}
            });
          });
      }
    });
  },
  methods: {
    renderingView() {
      creditAndFundPwdCheck()
        .then((res) => {
          // 0一致 1不一致
          this.creditFundAccount = res.data.fundAccount;
          // this.ifExistCreditAccount = res.data.ifExistCreditAccount;
          // this.specialFlag = res.data.specialFlag;
          // if (this.specialFlag === '0') {
          //   this.eventMessage(this, EVENT_NAME.NEXT_STEP);
          // }
        })
        .catch();
    }
  }
};
</script>

<style></style>
