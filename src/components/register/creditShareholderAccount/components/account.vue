<template>
  <article class="content">
    <ul class="com_infolist spel">
      <li>
        <span class="tit">信用资金账户</span>
        <p>{{ fundAccount }}</p>
      </li>
      <li>
        <span class="tit">一码通账户</span>
        <p>{{ acodeAccount || '无' }}</p>
      </li>
    </ul>
    <ul class="market_ctlist">
      <li v-for="(item, i) in accountList" :key="i">
        <div class="base">
          <div class="bg"><img src="@/assets/images/bg_shanghai.png" /></div>
          <h5>{{ exchangeTypeLabel(item.exchangeType) }}</h5>
          <!-- 开通账户按钮为显示 -->
          <template v-if="item.openAccountShow == 1">
            <p><span>未开通</span></p>
          </template>
          <!-- 开通账户按钮为不显示，联系客服按钮为显示 -->
          <template v-else-if="item.serviceCallShow == 1">
            <p><span style="color: #fa443a">账号异常</span></p>
          </template>
          <!-- 开通账户按钮为不显示，联系客服按钮为不显示 -->
          <template v-else>
            <!-- 展示账户及其对应的状态 -->
            <p>
              <span class="num">
                {{ item.creditStockAccount }}
                <!-- 当前账户如果没有状态则不展示状态 -->
                <template v-if="item.holderStatusDesc">
                  <em class="acct_s_tag"> {{ item.holderStatusDesc }} </em>
                </template>
              </span>
            </p>
          </template>
          <!-- 如果当前账户没有席位号则不展示席位号 -->
          <template v-if="item.seatNo">
            <span class="state"> 席位号：{{ item.seatNo }} </span>
          </template>
        </div>
        <!-- 当前开通账户按钮或联系客服按钮需要展示的时候才展示操作区间 -->
        <template v-if="item.openAccountShow == 1 || item.serviceCallShow == 1">
          <div class="opea">
            <!-- 开通账户按钮为显示 -->
            <template v-if="item.openAccountShow == 1">
              <a class="com_btn" @click="liberalAccountClick(item)">开通账户</a>
            </template>
            <!-- 开通账户按钮为不显示，联系客服按钮为显示 -->
            <template v-else-if="item.serviceCallShow == 1">
              <a class="com_btn" @click="toOther">联系客服</a>
            </template>
          </div>
        </template>
      </li>
    </ul>
  </article>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';

export default {
  inject: ['eventMessage'],
  props: {
    // 信用资金账号
    fundAccount: {
      type: String,
      default: ''
    },
    // 一码通账户
    acodeAccount: {
      type: String,
      default: ''
    },
    // 账户列表
    accountList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {};
  },
  computed: {},
  created() {},
  mounted() {},
  destroyed() {},
  methods: {
    // 深市/沪市描述转换
    exchangeTypeLabel(value) {
      return value == 1 ? '上海市场' : '深圳市场';
    },
    // 跳转至联系客服
    toOther() {
      const targetUrl =
        $hvue.customConfig.targetUrl +
        '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=onlineServiceForWeb';
      if ($hvue.platform == 0) {
        window.location.href = targetUrl;
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          params: {
            url: targetUrl,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        const res = $h.callMessageNative(reqParams);
      }
    },
    // 用户点击开通账户按钮
    liberalAccountClick(item) {
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        selected_accounts_data: JSON.stringify([
          {
            ...item,
            stockType: item.exchangeType == 1 ? '沪市信用账户' : '深市信用账户'
          }
        ])
      });
    }
  }
};
</script>
