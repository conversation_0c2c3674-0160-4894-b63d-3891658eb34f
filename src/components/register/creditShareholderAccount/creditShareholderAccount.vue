<template>
  <section class="main fixed" data-page="home" style="position: fixed">
    <t-header />
    <article v-if="isLoading" class="content" />
    <template v-else-if="isGuide">
      <!-- 账户列表 -->
      <Account
        :fundAccount="fundAccount"
        :acodeAccount="acodeAccount"
        :accountList="accountList"
      />
      <footer class="footer">
        <div class="ce_btn">
          <a class="p_button border-2" @click="toBiztypeKHQRD">
            查看开户确认单
          </a>
        </div>
        <div class="foot_tipbox">交易日9: 00-16: 00期间支持查看和下载</div>
      </footer>
    </template>
    <div
      v-else-if="creditFundAccountExist === '0'"
      style="height: 100vh; background: #ffffff; text-align: center"
    >
      <div class="acct_nodata">
        <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
        <h5>
          您当前暂未开通信用账户，请前往开通信用账户，如有疑问可咨询客服95310。
        </h5>
      </div>
      <div class="bus_txtbox">
        <p>
          如何开通信用账户?
          <a class="link_right_arrow" @click="jumpBusiness('010174')"
            >前往开通</a
          >
        </p>
      </div>
    </div>
    <div
      v-else-if="creditBankAccountExist === '0'"
      style="height: 100vh; background: #ffffff; text-align: center"
    >
      <div class="acct_nodata">
        <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
        <h5>
          您当前暂未开通信用三方存管，请先开通信用三方存管后再进行业务办理，如有疑问可咨询客服95310。
        </h5>
      </div>
      <div class="bus_txtbox">
        <p>
          <a class="link_right_arrow" @click="jumpBusiness('010294')"
            >前往开通信用三方存管</a
          >
        </p>
      </div>
    </div>
    <div v-else style="height: 100vh; background: #ffffff; text-align: center">
      <div class="acct_nodata">
        <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
        <h5>
          您当前信用账户异常，暂无法进行业务办理，如有疑问可咨询客服95310。
        </h5>
      </div>
    </div>
  </section>
</template>

<script>
import { bkCreditStockAccListQry } from '@/service/service.js';
import { creditAccountAndBankQueryV2 } from '@/service/lrService';
import Account from './components/account.vue';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'CreditShareholderAccount',
  inject: ['eventMessage'],
  components: { Account },
  data() {
    return {
      // 加载页
      isLoading: true,
      // 是否展示引导页
      isGuide: false,
      // 信用账户
      fundAccount: '',
      // 一码通账户
      acodeAccount: '',
      // 账户列表
      accountList: [],
      creditFundAccountExist: '', //信用资金账户是否存在：1 存在；0 不存在
      creditFundAccountStatus: '', //信用资金账户状态：1 正常 0 异常
      creditBankAccountExist: '' //信用存管账户是否存在：1 存在；0 不存在
    };
  },
  computed: {},
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
  },
  mounted() {
    this.xycOpenCheck();
  },
  destroyed() {},
  methods: {
    xycOpenCheck() {
      creditAccountAndBankQueryV2({})
        .then(({ data = {}, code, msg }) => {
          if (code === 0) {
            console.log(data);
            const {
              creditFundAccountExist = '0', //信用资金账户是否存在：1 存在；0 不存在
              creditFundAccountStatus = '0', //信用资金账户状态：1 正常 0 异常
              creditBankAccountExist = '0' //信用存管账户是否存在：1 存在；0 不存在
            } = data;
            const checkArray = [
              creditFundAccountExist,
              creditFundAccountStatus,
              creditBankAccountExist
            ].some((a) => a !== '1');
            if (checkArray) {
              this.creditFundAccountExist = creditFundAccountExist;
              this.creditFundAccountStatus = creditFundAccountStatus;
              this.creditBankAccountExist = creditBankAccountExist;
              this.accountList = [];
              this.isGuide = false;
              this.isLoading = false;
            } else {
              this.init();
            }
          } else {
            return Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },
    jumpBusiness(bizType = '') {
      if (bizType === '') throw new Error('bizType 不能为空');
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, {
          bizType,
          initJumpMode: '0'
        });
      });
    },
    // 查询账信息内容
    async init() {
      try {
        const { code, data, msg } = await bkCreditStockAccListQry();
        if (code == 0) {
          // 返回有值展示账户列表页
          this.isGuide = data.fundAccount != '';
          this.fundAccount = data.fundAccount;
          this.acodeAccount = data.acodeAccount;
          this.accountList = [
            // 沪市股东账户
            data.stockAccountSh,
            // 深市股东账户
            data.stockAccountSz
          ];
          this.isLoading = false;
        } else {
          _hvueToast({ mes: msg });
        }
      } catch (e) {
        _hvueToast({ mes: e });
        // 返回有值展示账户列表页
        this.isGuide = false;
        this.fundAccount = '';
        this.acodeAccount = '';
        this.accountList = [];
        this.isLoading = false;
      }
    },
    // 查看客户开户单
    toBiztypeKHQRD() {
      import('@/common/flowMixin.js').then((a) => {
        a.initFlow.call(this, '010003', '0-3057');
      });
    },
    showGuide() {
      this.$router.push({
        name: 'creditAccountGuide'
      });
    }
  }
};
</script>
<style scoped>
.acct_nodata h5 {
  text-align: left;
  font-size: 0.15rem;
  color: #333333;
}
</style>
