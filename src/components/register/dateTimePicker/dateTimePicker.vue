<template>
  <fragment>
    <van-field
      v-model="fieldValue"
      clickable
      :label="$attrs.label"
      :placeholder="$attrs.placeholder"
      @click-input="showPicker = true"
      @focus="iptFocus"
    >
      <template v-if="hasLong === '1'" #button
        ><span
          class="icon_check long_span"
          :class="{ checked: longTimeChecked }"
          @click="selectLongTime"
          >长期</span
        ></template
      ></van-field
    >
    <van-popup v-model="showPicker" position="bottom"
      ><van-datetime-picker
        v-model="currentDate"
        type="date"
        title="选择年月日"
        :min-date="minDate"
        :max-date="maxDate"
        @cancel="showPicker = false"
        @confirm="confirm"
    /></van-popup>
  </fragment>
</template>

<script>
export default {
  name: 'DateTimePicker',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    hasLong: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      defaultValue: '',
      fieldValue: '',
      showPicker: false,
      longTimeChecked: false,
      currentDate: new Date(),
      minDate: new Date(1950, 0, 1),
      maxDate: new Date(3000, 12, 31)
    };
  },
  watch: {
    value: {
      handler(newVal) {
        if (this.defaultValue === '') {
          this.defaultValue = newVal;
        }
        this.fieldValue = newVal.replace(/^(\d{4})(\d{2})(\d{2})$/, '$1-$2-$3');
        console.log(this.fieldValue);
        this.currentDate = new Date(Date.parse(this.fieldValue));
      },
      deep: false,
      immediate: false
    }
  },
  methods: {
    iptFocus() {
      // 禁止弹出手机键盘
      document.activeElement.blur();
    },

    confirm(time) {
      this.showPicker = false;
      this.fieldValue = new Date(time).format('yyyy-MM-dd');
      this.$emit('change', this.fieldValue.replace(/-/g, ''));
    },

    selectLongTime() {
      this.longTimeChecked = !this.longTimeChecked;
      if (this.longTimeChecked === true) {
        this.fieldValue = '3000-12-31';
        this.$emit('change', this.fieldValue.replace(/-/g, ''));
      } else {
        this.fieldValue = this.defaultValue.replace(
          /^(\d{4})(\d{2})(\d{2})$/,
          '$1-$2-$3'
        );
        this.$emit('change', this.fieldValue.replace(/-/g, ''));
      }
    }
  }
};
</script>
