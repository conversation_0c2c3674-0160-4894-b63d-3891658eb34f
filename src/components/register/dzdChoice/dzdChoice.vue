<template>
  <section>
    <article class="content">
      <div class="com_box">
        <div class="input_form form_tit_right">
          <div class="input_text text" @click="showFundAccountCode = true">
            <span class="tit">账单类型</span>
            <div class="dropdown">
              {{ this.fundAccountTypeName }}
            </div>
          </div>
          <!-- 对账单类型下拉列表 -->
          <van-popup v-model="showFundAccountCode" round position="bottom">
            <div class="layer_tit">
              <h3>账单类型</h3>
              <a class="close" @click="showFundAccountCode = false"></a>
            </div>
            <div class="layer_cont">
              <ul class="select_list">
                <li
                  v-for="item in fundAccountTypeList"
                  :key="item.code"
                  :class="{ active: submitForm.dzd_type === item.code }"
                  @click="choiceDZDType(item)"
                >
                  <span>{{ item.name }}</span>
                </li>
              </ul>
            </div>
          </van-popup>
          <div
            class="input_text text"
            @click="showFundAccountListPopup(submitForm.dzd_type)"
          >
            <span class="tit">资金账号</span>
            <div
              class="t1 disabled"
              :class="{ dropdown: submitForm.dzd_type == 4 }"
            >
              {{ this.submitForm.dzd_fund_account }}
            </div>
          </div>
          <!-- 资金账号下拉列表 -->
          <van-popup v-model="showFundAccountList" round position="bottom">
            <div class="layer_tit">
              <h3>账单类型</h3>
              <a class="close" @click="showFundAccountList = false"></a>
            </div>
            <div class="layer_cont">
              <ul class="select_list">
                <li
                  v-for="item in fundAccountList"
                  :key="item.fundAccount"
                  :class="{
                    active: submitForm.dzd_fund_account === item.fundAccount
                  }"
                  @click="choiceFundAccount(item)"
                >
                  <span>{{ item.fundAccount }}</span>
                </li>
              </ul>
            </div>
          </van-popup>
          <div class="input_text text">
            <span class="tit">选择周期</span>
            <div class="input_ct">
              <div class="tag_selebox">
                <span
                  class="tag_item"
                  v-for="item in dateList"
                  :key="item.code"
                  :class="{ active: item.code == dateCode }"
                  @click="choiceDate(item.code)"
                  >{{ item.name }}</span
                >
                <span
                  class="tag_item"
                  :class="{
                    active: dateCode == 'startDate' || dateCode == 'endDate'
                  }"
                >
                  <span class="start_date" @click="choiceDate('startDate')">{{
                    tempStartDate
                  }}</span>
                  <van-popup
                    v-model="showStartDatePicker"
                    position="bottom"
                    @click-overlay="clickOverlay"
                    ><van-datetime-picker
                      :value="startCurrentDate"
                      type="date"
                      :min-date="startDateMinDate"
                      :max-date="startDateMaxDate"
                      @cancel="showStartDatePicker = false"
                      @confirm="startDateConfirm"
                      :columns-order="['year', 'month', 'day']"
                      :formatter="formatter"
                  /></van-popup>
                  -
                  <span class="end_date" @click="choiceDate('endDate')">{{
                    tempEndDate
                  }}</span>
                  <van-popup
                    v-model="showEndDatePicker"
                    position="bottom"
                    @click-overlay="clickOverlay"
                    ><van-datetime-picker
                      :value="endCurrentDate"
                      type="date"
                      :min-date="endDateMinDate"
                      :max-date="endDateMaxDate"
                      @cancel="showEndDatePicker = false"
                      @confirm="endDateConfirm"
                      :columns-order="['year', 'month', 'day']"
                      :formatter="formatter"
                  /></van-popup>
                </span>
              </div>
            </div>
          </div>
          <div class="input_text text">
            <span class="tit">展示完整证件号码</span>
            <div class="input_ct">
              <div class="switch">
                <input
                  type="checkbox"
                  id="IDNO"
                  @click="isShowAllInfo('IDNO')"
                />
                <div class="switch-inner">
                  <div class="switch-arrow"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="input_text text">
            <span class="tit">展示完整证券账号</span>
            <div class="input_ct">
              <div class="switch">
                <input
                  type="checkbox"
                  id="FUNDNO"
                  @click="isShowAllInfo('FUNDNO')"
                />
                <div class="switch-inner">
                  <div class="switch-arrow"></div>
                </div>
              </div>
            </div>
            <div class="sub_tipbox">
              <p>
                对账单涉及个人隐私，请根据需要选择是否展示完整身份证号和证券账号
              </p>
            </div>
          </div>
          <div class="input_text text">
            <span class="tit active">手机号</span>
            <div class="input_ct">
              <div class="t1_layout">
                <input
                  class="t1 disabled"
                  type="text"
                  disabled="disabled"
                  :value="this.formatMobile(this.submitForm.dzd_mobile)"
                />
                <a class="com_link" @click="goToPresonal">
                  {{ this.submitForm.dzd_mobile.trim() ? this.verifyPhone(this.submitForm.dzd_mobile) ? '修改' : '去修改' : '去完善' }}
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="tip_txtbox">
        <p>{{ getTitBox() }}</p>
      </div>
    </article>
  </section>
</template>

<script>
import {
  clientInfoQry,
  fundAccountListQry,
  dzdzdConfirmFormCheck
} from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import moment from 'moment';

export default {
  name: 'DzdChoice',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      bizType: '',
      showFundAccountCode: false,
      showFundAccountList: false,
      fundAccountTypeList: [],
      fundAccountList: [],
      dateList: [
        { code: 'threeMonths', name: '近三月' },
        { code: 'halfYear', name: '近半年' },
        { code: 'oneYear', name: '近一年' }
      ],
      dateCode: '',
      showStartDatePicker: false,
      showEndDatePicker: false,
      startDateMinDate: new Date(2022, 0, 1),
      startDateMaxDate: new Date(moment().subtract(1, 'day')),
      endDateMinDate: new Date(2022, 0, 1),
      endDateMaxDate: new Date(moment().subtract(1, 'day')),
      tempStartDate: '年/月/日',
      tempEndDate: '年/月/日',
      startCurrentDate: '',
      endCurrentDate: '',
      fundAccountTypeName: '普通账户对账单',
      submitForm: {
        dzd_fund_account: '',
        dzd_type: '',
        dzd_mobile: '',
        dzd_start_date: '',
        dzd_end_date: '',
        show_idcard: 0,
        show_stock_account: 0,
        client_id: ''
      },
      openDate:'' //客户开户时间
    };
  },
  watch: {
    'submitForm.dzd_mobile'(newMobile, oldMobile) {
      // 设置下一步
      this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
        text: '下一步',
        display: true,
        btnStatus: this.verifyPhone(newMobile) ? 2 : 0,
        data: () => {
          this.toNext();
        }
      });
    }
  },
  created() {
    // 忽略此行代码，测试从新合并release用
    this.pageInit();
    // 设置下一步
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
      text: '下一步',
      display: true,
      btnStatus: this.verifyPhone(this.submitForm.dzd_mobile) ? 2 : 0,
      data: () => {
        this.toNext();
      }
    });
    window.viewShowCallBack = this.viewShowCallBack;
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  methods: {
    pageInit() {
      const { inProperty } = this.tkFlowInfo();
      this.bizType = inProperty.bizType;
      // 查询客户基本信息
      clientInfoQry().then((res) => {
        if (res.code == 0) {
          this.submitForm.dzd_mobile = res.data.mobileTel;
          this.submitForm.client_id = res.data.clientId;
          this.openDate = res.data.openDate
        }
      });
      // 查询客户所有账号
      fundAccountListQry().then((res) => {
        // 过滤状态异常账号
        let resFundAccountList = res.data.fundAccountList.filter(
          (item) => item.fundAccountStatus === '0'
        );
        this.fundAccountList = resFundAccountList;
        let haveOrdinaryAccount = resFundAccountList.some((item) => item.assetProp === '0');
        let haveCreditAccount = resFundAccountList.some((item) => item.assetProp === '7');
        let haveOptionAccount = resFundAccountList.some((item) => item.assetProp === 'B');
        if (haveOrdinaryAccount) {
          this.fundAccountTypeList.push({
            code: 1,
            name: '普通账户对账单',
            type: '0'
          });
        }
        if (haveCreditAccount) {
          this.fundAccountTypeList.push({
            code: 2,
            name: '信用账户对账单',
            type: '7'
          });
        }
        if (haveOptionAccount) {
          this.fundAccountTypeList.push({
            code: 3,
            name: '期权账户对账单',
            type: 'B'
          });
        }
        this.fundAccountTypeList.push({ code: 4, name: '银证转账流水' });
        this.submitForm.dzd_type = this.fundAccountTypeList[0].code;
        this.submitForm.dzd_fund_account = this.fundAccountList.find(
          (item) => item.mainFlag === '1'
        ).fundAccount;
      });
      this.choiceDate('threeMonths');
    },

    viewShowCallBack() {
      console.log('页面返回更新用户手机号');
      clientInfoQry().then((res) => {
        if (res.code == 0) {
          this.submitForm.dzd_mobile = res.data.mobileTel;
          this.submitForm.client_id = res.data.clientId;
          this.openDate = res.data.openDate
        }
      });
    },

    choiceDZDType(obj) {
      this.submitForm.dzd_type = obj.code;
      this.fundAccountTypeName = obj.name;
      if (obj.code == 4) {
        this.submitForm.dzd_fund_account = this.fundAccountList.find(
          (item) => item.assetProp === '0'
        ).fundAccount;
      } else {
        this.submitForm.dzd_fund_account = this.fundAccountList.find(
          (item) => item.assetProp === obj.type
        ).fundAccount;
      }
      this.showFundAccountCode = false;
    },

    showFundAccountListPopup(type) {
      if (type == 4) {
        this.showFundAccountList = true;
      }
    },

    choiceFundAccount(fundAccountObj) {
      this.submitForm.dzd_fund_account = fundAccountObj.fundAccount;
      this.showFundAccountList = false;
    },

    formatter(type, val) {
      if (type === 'year') {
        return val + '年';
      }
      if (type === 'month') {
        return val + '月';
      }
      if (type === 'day') {
        return val + '日';
      }
      return val;
    },

    formatMobile(phoneNum) {
      let mobile;
      if (phoneNum.length > 7) {
        mobile = phoneNum.substring(0, 3) + '****' + phoneNum.slice(-4);
      } else {
        mobile = phoneNum;
      }
      return mobile;
    },

    choiceDate(dateType) {
      this.tempDateCode = this.dateCode;
      this.dateCode = dateType;
      if (dateType == 'startDate') {
        this.showStartDatePicker = true;
        return;
      }
      if (dateType == 'endDate') {
        this.showEndDatePicker = true;
        return;
      }
      if (dateType == 'threeMonths') {
        this.submitForm.dzd_start_date = this.getRecentMonth(3);
      }
      if (dateType == 'halfYear') {
        this.submitForm.dzd_start_date = this.getRecentMonth(6);
      }
      if (dateType == 'oneYear') {
        this.submitForm.dzd_start_date = this.getRecentMonth(12);
      }
      this.submitForm.dzd_end_date = moment(new Date())
        .subtract(1, 'day')
        .format('YYYYMMDD');
      this.tempStartDate = '年/月/日';
      this.tempEndDate = '年/月/日';
      this.startCurrentDate = new Date('2022/01/01');
      this.endCurrentDate = new Date('2022/01/01');
      this.startDateMaxDate = new Date(moment().subtract(1, 'day'));
      this.endDateMinDate = new Date(2022, 0, 1);
    },

    clickOverlay() {
      console.log('点击遮罩层');
      this.dateCode = this.tempDateCode;
    },

    startDateConfirm(time) {
      this.showStartDatePicker = false;
      this.endDateMinDate = new Date(time);
      this.tempStartDate = new Date(time).format('yyyy-MM-dd');
      this.submitForm.dzd_start_date = new Date(time).format('yyyyMMdd');
    },

    endDateConfirm(time) {
      this.showEndDatePicker = false;
      this.startDateMaxDate = new Date(time);
      this.tempEndDate = new Date(time).format('yyyy-MM-dd');
      this.submitForm.dzd_end_date = new Date(time).format('yyyyMMdd');
    },

    isShowAllInfo(type) {
      let isChoice = document.getElementById(type).checked;
      if (type == 'IDNO') {
        this.submitForm.show_idcard = isChoice ? 1 : 0;
      } else {
        this.submitForm.show_stock_account = isChoice ? 1 : 0;
      }
    },

    // 获取最近n个月的时间
    getRecentMonth(n) {
      let month = moment(new Date())
        .subtract(1, 'day')
        .subtract(n, 'months')
        .format('YYYYMMDD');
      return month;
    },

    goToPresonal() {
      // 跳转至个人资料
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010004', initJumpMode: '0' });
      });
      /* // 跳转至个人资料
      if ($hvue.platform === '0') {
        window.location.href = $hvue.customConfig.thirdPartyUrl.basicInfo;
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          params: {
            url: $hvue.customConfig.thirdPartyUrl.basicInfo,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      } */
    },

    verifyPhone(phoneNum){
      let regPattern = /^1[3|4|5|8|6|7|9]\d{9}$/;
      return regPattern.test(phoneNum)
    },

    getTitBox() {
      let str;
      if (!this.submitForm.dzd_mobile.trim()) {
        str = '未查询到您的手机号，请点击去完善进行补充后，再提交申请。';
      } else if (!this.verifyPhone(this.submitForm.dzd_mobile)) {
        str = '查询到您的手机号格式不正确，请点击去修改后再提交申请。';
      } else {
        str = '您当天最多可提交10次申请，相同条件下当日最多可申请3次！';
      }
      return str;
    },

    toNext() {
      const {dzd_start_date, dzd_end_date} = this.submitForm
      if(moment(this.openDate).isSameOrAfter(moment().format('YYYYMMDD'))){
        this.$TAlert({
          title: '温馨提示',
          tips: '当天开户暂无法进行对账单信息的查询，请于下一个交易日前来申请，如有问题可拨打客服95310'
        });
        return
      }
      if(moment(dzd_end_date).isBefore(dzd_start_date)){
        this.$TAlert({
          title: '温馨提示',
          tips: '结束日期不能早于开始日期!'
        });
        return
      }
      let params = {
        dzdType: this.submitForm.dzd_type,
        bizType: this.bizType,
        clientId: this.submitForm.client_id,
        fundAccount: this.submitForm.dzd_fund_account,
        showIdcard: this.submitForm.show_idcard,
        showStockAccount: this.submitForm.show_stock_account,
        dzdStartDate: this.submitForm.dzd_start_date,
        dzdEndDate: this.submitForm.dzd_end_date,
        dzdMobile: this.submitForm.dzd_mobile
      };
      console.log('下一步===============================', params);
      // 查询电子对账单当天提交次数
      dzdzdConfirmFormCheck(params).then((res) => {
        if (res.code == 0) {
          if (
            !res.data.todaySubmitsNumCheck ||
            !res.data.alikeSubmitsNumCheck
          ) {
            this.$TAlert({
              title: '温馨提示',
              tips: '抱歉,您当日的对账单查询次数已经用完,请次日再进行办理'
            });
            return;
          }
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, { ...this.submitForm });
        }
      });
    }
  }
};
</script>

<style scoped>
.input_ct{
  padding-left: 0.88rem !important;
}
</style>
