<template>
  <section
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click="handlCLick"></a>
        <h1 class="title">投教视频观看</h1>
      </div>
    </header>
    <article class="content">
      <div class="tj_video_page">
        <p>
          根据《证券公司融资融券业务管理办法》，证券公司与客户签订融资融券合同前，应当采用适当的方式向客户讲解业务规则和合同内容，明确告知客户权力、义务及风险，特别是关于违约处置的风险控制安排，并将融资融券交易风险揭示书交由客户确认，请您<strong>完整观看视频，认真阅读风险揭示书</strong>后，继续开通。
        </p>
        <div class="tj_video_window">
          <video
            ref="videoPlayer"
            :src="videoUrl"
            class="video-js vjs-16-9 vjs-fluid"
            playsinline="false"
            webkit-playsinline="false"
          >
            <!-- <source :src="videoUrl" type="video/mp4" /> -->
          </video>
          <a v-if="isRefashVideo" class="p_button" @click="getVideoUrl">
            重新加载视频按钮
          </a>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a
          v-if="!videoCompleted && videoTime"
          class="p_button"
          :class="{ disabled: disabledBtn }"
          >请观看({{ videoTime }})</a
        >
        <!-- v-if="videoCompleted" -->
        <a v-else class="p_button" @click="toNext"> 我已阅读并同意 </a>
      </div>
    </footer>
  </section>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
import { addClientCritMark, getConfigMap } from '@/service/service';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';
export default {
  name: 'educationVideoWatch',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {},
  data() {
    return {
      timer: null,
      videoCompleted: false,
      // 视频原始地址
      originVideoUrl: '',
      // 重新加载视频按钮
      isRefashVideo: false,
      // 记录是否快进或快退
      isFast: false,
      // 视频总时长
      duration: 0,
      videoUrl: '',
      videoLoad: false,
      videoTime: '',

      currTime: null,
      maxTime: 0,
      watchTime: 1
    };
  },
  beforeDestroy() {
    this.$refs.videoPlayer.pause();
    this.timer = null;
  },
  computed: {
    disabledBtn() {
      if (this.videoCompleted) {
        return false;
      }
      return true;
    }
  },
  created() {
    this.getVideoUrl();
    this.getVideoTime();
    // this.eventMessage(this, EVENT_NAME.NEXT_STEP)
  },
  methods: {
    load(e) {
      if (this.videoLoad) return;
      // 初始化为空
      let time = this.convertTimeToMilliseconds(this.videoTime);
      if (time > this.$refs.videoPlayer.duration * 1000) {
        this.videoTime = this.convertMilliseconds(
          this.$refs.videoPlayer.duration * 1000
        );
      }
      this.duration = this.$refs.videoPlayer.duration * 1000;
      // this.duration = undefined * 1000;
      this.videoLoad = true;
      console.info('load:', this.$refs.videoPlayer.duration);
    },
    convertTimeToMilliseconds(timeString) {
      // 时分秒字符串转毫秒
      let timeArray = timeString.split(':'); // 按冒号分隔时、分、秒

      // let hours = parseInt(timeArray[0]); // 获取小时数
      let minutes = parseInt(timeArray[0]); // 获取分钟数
      let seconds = parseFloat(timeArray[1]); // 获取秒数（包括小数部分）

      return (minutes * 60 + seconds) * 1000; // 计算并返回时间毫秒值
    },

    convertMilliseconds(millis) {
      const minutes = Math.floor((millis % 3600000) / 60000);
      const seconds = Math.floor((millis % 60000) / 1000);
      if (minutes <= 0 && seconds <= 0) {
        this.$refs.videoPlayer.pause();
        this.pauseTimer();
        return '';
      }
      return this.timeFormat(minutes, seconds);
    },
    timeFormat(minutes, seconds) {
      return `${minutes > 9 ? minutes : '0' + minutes}:${
        seconds > 9 ? seconds : '0' + seconds
      }`;
    },

    startTimer(time) {
      this.timer = setInterval(() => {
        time = time - 1000;
        this.videoTime = this.convertMilliseconds(time);
      }, 1000);
    },

    timeupdate(e) {
      if (!this.videoLoad) return fasle;
      console.info(
        'timeupdate:',
        this.$refs.videoPlayer,
        this.$refs.videoPlayer[0],
        this.$refs.videoPlayer.currentTime
      );
      const currentTime = this.$refs.videoPlayer.currentTime;
      // if (currentTime - this.currTime > 1) {
      //   // 快进
      //   console.log('快进');
      //   this.isFast = true;
      //   currentTime =
      //     this.currTime > this.maxTime ? this.currTime : this.maxTime;
      // } else if (this.currTime > currentTime && this.videoCompleted === false) {
      //   // 快退
      //   console.log('快退');
      //   this.isFast = true;
      //   currentTime =
      //     this.currTime > this.maxTime ? this.currTime : this.maxTime;
      // } else {
      //   console.info('未知');
      //   this.isFast = false;
      // }
      this.currTime = currentTime;
      // this.maxTime =
      //   this.currTime > this.maxTime ? this.currTime : this.maxTime;
      if (this.currTime * 1000 >= this.duration && this.videoLoad) {
        console.info(this.duration);
        this.watchTime += 1;
      }
    },
    // 用户点击播放按钮
    playVideo(e) {
      console.log('~~~playVideo~~~');
      if (this.isFast) return (this.isFast = false);
      let time = this.convertTimeToMilliseconds(this.videoTime);
      // if (Number.isNaN(time) || time == 0) {
      //   return this.$refs.videoPlayer.pause();
      // }
      if (!this.videoCompleted && !Number.isNaN(time) && time != 0) {
        this.startTimer(time);
      }
    },
    // 用户点击暂停按钮
    pauseVideo(e) {
      console.log('~~~pauseVideo~~~');
      this.pauseTimer();
    },

    videoEnd() {
      if (this.isFast || this.currTime == 0) return (this.isFast = false);
      let time = this.convertTimeToMilliseconds(this.videoTime);
      if (Number.isNaN(time) || time == 0) {
        this.videoCompleted = true;
      }
      // this.maxTime = 0;
    },
    handlCLick() {
      this.eventMessage(this, EVENT_NAME.PREV_FLOW);
    },

    pauseTimer() {
      clearInterval(this.timer);
    },

    toNext() {
      const currTime = this.currTime * 1000 * this.watchTime;
      console.info(currTime, this.watchTime);
      const minutes = Math.floor((currTime % (1000 * 60 * 60)) / (1000 * 60));
      // Math.floor((currTime % 3600000) / 60000);
      const seconds = Math.floor((currTime % (1000 * 60)) / 1000);
      const originCurrTime = this.duration;
      const originMinutes = Math.floor(
        (originCurrTime % (1000 * 60 * 60)) / (1000 * 60)
      );
      const originSeconds = Math.floor((originCurrTime % (1000 * 60)) / 1000);
      console.info(
        currTime,
        minutes,
        seconds,
        this.timeFormat(minutes, seconds),
        this.duration,
        this.timeFormat(originMinutes, originSeconds)
      );

      addClientCritMark({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        markType: '16',
        markContent: `视频地址：${
          this.originVideoUrl
        },视频观看时常：${this.timeFormat(
          minutes,
          seconds
        )},视频总时长：${this.timeFormat(
          originMinutes,
          originSeconds
        )},用户观看次数:${this.watchTime == 0 ? 1 : this.watchTime}`,
        confirmFlag: '1'
      }).then((res) => {
        this.eventMessage(this, EVENT_NAME.NEXT_STEP);
      });
    },
    // 获取视频倒计时
    getVideoTime() {
      let key = 'bc.credit.video.duration';
      getConfigMap({
        configKey: key
      }).then((res) => {
        this.videoTime = res.data[key].configValue;
      });
    },
    // 获取视频地址
    getVideoUrl() {
      let key = 'bc.credit.video.url';
      getConfigMap({
        configKey: key
      }).then((res) => {
        this.originVideoUrl = res.data[key].configValue;
        // 根据视频地址下载对应的视频
        // this.UrlToBase64(res.data[key].configValue);
        // this.videoUrl = '/bc-h5-view/views/test.mp4';
        this.videoUrl = res.data[key].configValue;

        this.$nextTick(() => {
          // controlBar: true
          const _this = this;
          console.info(_this.$refs.videoPlayer);
          setTimeout(() => {
            this.player = videojs(
              this.$refs.videoPlayer,
              {
                preload: true,
                controls: true,
                controlBar: {
                  children: {
                    playToggle: true,
                    currentTimeDisplay: true,
                    timeDivider: true,
                    durationDisplay: true
                  }
                  // 全屏按钮先进行屏蔽
                  // { name: 'FullscreenToggle' }
                }
              },
              function () {
                _this.$refs.videoPlayer.load();
                this.on('loadedmetadata', (e) => {
                  if (_this.videoLoad) return;
                  // 初始化为空
                  let time = _this.convertTimeToMilliseconds(_this.videoTime);
                  if (time > _this.player.duration() * 1000) {
                    _this.videoTime = _this.convertMilliseconds(
                      _this.player.duration() * 1000
                    );
                  }
                  _this.duration = _this.player.duration() * 1000;
                  // this.duration = undefined * 1000;
                  _this.videoLoad = true;
                  console.info('load:', _this.player.duration());
                  console.info('loadedmetadata:', _this.player.currentTime());
                  console.info('loadedmetadata:', _this.player.duration());
                });
                // this.on('canplaythrough', (e) => {
                //   console.info('canplaythrough:', _this.$refs.videoPlayer);
                //   _this.load(e.target);
                // });
                this.on('canplay', (e) => {
                  console.info('canplay:', _this.$refs.videoPlayer);
                  _this.load(e.target);
                });
                this.on('timeupdate', (e) => {
                  console.info('timeUpdate', _this.player.currentTime());
                  // _this.timeupdate(e.target);

                  if (!_this.videoLoad) return fasle;
                  const currentTime = _this.player.currentTime();
                  // if (currentTime - this.currTime > 1) {
                  //   // 快进
                  //   console.log('快进');
                  //   this.isFast = true;
                  //   currentTime =
                  //     this.currTime > this.maxTime ? this.currTime : this.maxTime;
                  // } else if (this.currTime > currentTime && this.videoCompleted === false) {
                  //   // 快退
                  //   console.log('快退');
                  //   this.isFast = true;
                  //   currentTime =
                  //     this.currTime > this.maxTime ? this.currTime : this.maxTime;
                  // } else {
                  //   console.info('未知');
                  //   this.isFast = false;
                  // }
                  _this.currTime = currentTime;
                  // this.maxTime =
                  //   this.currTime > this.maxTime ? this.currTime : this.maxTime;
                  if (
                    _this.currTime * 1000 >= _this.duration &&
                    _this.videoLoad
                  ) {
                    console.info(this.duration);
                    _this.watchTime += 1;
                  }
                });
                this.on('play', (e) => _this.playVideo(e.target));
                this.on('pause', (e) => _this.pauseVideo(e.target));
                this.on('ended', (e) => _this.videoEnd(e.target));
              }
            );
          });
        }, 300);

        // this.UrlToBase64('/bc-h5-view/views/test.mp4');
      });
    }
    // 下载视频资源，转为视频地址
    // UrlToBase64(url) {
    //   _hvueLoading.open();
    //   return new Promise((resolve) => {
    //     fetch(url)
    //       .then((data) => {
    //         const blob = data.blob();
    //         return blob;
    //       })
    //       .then((blob) => {
    //         this.$nextTick(() => {
    //           this.isRefashVideo = false;
    //           this.videoUrl = window.URL.createObjectURL(blob);
    //           // this.$refs.videoPlayer.src = url || this.videoUrl;
    //           // console.info(this.$refs.videoPlayer.source);
    //           this.isVideoShow = true;
    //           // this.$refs.videoPlayer.load = () => {
    //           //   console.info('加载完成');
    //           //   this.$refs.videoPlayer.play();
    //           // };
    //         });
    //         resolve(this.videoUrl);
    //       })
    //       .catch((e) => {
    //         this.isRefashVideo = true;
    //       })
    //       .finally(() => {
    //         _hvueLoading.close();
    //       });
    //   });
    // }
  }
};
</script>

<style lang="less" scoped>
/deep/.tj_video_window {
  width: 100%;
  height: 205px;
  // &::-webkit-media-controls-enclosure {
  //   display: none;
  // }
  // &::-webkit-media-controls-timeline {
  //   display: none;
  // }
  // &::-webkit-media-controls-mute-button {
  //   display: none;
  // }
  // &::-webkit-media-controls-play-button {
  //   display: block;
  // }
  .vjs-current-time,
  .vjs-duration,
  .vjs-time-divider {
    display: block;
  }
  .vjs-time-divider {
    padding: 0px;
    min-width: auto;
  }
}
</style>
