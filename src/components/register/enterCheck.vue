<template>
  <div class="component">
    <article class="content">
      <div v-if="pageShow" class="com_box">
        <div class="com_title" style="text-align: left">
          <h5>办理此业务需要满足以下条件</h5>
        </div>
        <ul class="cond_list">
          <li
            v-for="(item, index) in viewList"
            :key="index"
            class="ok"
            :class="item.ruleResult === '1' ? 'ok' : 'error'"
          >
            <div class="tit" style="text-align: left">
              <h5>{{ JSON.parse(item.ruleResultDesc).title }}</h5>
              <p>{{ JSON.parse(item.ruleResultDesc).tips }}</p>
            </div>
          </li>
        </ul>
        <div v-if="!allPass" class="cond_tips">
          <p>抱歉，您不满足办理条件，无法办理此业务，详情可咨询客服人员。</p>
        </div>
      </div>
    </article>
  </div>
</template>

<script>
import { businessEgliCheck } from '@/service/service.js';
import { RULE_RESULT } from '@/common/enumeration';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'EnterCheck',
  inject: ['clearKeepAlive', 'eventMessage'],
  data() {
    return {
      viewList: [],
      bizName: '',
      allPass: false,
      RULE_RESULT,
      pageShow: false
    };
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    this.renderingView();
    this.clearKeepAlive();
  },
  /*activated() {
    this.renderingView();
  },*/
  // deactivated() {
  //   this.clearKeepAlive();
  // },
  methods: {
    renderingView() {
      let _this = this;
      const flowToken = sessionStorage.getItem('TKFlowToken');
      businessEgliCheck({
        flowToken
      })
        .then((data) => {
          _hvueLoading.close();
          _this.pageShow = true;
          _this.viewList = data.data.result;
          const strategyResult = data.data.strategyResult;
          if (strategyResult === RULE_RESULT.pass) {
            _this.allPass = true;
            if (this.$attrs.isAutoNext) {
              this.eventMessage(this, EVENT_NAME.NEXT_STEP);
            }
            this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: true });
          } else {
            _this.viewList = data.data.result;
            this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
              text: '返回首页',
              display: true,
              btnStatus: 2,
              data: () => {
                this.eventMessage(this, EVENT_NAME.TO_INDEX);
              }
            });
          }
        })
        .catch((err) => {
          _hvueLoading.close();
          _hvueToast({ mes: err });
        });
    }
  }
};
</script>

<style lang="less" scope>
.component {
  width: 100%;
  .van-cell {
    min-height: 0.24rem;
    line-height: 0.24rem;
    padding: 0;
  }
  .van-cell::after {
    display: none;
  }
}
</style>
