<template>
  <article class="content">
    <div class="wtclass_main">
      <ul class="wtclass_list">
        <li v-for="(item, index) in enChangeEntrustWayList" :key="index">
          <p>{{ item.name }}</p>
          <div class="switch">
            <input
              type="checkbox"
              :checked="item.checked"
              @click="checkEntrustWay($event, item.value)"
            />
            <div class="switch-inner">
              <div class="switch-arrow"></div>
            </div>
          </div>
        </li>
      </ul>
    </div>
    <div class="pword_tips">
      <ul>
        <li>
          电话：适用于通过400-618-3355进行电话委托交易，或人工服务进行业务办理时身份验证委托的委托方式。
        </li>
        <li>网上：适用于通过电脑进行交易的委托方式。</li>
        <li>手机：适用于手机交易的委托方式。</li>
        <li>请投资者根据实际情况选择相应的委托方式。</li>
      </ul>
    </div>
  </article>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
import { getDictData } from '@/service/commonService';
import { entrustmentOpenedQry } from '@/service/service';

export default {
  name: 'EntrustWayChange',
  data() {
    return {
      enChangeEntrustWayList: [],
      anotherEntrustWay: '',
      entrustWay: []
    };
  },
  computed: {
    openEnEntrustWay() {
      let arr = [];
      for (let item of this.enChangeEntrustWayList) {
        if (item.checked) {
          arr.push(item.value);
        }
      }
      return arr;
    }
  },
  mounted() {
    this.renderingView();
  },
  methods: {
    checkEntrustWay(event, entrustWay) {
      for (let item of this.enChangeEntrustWayList) {
        if (item.value === entrustWay) {
          if (item.checked) {
            if (this.entrustWay.includes(entrustWay)) {
              event.preventDefault();
              _hvueToast({ mes: '已开通不允许取消' });
            } else {
              this.$set(item, 'checked', false);
            }
          } else {
            this.$set(item, 'checked', true);
          }
          break;
        }
      }
      this.$emit('change', {
        enEntrustWay: this.openEnEntrustWay
          .concat(this.anotherEntrustWay)
          .join(','),
        entrustWay: this.entrustWay.join(',')
      });
    },

    renderingView() {
      let enChangeEntrustWay = this.$attrs.enChangeEntrustWay.split(',');
      getDictData({ dictType: 'bc.common.entrustWay' }).then((res) => {
        let objArr = res.data['bc.common.entrustWay'];
        enChangeEntrustWay.forEach((item) => {
          let obj = objArr.filter((it) => it.dictValue === item);
          this.enChangeEntrustWayList.push({
            name: obj[0].dictLabel,
            value: obj[0].dictValue
          });
          entrustmentOpenedQry().then((res) => {
            let entrustWay = res.data.entrustWay.split(';');
            this.entrustWay = entrustWay;
            this.anotherEntrustWay = entrustWay.filter(
              (item) => !enChangeEntrustWay.includes(item)
            );
            this.enChangeEntrustWayList.forEach((item) => {
              if (entrustWay.includes(item.value)) {
                this.$set(item, 'checked', true);
              } else {
                this.$set(item, 'checked', false);
              }
            });
            console.log(this.entrustWay);
            this.$emit('change', {
              enEntrustWay: this.openEnEntrustWay
                .concat(this.anotherEntrustWay)
                .join(','),
              entrustWay: this.entrustWay.join(',')
            });
          });
        });
      });
    }
  }
};
</script>
