<template>
  <article class="content">
    <div
      v-for="(item, index) in fileListArr"
      :key="index"
      class="file_box_01"
      :class="{ spel: !needRadio }"
    >
      <div class="item">
        <div class="base" @click="chooseArchives(item, index)">
          <span
            v-if="needRadio"
            class="icon_radio"
            :class="{ checked: item.isChecked }"
          ></span>
          <i class="icon_imp"></i>
          <h5>{{ item.image_label }}</h5>
          <p>拟申请C类专业投资者要求金融资产不少于300万元</p>
        </div>
        <div
          v-if="item.isChecked || !needRadio"
          class="cont"
          style="display: block"
        >
          <van-uploader
            v-model="item.fileList"
            multiple
            :max-count="item.pageMax"
            upload-icon="plus"
            upload-text="添加图片"
            preview-size="110px"
            :after-read="afterRead"
            @click-upload="clickUpload(index)"
          />
        </div>
      </div>
    </div>
  </article>
</template>

<script>
import { imageUpload, archivesInfoQry } from '@/service/service.js';
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}

export default {
  name: 'FilePhotoUpload',
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {
    imageNo: {
      type: String,
      default: ''
    },
    needRadio: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      uploadIndex: 0,
      fileListArr: [],
      previewImage: '',
      selectImageNo: ''
    };
  },
  mounted() {
    this.renderingView();
  },
  methods: {
    renderingView() {
      archivesInfoQry({ archivesNos: this.imageNo }).then((res) => {
        this.fileListArr = res.data.map((item) => {
          return {
            image_no: item.archivesNo,
            image_type: item.archivesType,
            image_label: item.name,
            fileList: [],
            pageMax: item.pageMax
          };
        });
      });
    },

    chooseArchives(item) {
      if (!this.needRadio) {
        return;
      }
      this.fileListArr.forEach((it) => {
        this.$set(it, 'isChecked', false);
        this.$set(it, 'fileList', []);
      });
      this.fileListArr.forEach((it) => {
        if (item.image_label === it.image_label) {
          this.$set(it, 'isChecked', true);
        }
      });
    },

    clickUpload(index) {
      this.uploadIndex = index;
    },

    async afterRead(file) {
      file.status = 'uploading';
      file.message = '上传中...';
      let imageFile = file.file;
      let imageFilePrev = await getBase64(imageFile);
      imageFile = imageFilePrev.split('base64,')[1];
      imageUpload({ imgContent: imageFile }).then((res) => {
        file.url = `${$hvue.customConfig.fileUrl}${res.data}`;
        file.status = 'done';

        let arr = JSON.parse(JSON.stringify(this.fileListArr)).map((item) => {
          let fileList = item.fileList;
          console.log(fileList);
          return {
            image_type: item.image_type,
            image_no: item.image_no,
            image_files: fileList.map((it, index) => {
              return {
                image_file_path: it.url,
                page_num: index + 1
              };
            })
          };
        });
        arr = arr.filter((item) => item.image_files.length > 0);
        console.log(JSON.stringify(arr));
        this.$emit('change', { scanimageData: JSON.stringify(arr) });
      });
    }
  }
};
</script>

<style></style>
