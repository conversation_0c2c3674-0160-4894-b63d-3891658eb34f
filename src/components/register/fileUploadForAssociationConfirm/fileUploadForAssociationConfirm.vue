<template>
  <div class="content">
    <div class="com_title">
      <div class="com_title_top">
        <span>请上传以下账户的无持仓证明</span>
        <span class="show_example" @click="showExampleFn">查看示例></span>
      </div>
    </div>
    <div class="com_title_tip">
      <div>
        提示:您可在中国结算APP首页-电子凭证-证券持有余额查询截止目前的证券持有余额并上传查询电子凭证截图作为无证券持有余额证明。
      </div>
      <div>上传文件总大小不能超过20M。</div>
    </div>
    <div
      v-for="(item, index) in fileListArrZc"
      :key="index + 'Zc'"
      class="file_box_01 spel"
    >
      <div class="item">
        <div class="base">
          <h5>
            {{ item.csdcHolderName }}
            {{ item.stockAccount }}
            <span>(最多可上传3张)</span>
          </h5>
        </div>
        <uploadFile
          v-if="needNativeUpload"
          v-model="item.fileList"
          :page-max="item.pageMax"
        ></uploadFile>
        <div class="cont" style="display: block">
          <van-uploader
            v-if="!needNativeUpload"
            v-model="item.fileList"
            :max-count="item.pageMax"
            upload-icon="plus"
            upload-text="添加图片"
            preview-size="110px"
            :preview-full-image="false"
            :capture="false"
            :before-read="beforeRead"
            :after-read="afterRead"
          />
        </div>
      </div>
    </div>
    <van-overlay
      :show="showExample"
      @click="showExample = false"
      z-index="9999"
      class-name="example_box"
    >
      <img src="@/assets/images/sample.png" />
      <div class="close_icon"></div>
    </van-overlay>
    <getImgBoxApp
      ref="getImgBoxThinkive"
      @getImgCallBack="getImgCallBack"
    ></getImgBoxApp>
  </div>
</template>

<script>
import { imageUpload } from '@/service/service.js';
import uploadFile from '../fileUploadForMajor/components/uploadFile.vue';
import { EVENT_NAME } from '@/common/formEnum';
import { uploadFileFormData } from '@/common/util';
import getImgBoxApp from '../fileUploadForMajor/components/getImage.vue';

function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}

export default {
  name: 'fileUploadForAssociationConfirm',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {
    getImgBoxApp,
    uploadFile
  },
  props: {
    imageNo: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      needNativeUpload: false,
      uploadIndex: 0,
      uploadArr: '',
      fileListArrZc: [],
      showExample: false,
      scanimageData: [],
      selectAccountsData: []
    };
  },
  watch: {
    fileListArrZc: {
      handler() {
        let arrZc = JSON.parse(JSON.stringify(this.fileListArrZc)).map(
          (item) => {
            let fileList = item.fileList;
            return {
              image_type: item.image_type,
              image_no: item.image_no,
              image_files: fileList
                .map((it, index) => {
                  return {
                    image_file_path: it.upUrl,
                    page_num: index + 1
                  };
                })
                .filter((itt) => itt.image_file_path),
              ext_image_type: [
                {
                  stock_account: item.stockAccount
                }
              ]
            };
          }
        );
        this.scanimageData = [arrZc];
      },
      deep: true
    }
  },
  created() {
    this.needNativeUpload = $hvue.customConfig.needNativeUpload;
  },
  mounted() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
      btnStatus: 2,
      data: () => {
        this.next();
      }
    });
    this.renderingView();
  },
  methods: {
    showExampleFn() {
      this.showExample = true;
    },

    renderingView() {
      const { inProperty } = this.tkFlowInfo();
      let { selectAccountsData } = this.tkFlowInfo().inProperty;
      console.log('inProperty=====>', inProperty);
      console.log('selectAccountsData=====>', JSON.parse(selectAccountsData));
      this.selectAccountsData = JSON.parse(selectAccountsData);

      this.fileListArrZc = JSON.parse(selectAccountsData)
        .filter((item) => item.needUploadNoPostition === '1')
        .map((item) => {
          return {
            ...item,
            image_no: '02504',
            image_type: '02',
            fileList: [],
            pageMax: 3
          };
        });
    },

    next() {
      let hasNoUpload = this.scanimageData[0].filter(item => !item.image_files.length).map(item => item.ext_image_type[0].stock_account).join('、')
      console.log('hasNoUpload----------',hasNoUpload)
      if(hasNoUpload){
        _hvueToast({
          mes: `${hasNoUpload}未上传无持仓证明，请检查并上传完整`
        });
        return
      }
      console.log('scanimageData----------', this.scanimageData);
      console.log('fileListArrZc----------', this.fileListArrZc);
      this.selectAccountsData = this.selectAccountsData.map((element) => {
        element.scanimage_data = [];
        console.log('element', element);
        this.fileListArrZc.forEach((item) => {
          if (element.stockAccount === item.stockAccount) {
            item.fileList.forEach((it) => {
              element.scanimage_data.push({
                src: it.upUrl,
                modl: 1
              });
            });
          }
        });
        return element
      });
      console.log('selectAccountsData----------', this.selectAccountsData);
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        scanimageData: JSON.stringify(this.scanimageData),
        selectAccountsData: JSON.stringify(this.selectAccountsData)
      });
    },

    clickUpload(index, fileArr) {
      console.log(index, fileArr);
      this.uploadIndex = index;
      this.uploadArr = fileArr;
      this.$refs.getImgBoxThinkive.getImg();
    },

    getImgCallBack(data) {
      console.log(data);
      imageUpload({
        imgContent: data.base64,
        flowToken: sessionStorage.getItem('TKFlowToken')
      }).then((res) => {
        console.log(res);
        this[this.uploadArr][this.uploadIndex].fileList.push({});
      });
    },

    beforeRead(file) {
      // return false;
      if(!(file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png' || file.type === 'image/tiff')){
        _hvueToast({
          mes: '上传文件格式不正确,请上传jpg、jpeg、png、tiff格式的文件'
        });
        file.status = 'failed';
        file.message = '上传失败';
        return false;
      }
      if (file.size > 8388608) {
        _hvueToast({
          mes: '单张文件大小不能超过8M，请重新选择'
        });
        file.status = 'failed';
        file.message = '上传失败';
        return false;
      } else {
        return true;
      }
      // else {
      //   _hvueLoading.open();
      //   return true;
      // }
    },

    async afterRead(file) {
      _hvueLoading.open();
      console.log(file);
      // file.status = 'uploading';
      // file.message = '上传中...';
      // let imageFile = file.file;
      // let imageFilePrev = await getBase64(imageFile);
      // imageFile = imageFilePrev.split('base64,')[1];
      // console.log(this.dataURLtoFile(imageFilePrev));
      uploadFileFormData(
        $hvue.customConfig.serverUrl + '/media/uploadWithMultipartFile',
        {
          file: file.file,
          flowToken: sessionStorage.getItem('TKFlowToken')
        },
        {
          success: (res) => {
            console.log(res);
            if (res.code === 0) {
              file.url = `${$hvue.customConfig.fileUrl}${res.data}`;
              // file.url = file.content;
              file.status = 'done';
              file.upUrl = res.data;
              file.size = file.file.size;

              let arrZc = JSON.parse(JSON.stringify(this.fileListArrZc)).map(
                (item) => {
                  let fileList = item.fileList;
                  return {
                    image_type: item.image_type,
                    image_no: item.image_no,
                    image_files: fileList
                      .map((it, index) => {
                        return {
                          image_file_path: it.upUrl,
                          page_num: index + 1
                        };
                      })
                      .filter((itt) => itt.image_file_path),
                    ext_image_type: [
                      {
                        stock_account: item.stockAccount
                      }
                    ]
                  };
                }
              );
              this.scanimageData = [arrZc];
              this.$emit('change', {
                scanimageData: JSON.stringify(this.scanimageData)
              });
            } else {
              file.status = 'failed';
              file.message = '上传失败';
              _hvueToast({
                mes: res.msg
              });
            }
            _hvueLoading.close();
          },
          progress: (count) => {
            console.log(count);
          },
          error: (e) => {
            file.status = 'failed';
            file.message = '上传失败';
            _hvueLoading.close();
            console.log(e);
          }
        }
      );
    }
  }
};
</script>

<style scoped lang="less">
.content {
  background: var(--bgColor, #f8f8f8);
}
.com_title_top {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333;
  font-size: 0.14rem;
  font-weight: 500;
  line-height: 0.18rem;
}
.show_example {
  color: #338aff;
}
.com_title_tip {
  padding: 0 0.16rem 0.12rem;
  background: var(--bgColor, #f8f8f8);
  color: var(--typography-333, #333);
  font-size: 0.14rem;
  line-height: 0.2rem;
}
.base {
  padding: 0.16rem 0 !important;
  border-bottom: 1px solid #ddd;
}
.cont {
  padding-top: 0.16rem !important;
}
.file_box_01 {
  margin-bottom: 0.08rem;
}
.example_box {
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  img {
    width: 100%;
  }
  .close_icon {
    width: 0.32rem;
    height: 0.32rem;
    background-image: url('../../../assets/images/icon_close3.png');
    background-size: contain;
    margin-top: 0.3rem;
  }
}
</style>
