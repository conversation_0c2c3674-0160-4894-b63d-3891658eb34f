<template>
  <div v-if="showSelImgBox">
    <!-- 遮罩层 -->
    <div class="dialog_overlay" style="display: block" @click="close"></div>
    <!-- 弹出层 -->
    <div class="upload_select show">
      <h5>请选择上传方式</h5>
      <ul>
        <li @click="selImg(1)">
          <a>拍照</a>
        </li>
        <li @click="selImg(2)">
          <a>从相册上传</a>
        </li>
      </ul>
      <a class="cancel" @click="close">取消</a>
    </div>
  </div>
</template>

<script>
import '@/nativeShell/nativeCallH5';

export default {
  props: {},
  data() {
    return {
      showSelImgBox: false
    };
  },
  methods: {
    getImg() {
      this.showSelImgBox = true;
    },
    close() {
      this.showSelImgBox = false;
    },
    selImg(selType) {
      window.imgCallBack = this.getImgCallBack;
      let phoneConfig = {
        funcNo: '50273',
        mode: selType !== 1 ? '1' : '2', // phone相册 pai拍照
        isAlbum: '0',
        compressSize: '600',
        width: '595',
        height: '842',
        cutFlag: '0',
        cameraFlag: '1',
        moduleName: $hvue.customConfig.moduleName // 必须为open
      };
      // var phoneConfig = {
      //   requestParam: 'test', // h5上传不需要 但原生判断了是否为空
      //   imgType: this.imgType === 'idfrontimg' ? '4' : '5', // 需上传图片类型 3大头照 4 身份证正面 5 反面
      //   url: $hvue.customConfig.serverUrl + '/servlet/json?',
      //   funcNo: (this.scan && selType) === 1 ? 60014 : 60013,
      //   action: selType !== 1 ? 'phone' : 'pai', // 60013  phone相册 pai拍照
      //   userId: '1', // h5上传不需要 但原生判断了是否为空
      //   isUpload: '0',
      //   isAlbum: '0', //是否显示相册 1表示显示相册，0或其他表示不显示 默认隐藏
      //   isTake: '1', //是否显示拍照按钮
      //   compressSize: 200, //原生压缩大小 不传默认200k
      //   moduleName: 'open' // 必须为open
      // };
      console.log(phoneConfig);
      let result = $h.callMessageNative(phoneConfig);
      this.close();
      if (result.error_no !== '0') {
        console.log({ mes: result.error_info });
      }
      // _hvueLoading.open();
    },
    getImgCallBack(data) {
      console.log(data);
      // _hvueLoading.close();
      this.$emit('getImgCallBack', {
        base64Image: this.filterBase64Pre(data.base64Image)
      });
    },
    filterBase64Pre(ndata) {
      let arr = ndata.split('base64,');
      return arr[arr.length - 1];
    }
  }
};
</script>
