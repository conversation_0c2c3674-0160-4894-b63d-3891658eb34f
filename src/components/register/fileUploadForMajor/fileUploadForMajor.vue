<template>
  <article class="content">
    <div class="fileUpload_scrollback" ref="scrollback">
      <div v-if="internalAssetsCheck">
        <div class="com_title">
          <h5>
            请在金融资产及近三年年均收入中选择一项上传<span
              v-if="!needNativeUpload"
              >（单张文件大小不能超过8M）</span
            >
          </h5>
        </div>
        <div
          v-for="(item, index) in fileListArr"
          :key="index"
          class="file_box_01"
        >
          <div class="item">
            <div class="base">
              <span
                class="icon_radio"
                :class="{ checked: item.isChecked }"
                @click="chooseArchives(item, index)"
              ></span>
              <i class="icon_imp" @click="showImp(`introduce${index + 1}`)"></i>
              <h5>{{ item.image_label }}</h5>
              <p v-if="index === 0">
                拟申请C类专业投资者要求金融资产不少于300万元
              </p>
              <p v-if="index === 1">
                拟申请C类专业投资者要求近三年年均收入不少于30万元
              </p>
            </div>
            <uploadFile
              v-if="item.isChecked && needNativeUpload"
              v-model="item.fileList"
              :page-max="item.pageMax"
            ></uploadFile>
            <div
              v-if="item.isChecked && !needNativeUpload"
              class="cont"
              style="display: block"
            >
              <van-uploader
                v-model="item.fileList"
                :max-count="item.pageMax"
                upload-icon="plus"
                upload-text="添加图片"
                preview-size="110px"
                :preview-full-image="false"
                :capture="false"
                :before-read="beforeRead"
                :after-read="afterRead"
              />
            </div>
          </div>
        </div>
      </div>

      <div v-if="customerTradedayCheck">
        <div class="com_title">
          <h5>
            请上传您的投资经历或相关工作经历证明材料<span
              v-if="!needNativeUpload"
              >（单张文件大小不能超过8M）</span
            >
          </h5>
        </div>
        <div
          v-for="(item, index) in fileListArrZc"
          :key="index + 'Zc'"
          class="file_box_01 spel"
        >
          <div class="item">
            <div class="base">
              <!-- <span
              class="icon_radio"
              :class="{ checked: item.isChecked }"
            ></span> -->
              <i class="icon_imp" @click="showImp('introduce3')"></i>
              <h5>{{ item.image_label }}</h5>
              <p>拟申请C类专业投资者要求证券交易经验满365天</p>
            </div>
            <uploadFile
              v-if="needNativeUpload"
              v-model="item.fileList"
              :page-max="item.pageMax"
            ></uploadFile>
            <div class="cont" style="display: block">
              <van-uploader
                v-if="!needNativeUpload"
                v-model="item.fileList"
                :max-count="item.pageMax"
                upload-icon="plus"
                upload-text="添加图片"
                preview-size="110px"
                :preview-full-image="false"
                :capture="false"
                :before-read="beforeRead"
                :after-read="afterRead"
              />
            </div>
          </div>
          <!-- <h-uploader :capture="false" @on-change="change" /> -->
        </div>
      </div>

      <introduce1 ref="introduce1" />
      <introduce2 ref="introduce2" />
      <introduce3 ref="introduce3" />
      <getImgBoxApp
        ref="getImgBoxThinkive"
        @getImgCallBack="getImgCallBack"
      ></getImgBoxApp>
      <div class="leave_mark" ref="leaveMark">
        <van-checkbox
          v-model="leaveMarkisChecked"
          shape="square"
          class="leaceMarkCheckbox"
          icon-size="15px"
        />
        <div>
          本人承诺以上材料均真实合法有效，若因本人提供材料虚假无效的，将由本人承担全部责任。
        </div>
      </div>
    </div>
  </article>
</template>

<script>
// import { uploadWithMultipartFile } from '@/service/service.js';
import { imageUpload, addClientCritMark } from '@/service/service.js';
import introduce1 from './components/introduce1.vue';
import introduce2 from './components/introduce2.vue';
import introduce3 from './components/introduce3.vue';
import uploadFile from './components/uploadFile.vue';
import { EVENT_NAME } from '@/common/formEnum';
import { uploadFileFormData } from '@/common/util';
import getImgBoxApp from './components/getImage.vue';
import scrollIntoView from 'smooth-scroll-into-view-if-needed';

function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}

export default {
  name: 'FileUploadForMajor',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {
    introduce1,
    introduce2,
    introduce3,
    getImgBoxApp,
    uploadFile
  },
  props: {
    imageNo: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      needNativeUpload: false,
      internalAssetsCheck: false,
      customerTradedayCheck: false,
      uploadIndex: 0,
      uploadArr: '',
      fileListArr: [],
      fileListArrZc: [],
      previewImage: '',
      selectImageNo: '',
      leaveMarkisChecked: false
    };
  },
  watch: {
    fileListArr: {
      handler() {
        let arr = JSON.parse(JSON.stringify(this.fileListArr)).map((item) => {
          let fileList = item.fileList;
          return {
            image_type: item.image_type,
            image_no: item.image_no,
            image_files: fileList
              .map((it, index) => {
                return {
                  image_file_path: it.upUrl,
                  page_num: index + 1
                };
              })
              .filter((itt) => itt.image_file_path)
          };
        });
        let arrZc = JSON.parse(JSON.stringify(this.fileListArrZc)).map(
          (item) => {
            let fileList = item.fileList;
            return {
              image_type: item.image_type,
              image_no: item.image_no,
              image_files: fileList
                .map((it, index) => {
                  return {
                    image_file_path: it.upUrl,
                    page_num: index + 1
                  };
                })
                .filter((itt) => itt.image_file_path)
            };
          }
        );
        let scanimageData = [arr, arrZc];
        let arrl = arr.filter((item) => item.image_files.length > 0);
        let arrZcl = arrZc.filter((item) => item.image_files.length > 0);
        let flagA = this.internalAssetsCheck && arrl.length <= 0;
        let flagB = this.customerTradedayCheck && arrZcl.length <= 0;
        if (!flagA && !flagB) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 2,
            data: () => {
              this.goNext(scanimageData);
              // this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
              //   scanimageData: JSON.stringify(scanimageData)
              // });
            }
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
        }
      },
      deep: true
    },
    fileListArrZc: {
      handler() {
        let arr = JSON.parse(JSON.stringify(this.fileListArr)).map((item) => {
          let fileList = item.fileList;
          return {
            image_type: item.image_type,
            image_no: item.image_no,
            image_files: fileList
              .map((it, index) => {
                return {
                  image_file_path: it.upUrl,
                  page_num: index + 1
                };
              })
              .filter((itt) => itt.image_file_path)
          };
        });
        let arrZc = JSON.parse(JSON.stringify(this.fileListArrZc)).map(
          (item) => {
            let fileList = item.fileList;
            return {
              image_type: item.image_type,
              image_no: item.image_no,
              image_files: fileList
                .map((it, index) => {
                  return {
                    image_file_path: it.upUrl,
                    page_num: index + 1
                  };
                })
                .filter((itt) => itt.image_file_path)
            };
          }
        );
        let scanimageData = [arr, arrZc];
        let arrl = arr.filter((item) => item.image_files.length > 0);
        let arrZcl = arrZc.filter((item) => item.image_files.length > 0);
        let flagA = this.internalAssetsCheck && arrl.length <= 0;
        let flagB = this.customerTradedayCheck && arrZcl.length <= 0;
        if (!flagA && !flagB) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 2,
            data: () => {
              this.goNext(scanimageData);
              // this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
              //   scanimageData: JSON.stringify(scanimageData)
              // });
            }
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
        }
      },
      deep: true
    }
  },
  created() {
    this.needNativeUpload = $hvue.customConfig.needNativeUpload;
  },
  mounted() {
    // let phoneConfig = {
    //   funcNo: '50001',
    //   moduleName: 'open' // 必须为open
    // };
    // console.log(phoneConfig);
    // let result = $h.callMessageNative(phoneConfig);
    // console.log(result);
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
    this.renderingView();
  },
  methods: {
    showImp(propType) {
      this.$refs[propType].show();
    },

    renderingView() {
      const { inProperty } = this.tkFlowInfo();
      const { customerTradedayCheck, internalAssetsCheck } = inProperty;
      if (internalAssetsCheck === '0') {
        this.internalAssetsCheck = true;
      }
      if (customerTradedayCheck === '0') {
        this.customerTradedayCheck = true;
      }
      this.fileListArr = [
        {
          image_no: '02501',
          image_type: '02',
          image_label: '金融资产证明',
          fileList: [],
          pageMax: 9,
          isChecked: true
        },
        {
          image_no: '02502',
          image_type: '02',
          image_label: '个人近三年年均收入证明',
          fileList: [],
          pageMax: 9,
          isChecked: false
        }
      ];
      this.fileListArrZc = [
        {
          image_no: '02503',
          image_type: '02',
          image_label: '投资经历或相关工作经历证明材料',
          fileList: [],
          pageMax: 9
        }
      ];
      // archivesInfoQry({ archivesNos: this.imageNo }).then((res) => {
      //   this.fileListArr = res.data.map((item) => {
      //     return {
      //       image_no: item.archivesNo,
      //       image_type: item.archivesType,
      //       image_label: item.name,
      //       fileList: [],
      //       pageMax: item.pageMax
      //     };
      //   });
      // });
    },

    chooseArchives(item) {
      this.fileListArr.forEach((it) => {
        this.$set(it, 'isChecked', false);
        this.$set(it, 'fileList', []);
      });
      this.fileListArr.forEach((it) => {
        if (item.image_label === it.image_label) {
          this.$set(it, 'isChecked', true);
        }
      });
    },

    clickUpload(index, fileArr) {
      console.log(index, fileArr);
      this.uploadIndex = index;
      this.uploadArr = fileArr;
      this.$refs.getImgBoxThinkive.getImg();
    },

    getImgCallBack(data) {
      console.log(data);
      imageUpload({
        imgContent: data.base64,
        flowToken: sessionStorage.getItem('TKFlowToken')
      }).then((res) => {
        console.log(res);
        this[this.uploadArr][this.uploadIndex].fileList.push({});
      });
    },

    beforeRead(file) {
      // return false;
      if (
        !(
          file.type === 'image/jpeg' ||
          file.type === 'image/jpg' ||
          file.type === 'image/png' ||
          file.type === 'image/tiff'
        )
      ) {
        _hvueToast({
          mes: '上传文件格式不正确，请上传jpg、jpeg、png、tiff格式的文件'
        });
        file.status = 'failed';
        file.message = '上传失败';
        return false;
      }
      if (file.size > 8388608) {
        _hvueToast({
          mes: '单张文件大小不能超过8M，请重新选择'
        });
        file.status = 'failed';
        file.message = '上传失败';
        return false;
      }
      return true;
      // else {
      //   _hvueLoading.open();
      //   return true;
      // }
    },

    async afterRead(file) {
      _hvueLoading.open();
      console.log(file);
      // file.status = 'uploading';
      // file.message = '上传中...';
      // let imageFile = file.file;
      // let imageFilePrev = await getBase64(imageFile);
      // imageFile = imageFilePrev.split('base64,')[1];
      // console.log(this.dataURLtoFile(imageFilePrev));
      uploadFileFormData(
        $hvue.customConfig.serverUrl + '/media/uploadWithMultipartFile',
        {
          file: file.file,
          flowToken: sessionStorage.getItem('TKFlowToken')
        },
        {
          success: (res) => {
            console.log(res);
            if (res.code === 0) {
              file.url = `${$hvue.customConfig.fileUrl}${res.data}`;
              // file.url = file.content;
              file.status = 'done';
              file.upUrl = res.data;
              file.size = file.file.size;

              let arr = JSON.parse(JSON.stringify(this.fileListArr)).map(
                (item) => {
                  let fileList = item.fileList;
                  return {
                    image_type: item.image_type,
                    image_no: item.image_no,
                    image_files: fileList
                      .map((it, index) => {
                        return {
                          image_file_path: it.upUrl,
                          page_num: index + 1
                        };
                      })
                      .filter((itt) => itt.image_file_path)
                  };
                }
              );
              let arrZc = JSON.parse(JSON.stringify(this.fileListArrZc)).map(
                (item) => {
                  let fileList = item.fileList;
                  return {
                    image_type: item.image_type,
                    image_no: item.image_no,
                    image_files: fileList
                      .map((it, index) => {
                        return {
                          image_file_path: it.upUrl,
                          page_num: index + 1
                        };
                      })
                      .filter((itt) => itt.image_file_path)
                  };
                }
              );
              let scanimageData = [arr, arrZc];
              let arrl = arr.filter((item) => item.image_files.length > 0);
              let arrZcl = arrZc.filter((item) => item.image_files.length > 0);
              let flagA = this.internalAssetsCheck && arrl.length <= 0;
              let flagB = this.customerTradedayCheck && arrZcl.length <= 0;
              if (!flagA && !flagB) {
                this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
                  btnStatus: 2,
                  data: () => {
                    this.goNext(scanimageData);
                    // this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
                    //   scanimageData: JSON.stringify(scanimageData)
                    // });
                  }
                });
              }
              this.$emit('change', {
                scanimageData: JSON.stringify(scanimageData)
              });
            } else {
              file.status = 'failed';
              file.message = '上传失败';
              _hvueToast({
                mes: res.msg
              });
            }
            _hvueLoading.close();
          },
          progress: (count) => {
            console.log(count);
          },
          error: (e) => {
            file.status = 'failed';
            file.message = '上传失败';
            _hvueLoading.close();
            console.log(e);
          }
        }
      );

      // try {
      //   uploadWithMultipartFile({
      //     flie: this.dataURLtoFile(imageFilePrev),
      //     flowToken: sessionStorage.getItem('TKFlowToken')
      //   })
      //     .then((res) => {
      //       // file.url = `${$hvue.customConfig.fileUrl}${res.data}`;
      //       file.url = file.content;
      //       file.status = 'done';
      //       file.upUrl = res.data;
      //       file.size = file.file.size;

      //       let arr = JSON.parse(JSON.stringify(this.fileListArr)).map(
      //         (item) => {
      //           let fileList = item.fileList;
      //           return {
      //             image_type: item.image_type,
      //             image_no: item.image_no,
      //             image_files: fileList
      //               .map((it, index) => {
      //                 return {
      //                   image_file_path: it.upUrl,
      //                   page_num: index + 1
      //                 };
      //               })
      //               .filter((itt) => itt.image_file_path)
      //           };
      //         }
      //       );
      //       let arrZc = JSON.parse(JSON.stringify(this.fileListArrZc)).map(
      //         (item) => {
      //           let fileList = item.fileList;
      //           return {
      //             image_type: item.image_type,
      //             image_no: item.image_no,
      //             image_files: fileList
      //               .map((it, index) => {
      //                 return {
      //                   image_file_path: it.upUrl,
      //                   page_num: index + 1
      //                 };
      //               })
      //               .filter((itt) => itt.image_file_path)
      //           };
      //         }
      //       );
      //       let scanimageData = [arr, arrZc];
      //       let arrl = arr.filter((item) => item.image_files.length > 0);
      //       let arrZcl = arrZc.filter((item) => item.image_files.length > 0);
      //       let flagA = this.internalAssetsCheck && arrl.length <= 0;
      //       let flagB = this.customerTradedayCheck && arrZcl.length <= 0;
      //       if (!flagA && !flagB) {
      //         this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
      //           btnStatus: 2,
      //           data: () => {
      //             this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
      //               scanimageData: JSON.stringify(scanimageData)
      //             });
      //           }
      //         });
      //       }
      //       this.$emit('change', {
      //         scanimageData: JSON.stringify(scanimageData)
      //       });
      //     })
      //     .catch((err) => {
      //       console.log('上传失败上传失败');
      //       file.status = 'failed';
      //       file.message = '上传失败';
      //     });
      // } catch (error) {
      //   file.status = 'failed';
      //   file.message = '上传失败';
      // }
    },

    goNext(scanimageData) {
      if (!this.leaveMarkisChecked) {
        // this.$refs['scrollback'].scrollTop = 1000;
        scrollIntoView(this.$refs['leaveMark'], { block: 'start', behavior: 'smooth', scrollMode: 'aways' });
        _hvueToast({
          mes: '请勾选承诺内容'
        });
        return;
      }
      addClientCritMark({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        markContent:
          '本人承诺以上材料均真实合法有效，若因本人提供材料虚假无效的，将由本人承担全部责任。',
        markType: '16',
        confirmFlag: '1'
      })
        .then((res) => {
          if (res.code == 0) {
            this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
              scanimageData: JSON.stringify(scanimageData)
            });
          } else {
            _hvueToast({
              mes: '操作异常请重试'
            });
          }
        })
        .catch((e) => {
          _hvueToast({
            mes: '操作异常请重试'
          });
        });
    }
  }
};
</script>

<style></style>
