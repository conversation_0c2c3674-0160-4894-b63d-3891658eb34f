<template>
  <section
    v-show="showPage"
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click="back"></a>
        <h1 class="title">证券交易经验证明材料</h1>
      </div>
    </header>
    <article class="content">
      <div class="protocol_box">
        <div class="protocol_cont">
          <p>您可提供以下任意一种或多种材料申请认证:</p>
          <p>
            1）客户以本人合法所有的人民币或外币成功认购银行理财产品（非保本型）、信托产品（包括资金信托和财产权信托）、股票（包括A股、B股、H股）、债券、基金（包括公募和私募，不包括货币基金）、纸黄金白银（通过银行渠道）、保险产品（符合监管要求）、外汇、期货及其他衍生品的经济行为，或通过互联网渠道成功认购上述产品的交易经验；材料需要体现客户实名、购买时间、产品名称及管理人或代销机构业务章/电子章。
          </p>
          <p>
            2）投资者可通过网页截图、APP截图等形式提供的其他书面文件，需要体现客户实名，购买时间，产品名称及管理人或代销机构电子章。
          </p>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="showPage = false">我已知晓</a>
      </div>
    </footer>
  </section>
</template>

<script>
export default {
  data() {
    return {
      showPage: false
    };
  },
  methods: {
    show() {
      this.showPage = true;
    },

    back() {
      this.showPage = false;
    }
  }
};
</script>

<style></style>
