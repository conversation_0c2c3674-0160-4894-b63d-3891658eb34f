<template>
  <div class="cont" style="display: block">
    <ul class="file_upload_list">
      <li v-for="(it, idx) in value" :key="idx">
        <div style="width: 110px; height: 110px">
          <img
            style="width: 100%; height: 100%; object-fit: cover"
            :src="'data:image/jpeg;base64,' + it.base64"
          />
          <div class="van-uploader__preview-delete" @click="deleteImg(idx)">
            <i
              class="van-icon van-icon-cross van-uploader__preview-delete-icon"
            ></i>
          </div>
        </div>
      </li>
      <li v-if="value.length < parseInt(pageMax)">
        <a class="add" @click="clickUpload"><span>添加图片</span></a>
      </li>
    </ul>
    <getImgBoxApp
      ref="getImgBoxThinkive"
      @getImgCallBack="getImgCallBack"
    ></getImgBoxApp>
  </div>
</template>

<script>
import { imageUpload } from '@/service/service.js';
import getImgBoxApp from './getImage.vue';
export default {
  components: {
    getImgBoxApp
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    pageMax: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      fileList: []
    };
  },
  methods: {
    deleteImg(index) {
      this.fileList.splice(index, 1);
      this.$emit('change', this.fileList);
    },
    clickUpload() {
      this.$refs.getImgBoxThinkive.getImg();
      // this.$emit('change', []);
      window.imgCallBack = this.getImgCallBack;
      // let phoneConfig = {
      //   funcNo: '50273',
      //   mode: '0',
      //   isAlbum: '0',
      //   compressSize: '600',
      //   cutFlag: '0',
      //   moduleName: 'open' // 必须为open
      // };
      // console.log(phoneConfig);
      // let result = $h.callMessageNative(phoneConfig);
      // if (result.error_no !== '0') {
      //   console.log({ mes: result.error_info });
      // }
    },

    getImgCallBack(data) {
      console.log(data);
      // _hvueLoading.open();
      if (!data.base64Image) {
        return;
      }
      imageUpload({
        imgContent: data.base64Image,
        flowToken: sessionStorage.getItem('TKFlowToken')
      }).then((res) => {
        this.fileList.push({
          upUrl: res.data,
          url: `${$hvue.customConfig.fileUrl}${res.data}`,
          base64: data.base64Image,
          status: 'done'
        });
        this.$emit('change', this.fileList);
      });
    }
  }
};
</script>

<style></style>
