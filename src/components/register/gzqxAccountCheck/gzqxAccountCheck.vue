<template>
  <article class="content">
    <div class="acct_cond_item">
      <ul class="acct_cond_list">
        <li v-for="(item, index) in accountList" :key="index">
          <div class="tit">
            {{ item.stockAccount
            }}<em class="acct_s_tag">{{ item.holderStatusDesc }}</em>
          </div>
          <span v-if="item.available" class="state_ic_ok"></span>
          <div v-if="item.unavailableDesc.length > 0" class="erro_cont">
            <p v-for="(it, idx) in item.unavailableDesc" :key="idx">
              {{ it }}
            </p>
          </div>
        </li>
      </ul>
    </div>
    <div class="wx_tips">
      <p class="error_color">
        抱歉，您选择的账户存在不满足办理条件，请先满足以上条件后再办理
      </p>
    </div>
  </article>
</template>

<script>
import { gzQxCancelCheck } from '@/service/service';

export default {
  name: 'GzqxAccountCheck',
  data() {
    return {
      accountList: []
    };
  },
  mounted() {
    this.renderingView();
  },
  methods: {
    renderingView() {
      gzQxCancelCheck({
        flowToken: sessionStorage.getItem('TKFlowToken')
      }).then((res) => {
        this.accountList = res.data.gzQxCancelCheckList;
      });
    }
  }
};
</script>
