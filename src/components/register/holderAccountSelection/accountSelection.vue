<template>
  <article class="content" style="background: #f5f6fa" v-if="loadinged">
    <template
      v-for="(
        { isDisabled, accountTypeName, stockAccList }, index
      ) in accountInfoList"
    >
      <div
        v-if="stockAccList.length > 0"
        :key="index + 'com_title'"
        class="com_title"
      >
        <h5>{{ accountTypeName }}</h5>
      </div>
      <div
        v-if="stockAccList.length > 0"
        :key="index + 'acct_status_item'"
        class="acct_status_item"
      >
        <ul class="acct_list">
          <li
            v-for="(
              {
                stockAccount,
                isChecked,
                holderKind,
                holderStatusDesc,
                holderRightsDesc,
                regFlagDesc,
                regflag,
                exchangeType,
                available,
                assetProp,
                mainFlag
              },
              i
            ) in stockAccList"
            :key="i"
            @click.stop="
              selectAcc({
                stockAccList,
                stockAccount,
                exchangeType,
                regflag,
                assetProp,
                isDisabled,
                available,
                isChecked,
                mainFlag,
                holderKind,
                i
              })
            "
          >
            <span
              class="icon_check"
              :class="{ checked: isChecked, disabled: !available }"
            >
              {{ stockAccount }}
              <i class="tag_zhu_span" v-show="!onlyMain && mainFlag === '1'"
                >主</i
              >
              <em v-if="regflag && regflag !== '0'" class="acct_s_tag">{{
                holderStatusDesc
              }}</em
              ><em v-if="regflag && regflag === '0'" class="acct_s_tag">{{
                regFlagDesc
              }}</em>
            </span>
            <span
              class="state"
              v-show="!$attrs.needCancel && holderRightsDesc === '已开通'"
              >{{ holderRightsDesc }}</span
            >
          </li>
        </ul>
      </div>
    </template>
    <template v-if="!$attrs.needCancel">
      <div v-if="bizType040066 && noAccount">
        <div class="acct_emptybox zqzytzz_view">
          <p>
            您当前暂无正常状态特转A账户，不可办理此权限。如需办理，您可拨打客服95310咨询或前往营业部办理。
          </p>
          <p>
            <a class="com_link" @click="queryBranchNo">查找营业部>></a>
          </p>
        </div>
      </div>
      <div v-else-if="noAccount">
        <div
          v-if="xyBizTypeFlag && creditFundAccountExist === '0'"
          style="height: 100vh; background: #ffffff; text-align: center"
        >
          <div class="acct_nodata">
            <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
            <h5>
              您当前暂未开通信用账户，请前往开通信用账户，如有疑问可咨询客服95310。
            </h5>
          </div>
          <div class="bus_txtbox">
            <p>
              如何开通信用账户?
              <a class="link_right_arrow" @click="jumpBusiness('010174')"
                >前往开通</a
              >
            </p>
          </div>
        </div>
        <div
          v-else-if="xyBizTypeFlag && creditBankAccountExist === '0'"
          style="height: 100vh; background: #ffffff; text-align: center"
        >
          <div class="acct_nodata">
            <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
            <h5>
              您当前暂未开通信用三方存管，请先开通信用三方存管后再进行业务办理，如有疑问可咨询客服95310。
            </h5>
          </div>
          <div class="bus_txtbox">
            <p>
              <a class="link_right_arrow" @click="jumpBusiness('010294')"
                >前往开通信用三方存管</a
              >
            </p>
          </div>
        </div>
        <div
          v-else-if="xyBizTypeFlag && creditFundAccountStatus === '0'"
          style="height: 100vh; background: #ffffff; text-align: center"
        >
          <div class="acct_nodata">
            <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
            <h5>
              您当前信用账户异常，暂无法进行业务办理，如有疑问可咨询客服95310。
            </h5>
          </div>
        </div>
        <!-- <div
          v-if="xyBizTypeFlag && !lrErrAccountFlag"
          style="height: 100vh; background: #ffffff; text-align: center"
        >
          <div class="acct_nodata">
            <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
            <h5>您当前暂未开通信用账户或信用账户异常，如有问题可咨询95310。</h5>
          </div>
          <div class="bus_txtbox">
            <p>
              如何开通信用账户?
              <a class="link_right_arrow" @click="showGuide()">前往开通</a>
            </p>
          </div>
        </div>
        <div
          v-else-if="xyBizTypeFlag && lrErrAccountFlag"
          style="height: 100vh; background: #ffffff; text-align: center"
        >
          <div class="acct_nodata">
            <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
            <h5>
              您当前的两融开户流程未完成，请按照原路径继续完成，如有问题可咨询95310。
            </h5>
          </div>
        </div> -->
        <fragment v-else>
          <div class="acct_emptybox">
            <p>您还没有正常状态账户，请前往开通</p>
            <a class="r_link_arrow" @click="gotoOpen">去开通</a>
          </div>
          <div class="tip_txtbox">
            <p>您本地还没有可办理业务的账户，请先前往开通。</p>
          </div>
        </fragment>
      </div>
    </template>
    <div v-if="!hasAccount && btmTips !== ''" class="wx_cm_tips">
      {{ btmTips }}
    </div>
    <div
      v-if="!$attrs.needCancel && !noOpenRights && $attrs.cancelBiztype !== ''"
      class="tip_txtbox spel"
    >
      <p>
        您需要取消权限，请点击
        <a class="com_link" @click="toBizType">取消权限</a>
      </p>
    </div>
    <div v-if="tips" class="acct_list_tips">
      <p class="tit">温馨提示</p>
      <p v-html="tips"></p>
    </div>
    <div v-if="bizType010086" class="acct_list_tips">
      为了方便您了解科创版及融资融券相关业务规则及风险，请点击观看<a
        @click="jumpPage('1')"
        >“科创版及相关规则介绍”</a
      ><span v-show="xyAccountFlag">和</span
      ><a v-show="xyAccountFlag" @click="jumpPage('2')"
        >“融资融券业务知识及风险提示”</a
      >
    </div>
    <div v-if="bizType010221" class="acct_list_tips">
      <p class="tit">温馨提示</p>
      <p>
        开通信用北交所权限，需先开通普通北交所权限/股转一类投资者/股转二类投资者权限。<a
          @click="toBizType({ bizType: '010180' })"
          >前往开通>></a
        >
      </p>
    </div>
  </article>
</template>

<script>
import {
  creditAndFundPwdCheck,
  stockAccountSelect,
  bjhgStockAccountQxCheck
} from '@/service/service';
import { groupBy, values } from 'lodash';
import { creditAccountAndBankQueryV2 } from '@/service/lrService';
import { jumpThirdPartyUrl } from '@/common/util';
import { ASSET_PROP, EXCHANGE_TYPE } from '@/common/enumeration';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'accountSelection',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      bizType: '',
      isAllOpenRights: '',
      noOpenRights: false,
      accountInfoList: [],
      loadinged: false,
      btmTips: '',
      tips: this.$attrs.tips,
      creditFundAccountExist: '', //信用资金账户是否存在：1 存在；0 不存在
      creditFundAccountStatus: '', //信用资金账户状态：1 正常 0 异常
      creditBankAccountExist: '' //信用存管账户是否存在：1 存在；0 不存在
    };
  },
  computed: {
    noAccount() {
      let allAccountList = [];
      this.accountInfoList.forEach((it) => {
        it.stockAccList.forEach((item) => {
          allAccountList.push(item);
        });
      });
      if (allAccountList.length === 0 && this.loadinged) {
        // this.btmTips = '您本地还没有可办理业务的账户，请先前往开通。';
        if (!this.xyBizTypeFlag) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            text: '返回首页',
            btnStatus: 2,
            display: true,
            data: () => {
              this.eventMessage(this, EVENT_NAME.TO_INDEX);
            }
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
        }
        return true;
      } else {
        return false;
      }
    },

    onlyMain() {
      return this.$attrs.is_show_only_main;
    },

    xyAccountFlag() {
      //判断是否存在信用账户
      return this.accountInfoList.some(({ stockAccList }) => {
        return stockAccList.some(
          ({ assetProp }) => assetProp === ASSET_PROP.CREDIT_ACCOUNT
        );
      });
    },

    //信用相关业务标识
    xyBizTypeFlag() {
      return this.bizType010245 || this.bizType010221;
    },

    bizType040066() {
      //机构债券专业投资者权限开通
      return this.bizType === '040066';
    },

    bizType010066() {
      //个人债券专业投资者权限开通
      return this.bizType === '010066';
    },

    bizType010086() {
      //科创版权限开通
      return this.bizType === '010086';
    },

    bizType010254() {
      //报价回购权限开通
      return this.bizType === '010254';
    },

    bizType010268() {
      //报价回购权限取消
      return this.bizType === '010268';
    },

    bizType010255() {
      //签署债券回购协议
      return this.bizType === '010255';
    },

    bizType010245() {
      //信用创业板权限开通
      return this.bizType === '010245';
    },

    bizType010180() {
      //北交所权限开通
      return this.bizType === '010180';
    },

    bizType010285() {
      //北交所权限取消
      return this.bizType === '010285';
    },

    bizType010221() {
      //信用北交所权限开通
      return this.bizType === '010221';
    },

    bizType001151() {
      //设置主股东账户
      return this.bizType === '001151';
    },

    hasAccount() {
      let canSelectAccount = [];
      this.accountInfoList.forEach((it) => {
        it.stockAccList.forEach((item) => {
          if (item.available) {
            canSelectAccount.push(item);
          }
        });
      });
      let allopenAccount = [];
      this.accountInfoList.forEach((it) => {
        it.stockAccList.forEach((item) => {
          if (!this.$attrs.needCancel) {
            if (item.holderRightsDesc === '已开通') {
              allopenAccount.push(item);
            }
          }
        });
      });
      let allAccountList = [];
      this.accountInfoList.forEach((it) => {
        it.stockAccList.forEach((item) => {
          allAccountList.push(item);
        });
      });
      if (allopenAccount.length === 0) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.noOpenRights = true;
      } else {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.noOpenRights = false;
      }
      if (
        allopenAccount.length > 0 &&
        allAccountList.length === allopenAccount.length &&
        this.loadinged
      ) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '返回首页',
          btnStatus: 2,
          display: true,
          data: () => {
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
        this.$TAlert({
          tips: '您当前所有账户均已开通当前业务权限。',
          confirm: () => {
            // this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
        return false;
      }
      if (
        canSelectAccount.length === 0 &&
        allAccountList.length !== 0 &&
        this.loadinged &&
        this.accountInfoList.length !== 0
      ) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.btmTips = '您在本地无可办理的账户，请确认账户状态是否正常。';
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '返回首页',
          btnStatus: 2,
          display: true,
          data: () => {
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
        return false;
      }

      return true;
    },

    openAccList() {
      let arr = [];
      for (let { isDisabled, stockAccList, accountTypeName } of this
        .accountInfoList) {
        if (isDisabled) continue;
        for (let {
          isChecked,
          exchangeType,
          stockAccount,
          holderKind,
          fundAccount,
          assetProp = '0' //assetProp 资产属性 0普通
        } of stockAccList) {
          if (isChecked)
            arr.push({
              exchangeType,
              fundAccount,
              stockAccount,
              assetProp,
              holderKind,
              stockType: accountTypeName
            });
        }
      }
      return arr;
    }
  },
  watch: {
    openAccList: {
      handler(list) {
        if (list.length > 0) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            text: '下一步',
            display: true,
            btnStatus: 2,
            data: async () => {
              const { inProperty } = this.tkFlowInfo();
              const profFlag = inProperty.profFlag;
              if (profFlag === '1' && this.$attrs.needInterceptProf) {
                // 专业投资者不用办理此业务
                this.$TAlert({
                  tips: '专业投资者无需开通此权限',
                  confirm: () => {}
                });
              } else {
                if (
                  this.openAccList.filter((item) => item.assetProp === '7')
                    .length > 0
                ) {
                  if (!this.checkAccount()) {
                    return;
                  }
                  const { flag } = await this.bjhgCheck();
                  if (!flag) return false;
                  creditAndFundPwdCheck()
                    .then((res) => {
                      // 0一致 1不一致
                      this.creditFundAccount = res.data.fundAccount;
                      this.ifExistCreditAccount = res.data.ifExistCreditAccount;
                      this.specialFlag = res.data.specialFlag;
                      if (this.specialFlag === '0') {
                        if (this.$attrs.needCancel) {
                          this.needCancelConfirm('0');
                        } else {
                          this.submitFlowData('0');
                        }
                      } else {
                        if (this.$attrs.needCancel) {
                          this.needCancelConfirm('1');
                        } else {
                          this.submitFlowData('1');
                        }
                      }
                    })
                    .catch();
                } else {
                  if (!this.checkAccount()) {
                    return;
                  }
                  const { flag } = await this.bjhgCheck();
                  if (!flag) return false;

                  if (this.$attrs.needCancel) {
                    this.needCancelConfirm();
                  } else {
                    this.submitFlowData();
                  }
                }
              }
            }
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
        }
      }
    }
  },
  created() {
    window.viewShowCallBack = this.viewShowCallBack;
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  mounted() {
    this.viewShowCallBack();
  },
  methods: {
    viewShowCallBack() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      this.bizType = bizType;
      this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
      if (this.xyBizTypeFlag) {
        this.xycybOpenCheck();
      } else {
        this.renderingView();
      }
    },

    jumpBusiness(bizType = '') {
      if (bizType === '') throw new Error('bizType 不能为空');
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, {
          bizType,
          initJumpMode: '0'
        });
      });
    },

    toBizType({ bizType }) {
      if (bizType) {
        import('@/common/flowMixinV2.js').then((a) => {
          a.initFlow.call(this, {
            bizType,
            initJumpMode: '0'
          });
        });
      } else {
        import('@/common/flowMixinV2.js').then((a) => {
          a.initFlow.call(this, {
            bizType: this.$attrs.cancelBiztype,
            initJumpMode: '0'
          });
        });
      }
    },

    xycybOpenCheck() {
      creditAccountAndBankQueryV2({})
        .then(({ data = {}, code, msg }) => {
          if (code === 0) {
            const {
              creditFundAccountExist = '0', //信用资金账户是否存在：1 存在；0 不存在
              creditFundAccountStatus = '0', //信用资金账户状态：1 正常 0 异常
              creditBankAccountExist = '0' //信用存管账户是否存在：1 存在；0 不存在
            } = data;
            const checkArray = [
              creditFundAccountExist,
              creditFundAccountStatus,
              creditBankAccountExist
            ].some((a) => a !== '1');
            if (checkArray) {
              this.creditFundAccountExist = creditFundAccountExist;
              this.creditFundAccountStatus = creditFundAccountStatus;
              this.creditBankAccountExist = creditBankAccountExist;
              this.accountInfoList = [];
              this.$nextTick(() => {
                this.loadinged = true;
              });
            } else {
              this.renderingView();
            }
          } else {
            return Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },

    showGuide() {
      this.$router.push({
        name: 'creditAccountGuide'
      });
    },

    checkRule(item, ruleObj) {
      let canFlagArr = [];
      Object.keys(ruleObj).forEach((key) => {
        let canFlag = false;
        Object.keys(item).forEach((itkey) => {
          if (itkey === key) {
            if (ruleObj[key].includes(item[itkey])) {
              canFlag = true;
            }
          }
        });
        if (canFlag) {
          canFlagArr.push(canFlag);
        }
      });
      if (canFlagArr.length === Object.keys(ruleObj).length) {
        return true;
      } else {
        return false;
      }
    },

    gotoOpen() {
      let bizType = '010044';
      if (this.bizType010245 || this.bizType010221) {
        bizType = '010099';
      }
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType, initJumpMode: '0' });
      });
      return;
      if ($hvue.platform == 0) {
        window.location.href = toUrl;
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          params: {
            url: toUrl,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      }
    },

    queryBranchNo() {
      if ($hvue.platform == 0) {
        window.location.href =
          window.$hvue.customConfig.thirdPartyUrl.businessDepartment;
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          params: {
            url: window.$hvue.customConfig.thirdPartyUrl.businessDepartment,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      }
    },

    async renderingView() {
      stockAccountSelect({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        filterStockType: this.$attrs.filter_stock_type,
        filterExchangeType: this.$attrs.filter_exchange_type,
        filterHolderKind: this.$attrs.filter_holder_kind,
        filterHolderStatus: this.$attrs.filter_holder_status,
        filterHolderRights: this.$attrs.filter_holder_rights,
        filterRegister: this.$attrs.filter_register,
        filterMainFlag: this.$attrs.filter_main_flag,
        isShowOnlyAvailable: this.$attrs.is_show_only_available ? '1' : '0',
        isQryZd: this.$attrs.is_qry_csdc ? '1' : '0', // 是否查询中登：1 是
        needCancel: this.$attrs.needCancel ? '1' : '0'
      }).then(async ({ data }) => {
        // 根据交易板块类别分类展示类别filterExchangeType
        this.isAllOpenRights = data.isAllOpenRights; //是否所有账号都开通了对应的业务 1全部开通
        let { stockAccList = [], groupVoList = [] } = data;
        if (stockAccList.length !== 0) {
          const groupedData = groupBy(stockAccList, 'exchangeType');
          this.accountInfoList = values(groupedData).map((item) => {
            const stockTypeLabel = this.exchangeTypeTransform(
              item[0].exchangeType
            );
            return {
              accountTypeName: stockTypeLabel + '账户',
              stockAccList: item
                .filter((it) => {
                  if (this.onlyMain && this.checkMainFlag(it)) {
                    return (
                      it.holderStatusDesc === '正常' && it.mainFlag === '1'
                    );
                  } else {
                    return it.holderStatusDesc === '正常';
                  }
                })
                .map(({ regflag = '', ...it }) => {
                  return it;
                })
            };
          });
        } else {
          this.accountInfoList =
            groupVoList.map((item) => {
              return {
                accountTypeName: item.stockTypeLabel + '账户',
                stockAccList: item.saccountQryVoList.filter((it) => {
                  if (this.onlyMain && this.checkMainFlag(it)) {
                    return (
                      it.holderStatusDesc === '正常' && it.mainFlag === '1'
                    );
                  } else {
                    return it.holderStatusDesc === '正常';
                  }
                })
              };
            }) || [];
        }
        // 签署债券回购协议业务需求
        if (this.bizType010255) {
          // 封闭式基金账户是展示接口返回的第一个正常状态的封闭式基金，如果第一条一直是正常，一直要展示这条；如果这个第一条异常了，就展示正常的第二条。
          this.accountInfoList = this.accountInfoList.map((item) => {
            if (
              ['深市场内基金账户', '沪市场内基金账户'].includes(
                item.accountTypeName
              )
            ) {
              if (item.stockAccList.length > 0)
                item.stockAccList = [item.stockAccList[0]];
            }
            return item;
          });
        }
        if (this.accountInfoList.length > 0) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
        }
        this.loadinged = true;
        /*******  北交所权限开通/取消 个性化判断 start *******/
        //优先判断是否有特转A主账账户且状态正常未开通该权限，没有再判断是否有深A主账状态正常未开通该权限（********反馈新增需求）
        if (this.bizType010180 || this.bizType010285) {
          let tzaGroupList = groupVoList.filter(({ saccountQryVoList }) => {
            return saccountQryVoList.some(
              ({ exchangeType, mainFlag, holderStatus }) =>
                EXCHANGE_TYPE.TZA === exchangeType &&
                mainFlag === '1' &&
                holderStatus === '0'
            );
          });
          this.accountInfoList =
            groupVoList
              .filter(({ saccountQryVoList = [] }) => {
                return tzaGroupList.length !== 0 &&
                  tzaGroupList[0].saccountQryVoList.length !== 0
                  ? saccountQryVoList[0].exchangeType === EXCHANGE_TYPE.TZA
                  : saccountQryVoList[0] &&
                      saccountQryVoList[0].exchangeType !== EXCHANGE_TYPE.TZA;
              })
              .map((item) => {
                return {
                  accountTypeName: item.stockTypeLabel + '账户',
                  stockAccList: item.saccountQryVoList.filter(
                    (it) =>
                      it.mainFlag === '1' && it.holderStatusDesc === '正常'
                  )
                };
              }) || [];
        }
        /*******  北交所权限开通个性化判断 end *******/

        /*******  信用北交所权限开通个性化判断 start *******/
        if (this.bizType010221) {
          let tzaGroupList = groupVoList.filter(({ saccountQryVoList }) => {
            return saccountQryVoList.some(
              ({ exchangeType, mainFlag, holderStatus, holderRightsDesc }) =>
                EXCHANGE_TYPE.TZA === exchangeType &&
                mainFlag === '1' &&
                holderStatus === '0' &&
                holderRightsDesc === '已开通'
            );
          });

          this.accountInfoList =
            groupVoList
              .filter(({ saccountQryVoList }) => {
                return saccountQryVoList[0]?.exchangeType === EXCHANGE_TYPE.SZ;
              })
              .map((item) => {
                const openXYBJSFlag =
                  tzaGroupList.length !== 0 &&
                  tzaGroupList[0].saccountQryVoList.length !== 0;
                return {
                  accountTypeName: item.stockTypeLabel + '账户',
                  stockAccList: item.saccountQryVoList
                    .filter(
                      (it) =>
                        it.mainFlag === '1' && it.holderStatusDesc === '正常'
                    )
                    .map((it) => {
                      return {
                        ...it,
                        holderRightsDesc: openXYBJSFlag
                          ? '已开通'
                          : it.holderRightsDesc,
                        available: openXYBJSFlag ? false : it.available
                      };
                    })
                };
              }) || [];
        }
        /*******  信用北交所权限开通个性化判断 end *******/
      });
    },

    checkMainFlag({ exchangeType = '', assetProp = '' }) {
      if (this.bizType010255 && assetProp === ASSET_PROP.FUND_ACCOUNT)
        return false;
      return !(
        ['010066', '040066', '010067', '040067'].includes(this.bizType) &&
        exchangeType === EXCHANGE_TYPE.TZA
      );
    },

    //报价回购校验
    bjhgCheck() {
      let accountListArr = [];
      this.accountInfoList.forEach(({ stockAccList }) => {
        stockAccList.forEach((item) => accountListArr.push(item));
      });
      return new Promise(async (resolve) => {
        let passFlag = true;
        if (this.bizType010254 || this.bizType010268) {
          const accList = accountListArr.filter(({ isChecked }) => isChecked);
          for (const item of accList) {
            try {
              const {
                code,
                data: { flag }
              } = await bjhgStockAccountQxCheck({
                exchangeType: item.exchangeType,
                flowToken: sessionStorage.getItem('TKFlowToken'),
                holderKind: item.holderKind,
                stockAccount: item.stockAccount
              });
              if (code !== 0 || flag === false) {
                passFlag = false;
                break;
              }
            } catch (e) {
              console.log(e);
              passFlag = false;
            }
          }
          if (!passFlag) {
            let tips = this.bizType010254
              ? '对不起，您当前勾选的账户存在当日注销权限，不可进行权限新开，请选择其他账户开通或下个交易日再来。'
              : '对不起，您当前所选账户存在当日新开权限，不可进行权限注销，请下个交易日再来。';
            this.$TAlert({
              title: '温馨提示',
              tips
            });
          }
          resolve({ flag: passFlag });
        } else {
          resolve({ flag: passFlag });
        }
      });
    },

    checkAccount() {
      let result = true;
      let accountListArr = [];
      this.accountInfoList.forEach(({ stockAccList }) => {
        stockAccList.forEach((item) => accountListArr.push(item));
      });
      if (!this.$attrs.needCancel) {
        // 开通时检查
        let xyList = this.openAccList.filter((it) => it.assetProp === '7');
        if (!this.bizType010245 && !this.bizType010221 && xyList.length > 0) {
          xyList.forEach(({ assetProp, exchangeType }) => {
            let sameExchangeNoXY = accountListArr.filter(
              (a) =>
                a.assetProp !== '7' &&
                a.exchangeType === exchangeType &&
                (a.isChecked || a.holderRightsDesc === '已开通')
            );
            if (sameExchangeNoXY.length === 0) {
              this.$TAlert({
                title: '温馨提示',
                tips: '开通信用账户权限前需先开通对应市场普通账户权限，请同时勾选对应市场普通账户。',
                confirmBtn: '我知道了'
              });
              result = false;
            }
          });
        }
      } else {
        // 取消时检查
        let accList = this.openAccList.filter((it) => it.assetProp !== '7');
        // 当前有无信用账户
        if (accList.length > 0) {
          accList.forEach(({ assetProp, exchangeType }) => {
            let flag7 = this.accountInfoList.some(({ stockAccList }) => {
              return (
                stockAccList.filter(
                  (a) => a.assetProp === '7' && a.exchangeType === exchangeType
                ).length > 0
              );
            });
            let sameExchangeNoCheckXY = accountListArr.filter(
              (a) =>
                a.assetProp === '7' &&
                a.exchangeType === exchangeType &&
                a.isChecked
            );
            if (sameExchangeNoCheckXY.length === 0 && flag7) {
              this.$TAlert({
                title: '温馨提示',
                tips: '取消普通账户权限前需先取消对应市场信用账户权限，请同时勾选对应市场信用账户。',
                confirmBtn: '我知道了'
              });
              result = false;
            }
          });
        }
      }
      return result;
    },

    needCancelConfirm(flag = '') {
      if (this.bizType010285) {
        this.queryXYBJSAccountInfo(flag);
        return;
      }
      const { bizName } = this.tkFlowInfo();
      this.$TAlert({
        title: '温馨提示',
        tips: `请确认是否需要${bizName}`,
        hasCancel: true,
        confirmBtn: '确认',
        confirm: () => {
          this.submitFlowData(flag);
        }
      });
    },

    async submitFlowData(creditTPwdSameFlag = '') {
      let ifrOpenkcbRights;
      if (this.bizType010180) {
        const { openFlag } = await this.queryOpenKcbPermission();
        ifrOpenkcbRights = openFlag ? '1' : '0';
      }

      const selectedAccountsData = this.openAccList;
      if (selectedAccountsData?.length > 0) {
        this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
          selectedAccountsData: JSON.stringify(selectedAccountsData),
          credit_t_pwd_same_flag: creditTPwdSameFlag,
          ifrOpenkcbRights
        });
      }
    },

    // 查询是否存在信用北交所权限
    queryXYBJSAccountInfo(creditFlag) {
      const { bizName } = this.tkFlowInfo();
      stockAccountSelect({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        filterStockType: '7|2|0,7|9|0',
        filterHolderStatus: '0',
        filterHolderRights: '$',
        isShowOnlyAvailable: '0'
      })
        .then(({ data }) => {
          let errorTips = `请确认是否需要${bizName}`;
          const flag = data.groupVoList.some((item) => {
            return item.saccountQryVoList.some(
              (it) => it.holderRightsDesc === '已开通'
            );
          });
          if (flag) {
            errorTips =
              '如您取消北交所权限，您的信用北交所权限也会同步取消。请确认是否取消北交所权限?';
          }
          this.$TAlert({
            title: '温馨提示',
            tips: errorTips,
            hasCancel: true,
            confirmBtn: '确认',
            confirm: () => {
              this.submitFlowData(creditFlag);
            }
          });
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },

    // 查询是否开通普通、信用科创板权限
    async queryOpenKcbPermission() {
      try {
        const { data } = await stockAccountSelect({
          flowToken: sessionStorage.getItem('TKFlowToken'),
          filterStockType: '0|1|0,7|1|0',
          filterHolderStatus: '0',
          filterHolderRights: 'O',
          isShowOnlyAvailable: '0'
        });
        if (data.groupVoList?.length > 0) {
          const openFlag = data.groupVoList.some(
            ({ saccountQryVoList = [] }) => {
              return (
                saccountQryVoList.length > 0 &&
                saccountQryVoList.some(({ holderRightsDesc }) => {
                  return holderRightsDesc === '已开通';
                })
              );
            }
          );
          return { openFlag };
        } else {
          return { openFlag: false };
        }
      } catch (err) {
        this.$TAlert({
          tips: err
        });
      }
    },

    async selectAcc(item, accountType) {
      // 未指定账户选择弹框
      if (![this.bizType001151].includes(true) && item.regflag === '0') {
        // 未指定账户
        if (!this.$attrs.needCancel) {
          this.$TAlert({
            tips: '账户为未指定状态时不能开通此权限，请先去规范账户后再来办理业务'
          });
          return;
        } else {
          this.$TAlert({
            tips: '账户为未指定状态时不能取消此权限，请先去规范账户后再来办理业务'
          });
          return;
        }
      }
      if (
        this.bizType010066 &&
        item.exchangeType === '1' &&
        item.regflag === '2'
      ) {
        this.$TAlert({
          tips: '您的账户为新指定状态，请于下一个交易日进行申请权限开通'
        });
        return;
      }
      let {
        stockAccList,
        available,
        assetProp,
        exchangeType,
        isDisabled,
        isChecked,
        mainFlag,
        i
      } = item;
      if (isDisabled || !available) return;
      if (!this.$attrs.is_multiple_choice_acc) {
        stockAccList = stockAccList.map((it) => {
          this.$set(it, 'isChecked', false);
          return it;
        });
      }
      this.$set(stockAccList[i], 'isChecked', !isChecked);
      /* this.$emit('change', {
        selectedAccountsData: JSON.stringify(this.openAccList)
      }); */
    },
    jumpPage(flag) {
      const { appId } = this.$store.state.user;
      if (flag === '1') {
        jumpThirdPartyUrl({
          url: `${$hvue.customConfig.thirdPartyUrl.kcbIntroduction}&app_id=${appId}`
        });
      } else {
        jumpThirdPartyUrl({
          url: `${$hvue.customConfig.thirdPartyUrl.rzrqIntroduction}&app_id=${appId}`
        });
      }
    },
    exchangeTypeTransform(type) {
      return (
        {
          [EXCHANGE_TYPE.SH]: '沪A股东',
          [EXCHANGE_TYPE.SZ]: '深A股东',
          [EXCHANGE_TYPE.TZA]: '特转A'
        }[type] || ''
      );
    }
  }
};
</script>
<style scoped>
.zqzytzz_view {
  display: block;
}
.acct_list_tips .tit {
  font-weight: 700;
}
.acct_list_tips a {
  color: #338aff;
}
.acct_nodata h5 {
  text-align: left;
  font-size: 0.15rem;
  color: #333333;
}
</style>
