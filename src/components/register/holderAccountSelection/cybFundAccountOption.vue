<template>
  <article class="content" style="background: #f5f6fa">
    <template
      v-for="(
        { isDisabled, accountTypeName, stockAccList }, index
      ) in accountInfoList"
    >
      <div
        v-if="stockAccList.length > 0"
        :key="index + 'com_title'"
        class="com_title"
      >
        <h5>{{ accountTypeName }}</h5>
      </div>
      <div
        v-if="stockAccList.length > 0"
        :key="index + 'acct_status_item'"
        class="acct_status_item"
      >
        <ul class="acct_list">
          <li
            v-for="(
              {
                stockAccount,
                isChecked,
                holderStatusDesc,
                holderRightsDesc,
                regFlagDesc,
                regflag,
                exchangeType,
                available,
                assetProp,
                mainFlag
              },
              i
            ) in stockAccList"
            :key="i"
            @click.stop="
              selectAcc({
                stockAccList,
                stockAccount,
                exchangeType,
                regflag,
                assetProp,
                isDisabled,
                available,
                isChecked,
                mainFlag,
                i
              })
            "
          >
            <span
              class="icon_check"
              :class="{ checked: isChecked, disabled: !available }"
            >
              {{ stockAccount }}
              <i class="tag_zhu_span" v-show="!onlyMain && mainFlag === '1'">主</i>
              <em v-if="regflag !== '0'" class="acct_s_tag">{{
                holderStatusDesc
              }}</em
              ><em v-if="regflag === '0'" class="acct_s_tag"
                >{{ regFlagDesc
                }}</em
              >
              </span>
            <span class="state" v-show="!$attrs.needCancel && holderRightsDesc === '已开通'">{{ holderRightsDesc }}</span>
          </li>
        </ul>
      </div>
    </template>
    <div v-if="!noAccount && tips" class="acct_list_tips" v-html="tips" />
    <template v-if="!$attrs.needCancel">
      <div v-if="noAccount" class="acct_emptybox">
        <p>您还没有正常状态账户，请前往开通</p>
        <a class="r_link_arrow" @click="gotoOpen">去开通</a>
      </div>
      <div v-if="noAccount" class="tip_txtbox">
        <p>您本地还没有可办理业务的账户，请先前往开通。</p>
      </div>
    </template>
  </article>
</template>

<script>
import {
  creditAndFundPwdCheck,
  cybAccountOpenCheck,
  businessStrategyCheck,
  stockAccountSelect
} from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'CybFundAccountOption',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      subBizType: '', //0 权限已开通；1 新开；2 转签；3 补签
      noOpenRights: false,
      accountInfoList: [],
      loadinged: false,
      btmTips: '',
      tips: this.$attrs.tips,
      zdTimeFlag: false
    };
  },
  computed: {
    onlyMain() {
      return this.$attrs.is_show_only_main;
    },
    noAccount() {
      let allAccountList = [];
      this.accountInfoList.forEach((it) => {
        it.stockAccList.forEach((item) => {
          allAccountList.push(item);
        });
      });
      if (allAccountList.length === 0 && this.loadinged) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '返回首页',
          btnStatus: 2,
          display: true,
          data: () => {
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
        return true;
      } else {
        return false;
      }
    },

    allOpenAccFlag() {
      let allopenAccount = [];
      this.accountInfoList.forEach((it) => {
        it.stockAccList.forEach((item) => {
          if (this.subBizType !== '3' && item.holderRightsDesc === '已开通') {
            allopenAccount.push(item);
          }
        });
      });
      return allopenAccount.length > 0 && this.loadinged;
    },

    openAccList() {
      let arr = [];
      for (let { isDisabled, stockAccList, accountTypeName } of this
        .accountInfoList) {
        if (isDisabled) continue;
        for (let {
          isChecked,
          exchangeType,
          stockAccount,
          holderKind,
          fundAccount,
          holderRights,
          assetProp = '0' //assetProp 资产属性 0普通
        } of stockAccList) {
          if (isChecked || (this.subBizType === '3' && this.zdTimeFlag))
            arr.push({
              exchangeType,
              fundAccount,
              stockAccount,
              assetProp,
              holderKind,
              holderRights,
              stockType: accountTypeName
            });
        }
        return arr;
      }
    }
  },
  watch: {
    openAccList: {
      handler(arr) {
        const text = this.subBizType === '3' ? '签署新版风险揭示书' : '下一步';
        if (arr.length > 0) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            text,
            display: true,
            btnStatus: 2,
            data: () => {
              if (arr.filter((item) => item.assetProp === '7').length > 0) {
                if (!this.checkAccount()) {
                  return;
                }
                creditAndFundPwdCheck()
                  .then((res) => {
                    // 0一致 1不一致
                    this.creditFundAccount = res.data.fundAccount;
                    this.ifExistCreditAccount = res.data.ifExistCreditAccount;
                    this.specialFlag = res.data.specialFlag;
                    if (this.specialFlag === '0') {
                      if (this.$attrs.needCancel) {
                        // 取消时二次弹窗确认
                        const { bizName } = this.tkFlowInfo();
                        this.$TAlert({
                          title: '温馨提示',
                          tips: `请确认是否需要${bizName}`,
                          hasCancel: true,
                          confirmBtn: '确认',
                          confirm: () => {
                            this.submitFlowData();
                          }
                        });
                      } else {
                        this.submitFlowData();
                      }
                    } else {
                      if (this.$attrs.needCancel) {
                        // 取消时二次弹窗确认
                        const { bizName } = this.tkFlowInfo();
                        this.$TAlert({
                          title: '温馨提示',
                          tips: `请确认是否需要${bizName}`,
                          hasCancel: true,
                          confirmBtn: '确认',
                          confirm: () => {
                            this.submitFlowData();
                          }
                        });
                      } else {
                        this.submitFlowData();
                      }
                    }
                  })
                  .catch();
              } else {
                if (!this.checkAccount()) {
                  return;
                }
                if (this.$attrs.needCancel) {
                  // 取消时二次弹窗确认
                  const { bizName } = this.tkFlowInfo();
                  this.$TAlert({
                    title: '温馨提示',
                    tips: `请确认是否需要${bizName}`,
                    hasCancel: true,
                    confirmBtn: '确认',
                    confirm: () => {
                      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
                        selectedAccountsData: JSON.stringify(arr),
                        subBizType: this.subBizType
                      });
                    }
                  });
                } else {
                  this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
                    selectedAccountsData: JSON.stringify(arr),
                    subBizType: this.subBizType
                  });
                }
              }
            }
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0, text });
        }
      }
    },
    allOpenAccFlag: {
      handler(flag) {
        if (flag) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            text: '返回首页',
            btnStatus: 2,
            display: true,
            data: () => {
              this.eventMessage(this, EVENT_NAME.TO_INDEX);
            }
          });
          this.$TAlert({
            tips: '您当前所有账户均已开通当前业务权限。'
          });
        }
      }
    }
  },
  created() {
    window.viewShowCallBack = this.viewShowCallBack;
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  mounted() {
    $h.clearSession('addShareHoldAccount');
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
    this.renderingView();
  },
  methods: {
    viewShowCallBack() {
      this.renderingView();
    },

    toBizType() {
      import('@/common/flowMixin.js').then((a) => {
        a.initFlow.call(this, this.$attrs.cancelBiztype);
      });
    },

    checkRule(item, ruleObj) {
      let canFlagArr = [];
      Object.keys(ruleObj).forEach((key) => {
        let canFlag = false;
        Object.keys(item).forEach((itkey) => {
          if (itkey === key) {
            if (ruleObj[key].includes(item[itkey])) {
              canFlag = true;
            }
          }
        });
        if (canFlag) {
          canFlagArr.push(canFlag);
        }
      });
      if (canFlagArr.length === Object.keys(ruleObj).length) {
        return true;
      } else {
        return false;
      }
    },

    gotoOpen() {
      // 跳转至补开股东户
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010044', initJumpMode: '0' });
      });
      /* if ($hvue.platform == 0) {
        // 支付宝渠道
        if ($h.getSession('channelType') === '2095000000000') {
          AlipayJSBridge.call('pushWindow', {
            url:
              window.$hvue.customConfig.targetUrl +
              '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=stockAForWeb' +
              '?sd_token=' +
              $h.getSession('authorization'),
            param: {
              readTitle: true
            }
          });
        } else {
          window.location.href =
            window.$hvue.customConfig.targetUrl +
            '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=stockAForWeb';
        }
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          // targetModule: 'open',
          params: {
            url:
              window.$hvue.customConfig.targetUrl +
              '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=stockAForWeb' +
              '&sd_token=' +
              $h.getSession('authorization'),
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      } */
    },

    async renderingView() {
      // 查询是否中登时间
      try {
        const {
          data: { strategyResult }
        } = await businessStrategyCheck({
          strategyNo: 'wt_business_cyb_zd_time_check',
          flowToken: sessionStorage.getItem('TKFlowToken')
        });
        if (strategyResult === '1') {
          this.zdTimeFlag = true;
          this._cybAccountOpenCheck();
        } else {
          this.zdTimeFlag = false;
          this._stockAccountSelect();
        }
      } catch (error) {
        this.$TAlert({
          tips: error
        });
      }
    },
    _cybAccountOpenCheck() {
      cybAccountOpenCheck({})
        .then(async ({ data }) => {
          this.subBizType = data.subBizType; //0 权限已开通；1 新开；2 转签；3 补签
          this.accountInfoList = [];
          this.accountInfoList.push({
            accountTypeName: '深A股东账户',
            stockAccList: [{ ...data.stockAccount }]
              .filter(
                (it) => it.holderStatusDesc === '正常' && it.mainFlag === '1'
              )
              .map((it) => {
                const openFlag = ['0', '3'].includes(this.subBizType);
                it.isChecked = false;
                it.holderRightsDesc = openFlag ? '已开通' : '未开通';
                it.available = !openFlag;
                return it;
              })
          });
          this.loadinged = true;
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },
    _stockAccountSelect() {
      stockAccountSelect({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        filterStockType: this.$attrs.filter_stock_type,
        filterExchangeType: this.$attrs.filter_exchange_type,
        filterHolderKind: this.$attrs.filter_holder_kind,
        filterHolderStatus: this.$attrs.filter_holder_status,
        filterHolderRights: this.$attrs.filter_holder_rights,
        filterRegister: this.$attrs.filter_register,
        filterMainFlag: this.$attrs.filter_main_flag,
        isShowOnlyAvailable: this.$attrs.is_show_only_available ? '1' : '0',
        isQryZd: this.$attrs.is_qry_csdc ? '1' : '0', // 是否查询中登：1 是
        needCancel: this.$attrs.needCancel ? '1' : '0'
      })
        .then(async ({ data }) => {
          // 根据交易板块类别分类展示类别filterExchangeType
          if (data.stockAccList) {
            this.accountInfoList = [];
          } else {
            this.accountInfoList =
              data.groupVoList.map((item) => {
                return {
                  accountTypeName: item.stockTypeLabel + '账户',
                  stockAccList: item.saccountQryVoList.filter((it) => {
                    if (this.$attrs.is_show_only_main) {
                      return (
                        it.holderStatusDesc === '正常' && it.mainFlag === '1'
                      );
                    } else {
                      return it.holderStatusDesc === '正常';
                    }
                  })
                };
              }) || [];
          }
          const { inProperty } = this.tkFlowInfo();
          this.subBizType = inProperty.subBizType;
          this.loadinged = true;
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },
    checkAccount() {
      let result = true;
      let accountListArr = [];
      this.accountInfoList.forEach(({ stockAccList }) => {
        stockAccList.forEach((item) => accountListArr.push(item));
      });
      if (!this.$attrs.needCancel) {
        // 开通时检查
        let xyList = this.openAccList.filter((it) => it.assetProp === '7');
        if (xyList.length > 0) {
          xyList.forEach(({ assetProp, exchangeType }) => {
            let sameExchangeNoXY = accountListArr.filter(
              (a) =>
                a.assetProp !== '7' &&
                a.exchangeType === exchangeType &&
                (a.isChecked || a.holderRightsDesc === '已开通')
            );
            if (sameExchangeNoXY.length === 0) {
              this.$TAlert({
                title: '温馨提示',
                tips: '开通信用账户权限前需先开通对应市场普通账户权限，请同时勾选对应市场普通账户。',
                confirmBtn: '我知道了'
              });
              result = false;
            }
          });
        }
      } else {
        // 取消时检查
        let accList = this.openAccList.filter((it) => it.assetProp !== '7');
        // 当前有无信用账户
        if (accList.length > 0) {
          accList.forEach(({ assetProp, exchangeType }) => {
            let flag7 = this.accountInfoList.some(({ stockAccList }) => {
              return (
                stockAccList.filter(
                  (a) => a.assetProp === '7' && a.exchangeType === exchangeType
                ).length > 0
              );
            });
            let sameExchangeNoCheckXY = accountListArr.filter(
              (a) =>
                a.assetProp === '7' &&
                a.exchangeType === exchangeType &&
                a.isChecked
            );
            if (sameExchangeNoCheckXY.length === 0 && flag7) {
              this.$TAlert({
                title: '温馨提示',
                tips: '取消普通账户权限前需先取消对应市场信用账户权限，请同时勾选对应市场信用账户。',
                confirmBtn: '我知道了'
              });
              result = false;
            }
          });
        }
      }
      return result;
    },

    submitFlowData() {
      const selectedAccountsData = this.openAccList;
      if (selectedAccountsData?.length > 0) {
        this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
          selectedAccountsData: JSON.stringify(selectedAccountsData),
          subBizType: this.subBizType
        });
      }
    },

    selectAcc(item) {
      // 未指定账户选择弹框
      if (item.regflag === '0') {
        // 未指定账户
        if (!this.$attrs.needCancel) {
          this.$TAlert({
            tips: '账户为未指定状态时不能开通此权限，请先去规范账户后再来办理业务'
          });
          return;
        } else {
          this.$TAlert({
            tips: '账户为未指定状态时不能取消此权限，请先去规范账户后再来办理业务'
          });
          return;
        }
      }
      const { stockAccList, available, isDisabled, isChecked, i } = item;
      if (isDisabled || !available) return;
      this.$set(stockAccList[i], 'isChecked', !isChecked);
      this.$emit('change', {
        selectedAccountsData: JSON.stringify(this.openAccList),
        subBizType: this.subBizType
      });
    }
  }
};
</script>
