<template>
  <section
    class="main fixed"
    :class="{ white_bg: notOpenFlag || pageStep === 1 }"
    data-page="home"
    style="position: fixed; z-index: 1400"
    v-if="!loading"
  >
    <header class="header">
      <div class="top_height"></div>
      <div class="header_inner">
        <a class="icon_back" @click="goBack()"></a>
        <h1 class="title">股权激励授权签署</h1>
      </div>
    </header>
    <article class="content" v-if="pageStep === 0">
      <div
        v-if="
          normalGroupVoList.length !== 0 &&
          normalOpenFlag &&
          (!notOpenFlag || historyGroupVoList.length !== 0)
        "
      >
        <div class="com_title">
          <h5>请选择股权激励授权的账户</h5>
        </div>
        <div
          class="acct_status_item"
          v-for="({ companyName, listInfo }, index) in normalGroupVoList"
          :key="index"
        >
          <div class="tit">
            <h5 class="pr_0">上市公司：{{ companyName }}</h5>
          </div>
          <template v-if="listInfo.length !== 0">
            <ul class="acct_list" v-for="(it, i) in listInfo" :key="i">
              <li @click="selectLi(it, listInfo)">
                <span
                  class="icon_check"
                  :class="{ checked: it.isChecked, disabled: !it.available }"
                  >{{ it.exchangeTypeDesc }}：{{ it.stockAccount }}
                  {{ it.mainFlag === '1' ? '(主)' : '' }}
                  <em class="acct_s_tag">{{ it.holderStatusDesc }}</em></span
                ><span
                  class="state"
                  v-show="it.holderRightsDesc === '已开通'"
                  >{{ it.signingStatusDesc }}</span
                >
              </li>
            </ul>
          </template>
          <div class="acct_emptybox" v-else>
            <p>您还没有正常状态账户</p>
            <a class="r_link_arrow" @click="toOpen()">前往开通</a>
          </div>
        </div>
        <div class="com_tips">
          <p class="txt_center">
            <a class="com_link" @click="pageStep = 1">历史记录</a>
          </p>
        </div>
      </div>
      <div v-else-if="!normalOpenFlag && normalGroupVoList.length !== 0">
        <div class="com_title">
          <h5>选择开通的账户</h5>
        </div>
        <div class="acct_emptybox">
          <p>您还没有正常状态账户</p>
          <a class="r_link_arrow" @click="toOpen()">前往开通</a>
        </div>
        <div class="tip_txtbox">
          <p>您本地还没有可办理业务的账户，请先前往开通。</p>
        </div>
      </div>
      <div v-else>
        <div class="acct_nodata">
          <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
          <h5>当前暂无需要签署股权激励授权书的账户</h5>
        </div>
        <div class="txt_center">
          <a class="com_btn border" @click="pageStep = 1">历史记录</a>
        </div>
      </div>
    </article>
    <article class="content" v-else>
      <div v-if="historyGroupVoList.length !== 0">
        <div class="com_title">
          <h5>历史记录</h5>
        </div>
        <div
          class="bus_recordlist"
          v-for="(it, index) in historyGroupVoList"
          :key="index"
        >
          <div
            class="bus_record_item"
            v-for="(
              item, i
            ) in it.obtainTheListOfListedCompaniesToBeSignedVOList"
            :key="i"
          >
            <div class="title">
              <h5>{{ item.exchangeTypeDesc }}：{{ item.stockAccount }}</h5>
            </div>
            <div class="cont">
              <p>签署状态：{{ item.signingStatusDesc }}</p>
              <p>上市公司：{{ item.companyName }}</p>
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <div class="acct_nodata">
          <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
          <h5>暂无历史签署记录</h5>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn block" v-if="pageStep === 0">
        <a
          v-if="
            normalGroupVoList.length !== 0 &&
            normalOpenFlag &&
            (!notOpenFlag || historyGroupVoList.length !== 0)
          "
          class="p_button"
          :class="{ disabled: submitList.length === 0 }"
          @click="submitInfo"
          >下一步</a
        >
        <a
          v-else-if="!normalOpenFlag && normalGroupVoList.length !== 0"
          class="p_button"
          @click="toIndex"
          >返回</a
        >
      </div>
      <div class="ce_btn block" v-else-if="historyGroupVoList.length !== 0">
        <a class="p_button" @click="pageStep = 0">返回</a>
      </div>
    </footer>
  </section>
</template>

<script>
import { eqIncCompanyQry } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'eqIncPrdList',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      normalGroupVoList: [],
      historyGroupVoList: [],
      loading: true,
      pageStep: 0 // 0办理列表，1历史记录
    };
  },
  computed: {
    normalOpenFlag() {
      return (
        (this.normalGroupVoList.length !== 0 && !this.notOpenFlag) ||
        this.historyGroupVoList.length !== 0
      );
    },
    notOpenFlag() {
      return this.normalGroupVoList.every(
        ({ listInfo, isOpen = '' }) => isOpen === '1' && listInfo.length === 0
      );
    },
    submitList() {
      let arr = [];
      for (let { listInfo } of this.normalGroupVoList) {
        for (const { isChecked, ...it } of listInfo) {
          if (isChecked) {
            arr.push({
              marketCompany: it.companyName,
              companyCode: it.companyCode,
              acodeAccount: it.acodeAccount,
              stockAccount: it.stockAccount,
              holderKind: it.holderKind,
              fundAccount: it.fundAccount,
              exchangeType: it.exchangeType,
              assetProp: it.assetProp
            });
          }
        }
      }
      return arr;
    }
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
    this.renderingView();
  },
  methods: {
    renderingView() {
      eqIncCompanyQry({
        flowToken: sessionStorage.getItem('TKFlowToken')
      })
        .then(({ code, msg, data }) => {
          if (code === 0) {
            this.loading = false;
            const { normalGroupVoList = [], historyGroupVoList = [] } = data;
            if (normalGroupVoList.length !== 0) {
              this.normalGroupVoList = normalGroupVoList.map((it) => {
                return {
                  companyCode: it.companyCode,
                  companyName: it.companyName,
                  isOpen: it.isOpen,
                  listInfo:
                    it.obtainTheListOfListedCompaniesToBeSignedVOList.map(
                      (item) => {
                        return {
                          ...item,
                          isChecked: false,
                          available: item.signingStatus === '0'
                        };
                      }
                    )
                };
              });
            }
            if (historyGroupVoList.length !== 0) {
              this.historyGroupVoList = historyGroupVoList;
            }
          } else {
            return Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            title: '温馨提示',
            tips: err,
            confirm: this.toIndex
          });
        });
    },
    selectLi(item, list) {
      if (!item.available) return;
      const flag = list.some(
        ({ isChecked, stockAccount }) =>
          isChecked && stockAccount !== item.stockAccount
      );
      if (flag) {
        this.$TAlert({
          title: '温馨提示',
          tips: '每个公司只能选择一个账户，请您重新选择。'
        });
        return;
      }
      this.$set(item, 'isChecked', !item.isChecked);
    },
    submitInfo() {
      if (this.submitList.length !== 0) {
        this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
          selectedAccountsData: JSON.stringify(this.submitList)
        });
      }
    },
    toIndex() {
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    },
    toOpen() {
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010044', initJumpMode: '0' });
      });
      return;
    },
    goBack() {
      if (this.pageStep === 0) {
        this.toIndex();
      } else {
        this.pageStep--;
      }
    }
  }
};
</script>

<style></style>
