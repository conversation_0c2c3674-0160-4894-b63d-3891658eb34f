<template>
  <article class="content" style="background: #f5f6fa">
    <div
      v-for="(
        { isDisabled, tip, accountTypeName, stockAccList }, index
      ) in accountInfoList"
      :key="index"
      class="acct_status_item"
    >
      <div class="tit">
        <h5>{{ accountTypeName }}</h5>
      </div>
      <ul class="acct_list">
        <li
          v-for="(
            {
              stockAccount,
              isChecked,
              holderStatusDesc,
              holderStatus,
              available
            },
            i
          ) in stockAccList"
          :key="i"
          @click.stop="selectAcc(stockAccount, accountTypeName)"
        >
          <span
            class="icon_check"
            :class="{ checked: isChecked, disabled: !available }"
          >
            {{ stockAccount }}<em class="acct_s_tag">{{ holderStatusDesc }}</em>
          </span>
          <span class="state">已开通</span>
        </li>
      </ul>
    </div>
    <div v-if="hasAccount" class="tip_txtbox">
      <p>
        温馨提示：如果关闭权限，您将在T+1日于我司无法进行买入委托，卖出不受影响，如有疑问请致电您的客户经理或我司客服95503。
      </p>
    </div>
    <div v-if="!hasAccount" class="wx_cm_tips">{{ btmTips }}</div>
  </article>
</template>

<script>
import { stockAccountQry } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'GzqxAccountSelection',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      loadinged: false,
      accountInfoList: []
    };
  },
  computed: {
    hasAccount() {
      let canSelectAccount = [];
      this.accountInfoList.forEach((it) => {
        it.stockAccList.forEach((item) => {
          if (item.available) {
            canSelectAccount.push(item);
          }
        });
      });
      let allopenAccount = [];
      this.accountInfoList.forEach((it) => {
        it.stockAccList.forEach((item) => {
          if (item.holderStatusDesc === '已开通') {
            allopenAccount.push(item);
          }
        });
      });
      let allAccountList = [];
      this.accountInfoList.forEach((it) => {
        it.stockAccList.forEach((item) => {
          allAccountList.push(item);
        });
      });
      if (
        allopenAccount.length > 0 &&
        allAccountList.length === allopenAccount.length &&
        this.loadinged
      ) {
        this.$TAlert({
          tips: '您当前所有账户均已开通当前业务权限。',
          confirm: () => {
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
        return false;
      }
      if (this.accountInfoList.length === 0 && this.loadinged) {
        this.btmTips = '您本地还没有可办理业务的账户，请先前往开通。';
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '返回首页',
          btnStatus: 2,
          display: true,
          data: () => {
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
        return false;
      }
      if (canSelectAccount.length === 0 && this.loadinged) {
        this.btmTips = '您在本地无可办理的账户，请确认账户状态是否正常。';
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '返回首页',
          btnStatus: 2,
          display: true,
          data: () => {
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
        return false;
      }
      return true;
    },

    openAccList() {
      let arr = [];
      for (let { isDisabled, stockAccList } of this.accountInfoList) {
        if (isDisabled) continue;
        for (let {
          isChecked,
          exchangeType,
          stockAccount,
          holderKind,
          assetProp = '0' //assetProp 资产属性 0普通
        } of stockAccList) {
          if (isChecked)
            arr.push({ exchangeType, stockAccount, assetProp, holderKind });
        }
      }
      if (arr.length > 0) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 1 });
      } else {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
      }
      return arr;
    }
  },
  mounted() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
    this.renderingView();
  },
  methods: {
    async renderingView() {
      const { inProperty } = this.tkFlowInfo();
      const { clientId, bizType, branchNo } = inProperty;
      stockAccountQry({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        filterAssetProp: this.$attrs.filter_asset_prop,
        filterExchangeType: this.$attrs.filter_exchange_type,
        filterHolderKind: this.$attrs.filter_holder_kind,
        filterHolderStatus: this.$attrs.filter_holder_status,
        filterHolderRights: this.$attrs.filter_holder_rights,
        filterRegister: this.$attrs.filter_register,
        filterMainFlag: this.$attrs.filter_main_flag,
        isShowOnlyAvailable: this.$attrs.is_show_only_available ? '1' : '0',
        isQryZd: this.$attrs.is_qry_csdc ? '1' : '0' // 是否查询中登：1 是
      })
        .then(({ data }) => {
          /*
           *  assetProp // 资产属性
           *  available // 是否可选
           *  exchangeType // 交易类别
           *  holderKind // 账户类别
           *  holderRights // 股东权限
           *  holderStatus // 股东账户状态
           *  mainFlag // 主账户标志
           *  regflag //是否指定
           *  local // 是否本地账户
           * 股转账户(特转A，不用考虑特转B) 交易类别：9 资产属性：0   账户类别：0
           * 深A股账户 exchangeType = 2；holderKind = 0
           * 沪A股东账户 exchangeType = 1；holderKind = 0
           * */
          this.accountInfoList.push({
            accountTypeName: '股转市场证券账户',
            stockAccList: data.stockAccList
          });
          this.loadinged = true;
          this.accountInfoList = this.accountInfoList.map((item) => {
            item.stockAccList = item.stockAccList.filter(({ local }) => {
              let hasLocal = item.stockAccList.some((a) => a.local);
              return hasLocal === local;
            });
            return item;
          });
          this.hasAccount;
        })
        .catch((err) => {
          this.$TAlert({
            tips: err,
            confirm: () => {
              this.eventMessage(this, EVENT_NAME.TO_INDEX);
            }
          });
        });
    },

    selectAcc(stockAcc, accountType) {
      this.accountInfoList.forEach((item) => {
        item.stockAccList.forEach((it) => {
          if (it.stockAccount !== stockAcc) {
            this.$set(it, 'isChecked', false);
          }
        });
      });
      for (let { isDisabled, stockAccList } of this.accountInfoList) {
        if (isDisabled) continue;
        for (let item of stockAccList) {
          if (item.stockAccount === stockAcc) {
            if (!item.available) {
              break;
            }
            if (item.isChecked) {
              this.$set(item, 'isChecked', false);
            } else {
              this.$set(item, 'isChecked', true);
            }
            break;
          }
        }
      }
      this.$emit('change', {
        selectedAccountsData: JSON.stringify(this.openAccList)
      });
    }
  }
};
</script>
