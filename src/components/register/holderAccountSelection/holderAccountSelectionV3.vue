<template>
  <article class="content" style="background: #f5f6fa">
    <template
      v-for="(
        { isDisabled, tip, accountTypeName, stockAccList }, index
      ) in accountInfoList"
    >
      <div
        v-if="stockAccList.length > 0"
        :key="index + 'com_title'"
        class="com_title"
      >
        <h5>{{ accountTypeName }}</h5>
      </div>
      <div
        v-if="stockAccList.length > 0"
        :key="index + 'acct_status_item'"
        class="acct_status_item"
      >
        <ul class="acct_list">
          <li
            v-for="(
              {
                stockAccount,
                isChecked,
                holderStatusDesc,
                holderRightsDesc,
                regFlagDesc,
                regflag,
                exchangeType,
                available,
                assetProp,
                mainFlag
              },
              i
            ) in stockAccList"
            :key="i"
            @click.stop="
              selectAcc({
                stockAccList,
                stockAccount,
                exchangeType,
                regflag,
                assetProp,
                isDisabled,
                available,
                isChecked,
                mainFlag,
                i
              })
            "
          >
            <span
              class="icon_check"
              :class="{ checked: isChecked, disabled: !available }"
            >
              {{ stockAccount }}
              <i class="tag_zhu_span" v-show="!onlyMain && mainFlag === '1'">主</i>
              <em v-if="regflag !== '0'" class="acct_s_tag">{{
                holderStatusDesc
              }}</em
              ><em v-if="regflag === '0'" class="acct_s_tag"
                >{{ regFlagDesc
                }}</em
              >
              </span>
            <span class="state" v-show="!$attrs.needCancel && holderRightsDesc === '已开通'">{{ holderRightsDesc }}</span>
          </li>
        </ul>
      </div>
    </template>
    <div v-if="!noAccount && tips" class="acct_list_tips" v-html="tips" />
    <!-- <template v-if="!$attrs.needCancel">
      <div v-if="noAccount" class="acct_emptybox">
        <p>您还没有账户，请先前往开通</p>
        <a class="r_link_arrow" href="#">去开通</a>
      </div>
      <div v-if="noAccount" class="tip_txtbox">
        <p>您本地还没有可办理业务的账户，请先前往开通。</p>
      </div>
    </template> -->
    <template v-if="!$attrs.needCancel">
      <div v-if="noAccount" class="acct_emptybox">
        <p>您还没有正常状态账户，请前往开通</p>
        <a class="r_link_arrow" @click="gotoOpen">去开通</a>
      </div>
      <div v-if="noAccount" class="tip_txtbox">
        <p>您本地还没有可办理业务的账户，请先前往开通。</p>
      </div>
    </template>
    <div v-if="!hasAccount" class="wx_cm_tips">
      {{ btmTips }}
    </div>
    <div v-if="!$attrs.needCancel && !noOpenRights" class="tip_txtbox spel">
      <p>
        您需要取消权限，请点击
        <a class="com_link" @click="toBizType">取消权限</a>
      </p>
    </div>
  </article>
</template>

<script>
import { creditAndFundPwdCheck, stockAccountSelect } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'HolderAccountSelectionV3',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      isAllOpenRights: '',
      noOpenRights: false,
      accountInfoList: [],
      loadinged: false,
      btmTips: '',
      tips: this.$attrs.tips
    };
  },
  computed: {
    onlyMain() {
      return this.$attrs.is_show_only_main;
    },
    noAccount() {
      let allAccountList = [];
      this.accountInfoList.forEach((it) => {
        it.stockAccList.forEach((item) => {
          allAccountList.push(item);
        });
      });
      if (allAccountList.length === 0 && this.loadinged) {
        // this.btmTips = '您本地还没有可办理业务的账户，请先前往开通。';
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '返回首页',
          btnStatus: 2,
          display: true,
          data: () => {
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
        return true;
      } else {
        return false;
      }
    },

    hasAccount() {
      let canSelectAccount = [];
      this.accountInfoList.forEach((it) => {
        it.stockAccList.forEach((item) => {
          if (item.available) {
            canSelectAccount.push(item);
          }
        });
      });
      let allopenAccount = [];
      this.accountInfoList.forEach((it) => {
        it.stockAccList.forEach((item) => {
          if (!this.$attrs.needCancel) {
            if (item.holderRightsDesc === '已开通') {
              allopenAccount.push(item);
            }
          }
        });
      });
      let allAccountList = [];
      this.accountInfoList.forEach((it) => {
        it.stockAccList.forEach((item) => {
          allAccountList.push(item);
        });
      });
      if (allopenAccount.length === 0) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.noOpenRights = true;
      } else {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.noOpenRights = false;
      }
      if (
        allopenAccount.length > 0 &&
        allAccountList.length === allopenAccount.length &&
        this.loadinged
      ) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
        this.$TAlert({
          tips: '您当前所有账户均已开通当前业务权限。',
          confirm: () => {
            // this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
        return false;
      }
      if (
        canSelectAccount.length === 0 &&
        allAccountList.length !== 0 &&
        this.loadinged &&
        this.accountInfoList.length !== 0
      ) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.btmTips = '您在本地无可办理的账户，请确认账户状态是否正常。';
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '返回首页',
          btnStatus: 2,
          display: true,
          data: () => {
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
        return false;
      }

      return true;
    },

    openAccList() {
      let arr = [];
      for (let { isDisabled, stockAccList, accountTypeName } of this
        .accountInfoList) {
        if (isDisabled) continue;
        for (let {
          isChecked,
          exchangeType,
          stockAccount,
          holderKind,
          fundAccount,
          assetProp = '0' //assetProp 资产属性 0普通
        } of stockAccList) {
          if (isChecked)
            arr.push({
              exchangeType,
              fundAccount,
              stockAccount,
              assetProp,
              holderKind,
              stockType: accountTypeName
            });
        }
      }
      if (arr.length > 0) {
        // this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 1 });
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '下一步',
          display: true,
          btnStatus: 2,
          data: () => {
            const { inProperty } = this.tkFlowInfo();
            const profFlag = inProperty.profFlag;
            if (profFlag === '1' && this.$attrs.needInterceptProf) {
              // 专业投资者不用办理此业务
              this.$TAlert({
                tips: '专业投资者无需开通此权限',
                confirm: () => {}
              });
            } else {
              if (
                this.openAccList.filter((item) => item.assetProp === '7')
                  .length > 0
              ) {
                if (!this.checkAccount()) {
                  return;
                }
                // eslint-disable-next-line vue/no-async-in-computed-properties
                creditAndFundPwdCheck()
                  .then((res) => {
                    // 0一致 1不一致
                    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                    this.creditFundAccount = res.data.fundAccount;
                    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                    this.ifExistCreditAccount = res.data.ifExistCreditAccount;
                    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                    this.specialFlag = res.data.specialFlag;
                    if (this.specialFlag === '0') {
                      if (this.$attrs.needCancel) {
                        // 取消时二次弹窗确认
                        const { bizName } = this.tkFlowInfo();
                        this.$TAlert({
                          title: '温馨提示',
                          tips: `请确认是否需要${bizName}`,
                          hasCancel: true,
                          confirmBtn: '确认',
                          confirm: () => {
                            this.submitFlowData('0');
                          }
                        });
                      } else {
                        this.submitFlowData('0');
                      }
                    } else {
                      if (this.$attrs.needCancel) {
                        // 取消时二次弹窗确认
                        const { bizName } = this.tkFlowInfo();
                        this.$TAlert({
                          title: '温馨提示',
                          tips: `请确认是否需要${bizName}`,
                          hasCancel: true,
                          confirmBtn: '确认',
                          confirm: () => {
                            this.submitFlowData('1');
                          }
                        });
                      } else {
                        this.submitFlowData('1');
                      }
                    }
                  })
                  .catch();
              } else {
                if (!this.checkAccount()) {
                  return;
                }
                if (this.$attrs.needCancel) {
                  // 取消时二次弹窗确认
                  const { bizName } = this.tkFlowInfo();
                  this.$TAlert({
                    title: '温馨提示',
                    tips: `请确认是否需要${bizName}`,
                    hasCancel: true,
                    confirmBtn: '确认',
                    confirm: () => {
                      this.submitFlowData();
                    }
                  });
                } else {
                  this.submitFlowData();
                }
              }
            }
          }
        });
      } else {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
      }
      return arr;
    }
  },
  created() {
    window.viewShowCallBack = this.viewShowCallBack;
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  mounted() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
    this.renderingView();
  },
  methods: {
    viewShowCallBack() {
      this.renderingView();
    },

    toBizType() {
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: this.$attrs.cancelBiztype, initJumpMode: '0' });
      });
    },

    checkRule(item, ruleObj) {
      let canFlagArr = [];
      Object.keys(ruleObj).forEach((key) => {
        let canFlag = false;
        Object.keys(item).forEach((itkey) => {
          if (itkey === key) {
            if (ruleObj[key].includes(item[itkey])) {
              canFlag = true;
            }
          }
        });
        if (canFlag) {
          canFlagArr.push(canFlag);
        }
      });
      if (canFlagArr.length === Object.keys(ruleObj).length) {
        return true;
      } else {
        return false;
      }
    },

    gotoOpen() {
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType:'010044',  initJumpMode: '0' });
      });
      return
      if ($hvue.platform == 0) {
        window.location.href =
          window.$hvue.customConfig.targetUrl +
          '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=stockAForWeb';
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: 'open',
          actionType: '6',
          // targetModule: 'open',
          params: {
            url:
              window.$hvue.customConfig.targetUrl +
              '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=stockAForWeb',
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      }
    },

    async renderingView() {
      // const { inProperty } = this.tkFlowInfo();
      // const { clientId, bizType, branchNo } = inProperty;
      stockAccountSelect({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        filterStockType: this.$attrs.filter_stock_type,
        filterExchangeType: this.$attrs.filter_exchange_type,
        filterHolderKind: this.$attrs.filter_holder_kind,
        filterHolderStatus: this.$attrs.filter_holder_status,
        filterHolderRights: this.$attrs.filter_holder_rights,
        filterRegister: this.$attrs.filter_register,
        filterMainFlag: this.$attrs.filter_main_flag,
        isShowOnlyAvailable: this.$attrs.is_show_only_available ? '1' : '0',
        isQryZd: this.$attrs.is_qry_csdc ? '1' : '0', // 是否查询中登：1 是
        needCancel: this.$attrs.needCancel ? '1' : '0'
      }).then(async ({ data }) => {
        // 根据交易板块类别分类展示类别filterExchangeType
        this.isAllOpenRights = data.isAllOpenRights; //是否所有账号都开通了对应的业务 1全部开通
        if (data.stockAccList) {
          this.accountInfoList = [];
        } else {
          this.accountInfoList =
            data.groupVoList.map((item) => {
              return {
                accountTypeName: item.stockTypeLabel + '账户',
                stockAccList: item.saccountQryVoList.filter((it) => {
                  if (
                    item.stockTypeValue === '0|7|0' ||
                    item.stockTypeValue === '0|9|0'
                  ) {
                    return it.holderStatusDesc === '正常';
                  } else {
                    if (this.$attrs.is_show_only_main) {
                      return (
                        it.holderStatusDesc === '正常' && it.mainFlag === '1'
                      );
                    } else {
                      return it.holderStatusDesc === '正常';
                    }
                  }
                })
              };
            }) || [];
        }
        if (this.accountInfoList.length > 0) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
        }
        this.loadinged = true;
      });
    },

    checkAccount() {
      let result = true;
      let accountListArr = [];
      this.accountInfoList.forEach(({ stockAccList }) => {
        stockAccList.forEach((item) => accountListArr.push(item));
      });
      if (!this.$attrs.needCancel) {
        // 开通时检查
        let xyList = this.openAccList.filter((it) => it.assetProp === '7');
        if (xyList.length > 0) {
          xyList.forEach(({ assetProp, exchangeType }) => {
            let sameExchangeNoXY = accountListArr.filter(
              (a) =>
                a.assetProp !== '7' &&
                a.exchangeType === exchangeType &&
                (a.isChecked || a.holderRightsDesc === '已开通')
            );
            if (sameExchangeNoXY.length === 0) {
              this.$TAlert({
                title: '温馨提示',
                tips: '开通信用账户权限前需先开通对应市场普通账户权限，请同时勾选对应市场普通账户。',
                confirmBtn: '我知道了'
              });
              result = false;
            }
          });
        }
      } else {
        // 取消时检查
        let accList = this.openAccList.filter((it) => it.assetProp !== '7');
        // 当前有无信用账户
        if (accList.length > 0) {
          accList.forEach(({ assetProp, exchangeType }) => {
            let flag7 = this.accountInfoList.some(({ stockAccList }) => {
              return (
                stockAccList.filter(
                  (a) => a.assetProp === '7' && a.exchangeType === exchangeType
                ).length > 0
              );
            });
            let sameExchangeNoCheckXY = accountListArr.filter(
              (a) =>
                a.assetProp === '7' &&
                a.exchangeType === exchangeType &&
                a.isChecked
            );
            if (sameExchangeNoCheckXY.length === 0 && flag7) {
              this.$TAlert({
                title: '温馨提示',
                tips: '取消普通账户权限前需先取消对应市场信用账户权限，请同时勾选对应市场信用账户。',
                confirmBtn: '我知道了'
              });
              result = false;
            }
          });
        }
      }
      return result;
    },

    submitFlowData(creditTPwdSameFlag = '') {
      const selectedAccountsData = this.openAccList;
      if (selectedAccountsData?.length > 0) {
        this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
          selectedAccountsData: JSON.stringify(selectedAccountsData),
           credit_t_pwd_same_flag: creditTPwdSameFlag
        });
      }
    },

    selectAcc(item, accountType) {
      // 未指定账户选择弹框
      if (item.regflag === '0') {
        // 未指定账户
        if (!this.$attrs.needCancel) {
          this.$TAlert({
            tips: '账户为未指定状态时不能开通此权限，请先去规范账户后再来办理业务'
          });
          return;
        } else {
          this.$TAlert({
            tips: '账户为未指定状态时不能取消此权限，请先去规范账户后再来办理业务'
          });
          return;
        }
      }
      const {
        stockAccList,
        available,
        assetProp,
        exchangeType,
        isDisabled,
        isChecked,
        mainFlag,
        i
      } = item;
      if (isDisabled || !available) return;

      // let accountListArr = [];
      // this.accountInfoList.forEach(({ stockAccList }) => {
      //   stockAccList.forEach((item) => accountListArr.push(item));
      // });
      // if (!this.$attrs.needCancel) {
      //   // 选择账户类型为开通权限
      //   // 当前对应市场普通账户（包括深沪A和深沪场内基金账户）是否存在至少一个已开通或已同步勾选数组
      //   let sameExchangeNoXY = accountListArr.filter(
      //     (a) =>
      //       a.assetProp !== '7' &&
      //       a.exchangeType === exchangeType &&
      //       (a.isChecked || a.holderRightsDesc === '已开通')
      //   );
      //   // 开通信用账户权限时需检测对应市场普通账户（包括深沪A和深沪场内基金账户）是否存在至少一个已开通或已同步勾选，若未开通或未勾选则弹窗提示
      //   if (assetProp === '7' && !item.isChecked) {
      //     if (sameExchangeNoXY.length === 0) {
      //       this.$TAlert({
      //         tips: '开通信用账户权限前需先开通对应市场普通账户权限，请确认是否同步开通',
      //         hasCancel: true,
      //         confirmBtn: '确定',
      //         confirm: () => {
      //           this.accountInfoList.forEach(({ stockAccList }) => {
      //             stockAccList.forEach((a) => {
      //               if (
      //                 a.assetProp === '0' &&
      //                 a.exchangeType === exchangeType &&
      //                 a.mainFlag === '1'
      //               ) {
      //                 a.isChecked = true;
      //               }
      //             });
      //           });
      //           this.$set(stockAccList[i], 'isChecked', !isChecked);
      //           this.$emit('change', {
      //             selectedAccountsData: JSON.stringify(this.openAccList)
      //           });
      //         }
      //       });
      //       return;
      //     }
      //   }
      // } else {
      //   // 选择账户类型为取消权限
      //   // 当前对应市场信用账户对应市场信用证券账户是否已取消或已勾选数组
      //   let sameExchangeNoCheckXY = accountListArr.filter(
      //     (a) =>
      //       a.assetProp === '7' &&
      //       a.exchangeType === exchangeType &&
      //       a.isChecked
      //   );
      //   if (assetProp !== '7' && !item.isChecked) {
      //     if (sameExchangeNoCheckXY.length === 0) {
      //       this.$TAlert({
      //         tips: '取消普通账户权限前需先取消对应市场信用账户权限，请确认是否同步取消。',
      //         hasCancel: true,
      //         confirmBtn: '确定',
      //         confirm: () => {
      //           this.accountInfoList.forEach(({ stockAccList }) => {
      //             stockAccList.forEach((a) => {
      //               if (
      //                 a.assetProp === '7' &&
      //                 a.exchangeType === exchangeType
      //               ) {
      //                 a.isChecked = true;
      //               }
      //             });
      //           });
      //           this.$set(stockAccList[i], 'isChecked', !isChecked);
      //           this.$emit('change', {
      //             selectedAccountsData: JSON.stringify(this.openAccList)
      //           });
      //         }
      //       });
      //       return;
      //     }
      //   }
      // }
      this.$set(stockAccList[i], 'isChecked', !isChecked);
      this.$emit('change', {
        selectedAccountsData: JSON.stringify(this.openAccList)
      });
    }
  }
};
</script>
