<template>
  <article class="content" style="background: #f5f6fa">
    <template
      v-for="({ isDisabled, saccountQryVoList }, index) in accountInfoList"
    >
      <div
        v-if="saccountQryVoList.length > 0"
        :key="index + 'com_title'"
        class="com_title"
      >
        <h5>{{ $attrs.needCancel ? '特转A账户' : '深A账户' }}</h5>
      </div>
      <div
        v-if="saccountQryVoList.length > 0"
        :key="index + 'acct_status_item'"
        class="acct_status_item"
      >
        <ul class="acct_list">
          <li
            v-for="(
              {
                stockAccount,
                isChecked,
                holderStatusDesc,
                holderRightsDesc,
                regFlagDesc,
                regflag,
                exchangeType,
                available,
                assetProp,
                mainFlag
              },
              i
            ) in saccountQryVoList"
            :key="i"
            @click.stop="
              selectAcc({
                saccountQryVoList,
                stockAccount,
                exchangeType,
                regflag,
                assetProp,
                isDisabled,
                available,
                isChecked,
                mainFlag,
                i
              })
            "
          >
            <span
              v-if="$attrs.needCancel && allOpen"
              style="
                padding: 0.16rem 0.6rem 0.16rem 0;
                line-height: 1.5;
                display: block;
              "
              >{{ stockAccount
              }}<em v-if="regflag !== '0'" class="acct_s_tag">{{
                holderStatusDesc
              }}</em
              ><em v-if="regflag === '0'" class="acct_s_tag">{{
                regFlagDesc
              }}</em></span
            >
            <span
              v-else
              class="icon_check"
              :class="{ checked: isChecked, disabled: !available }"
            >
              {{ stockAccount
              }}<em v-if="regflag !== '0'" class="acct_s_tag">{{
                holderStatusDesc
              }}</em
              ><em v-if="regflag === '0'" class="acct_s_tag">{{
                regFlagDesc
              }}</em></span
            >
           <span class="state">{{ holderRightsDesc }}</span>
          </li>
        </ul>
      </div>
    </template>
    <div v-if="!$attrs.needCancel && !noOpenRights" class="tip_txtbox spel">
      <p>
        您需要取消权限，请点击
        <a class="com_link" @click="toBizType">取消权限</a>
      </p>
    </div>
    <template v-if="$attrs.needCancel && allOpen">
      <div class="com_title">
        <h5>请选择需要注销的权限</h5>
      </div>
      <div class="acct_status_item">
        <ul class="acct_list">
          <li @click="chooseRights('7')">
            <span
              class="icon_check"
              :class="{ checked: chooseHolderRights.includes('7') }"
              >一类权限</span
            >
          </li>
          <li @click="chooseRights('8')">
            <span
              class="icon_check"
              :class="{ checked: chooseHolderRights.includes('8') }"
              >二类权限</span
            >
          </li>
        </ul>
      </div>
    </template>

    <template v-if="!$attrs.needCancel">
      <div v-if="noAccount" class="acct_emptybox">
        <p>您还没有正常状态账户，请前往开通</p>
        <a class="r_link_arrow" @click="gotoOpen">去开通</a>
      </div>
      <div v-if="noAccount" class="tip_txtbox">
        <p>您本地还没有可办理业务的账户，请先前往开通。</p>
      </div>
    </template>
    <div v-if="!hasAccount" class="wx_cm_tips">
      {{ btmTips }}
    </div>
    <div v-if="tips" class="tip_txtbox" v-html="tips" />
  </article>
</template>

<script>
import { creditAndFundPwdCheck, stockAccountSelect } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'holderAccountSelectionXSB',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      isAllOpenRights: '',
      noOpenRights: false,
      accountInfoList: [],
      loadinged: false,
      btmTips: '',
      tips: this.$attrs.tips,
      chooseHolderRights: '',
      allOpen: false
    };
  },
  computed: {
    onlyMain() {
      return this.$attrs.is_show_only_main;
    },
    noAccount() {
      let allAccountList = [];
      this.accountInfoList.forEach((it) => {
        it.saccountQryVoList.forEach((item) => {
          allAccountList.push(item);
        });
      });
      if (allAccountList.length === 0 && this.loadinged) {
        // this.btmTips = '您本地还没有可办理业务的账户，请先前往开通。';
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '返回首页',
          btnStatus: 2,
          display: true,
          data: () => {
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
        return true;
      } else {
        return false;
      }
    },

    hasAccount() {
      let canSelectAccount = [];
      this.accountInfoList.forEach((it) => {
        it.saccountQryVoList.forEach((item) => {
          if (item.available) {
            canSelectAccount.push(item);
          }
        });
      });
      let allopenAccount = [];
      let twoHolderRight = [];
      this.accountInfoList.forEach((it) => {
        it.saccountQryVoList.forEach((item) => {
          if (!this.$attrs.needCancel) {
            if (item.holderRights.includes('7')) {
              allopenAccount.push(item);
            }
            if (item.holderRights.includes('8')) {
              twoHolderRight.push(item);
            }
          }
        });
      });
      let allAccountList = [];
      this.accountInfoList.forEach((it) => {
        it.saccountQryVoList.forEach((item) => {
          allAccountList.push(item);
        });
      });
      if (allopenAccount.length === 0) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        if (twoHolderRight.length) {
          this.noOpenRights = false;
        } else {
          this.noOpenRights = true;
        }
      } else {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.noOpenRights = false;
      }
      if (
        allopenAccount.length > 0 &&
        allAccountList.length === allopenAccount.length &&
        this.loadinged
      ) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '返回',
          btnStatus: 2,
          display: true,
          data: () => {
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
        this.$TAlert({
          tips: '您当前所有账户均已开通当前业务权限。',
          confirm: () => {
            // this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
        return false;
      }
      if (
        canSelectAccount.length === 0 &&
        allAccountList.length !== 0 &&
        this.loadinged &&
        this.accountInfoList.length !== 0
      ) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        // this.btmTips = '您在本地无可办理的账户，请确认账户状态是否正常。';
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '返回首页',
          btnStatus: 2,
          display: true,
          data: () => {
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
        return false;
      }

      return true;
    },

    openAccList() {
      let arr = [];
      for (let { isDisabled, saccountQryVoList, accountTypeName } of this
        .accountInfoList) {
        if (isDisabled) continue;
        for (let {
          isChecked,
          exchangeType,
          stockAccount,
          holderKind,
          holderRights,
          fundAccount,
          assetProp = '0' //assetProp 资产属性 0普通
        } of saccountQryVoList) {
          if (isChecked)
            arr.push({
              exchangeType,
              fundAccount,
              stockAccount,
              assetProp,
              holderKind,
              holderRights,
              stockType: this.$attrs.needCancel ? '特转A账户' : '深A账户'
            });
        }
      }
      return arr;
    }
  },
  watch: {
    openAccList: {
      handler(list) {
        if (list.length > 0) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            text: '下一步',
            display: true,
            btnStatus: 2,
            data: () => {
              if (
                this.openAccList.filter((item) => item.assetProp === '7')
                  .length > 0
              ) {
                if (!this.checkAccount()) {
                  return;
                }
                creditAndFundPwdCheck()
                  .then((res) => {
                    // 0一致 1不一致
                    this.creditFundAccount = res.data.fundAccount;
                    this.ifExistCreditAccount = res.data.ifExistCreditAccount;
                    this.specialFlag = res.data.specialFlag;
                    if (this.specialFlag === '0') {
                      if (this.$attrs.needCancel) {
                        // 取消时二次弹窗确认
                        this.needCancelConfirm('0');
                      } else {
                        this.submitFlowData('0');
                      }
                    } else {
                      if (this.$attrs.needCancel) {
                        // 取消时二次弹窗确认
                        this.needCancelConfirm('1');
                      } else {
                        this.submitFlowData('1');
                      }
                    }
                  })
                  .catch();
              } else {
                if (!this.checkAccount()) {
                  return;
                }
                if (this.$attrs.needCancel) {
                  // 取消时二次弹窗确认
                  const { bizName } = this.tkFlowInfo();
                  let isOpenBjs =
                    this.openAccList.filter((item) =>
                      item.holderRights.includes('$')
                    ).length > 0;
                  let tips = isOpenBjs
                    ? '因您已开通北交所权限，取消新三板权限后不会同步注销北交所权限。请确认是否需要取消？'
                    : `请确认是否需要${bizName}`;
                  this.$TAlert({
                    title: '温馨提示',
                    tips,
                    hasCancel: true,
                    confirmBtn: '确认',
                    confirm: () => {
                      this.submitFlowData();
                    }
                  });
                } else {
                  this.$TAlert({
                    title: '温馨提示',
                    tips: '如您没有特转A账户，我们在为您开通新三板权限的同时，将同步开通特转A账户。',
                    confirmBtn: '我知道了',
                    confirm: () => {
                      this.submitFlowData();
                    }
                  });
                }
              }
            }
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
        }
      }
    }
  },
  created() {
    window.viewShowCallBack = this.viewShowCallBack;
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  mounted() {
    this.renderingView();
  },
  methods: {
    viewShowCallBack() {
      this.renderingView();
    },

    needCancelConfirm(flag = '') {
      const { bizName } = this.tkFlowInfo();
      this.$TAlert({
        title: '温馨提示',
        tips: `请确认是否需要${bizName}`,
        hasCancel: true,
        confirmBtn: '确认',
        confirm: () => {
          this.submitFlowData(flag);
        }
      });
    },

    submitFlowData(creditTPwdSameFlag = '') {
      const selectedAccountsData = this.openAccList;
      if (selectedAccountsData?.length > 0) {
        let params = this.$attrs.needCancel
          ? {
              selectedAccountsData: JSON.stringify(selectedAccountsData),
              chooseHolderRights: this.chooseHolderRights,
              credit_t_pwd_same_flag: creditTPwdSameFlag
            }
          : {
              selectedAccountsData: JSON.stringify(selectedAccountsData),
              credit_t_pwd_same_flag: creditTPwdSameFlag
            };
        this.eventMessage(this, EVENT_NAME.NEXT_STEP, params);
      }
    },

    toBizType() {
      import('@/common/flowMixin.js').then((a) => {
        a.initFlow.call(this, this.$attrs.cancelBiztype);
      });
    },

    checkRule(item, ruleObj) {
      let canFlagArr = [];
      Object.keys(ruleObj).forEach((key) => {
        let canFlag = false;
        Object.keys(item).forEach((itkey) => {
          if (itkey === key) {
            if (ruleObj[key].includes(item[itkey])) {
              canFlag = true;
            }
          }
        });
        if (canFlag) {
          canFlagArr.push(canFlag);
        }
      });
      if (canFlagArr.length === Object.keys(ruleObj).length) {
        return true;
      } else {
        return false;
      }
    },

    gotoOpen() {
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010044', initJumpMode: '0' });
      });
    },

    async renderingView() {
      stockAccountSelect({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        filterStockType: this.$attrs.filter_stock_type,
        filterExchangeType: this.$attrs.filter_exchange_type,
        filterHolderKind: this.$attrs.filter_holder_kind,
        filterHolderStatus: this.$attrs.filter_holder_status,
        filterHolderRights: this.$attrs.filter_holder_rights,
        filterRegister: this.$attrs.filter_register,
        filterMainFlag: this.$attrs.filter_main_flag,
        isShowOnlyAvailable: this.$attrs.is_show_only_available ? '1' : '0',
        isQryZd: this.$attrs.is_qry_csdc ? '1' : '0', // 是否查询中登：1 是
        needCancel: this.$attrs.needCancel ? '1' : '0'
      }).then(async ({ data }) => {
        // 根据交易板块类别分类展示类别filterExchangeType
        this.isAllOpenRights = data.isAllOpenRights; //是否所有账号都开通了对应的业务 1全部开通
        if (data.stockAccList) {
          this.accountInfoList = [];
        } else {
          let SAList = [];
          for (let item of data.groupVoList) {
            if (
              item.saccountQryVoList.length &&
              item.stockTypeValue == '0|2|0'
            ) {
              let filterArr = item.saccountQryVoList.filter(
                (it) => it.mainFlag == '1' && it.holderStatus == '0'
              );
              if (filterArr.length) {
                SAList.push(filterArr);
              }
            }
          }
          if (SAList.length || this.$attrs.needCancel) {
            let list = [];
            if (SAList.length) {
              let SAStockAccount = this.getStockAccounts(SAList);
              list =
                data.groupVoList.filter(
                  (item) =>
                    item.stockTypeValue == '0|9|0' &&
                    item.saccountQryVoList.length &&
                    item.saccountQryVoList.filter(
                      (it) =>
                        it.mainFlag == '1' &&
                        it.holderStatus == '0' &&
                        it.stockAccount == SAStockAccount
                    ).length
                ) || [];
            }
            if (this.$attrs.needCancel) {
              list =
                data.groupVoList.filter(
                  (item) =>
                    item.stockTypeValue == '0|9|0' &&
                    item.saccountQryVoList.length &&
                    item.saccountQryVoList.filter(
                      (it) => it.mainFlag == '1' && it.holderStatus == '0'
                    ).length
                ) || [];
            }

            if (list.length <= 0) {
              list = data.groupVoList.filter(
                (item) =>
                  item.stockTypeValue == '0|2|0' &&
                  item.saccountQryVoList.length
              );
            }
            let arr = [];
            for (let item of list) {
              if (item.saccountQryVoList.length) {
                let filterArr = item.saccountQryVoList.filter(
                  (it) => it.mainFlag == '1' && it.holderStatus == '0'
                );
                if (filterArr.length) {
                  item.saccountQryVoList = filterArr;
                  arr.push(item);
                }
              }
            }
            list = arr;
            list.map((item) => {
              return item.saccountQryVoList.map((itm) => {
                if (
                  itm.holderRights.includes('7') &&
                  itm.holderRights.includes('8')
                ) {
                  this.$set(itm, 'holderRightsDesc', '已开通一类、二类');
                  if (!this.$attrs.needCancel) {
                    this.$set(itm, 'available', false);
                  }
                  this.allOpen = true;
                } else if (itm.holderRights.includes('7')) {
                  this.$set(itm, 'holderRightsDesc', '已开通一类');
                  if (!this.$attrs.needCancel) {
                    this.$set(itm, 'available', false);
                  } else {
                    this.chooseHolderRights = '7';
                  }
                } else if (itm.holderRights.includes('8')) {
                  this.$set(itm, 'holderRightsDesc', '已开通二类');
                  if (this.$attrs.needCancel) {
                    this.chooseHolderRights = '8';
                  }
                } else {
                  this.$set(itm, 'holderRightsDesc', '未开通');
                }
              });
            });
            this.accountInfoList = list;
          }
        }
        if (this.accountInfoList.length > 0) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
        }
        this.loadinged = true;
      });
    },

    chooseRights(val) {
      let a = this.chooseHolderRights ? this.chooseHolderRights.split(',') : [];
      if (a.includes(val)) {
        let list = a.filter((item) => item != val);
        a = list.length ? list : [];
      } else {
        a.push(val);
      }
      this.$set(this, 'chooseHolderRights', a.join(','));
      this.accountInfoList.map((item) => {
        item.saccountQryVoList.map((it) => {
          if (it.exchangeType == '9') {
            this.$set(it, 'isChecked', this.chooseHolderRights != '');
          }
        });
        // this.$emit('change', {
        //   selectedAccountsData: JSON.stringify(this.openAccList)
        // });
      });
    },

    getStockAccounts(arr) {
      let stockAccounts = [];
      for (let item of arr) {
        for (let data of item) {
          stockAccounts.push(data.stockAccount);
        }
      }
      return stockAccounts;
    },

    checkAccount() {
      let result = true;
      let accountListArr = [];
      this.accountInfoList.forEach(({ saccountQryVoList }) => {
        saccountQryVoList.forEach((item) => accountListArr.push(item));
      });
      if (!this.$attrs.needCancel) {
        // 开通时检查
        let xyList = this.openAccList.filter((it) => it.assetProp === '7');
        if (xyList.length > 0) {
          xyList.forEach(({ assetProp, exchangeType }) => {
            let sameExchangeNoXY = accountListArr.filter(
              (a) =>
                a.assetProp !== '7' &&
                a.exchangeType === exchangeType &&
                (a.isChecked || a.holderRightsDesc === '已开通')
            );
            if (sameExchangeNoXY.length === 0) {
              this.$TAlert({
                title: '温馨提示',
                tips: '开通信用账户权限前需先开通对应市场普通账户权限，请同时勾选对应市场普通账户。',
                confirmBtn: '我知道了'
              });
              result = false;
            }
          });
        }
      } else {
        // 取消时检查
        let accList = this.openAccList.filter((it) => it.assetProp !== '7');
        // 当前有无信用账户
        if (accList.length > 0) {
          accList.forEach(({ assetProp, exchangeType }) => {
            let flag7 = this.accountInfoList.some(({ saccountQryVoList }) => {
              return (
                saccountQryVoList.filter(
                  (a) => a.assetProp === '7' && a.exchangeType === exchangeType
                ).length > 0
              );
            });
            let sameExchangeNoCheckXY = accountListArr.filter(
              (a) =>
                a.assetProp === '7' &&
                a.exchangeType === exchangeType &&
                a.isChecked
            );
            if (sameExchangeNoCheckXY.length === 0 && flag7) {
              this.$TAlert({
                title: '温馨提示',
                tips: '取消普通账户权限前需先取消对应市场信用账户权限，请同时勾选对应市场信用账户。',
                confirmBtn: '我知道了'
              });
              result = false;
            }
          });
        }
      }
      return result;
    },

    selectAcc(item, accountType) {
      // 未指定账户选择弹框
      if (item.regflag === '0') {
        // 未指定账户
        if (!this.$attrs.needCancel) {
          this.$TAlert({
            tips: '账户为未指定状态时不能开通此权限，请先去规范账户后再来办理业务'
          });
          return;
        } else {
          this.$TAlert({
            tips: '账户为未指定状态时不能取消此权限，请先去规范账户后再来办理业务'
          });
          return;
        }
      }
      if (this.allOpen) {
        return;
      }
      const { saccountQryVoList, available, isDisabled, isChecked, i } = item;
      if (isDisabled || !available) return;
      this.$set(saccountQryVoList[i], 'isChecked', !isChecked);
      // this.$emit('change', {
      //   selectedAccountsData: JSON.stringify(this.openAccList)
      // });
    }
  }
};
</script>
<style scoped>
.tip_txtbox{
  background-color: transparent;
}
.acct_list {
    padding: 0 0.16rem;
    background: #ffffff;
}
.acct_list li .icon_radio, .acct_list li .icon_check {
    display: block;
    padding: 0.16rem 0 0.16rem 0.3rem;
    font-size: 0.16rem;
    line-height: 1.5;
    color: #333333;
}
.acct_list li .icon_radio:before, .acct_list li .icon_check:before {
    left: 0;
    right: auto;
}
.icon_radio.checked:before {
    content: "\E61F";
    border-color:  #F93838;
    background:  #F93838;
}
.icon_radio:before {
    content: '';
    box-sizing: border-box;
    width: 0.18rem;
    height: 0.18rem;
    border: 1px solid #BBBBBB;
    border-radius: 50%;
    font-size: 0.16rem;
    line-height: 1;
    color: #ffffff;
    font-family: "wt-iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    top: 50%;
    left: 0;
    margin: -0.09rem 0 0 0;
}
.acct_s_tag {
    display: inline-block;
    padding: 0 0.03rem;
    border-radius: 0.02rem;
    border: 1px solid #E0953F;
    color: #E0953F;
    font-size: 0.12rem;
    line-height: 1.33333;
    vertical-align: middle;
    margin-left: 0.08rem;
}
</style>