<template>
  <article class="content" style="background: #f5f6fa">
    <div v-if="openedAccount.length === 0">
      <template
        v-for="(
          { isDisabled, tip, accountTypeName, stockAccList }, index
        ) in accountInfoList"
      >
        <div
          v-if="stockAccList.length > 0"
          :key="index + 'com_title'"
          class="com_title"
        >
          <h5>{{ accountTypeName }}</h5>
        </div>
        <div
          v-if="stockAccList.length > 0"
          :key="index + 'acct_status_item'"
          class="acct_status_item"
        >
          <ul class="acct_list">
            <li
              v-for="(
                {
                  stockAccount,
                  isChecked,
                  holderStatusDesc,
                  holderRightsDesc,
                  regFlagDesc,
                  regflag,
                  exchangeType,
                  available,
                  assetProp,
                  mainFlag
                },
                i
              ) in stockAccList"
              :key="i"
              @click.stop="
                selectAcc({
                  stockAccList,
                  stockAccount,
                  exchangeType,
                  regflag,
                  assetProp,
                  isDisabled,
                  available,
                  isChecked,
                  mainFlag,
                  i
                })
              "
            >
              <span
                class="icon_check"
                :class="{ checked: isChecked, disabled: !available }"
              >
                {{ stockAccount
                }}<em v-if="regflag !== '0'" class="acct_s_tag">{{
                  holderStatusDesc
                }}</em
                ><em v-if="regflag === '0'" class="acct_s_tag">{{
                  regFlagDesc
                }}</em></span
              >
              <span class="state" v-show="holderRightsDesc === '已开通'">{{
                holderRightsDesc
              }}</span>
            </li>
          </ul>
        </div>
      </template>
    </div>
    <div v-if="openedAccount.length > 0">
      <template
        v-for="(
          { isDisabled, tip, accountTypeName, stockAccList }, index
        ) in openedAccountInfoList"
      >
        <div
          v-if="stockAccList.length > 0"
          :key="index + 'com_title'"
          class="com_title"
        >
          <h5>{{ accountTypeName }}</h5>
        </div>
        <div
          v-if="stockAccList.length > 0"
          :key="index + 'acct_status_item'"
          class="acct_status_item"
        >
          <ul class="acct_list">
            <li
              v-for="(
                {
                  stockAccount,
                  isChecked,
                  holderStatusDesc,
                  regFlagDesc,
                  regflag
                },
                i
              ) in stockAccList"
              :key="i"
              class="opacity5"
            >
              <span class="icon_check disabled" :class="{ checked: isChecked }">
                {{ stockAccount
                }}<em v-if="regflag !== '0'" class="acct_s_tag">{{
                  holderStatusDesc
                }}</em
                ><em v-if="regflag === '0'" class="acct_s_tag">{{
                  regFlagDesc
                }}</em></span
              >
              <span class="state">已开通</span>
            </li>
          </ul>
        </div>
      </template>
    </div>
    <div v-if="noAccount" class="acct_emptybox">
      <p>您还没有深A账户，请先前往开通</p>
      <a class="r_link_arrow" @click="gotoOpen">去开通</a>
    </div>
    <div v-if="tips" class="acct_list_tips" v-html="tips" />
  </article>
</template>

<script>
import { creditAndFundPwdCheck, stockAccountSelect } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'OpenAccountSelection',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      noOpenRights: false,
      accountInfoList: [],
      openedAccountInfoList: [],
      openedAccount: [],
      loadinged: false,
      tips: this.$attrs.tips
    };
  },
  computed: {
    noAccount() {
      let allAccountList = [];
      this.accountInfoList.forEach((it) => {
        it.stockAccList.forEach((item) => {
          allAccountList.push(item);
        });
      });
      let allOpenedAccountList = [];
      this.openedAccountInfoList.forEach((it) => {
        it.stockAccList.forEach((item) => {
          allOpenedAccountList.push(item);
        });
      });
      console.log('allAccountList', JSON.stringify(allAccountList));
      console.log('allOpenedAccountList', JSON.stringify(allOpenedAccountList));
      console.log('loadinged', JSON.stringify(this.loadinged));
      if (
        allAccountList.length === 0 &&
        allOpenedAccountList.length === 0 &&
        this.loadinged
      ) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '返回',
          btnStatus: 2,
          display: true,
          data: () => {
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
        return true;
      } else {
        return false;
      }
    },

    openAccList() {
      let arr = [];
      for (let { isDisabled, stockAccList, accountTypeName } of this
        .accountInfoList) {
        if (isDisabled) continue;
        for (let {
          isChecked,
          exchangeType,
          stockAccount,
          holderKind,
          fundAccount,
          assetProp = '0' //assetProp 资产属性 0普通
        } of stockAccList) {
          if (isChecked)
            arr.push({
              exchangeType,
              fundAccount,
              stockAccount,
              assetProp,
              holderKind,
              stockType: accountTypeName
            });
        }
      }
      if (arr.length > 0) {
        // this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 1 });
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '下一步',
          display: true,
          btnStatus: 2,
          data: () => {
            if (
              this.openAccList.filter((item) => item.assetProp === '7').length >
              0
            ) {
              // 信用账户校验逻辑
              // if (!this.checkAccount()) {
              //   return;
              // }
              // eslint-disable-next-line vue/no-async-in-computed-properties
              creditAndFundPwdCheck()
                .then((res) => {
                  // 0一致 1不一致
                  // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                  this.creditFundAccount = res.data.fundAccount;
                  // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                  this.ifExistCreditAccount = res.data.ifExistCreditAccount;
                  // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                  this.specialFlag = res.data.specialFlag;
                  this.submitFlowData();
                })
                .catch();
            } else {
              this.submitFlowData();
            }
          }
        });
      } else {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
      }
      return arr;
    }
  },
  created() {
    window.viewShowCallBack = this.viewShowCallBack;
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  mounted() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
    this.renderingView();
  },
  methods: {
    viewShowCallBack() {
      this.renderingView();
    },

    toBizType() {
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: this.$attrs.cancelBiztype });
      });
    },

    checkRule(item, ruleObj) {
      let canFlagArr = [];
      Object.keys(ruleObj).forEach((key) => {
        let canFlag = false;
        Object.keys(item).forEach((itkey) => {
          if (itkey === key) {
            if (ruleObj[key].includes(item[itkey])) {
              canFlag = true;
            }
          }
        });
        if (canFlag) {
          canFlagArr.push(canFlag);
        }
      });
      if (canFlagArr.length === Object.keys(ruleObj).length) {
        return true;
      } else {
        return false;
      }
    },

    gotoOpen() {
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010044', initJumpMode: '0' });
      });
      return;
      if ($hvue.platform == 0) {
        window.location.href =
          window.$hvue.customConfig.targetUrl +
          '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=stockAForWeb';
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: 'open',
          actionType: '6',
          // targetModule: 'open',
          params: {
            url:
              window.$hvue.customConfig.targetUrl +
              '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=stockAForWeb',
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      }
    },

    async renderingView() {
      // const { inProperty } = this.tkFlowInfo();
      // const { clientId, bizType, branchNo } = inProperty;
      console.log('this.$attrs', this.$attrs);
      stockAccountSelect({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        filterStockType: this.$attrs.filter_stock_type,
        filterExchangeType: this.$attrs.open_exchange_type,
        filterHolderKind: this.$attrs.open_holder_kind,
        filterMainFlag: this.$attrs.filter_main_flag,
        isQryZd: this.$attrs.is_qry_csdc ? '1' : '0' // 是否查询中登：1 是
      }).then(async ({ data }) => {
        // 根据交易板块类别分类展示类别filterExchangeType

        if (data.stockAccList) {
          this.accountInfoList = [];
        } else {
          this.accountInfoList =
            data.groupVoList.map((item) => {
              return {
                accountTypeName: item.stockTypeLabel + '账户',
                stockAccList: item.saccountQryVoList.filter(
                  (it) => it.holderStatusDesc === '正常'
                )
              };
            }) || [];
          this.openedAccountInfoList =
            data.groupVoList.map((item) => {
              return {
                accountTypeName: item.stockTypeLabel + '账户',
                stockAccList: item.saccountQryVoList.filter(
                  (it) => it.exchangeType === this.$attrs.open_exchange_type
                )
              };
            }) || [];
          let openedAccount = [];
          this.accountInfoList.forEach((item) => {
            item.stockAccList.forEach((it) => {
              if (it.exchangeType === this.$attrs.open_exchange_type) {
                openedAccount.push(it);
              }
            });
          });
          this.openedAccount = openedAccount;
        }
        if (this.accountInfoList.length > 0 && this.openedAccount === 0) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
        }
        if (this.openedAccount.length > 0) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            text: '返回',
            btnStatus: 2,
            display: true,
            data: () => {
              this.eventMessage(this, EVENT_NAME.TO_INDEX);
            }
          });
        }
        this.loadinged = true;
      });
    },

    checkAccount() {
      let result = true;

      // let accountListArr = [];
      // this.accountInfoList.forEach(({ stockAccList }) => {
      //   stockAccList.forEach((item) => accountListArr.push(item));
      // });
      // // 开通时检查
      // let xyList = this.openAccList.filter((it) => it.assetProp === '7');
      // if (xyList.length > 0) {
      //   xyList.forEach(({ assetProp, exchangeType }) => {
      //     let sameExchangeNoXY = accountListArr.filter(
      //       (a) =>
      //         a.assetProp !== '7' &&
      //         a.exchangeType === exchangeType &&
      //         (a.isChecked || a.holderRightsDesc === '已开通')
      //     );
      //     if (sameExchangeNoXY.length === 0) {
      //       this.$TAlert({
      //         title: '温馨提示',
      //         tips: '开通信用账户权限前需先开通对应市场普通账户权限，请同时勾选对应市场普通账户。',
      //         confirmBtn: '我知道了'
      //       });
      //       result = false;
      //     }
      //   });
      // }
      return result;
    },

    submitFlowData() {
      const selectedAccountsData = this.openAccList;
      if (selectedAccountsData?.length > 0) {
        this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
          selectedAccountsData: JSON.stringify(selectedAccountsData)
        });
      }
    },

    selectAcc(item, accountType) {
      // 未指定账户选择弹框
      if (item.regflag === '0') {
        // 未指定账户
        if (!this.$attrs.needCancel) {
          this.$TAlert({
            tips: '账户为未指定状态时不能开通此权限，请先去规范账户后再来办理业务'
          });
          return;
        } else {
          this.$TAlert({
            tips: '账户为未指定状态时不能取消此权限，请先去规范账户后再来办理业务'
          });
          return;
        }
      }
      const {
        stockAccList,
        available,
        assetProp,
        exchangeType,
        isDisabled,
        isChecked,
        mainFlag,
        i
      } = item;
      if (isDisabled || !available) return;
      this.$set(stockAccList[i], 'isChecked', !isChecked);
      this.$emit('change', {
        selectedAccountsData: JSON.stringify(this.openAccList)
      });
    }
  }
};
</script>
