<template>
  <article class="content" style="background: #f5f6fa">
    <template v-for="({ accList, assetPropName }, index) in zqFundAccountList">
      <div
        v-show="accList.length !== 0"
        class="com_title"
        :key="`${index}_tit`"
      >
        <h5>{{ assetPropName }}资金账户</h5>
      </div>
      <div
        v-show="accList.length !== 0"
        class="acct_status_item"
        :key="`${index}_status`"
      >
        <ul class="acct_list">
          <li
            v-for="(it, i) in accList"
            :key="i"
            @click.stop="selectAcc(it, i)"
          >
            <span
              class="icon_check"
              :class="{ checked: it.isChecked, disabled: !it.available }"
            >
              {{ it.fundAccount
              }}<em class="acct_s_tag">{{
                it.fundAccountStatus === '0' ? '正常' : '异常'
              }}</em></span
            >
            <span class="state" v-show="!$attrs.needCancel && it.ipofrozenName === '已开通'">{{ it.ipofrozenName }}</span>
          </li>
        </ul>
      </div>
    </template>
    <template v-if="!$attrs.needCancel && noAccount">
      <div>
        <div class="acct_emptybox">
          <p>您还没有正常状态账户，请前往开通</p>
          <a class="r_link_arrow" @click="gotoOpen">去开通</a>
        </div>
        <div class="tip_txtbox">
          <p>您本地还没有可办理业务的账户，请先前往开通。</p>
        </div>
      </div>
    </template>
    <div v-if="!$attrs.needCancel && noOpenRights" class="tip_txtbox spel">
      <p>
        您需要取消权限，请点击
        <a class="com_link" @click="toBizType">取消权限</a>
      </p>
    </div>
    <div v-if="!noAccount && tips" class="acct_list_tips">
      <p class="tit">温馨提示</p>
      <p v-html="tips"></p>
    </div>
  </article>
</template>

<script>
import {
  creditAndFundPwdCheck,
  stockAccountSelect,
  zqFundAccountQry
} from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import { ASSET_PROP } from '@/common/enumeration';

export default {
  name: 'zqydjFundAccountOption',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      bizType: '',
      zqFundAccountList: [],
      loadinged: false,
      btmTips: '',
      tips: this.$attrs.tips
    };
  },
  watch: {
    allOpenRights: {
      handler(flag) {
        if (flag) {
          this.$TAlert({
            tips: '您当前所有账户均已开通当前业务权限。'
          });
        }
      },
      deep: true
    },
    openAccList: {
      handler(arr) {
        if (arr.length > 0) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            text: '下一步',
            display: true,
            btnStatus: 2,
            data: () => {
              const { inProperty } = this.tkFlowInfo();
              const profFlag = inProperty.profFlag;
              if (profFlag === '1' && this.$attrs.needInterceptProf) {
                // 专业投资者不用办理此业务
                this.$TAlert({
                  tips: '专业投资者无需开通此权限',
                  confirm: () => {}
                });
              } else {
                if (
                  this.openAccList.filter((item) => item.assetProp === '7')
                    .length > 0
                ) {
                  creditAndFundPwdCheck()
                    .then((res) => {
                      this.specialFlag = res.data.specialFlag;
                      if (this.specialFlag === '0') {
                        if (this.$attrs.needCancel) {
                          // 取消时二次弹窗确认
                          this.needCancelConfirm('0');
                        } else {
                          this.submitFlowData('0');
                        }
                      } else {
                        if (this.$attrs.needCancel) {
                          // 取消时二次弹窗确认
                          this.needCancelConfirm('1');
                        } else {
                          this.submitFlowData('1');
                        }
                      }
                    })
                    .catch();
                } else {
                  if (this.$attrs.needCancel) {
                    // 取消时二次弹窗确认
                    this.needCancelConfirm();
                  } else {
                    this.submitFlowData();
                  }
                }
              }
            }
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
        }
      }
    }
  },
  computed: {
    onlyMain() {
      return this.$attrs.is_show_only_main;
    },
    noAccount() {
      if (this.zqFundAccountList.length === 0 && this.loadinged) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '返回首页',
          btnStatus: 2,
          display: true,
          data: () => {
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
        return true;
      } else {
        return false;
      }
    },

    noOpenRights() {
      if (this.$attrs.needCancel) return false;
      return this.zqFundAccountList.some(({ accList }) => {
        return accList.some(({ ipofrozenName }) => ipofrozenName === '已开通');
      });
    },

    allOpenRights() {
      if (this.$attrs.needCancel) return false;
      if (!this.loadinged) return false;
      return this.zqFundAccountList.every(({ accList }) => {
        return accList.every(({ ipofrozenName }) => ipofrozenName === '已开通');
      });
    },

    openAccList() {
      let arr = [];
      for (let { isDisabled, accList } of this.zqFundAccountList) {
        if (isDisabled) continue;
        for (let it of accList) {
          if (it.isChecked) arr.push(it);
        }
      }
      return arr;
    }
  },
  created() {
    window.viewShowCallBack = this.viewShowCallBack;
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  mounted() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
    this.renderingView();
  },
  methods: {
    viewShowCallBack() {
      this.renderingView();
    },

    toBizType() {
      import('@/common/flowMixin.js').then((a) => {
        a.initFlow.call(this, this.$attrs.cancelBiztype);
      });
    },

    gotoOpen() {
      // 跳转至补开股东户
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010044', initJumpMode: '0' });
      });
      /*  if ($hvue.platform == 0) {
        window.location.href =
          window.$hvue.customConfig.targetUrl +
          '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=stockAForWeb';
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          // targetModule: 'open',
          params: {
            url:
              window.$hvue.customConfig.targetUrl +
              '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=stockAForWeb',
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      } */
    },

    queryBranchNo() {
      if ($hvue.platform == 0) {
        window.location.href =
          window.$hvue.customConfig.thirdPartyUrl.businessDepartment;
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          params: {
            url: window.$hvue.customConfig.thirdPartyUrl.businessDepartment,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      }
    },

    getAssetPropMap(v) {
      let getMap = new Map();
      getMap.set(ASSET_PROP.DERIVATIVES_ACCOUNT, '期权');
      getMap.set(ASSET_PROP.ORDINARY_ACCOUNT, '普通');
      getMap.set(ASSET_PROP.CREDIT_ACCOUNT, '信用');
      getMap.set(ASSET_PROP.OPTIONS_ACCOUNT, '期权');
      getMap.set(ASSET_PROP.FUND_ACCOUNT, '基金');
      return getMap.get(v) || '';
    },

    async renderingView() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      this.bizType = bizType;
      zqFundAccountQry({}).then(async ({ data }) => {
        this.zqFundAccountList = data.zqFundAccountList.reduce(
          (total, currentItem) => {
            const flag = total.some(
              ({ assetPropName }) =>
                assetPropName === this.getAssetPropMap(currentItem.assetProp)
            );
            if (!flag) {
              total.push({
                assetPropName: this.getAssetPropMap(currentItem.assetProp),
                accList: data.zqFundAccountList
                  .filter(
                    ({ assetProp, fundAccountStatus, ipofrozenStr = '' }) => {
                      if (
                        this.$attrs.needCancel &&
                        ipofrozenStr.indexOf('1') !== 0
                      ) {
                        return false;
                      }
                      return (
                        fundAccountStatus === '0' &&
                        currentItem.assetProp === assetProp
                      );
                    }
                  )
                  .map((it) => {
                    const { ipofrozenStr = '' } = it;
                    it.isChecked = false;
                    it.ipofrozenName =
                      ipofrozenStr.indexOf('1') === 0 ? '已开通' : '未开通';
                    it.available = !this.$attrs.needCancel
                      ? ipofrozenStr.indexOf('1') !== 0
                      : ipofrozenStr.indexOf('1') === 0;
                    return it;
                  })
              });
            }
            return total;
          },
          []
        );

        if (this.zqFundAccountList.length > 0) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
        }
        this.loadinged = true;
      });
    },

    selectAcc(item, i) {
      const { available, isChecked } = item;
      if (!available) return;
      this.$set(item, 'isChecked', !isChecked);
      this.$emit('change', {
        selectedAccountsData: JSON.stringify(this.openAccList)
      });
    },

    //中签资金确认
    frozenConfirm(flag) {
      let errMsg = '';
      const frozen = this.openAccList.some(
        ({ ipofrozenFlag }) => ipofrozenFlag === '1'
      ); //已冻结
      const unfrozen = this.openAccList.some(
        ({ ipofrozenFlag }) => ipofrozenFlag === '2'
      ); //未冻结
      if (frozen && unfrozen) {
        errMsg =
          '您当前已中签，也存在已冻结的中签资金，如您关闭权限，已冻结的资金不会自动解冻；已中签未冻结的资金，也不会自动冻结。确认要关闭中签资金预冻结权限吗?';
      } else if (frozen) {
        errMsg =
          '您当前已有冻结中签资金，已冻结的资金不会自动解冻。确认要关闭中签资金预冻结权限吗？';
      } else if (unfrozen) {
        errMsg =
          '您当前已中签，权限关闭后您的已中签未冻结资金，将不会自动冻结。确认要关闭中签资金预冻结权限吗？';
      }
      if (errMsg !== '') {
        this.$TAlert({
          title: '温馨提示',
          tips: errMsg,
          hasCancel: true,
          confirmBtn: '确认',
          confirm: () => {
            this.needCancelConfirm(flag);
          }
        });
      } else {
        this.needCancelConfirm(flag);
      }
    },

    submitFlowData(creditTPwdSameFlag = '') {
      const selectedAccountsData = this.openAccList;
      if (selectedAccountsData?.length > 0) {
        this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
          selectedAccountsData: JSON.stringify(selectedAccountsData),
          credit_t_pwd_same_flag: creditTPwdSameFlag
        });
      }
    },

    needCancelConfirm(flag = '') {
      const { bizName } = this.tkFlowInfo();
      this.$TAlert({
        title: '温馨提示',
        tips: `请确认是否需要${bizName}`,
        hasCancel: true,
        confirmBtn: '确认',
        confirm: () => {
          this.submitFlowData(flag);
        }
      });
    }
  }
};
</script>
<style scoped>
.acct_list_tips .tit {
  font-weight: 700;
}
</style>
