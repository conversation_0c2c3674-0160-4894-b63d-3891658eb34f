<template>
  <fragment>
    <van-field
      v-model="fieldValue"
      input-align="left"
      maxlength="4"
      :label="$attrs.label"
      :placeholder="$attrs.placeholder"
    >
      <template #button
        ><a class="code_img" @click="imgClick"><img :src="imgSrc" /></a
      ></template>
    </van-field>
  </fragment>
</template>

<script>
import { getImgCode } from '@/service/service';
export default {
  name: 'ImageCode',
  inject: ['setPropsByForm'],
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fieldValue: '',
      imgSrc: '',
      captchaToken: ''
    };
  },
  watch: {
    fieldValue: {
      handler(val) {
        if (val.length == '4') {
          this.setPropsByForm(
            this.$attrs.propKey,
            'captchaToken',
            this.captchaToken
          );
          this.setPropsByForm(this.$attrs.propKey, 'captcha', val);
          this.$emit('change', val);
        }
      },
      deep: false,
      immediate: false
    },
    '$attrs.resetImageCode': {
      handler(newVal) {
        if (newVal === true) {
          this.imgClick();
          this.setPropsByForm(this.$attrs.propKey, 'resetImageCode', false);
        }
      },
      deep: false,
      immediate: false
    }
  },
  mounted() {
    this.imgClick();
  },
  methods: {
    imgClick() {
      getImgCode({}, { loading: false }).then((data) => {
        if (data.code === 0) {
          let results = data.data;
          this.imgSrc = results.image;
          this.captchaToken = results.token;
        }
      });
    }
  }
};
</script>

<style lang="less" scope>
// .component {
//   width: 100%;
//   // .van-cell {
//   //   min-height: 0.24rem;
//   //   line-height: 0.24rem;
//   //   padding: 0;
//   // }
//   // .van-cell::after {
//   //   display: none;
//   // }
// }
</style>
