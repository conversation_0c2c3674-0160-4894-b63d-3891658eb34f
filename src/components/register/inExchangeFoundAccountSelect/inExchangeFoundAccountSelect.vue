<template>
  <section class="main fixed" style="position: fixed">
    <t-header></t-header>
    <article class="content">
      <div v-if="outTips" class="top_error_tips">
        <p>
          {{ outTips }}
          <a
            v-if="outTips.includes('关联关系')"
            class="com_btn border"
            @click="toGlgx"
            >前往办理</a
          >
        </p>
      </div>
      <div>
        <div
          v-for="(item, index) in accountList"
          :key="index"
          class="acct_status_item"
        >
          <div class="acct_add_tit">
            <p>{{ item.exchangeTypeName }}</p>
            <div class="opea">
              <span
                class="icon_radio"
                :class="{
                  disabled: !item.operateTypeInfos.filter(
                    (it) => it.operateType === '1'
                  )[0].available,
                  checked: item.operateTypeInfos.filter(
                    (it) => it.operateType === '1'
                  )[0].checked
                }"
                @click="zkAccount(item, index)"
                >新开</span
              >
              <span
                class="icon_radio"
                :class="{
                  disabled: !item.operateTypeInfos.filter(
                    (it) => it.operateType === '2'
                  )[0].available,
                  checked: item.operateTypeInfos.filter(
                    (it) => it.operateType === '2'
                  )[0].checked
                }"
                @click="jgAccount(item, index)"
                >转户</span
              >
            </div>
          </div>
          <div
            v-if="
              !isZdTime &&
              item.operateTypeInfos.filter((it) => it.operateType === '2')[0]
                .checked
            "
            class="acct_add_cont"
          >
            <div class="input_text">
              <input
                v-model="
                  item.operateTypeInfos.filter(
                    (it) => it.operateType === '2'
                  )[0].stockAccount
                "
                type="text"
                placeholder="请填写转入股东账号"
              />
            </div>
          </div>
          <div
            v-if="
              isZdTime &&
              item.operateTypeInfos.filter((it) => it.operateType === '2')[0]
                .checked
            "
            class="acct_add_cont"
          >
            <ul class="zh_acct_list">
              <li
                v-for="(it, idx) in item.operateTypeInfos.filter(
                  (it) => it.operateType === '2'
                )[0].stockAccUsageInfos"
                @click="selectAccount(it, idx, item)"
                :key="idx"
              >
                <span
                  class="icon_radio"
                  :class="{
                    checked: it.checked,
                    disabled: it.holderStatus === '0' ? false : true
                  }"
                ></span>
                <h5>{{ it.stockAccount }}</h5>
                <div
                  v-if="
                    it.stockAccountUsageInformationQryVO &&
                    it.stockAccountUsageInformationQryVO.length > 0
                  "
                  class="ct"
                >
                  <div class="txt">
                    <a
                      v-if="it.stockAccountUsageInformationQryVO.length > 1"
                      class="com_link"
                      @click="
                        showMore(
                          it.stockAccountUsageInformationQryVO,
                          item.exchangeTypeName
                        )
                      "
                      >查看更多</a
                    >
                    <span v-if="item.exchangeTypeName === '沪市场内基金'"
                      >已指定-{{
                        it.stockAccountUsageInformationQryVO
                          .map((a) => a.csdcRegcompanyName)
                          .join(',')
                      }}</span
                    >
                    <span v-if="item.exchangeTypeName === '深市场内基金'"
                      >已下挂-{{
                        it.stockAccountUsageInformationQryVO
                          .map((a) => a.csdcUsecompanyName)
                          .join(',')
                      }}</span
                    >
                    <p
                      v-if="
                        item.exchangeTypeName === '沪市场内基金' &&
                        !it.stockAccountUsageInformationQryVO
                          .map((a) => a.csdcRegcompanyName)[0]
                          .includes('国金')
                      "
                      style="color: #ff4848; font-size: 10px"
                    >
                      需联系{{
                        it.stockAccountUsageInformationQryVO.map(
                          (a) => a.csdcRegcompanyName
                        )[0]
                      }}撤销指定后方可正常交易
                    </p>
                  </div>
                </div>
                <span class="state">{{
                  it.holderStatus === '0' ? '正常' : '异常'
                }}</span>
              </li>
            </ul>
          </div>

          <div v-if="item.tips" class="imp_c_tips">
            <p class="warn">{{ item.tips }}</p>
          </div>
        </div>
      </div>
      <div class="tip_txtbox spel">
        <p>温馨提示：</p>
        <p>
          1、每个市场最多三个账户挂入我司交易账户，请仔细核对您要挂入的账号，以免影响您的交易。
        </p>
        <p>2、封闭式基金账户无法申购新股且无法交易股票，请您知悉。</p>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" :class="{ disabled: !canNext }" @click="toNext"
          >下一步</a
        >
      </div>
    </footer>

    <div v-if="showMoreTips">
      <div class="dialog_overlay"></div>
      <div class="dialog_box">
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>已下挂</h3>
          <div>
            <p v-for="(item, index) in moreContent" :key="index">
              {{ index + 1 }}、{{ item }}
            </p>
          </div>
        </div>
        <div class="dialog_btn">
          <a @click.stop="showMoreTips = false">我知道了</a>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
import {
  videoRecord,
  bkCreateAndHangStockAccountQry,
  stockAccountApproproateness,
  bkShExpirationNotSpecifiedQry
} from '@/service/shareholdAccountService';
import { exchangeTradedFundAccInfoCheck } from '@/service/inExchangeFoundAccountService';
import { addClientCritMark } from '@/service/service';

export default {
  name: 'inExchangeFoundAccountSelect',
  inject: ['eventMessage', 'tkFlowInfo'],
  data() {
    return {
      showMoreTips: false,
      moreContent: [],
      nowAccount: {},
      isZdTime: true,
      accountList: [
        {
          exchangeTypeName: '沪市场内基金',
          operateTypeInfos: [
            {
              stockAccount: '',
              operateType: '1',
              exchangeType: '1',
              assetProp: '0',
              holderKind: '0',
              available: false
            },
            {
              stockAccount: '',
              operateType: '2',
              exchangeType: '1',
              assetProp: '0',
              holderKind: '0',
              available: false
            }
          ]
        },
        {
          exchangeTypeName: '深市场内基金',
          operateTypeInfos: [
            {
              stockAccount: '',
              operateType: '1',
              exchangeType: '2',
              assetProp: '0',
              holderKind: '0',
              available: false
            },
            {
              stockAccount: '',
              operateType: '2',
              exchangeType: '2',
              assetProp: '0',
              holderKind: '0',
              available: false
            }
          ]
        }
      ],
      outTips: '',
      videoCompote: ''
    };
  },
  computed: {
    canNext() {
      if (this.openAccList.length > 0) {
        return true;
      } else {
        return false;
      }
    },

    openAccList() {
      let arr = [];
      this.accountList.forEach((item) => {
        for (let {
          checked,
          exchangeType,
          assetProp,
          holderKind,
          operateType,
          stockAccount,
          stockAccUsageInfos = []
        } of item.operateTypeInfos) {
          if (checked) {
            if (stockAccUsageInfos.length !== 0) {
              stockAccUsageInfos.forEach((it) => {
                if (it.checked) {
                  arr.push({
                    fundAccount: this.$store.state.user.userInfo.fundAccount,
                    exchangeType,
                    stockAccount: it.stockAccount,
                    assetProp,
                    holderKind,
                    operateType,
                    isChangeAccountShow: item.isChangeAccountShow
                      ? item.isChangeAccountShow
                      : ''
                  });
                }
              });
            } else {
              arr.push({
                fundAccount: this.$store.state.user.userInfo.fundAccount,
                exchangeType,
                stockAccount,
                assetProp,
                holderKind,
                operateType,
                isChangeAccountShow: item.isChangeAccountShow
                  ? item.isChangeAccountShow
                  : ''
              });
            }
          }
        }
      });
      return arr;
    }
  },
  mounted() {
    // this.eventMessage(this, EVENT_NAME.NEXT_STEP);
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    this.renderingView();
  },
  methods: {
    toGlgx() {
      // 跳转至关联关系确认
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010197', initJumpMode: '0' });
      });
      /* let targetUrl =
        $hvue.customConfig.targetUrl +
        '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=associationConfirmForWeb';
      if ($hvue.platform == 0) {
        window.location.href = targetUrl;
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          // targetModule: 'open',
          params: {
            url: targetUrl,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      } */
    },

    renderingView() {
      const { inProperty } = this.tkFlowInfo();
      let extInitParams = JSON.parse(inProperty.extInitParams);
      videoRecord({
        flowToken: sessionStorage.getItem('TKFlowToken')
      }).then((res) => {
        this.videoCompote = res.data.complete;
      });
      stockAccountApproproateness({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        bizFlag: '1',
        exchangeType: extInitParams.exchangeType,
        isChangeAccountShowSz: extInitParams.isChangeAccountShowSz,
        isChangeAccountShowSh: extInitParams.isChangeAccountShowSh,
        isOpenAccountShowSz: extInitParams.isOpenAccountShowSz,
        isOpenAccountShowSh: extInitParams.isOpenAccountShowSh
      }).then();
      // 判断是否中登时间
      if (this.isZdTime) {
        // 中登时间
        exchangeTradedFundAccInfoCheck({
          flowToken: sessionStorage.getItem('TKFlowToken'),
          bizFlag: '1',
          exchangeType: extInitParams.exchangeType,
          isChangeAccountShowSz: extInitParams.isChangeAccountShowSz,
          isChangeAccountShowSh: extInitParams.isChangeAccountShowSh,
          isOpenAccountShowSz: extInitParams.isOpenAccountShowSz,
          isOpenAccountShowSh: extInitParams.isOpenAccountShowSh
        }).then((res) => {
          if (res.data.stockAccList.length > 0) {
            this.accountList = res.data.stockAccList;
          }
          //   this.nowAccount = res.data.changeStockAccSh[0];
          this.outTips = res.data.outTips;
        });
      } else {
        this.accountList = [
          {
            exchangeTypeName: '沪市场内基金',
            operateTypeInfos: [
              {
                stockAccount: '',
                operateType: '1',
                exchangeType: '1',
                assetProp: '0',
                holderKind: '0',
                available: true
              },
              {
                stockAccount: '',
                operateType: '2',
                exchangeType: '1',
                assetProp: '0',
                holderKind: '0',
                available: true
              }
            ]
          },
          {
            exchangeTypeName: '深市场内基金',
            operateTypeInfos: [
              {
                stockAccount: '',
                operateType: '1',
                exchangeType: '2',
                assetProp: '0',
                holderKind: '0',
                available: true
              },
              {
                stockAccount: '',
                operateType: '2',
                exchangeType: '2',
                assetProp: '0',
                holderKind: '0',
                available: true
              }
            ]
          }
        ];
      }
    },

    showMore(arr, exchangeTypeName) {
      if (exchangeTypeName === '深市场内基金') {
        this.moreContent = arr.map((it) => it.csdcUsecompanyName);
      } else {
        this.moreContent = arr.map((it) => it.csdcRegcompanyName);
      }
      this.showMoreTips = true;
    },

    selectAccount(it, idx, item) {
      if (it.holderStatus !== '0') {
        return;
      }
      /* this.accountList.forEach((a) => {
        a.operateTypeInfos.forEach((b) => {
          if (
            b.stockAccUsageInfos &&
            a.exchangeTypeName === item.exchangeTypeName
          ) {
            b.stockAccUsageInfos.forEach((item) => {
              this.$set(item, 'checked', false);
            });
          }
        });
      }); */
      this.accountList.forEach((a) => {
        a.operateTypeInfos.forEach((b) => {
          if (b.stockAccUsageInfos) {
            b.stockAccUsageInfos.forEach((item) => {
              if (item.stockAccount === it.stockAccount) {
                this.$set(item, 'checked', !item.checked);
                this.$set(b, 'stockAccount', item.stockAccount);
              }
            });
          }
        });
      });
    },

    checkAccount() {
      let jgArr = this.openAccList.filter((item) => {
        if (item.operateType === '2') {
          return true;
        }
        return false;
      });
      let errTips = [];
      for (const item of jgArr) {
        if (item.exchangeType === '1') {
          if (item.stockAccount === '' || item.stockAccount === undefined) {
            errTips.push('沪市转户账号不能为空');
            break;
          }
          // if (!/A\d{9}/.test(item.stockAccount)) {
          //   errTips.push('沪市转户账号格式不正确');
          //   break;
          // }
        } else if (item.exchangeType === '2') {
          if (item.stockAccount === '' || item.stockAccount === undefined) {
            errTips.push('深市转户账号不能为空');
            break;
          }
          // if (!/(01|02|00)\d{8}$/.test(item.stockAccount)) {
          //   errTips.push('深市转户账号格式不正确');
          //   break;
          // }
        }
      }
      if (errTips.length > 0) {
        _hvueToast({ mes: errTips[0] });
        return false;
      }
      return true;
    },

    toNext() {
      if (!this.canNext) {
        return;
      }
      if (!this.checkAccount()) {
        return;
      }
      // 单边市场判断
      let dbFlag = true;
      let sFlag = true;
      let fFlag = true;
      if (this.accountList.length > 1) {
        fFlag =
          this.accountList[0].operateTypeInfos.filter((item) => item.available)
            .length > 0
            ? true
            : false;
        sFlag =
          this.accountList[1].operateTypeInfos.filter((item) => item.available)
            .length > 0
            ? true
            : false;
      } else {
        fFlag = false;
        sFlag = false;
      }
      if (sFlag && fFlag) {
        dbFlag = true;
      } else {
        dbFlag = false;
      }
      if (this.openAccList.length === 1 && dbFlag) {
        this.$TAlert({
          title: '温馨提示',
          tips: '您当前仅选择开通一个市场，如您点击继续开通，则需要等待审核完成后才可以开通其他市场的账户',
          hasCancel: true,
          cancelBtn: '我再想想',
          confirmBtn: '继续开通',
          cancelRight: true,
          confirm: () => {
            this.toNextConfirm();
            // this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
            //   acodeAccount: '',
            //   selectedAccountsData: JSON.stringify(this.openAccList),
            //   videoComplete: this.videoCompote
            // });
          }
        });
      } else {
        this.toNextConfirm();
        // this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        //   acodeAccount: '',
        //   selectedAccountsData: JSON.stringify(this.openAccList),
        //   videoComplete: this.videoCompote
        // });
      }
    },

    toNextConfirm() {
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        acodeAccount: '',
        selectedAccountsData: JSON.stringify(this.openAccList),
        videoComplete: this.videoCompote
      });
      //   console.log(this.nowAccount);
      //   console.log(this.openAccList.some((a) => a.exchangeType === '1'));
      //   if (
      //     this.nowAccount &&
      //     this.openAccList.some((a) => a.exchangeType === '1')
      //   ) {
      //     bkShExpirationNotSpecifiedQry({
      //       stockAccount: this.nowAccount.stockAccount
      //     }).then((res) => {
      //       if (res.data.expirationStockAccSh.length > 0) {
      //         let addClientCritMarkTips = `如您继续变更，会同步注销您未指定的账户：${res.data.expirationStockAccSh
      //           .map((item) => item.stockAccount)
      //           .join(',')}。请确认。如您不想注销此账户可拨打客服95310进行咨询`;
      //         this.$TAlert({
      //           title: '温馨提示',
      //           tips: `如您继续变更，会同步注销您未指定的账户：${res.data.expirationStockAccSh
      //             .map((item) => item.stockAccount)
      //             .join(',')}。`,
      //           smallTips: '请确认。如您不想注销此账户可拨打客服95310进行咨询',
      //           hasCancel: true,
      //           confirmBtn: '确认',
      //           cancel: () => {},
      //           confirm: () => {
      //             addClientCritMark({
      //               flowToken: sessionStorage.getItem('TKFlowToken'),
      //               markType: '14',
      //               markContent: addClientCritMarkTips,
      //               confirmFlag: '1'
      //             }).then((res) => {
      //               this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
      //                 acodeAccount: '',
      //                 selectedAccountsData: JSON.stringify(this.openAccList),
      //                 videoComplete: this.videoCompote
      //               });
      //             });
      //           }
      //         });
      //       } else {
      //         this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
      //           acodeAccount: '',
      //           selectedAccountsData: JSON.stringify(this.openAccList),
      //           videoComplete: this.videoCompote
      //         });
      //       }
      //     });
      //   } else {
      //     this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
      //       acodeAccount: '',
      //       selectedAccountsData: JSON.stringify(this.openAccList),
      //       videoComplete: this.videoCompote
      //     });
      //   }
    },

    zkAccount(item, index) {
      if (
        !this.accountList[index].operateTypeInfos.filter(
          (it) => it.operateType === '1'
        )[0].available
      ) {
        return;
      }
      this.accountList[index].operateTypeInfos.forEach((it) => {
        if (it.operateType === '1') {
          if (it.checked) {
            this.$set(it, 'checked', false);
          } else {
            this.$set(it, 'checked', true);
          }
        } else {
          this.$set(it, 'checked', false);
        }
      });
    },

    jgAccount(item, index) {
      if (
        !this.accountList[index].operateTypeInfos.filter(
          (it) => it.operateType === '2'
        )[0].available
      ) {
        return;
      }
      this.accountList[index].operateTypeInfos.forEach((it) => {
        if (it.operateType === '2') {
          if (it.checked) {
            this.$set(it, 'checked', false);
          } else {
            this.$set(it, 'checked', true);
          }
        } else {
          this.$set(it, 'checked', false);
        }
      });
    }
  }
};
</script>

<style></style>
