<template>
  <fragment>
    <van-field
      v-model="inputVal"
      :label="$attrs.label"
      :placeholder="$attrs.placeholder"
      right-icon="photograph"
      @click-right-icon="clickRightIcon"
    />
    <getImgBoxBrowser
      ref="getImgBoxBrowser"
      :scan="true"
      @getImgCallBack="getImgCallBack"
    ></getImgBoxBrowser>
  </fragment>
</template>

<script>
import getImgBoxBrowser from '@/components/getImg_browser';
import { uploadFile } from '@/common/util';
export default {
  name: 'OcrInput',
  inject: ['setPropsByForm'],
  components: {
    getImgBoxBrowser
  },
  data() {
    return {
      inputVal: ''
    };
  },
  watch: {
    '$attrs.value': {
      handler(newVal) {
        this.inputVal = newVal;
      }
    },
    inputVal(newVal) {
      this.$emit('change', newVal);
    }
  },
  methods: {
    clickRightIcon() {
      this.$refs.getImgBoxBrowser.getImg();
    },
    getImgCallBack(imgInfo) {
      _hvueLoading.open();
      uploadFile(
        $hvue.customConfig.serverUrl + '/client/ocrParseBankCard',
        imgInfo.base64,
        {
          success: async (data) => {
            _hvueLoading.close();
            if (data.code === 0) {
              const { cardNumber, issuer } = data.data;
              if (cardNumber) {
                this.inputVal = cardNumber;
                this.setPropsByForm(this.$attrs.propKey, 'bankName', issuer);
                this.$emit('change', cardNumber);
              }
            } else {
              _hvueAlert({ mes: data.msg });
            }
          },
          progress: (count) => {
            console.log(count);
          },
          error: (e) => {
            _hvueLoading.close();
            console.log(e);
          }
        },
        {}
      );
    }
  }
};
</script>

<style scoped></style>
