<template>
  <section
    v-show="showPage"
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click="back"></a>
        <h1 class="title">期权买入额度调整标准</h1>
      </div>
    </header>
    <article class="content">
      <div class="card">
        <h3 class="tit">若投资者满足以下条件：</h3>
        <p>1）三级交易权限</p>
        <!-- <p>2）权利仓持仓限额为1000张</p> -->
        <p>2）风险承受能力为C4及以上且有效期一年以上</p>
        <h3 class="tit">则买入额度调整标准为：</h3>
        <p>
          max（全部账户自有资产的20%，前6个月日均持有沪深证券市值的20%）（按万向上取整）
        </p>
      </div>
      <div class="card">
        <h3 class="tit">若投资者满足以下条件：</h3>
        <p>1）权利仓持仓限额为2000张及以上</p>
        <p>2）风险承受能力为C4及以上且有效期一年以上</p>
        <h3 class="tit">则买入额度调整标准为：</h3>
        <p>
          max（全部账户自有资产的30%，前6个月日均持有沪深证券市值的20%）（按万向上取整）
        </p>
      </div>
      <div class="card">
        <h3 class="tit">若投资者满足以下条件：</h3>
        <p>
          若投资者在以上两种情况以外，则买入额度调整标准为：max（全部账户自有资产的10%，前6个月日均持有沪深证券市值的20%）（按万向上取整）
        </p>
      </div>
    </article>
  </section>
</template>

<script>
export default {
  data() {
    return {
      showPage: false
    };
  },
  methods: {
    show() {
      this.showPage = true;
    },

    back() {
      this.showPage = false;
    }
  }
};
</script>

<style lang="less" scoped>
.card {
  background: #ffffff;
  margin-bottom: 0.1rem;
  padding: 0.18rem 0.18rem;
  .tit {
    font-size: 16px;
    margin: 8px 0 5px 0;
  }
  p{
    color: #777777
  }
}
</style>
