<template>
  <fragment v-if="loaded">
    <div v-if="type !== 0">
      <div class="acct_nodata">
        <div class="icon">
          <img src="@/assets/images/noData2.svg" />
        </div>
      </div>
      <div class="bus_txtbox">
        <p v-if="type === 3">
          申请开立深市衍生品合约账户，您需要先开通或下挂深圳A股账户。在办理成功后，您可以重新申请开立深市衍生品合约账户。
        </p>
        <p v-else-if="type === 2">
          您已在其他机构开立了深市衍生品合约账户。其对应的深圳A股账户为:
        </p>
        <p v-else>
          您已在其他机构开立了3个深市衍生品合约账户，无法在我司再次申请。您可以先注销在其他机构开立的衍生品合约账户后，再来我司申请开通。详询95310。
        </p>
        <div v-show="type === 2" class="cx_acct_txt">
          {{ stockZDAccount }}
        </div>
        <p v-show="type === 2">
          如果您希望在我司申请开立深市衍生品合约账户，需要线下挂该深市A股账户至我司。您可前往补开股东户功能进行下挂，如无法下挂可拨打客服95310进行咨询。
        </p>
      </div>
    </div>
    <div
      v-else-if="
        !onlyMainAccount &&
        ((onlyAccount && this.stockAccountList[0].holderStatus === '0') ||
          !onlyAccount)
      "
    >
      <div class="com_title">
        <h5>深市A股账户</h5>
      </div>
      <ul class="acct_list">
        <li
          v-for="(
            { stockAccount, holderStatusDesc, holderStatus }, index
          ) in stockAccountList"
          :key="index"
          @click="selectLi(index)"
        >
          <span
            class="icon_check"
            :class="{
              checked: selectIndex === index,
              disabled: holderStatus !== '0'
            }"
            >{{ stockAccount
            }}<em class="acct_s_tag">{{ holderStatusDesc }}</em></span
          >
        </li>
      </ul>
    </div>
    <div v-else class="gray_bg">
      <div class="com_title">
        <h5>您开通期权的深圳账户如下：</h5>
      </div>
      <ul class="com_infolist spel">
        <li>
          <span class="tit"
            >深市A股账户{{
              onlyAccount && stockAccountList[0].mainFlag === '1'
                ? '（主）'
                : ''
            }}<em class="acct_s_tag">{{
              stockAccountList[0].holderStatusDesc
            }}</em></span
          >
          <p>{{ stockAccountList[0].stockAccount }}</p>
        </li>
      </ul>
      <div v-show="stockAccountList[0].holderStatus !== '0'" class="bus_txtbox">
        <p>
          您已在其他期权经营机构开立了深市衍生品合约账户。该深圳A股账户状态异常，请先去规范账户后再来办理此业务。如有疑问可拨打客服95310进行咨询。
        </p>
      </div>
    </div>
  </fragment>
</template>

<script>
import { optionAccountOpenCheck } from '@/service/service';
import { jumpThirdPartyUrl } from '@/common/util';
import { EVENT_NAME } from '@/common/formEnum';
export default {
  name: 'OpenOptAccCheck',
  inject: ['eventMessage'],
  data() {
    return {
      loaded: false,
      stockAccountList: [],
      stockZDAccount: '',
      selectIndex: null,
      type: '' // 结果类型：0 正常；1 中登满3户；2 深A账户中登存在，本地不存在；3 深A本地不存在
    };
  },
  computed: {
    onlyMainAccount() {
      return (
        this.type === 0 &&
        this.stockAccountList.length === 1 &&
        this.stockAccountList[0].mainFlag === '1'
      );
    },
    onlyAccount() {
      return this.type === 0 && this.stockAccountList.length === 1;
    }
  },
  watch: {
    selectIndex: {
      handler() {
        this.setBtnStatus();
      },
      immediate: true
    }
  },
  mounted() {
    this.renderingView();
  },
  methods: {
    renderingView() {
      optionAccountOpenCheck({})
        .then(({ data }) => {
          this.loaded = true;
          this.stockAccountList = data.stockAccountList || [];
          this.type = data.type;
          this.stockZDAccount = data.stockZDAccount || '';

          if (this.stockZDAccount !== '') {
            let zdList = this.stockZDAccount.split(',');
            zdList = Array.from(new Set(zdList));
            this.stockAccountList = this.stockAccountList.filter((item) =>
              zdList.includes(item.stockAccount)
            );
            this.stockZDAccount = zdList.join(',');
          } else {
            // 中登没有深A期权账户，并且没有可选择的账号，展示类型3
            this.stockAccountList = this.stockAccountList.filter(
              ({ holderStatus }) => holderStatus === '0'
            );
          }

          if (this.type === 0) {
            if (this.stockAccountList.length === 0) {
              this.type = 3;
              this.$store.commit('flow/setWhiteBg', true);
            } else {
              const mainAccount = this.stockAccountList.filter(
                ({ mainFlag }) => mainFlag === '1'
              );
              //如果存有主账，只显示主账账户
              if (mainAccount.length !== 0) {
                this.stockAccountList = mainAccount;
              }
              this.$store.commit('flow/setWhiteBg', false);
            }
          } else {
            this.$store.commit('flow/setWhiteBg', true);
          }
          if (this.onlyMainAccount) {
            const selectedAccountsData = this.stockAccountList.map(
              ({
                assetProp,
                exchangeType,
                holderStatus,
                stockAccount,
                fundAccount
              }) => {
                return {
                  assetProp,
                  holderStatus,
                  exchangeType,
                  stockAccount,
                  fundAccount
                };
              }
            );
            this.$emit('change', {
              selectedAccountsData: JSON.stringify(selectedAccountsData)
            });
          }
          this.setBtnStatus();
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },
    nextBtn() {
      if (this.type === 0) {
        const selectedAccountsData = [
          this.stockAccountList[this.selectIndex]
        ].map(
          ({
            assetProp,
            exchangeType,
            holderStatus,
            stockAccount,
            fundAccount,
            changeMainFlag
          }) => {
            return {
              assetProp,
              holderStatus,
              exchangeType,
              stockAccount,
              fundAccount,
              changeMainFlag
            };
          }
        );
        this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
          selectedAccountsData: JSON.stringify(selectedAccountsData)
        });
      } else {
        // 跳转至补开股东户
        import('@/common/flowMixinV2.js').then((a) => {
          a.initFlow.call(this, { bizType: '010044', initJumpMode: '0' });
        });
        /* jumpThirdPartyUrl({
          bizType: '010044',
          url: $hvue.customConfig.thirdPartyUrl.reissueStockAcc
        }); */
      }
    },
    selectLi(i) {
      if (
        this.selectIndex === i ||
        this.stockAccountList[i].holderStatus !== '0'
      )
        return;
      if (this.stockAccountList[i].mainFlag !== '1') {
        this.$TAlert({
          title: '温馨提示',
          tips: '您当前勾选的账户不是主股东账户，请确认是否设置为主股东账户？如果不进行设置，后需交易需手动选择交易账户。',
          hasCancel: true,
          confirmBtn: '确认',
          cancelBtn: '暂不设置',
          confirm: () => {
            this.selectIndex = i;
            this.stockAccountList[i].changeMainFlag = '1';
          },
          cancel: () => {
            this.selectIndex = i;
            this.stockAccountList[i].changeMainFlag = '0';
          }
        });
      } else {
        this.selectIndex = i;
        this.stockAccountList[i].changeMainFlag = '0';
      }
    },
    setBtnStatus() {
      if (this.onlyMainAccount) {
        if (this.stockAccountList[0].holderStatus === '0') {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 1
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            text: '返回',
            btnStatus: 2,
            data: () => {
              this.eventMessage(this, EVENT_NAME.TO_INDEX);
            }
          });
        }
        return;
      }
      if (this.onlyAccount && this.stockAccountList[0].holderStatus !== '0') {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '返回',
          btnStatus: 2,
          data: () => {
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
        return;
      }

      let hiddenBtnList = [1];
      let btnText = '下一步';
      if (this.type === 2) {
        btnText = '下挂深A账户';
      } else if (this.type === 3) {
        btnText = '开通或下挂深A账户';
      }
      if (this.type === 0 && this.selectIndex === null) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          btnStatus: 0
        });
        return;
      }
      this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
        display: !hiddenBtnList.includes(this.type),
        text: btnText,
        btnStatus: 2,
        data: this.nextBtn
      });
    }
  }
};
</script>

<style>
.gray_bg {
  background-color: #f5f6fa;
}
</style>
