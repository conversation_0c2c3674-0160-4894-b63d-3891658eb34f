<template>
  <article v-if="loaded" class="content">
    <div class="acct_nodata">
      <div class="icon">
        <img :src="require('@/assets/images/noData2.svg')" />
      </div>
      <h5>{{ pageTips }}</h5>
    </div>
  </article>
</template>

<script>
import { optionAccountInfoCheck } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'OptAccCheck',
  inject: ['eventMessage'],
  data() {
    return {
      loaded: false,
      pageTips: ''
    };
  },
  created() {
    this.$store.commit('flow/setWhiteBg', true);
  },
  mounted() {
    this.loaded = false;
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    window.viewShowCallBack = this.renderingView;
    this.renderingView();
  },
  methods: {
    renderingView() {
      const _this = this;
      this.pageTips = this.$options.data().pageTips;
      optionAccountInfoCheck({})
        .then(({ code, msg, data }) => {
          let text = '开通股票期权';
          if (code === 0) {
              _this.eventMessage(_this, EVENT_NAME.NEXT_STEP);
              return;
            } else if(code === 1001) {
              this.pageTips = '您尚未开通期权资金账号，请开通后再办理业务。';
            } else if(code === 1002) {
              this.pageTips = '您的期权资金账户状态异常，请先去规范账户后再办理业务。如有疑问可拨打客服95310进行咨询。';
              text = '查找营业部';
            } else if(code === 1003) {
              this.pageTips = '您尚未开通衍生品合约账户，请开通后再办理业务。';
            } else if(code === 1004) {
              this.pageTips = '您当前暂未设置初始额度，需临柜处理。如有疑问可拨打客服95310进行咨询。';
              text = '查找营业部';
            } else {
              return Promise.reject(msg);
            }
          _this.loaded = true;
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 2,
            text,
            data: ()=> this.toOpen(code)
          });
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          })
          _this.loaded = true;
        });
    },
    toOpen(code) {
      let url = $hvue.customConfig.thirdPartyUrl.openOptionAccount;
      if ([1002, 1004].includes(code)) {
        url = $hvue.customConfig.thirdPartyUrl.businessDepartment;
      }
      if ($hvue.platform === '0') {
        window.location.href = url;
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          params: {
            url,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      }
    }
  }
};
</script>

<style></style>
