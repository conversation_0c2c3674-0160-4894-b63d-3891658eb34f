<template>
  <fragment v-if="!loading">
    <div v-if="showPrompt">
      <div v-if="!noOptAcc" class="acct_nodata">
        <div class="icon">
          <img src="@/assets/images/noData2.svg" />
        </div>
        <!-- <h5 v-if="noOptAcc">您尚未开通股票期权账户</h5> -->
        <h5 v-if="szPrompt">如何开通深圳衍生品合约账户</h5>
        <h5 v-else>如何开通上海衍生品合约账户</h5>
      </div>
      <!-- <div v-if="noOptAcc" class="bus_txtbox">
        <p>
          请您本人携带证件亲临国金证券营业部，现场申请开立股票期权账户。<a
            class="link_right_arrow"
            @click="jumpPage"
            >点击查看营业部地址</a
          >
        </p>
        <p>申请开立股票期权账户，您需要满足以下条件：</p>
        <p>
          （一）申请开户前20个交易日日均托管在公司的证券市值与资金账户可用余额（不含通过融资融券交易融入的资金或证券），合计不低于人民币50万元;
        </p>
        <p>（二）风险承受能力为C3及以上，且有效期不低于1年;</p>
        <p>
          （三）在证券公司开户6个月以上并具备融资融券参与资格或者金融期货交易经历;
        </p>
        <p>（四）具备期权基础知识，通过交易所认可的相应等级知识测试;</p>
        <p>（五）具有交易所认可的期权模拟交易经历;</p>
        <p>
          （六）不存在严重不良诚信记录，不存在法律、法规、规章和交易所业务规则禁止或者限制从事期权交易的情形。
        </p>
        <p>详询客服热线 95310。</p>
      </div> -->
      <div v-if="noOptAcc">
        <div class="acct_nodata">
          <div class="icon"><img src="@/assets/images/noData3.svg" /></div>
          <h5>您尚未开通沪市股票期权账户</h5>
          <p>线上开通深市股票期权账户前，您需临柜开立沪市股票期权账户。</p>
          <div class="cm_btn_wrap">
            <a class="com_btn border" @click="toBizType('010169')">立即预约</a>
          </div>
        </div>
        <div class="bus_txtbox">
          <div class="sub_txt">
            <h5>如何开立沪市期权账户？</h5>
            <p>
              <span class="num">1、</span
              >投资者风险承受能力为C3及以上且有效期一年以上(一级交易权限:C3及以上或者专业投资者;二、三级交易权限均需为C4及以上或者专业投资者);
            </p>
            <p>
              <span class="num">2、</span
              >申请开户前20个交易日日均托管在公司的证券市值与资金账户可用余额(不含通过融资融券交易融入的资金或证券)合计不低于人民币50万元;
            </p>
            <p>
              <span class="num">3、</span
              >在证券公司开户6个月以上并具备融资融券参与资格或者金融期货交易经历;
            </p>
            <p>
              <span class="num">4、</span
              >具备期权基础知识，通过交易所认可的相应等级知识测试；
            </p>
            <p>
              <span class="num">5、</span>具有交易所认可的期权模拟交易经历；
            </p>
            <div style="margin: 0.08rem 0 0.08rem 0.2rem">
              <a class="com_btn border" @click="toBizType('010135')"
                >申请开通股票期权全真模拟交易账户</a
              >
            </div>
            <p>
              <span class="num">6、</span
              >不存在严重不良诚信记录，不存在法律、法规、规章和交易所业务规则禁止或者限制从事期权交易的情形;
            </p>
          </div>
          <p>
            已开立正常状态的沪市衍生品合约账户、风险承受能力C3及以上且有效期在一年以上、满足上述第6项条件的个人投资者，开立深市期权账户时视同符合本条要求。
          </p>
        </div>
      </div>
      <div v-else-if="szPrompt" class="bus_txtbox">
        <p style="margin-bottom: 0.12rem">
          开通深圳衍生品合约账户需先开通上海市场，上海衍生品合约账户开通方式如下：
        </p>
        <p style="margin-bottom: 0.12rem">
          请您本人携带证件亲临国金证券营业部，现场申请开立上海市场衍生品合约账户。<a
            class="link_right_arrow"
            @click="jumpPage"
            >点击查看营业部地址</a
          >
        </p>
        <p>申请开立股票期权账户，您需要满足以下条件:</p>
        <p>
          （一）申请开户前20个交易日日均托管在公司的证券市值与资金账户可用余额（不含通过融资融券交易融入的资金或证券），合计不低于人民币50万元;
        </p>
        <p>（二）风险承受能力为C3及以上，且有效期不低于1年;</p>
        <p>
          （三）在证券公司开户6个月以上并具备融资融券参与资格或者金融期货交易经历;
        </p>
        <p>（四）具备期权基础知识，通过交易所认可的相应等级知识测试;</p>
        <p>（五）具有交易所认可的期权模拟交易经历;</p>
        <p>
          （六）不存在严重不良诚信记录，不存在法律、法规、规章和交易所业务规则禁止或者限制从事期权交易的情形。
        </p>
        <p>详询客服热线 95310。</p>
      </div>
      <!-- <div v-else>
        <div class="acct_nodata">
          <div class="icon"><img src="@/assets/images/noData3.svg" /></div>
          <h5>您尚未开通沪市股票期权账户</h5>
          <p>线上开通深市股票期权账户前，您需临柜开立沪市股票期权账户。</p>
          <div class="cm_btn_wrap">
            <a class="com_btn border" @click="toBizType('010169')">立即预约</a>
          </div>
        </div>
        <div class="bus_txtbox">
          <div class="sub_txt">
            <h5>如何开立沪市期权账户？</h5>
            <p>
              <span class="num">1、</span
              >投资者风险承受能力为C3及以上且有效期一年以上(一级交易权限:C3及以上或者专业投资者;二、三级交易权限均需为C4及以上或者专业投资者);
            </p>
            <p>
              <span class="num">2、</span
              >申请开户前20个交易日日均托管在公司的证券市值与资金账户可用余额(不含通过融资融券交易融入的资金或证券)合计不低于人民币50万元;
            </p>
            <p>
              <span class="num">3、</span
              >在证券公司开户6个月以上并具备融资融券参与资格或者金融期货交易经历;
            </p>
            <p>
              <span class="num">4、</span
              >具备期权基础知识，通过交易所认可的相应等级知识测试；
            </p>
            <p>
              <span class="num">5、</span>具有交易所认可的期权模拟交易经历；
            </p>
            <div style="margin: 0.08rem 0 0.08rem 0.2rem">
              <a class="com_btn border" @click="toBizType('010135')"
                >申请开通股票期权全真模拟交易账户</a
              >
            </div>
            <p>
              <span class="num">6、</span
              >不存在严重不良诚信记录，不存在法律、法规、规章和交易所业务规则禁止或者限制从事期权交易的情形;
            </p>
          </div>
          <p>
            已开立正常状态的沪市衍生品合约账户、风险承受能力C3及以上且有效期在一年以上、满足上述第6项条件的个人投资者，开立深市期权账户时视同符合本条要求。
          </p>
        </div>
      </div> -->
      <div v-else class="bus_txtbox">
        <p style="margin-bottom: 0.12rem">
          请您本人携带证件亲临国金证券营业部，现场申请开立上海市场衍生品合约账户。<a
            class="link_right_arrow"
            @click="jumpPage"
            >点击查看营业部地址</a
          >
        </p>
        <p>申请开立股票期权账户，您需要满足以下条件:</p>
        <p>
          （一）申请开户前20个交易日日均托管在公司的证券市值与资金账户可用余额（不含通过融资融券交易融入的资金或证券），合计不低于人民币50万元;
        </p>
        <p>（二）风险承受能力为C3及以上，且有效期不低于1年;</p>
        <p>
          （三）在证券公司开户6个月以上并具备融资融券参与资格或者金融期货交易经历;
        </p>
        <p>（四）具备期权基础知识，通过交易所认可的相应等级知识测试;</p>
        <p>（五）具有交易所认可的期权模拟交易经历;</p>
        <p>
          （六）不存在严重不良诚信记录，不存在法律、法规、规章和交易所业务规则禁止或者限制从事期权交易的情形。
        </p>
        <p>详询客服热线 95310。</p>
      </div>
    </div>
    <div v-else class="gray_bg">
      <ul class="com_infolist spel">
        <li>
          <span class="tit">股票期权账户</span>
          <p>{{ dataList.optionFundAccount }}</p>
        </li>
        <li>
          <span class="tit">一码通账户</span>
          <p>{{ dataList.acodeAccount || '--' }}</p>
        </li>
      </ul>
      <ul class="market_ctlist">
        <li>
          <div class="base">
            <div class="bg">
              <img src="@/assets/images/bg_shanghai.png" />
            </div>
            <h5>上海市场衍生品合约账户</h5>
            <p v-if="shAccInfo.stockAccount">
              {{ shAccInfo.stockAccount }}
            </p>
            <p v-else>未开通</p>
            <span v-if="shAccInfo.holderStatusDesc" class="state">{{
              shAccInfo.holderStatusDesc
            }}</span>
          </div>
          <div class="opea">
            <a
              v-show="!shAccInfo.stockAccount"
              class="com_btn"
              @click="shPrompt = true"
              >开通账户</a
            >
          </div>
        </li>
        <li>
          <div class="base">
            <div class="bg">
              <img src="@/assets/images/bg_shenzhen.png" />
            </div>
            <h5>深圳市场衍生品合约账户</h5>
            <p v-if="szAccInfo.stockAccount">
              {{ szAccInfo.stockAccount }}
            </p>
            <p v-else>未开通</p>
            <span v-if="szAccInfo.holderStatusDesc" class="state">{{
              szAccInfo.holderStatusDesc
            }}</span>
          </div>
          <div class="opea">
            <a v-show="openSZProduct" class="com_btn" @click="toNext"
              >开通账户</a
            >
          </div>
        </li>
      </ul>
      <div
        v-show="
          shAccInfo.holderStatusDesc && shAccInfo.holderStatusDesc !== '正常'
        "
        class="wx_tips"
      >
        <p>温馨提示：</p>
        <p>
          您当前上海市场衍生品合约账户状态异常，无法办理此业务。如有疑问您可拨打客服95310进行咨询。
        </p>
      </div>
    </div>
  </fragment>
</template>

<script>
import { optionAccountQuery } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import { EXCHANGE_TYPE } from '@/common/enumeration';
export default {
  name: 'OptAccOpen',
  inject: ['eventMessage'],
  data() {
    return {
      loading: true,
      dataList: {},
      szPrompt: false,
      shPrompt: false
    };
  },
  computed: {
    showPrompt() {
      return this.noOptAcc || this.szPrompt || this.shPrompt;
    },
    noOptAcc() {
      return (
        this.dataList.optionFundAccount === '' ||
        this.dataList.fundAccountStatus !== '0'
      );
    },
    shAccInfo() {
      return (
        this.dataList.optionAccountInfoList.filter(
          ({ exchangeType }) => EXCHANGE_TYPE.SH === exchangeType
        )[0] || {}
      );
    },
    szAccInfo() {
      return (
        this.dataList.optionAccountInfoList.filter(
          ({ exchangeType }) => EXCHANGE_TYPE.SZ === exchangeType
        )[0] || {}
      );
    },
    toNextFlag() {
      return (
        this.shAccInfo?.holderStatus === '0' && !this.szAccInfo.stockAccount
      );
    },
    openSZProduct() {
      return (
        this.toNextFlag ||
        (!this.shAccInfo.stockAccount && !this.szAccInfo.stockAccount)
      );
    }
  },
  watch: {
    showPrompt: {
      handler(bool) {
        if (bool) {
          this.$store.commit('flow/setWhiteBg', true);
        } else {
          this.$store.commit('flow/setWhiteBg', false);
        }
      },
      immediate: true
    }
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    this.eventMessage(this, EVENT_NAME.BACK_BTN, { event: this.back });
  },
  mounted() {
    this.renderingView();
  },
  methods: {
    back() {
      if (this.szPrompt) {
        this.szPrompt = false;
      } else if (this.shPrompt) {
        this.shPrompt = false;
      } else {
        this.eventMessage(this, EVENT_NAME.PREV_FLOW);
      }
    },
    renderingView() {
      optionAccountQuery({}, { filter: true })
        .then(({ data, code, msg }) => {
          this.loading = false;
          if (code === 0) {
            this.dataList = data;
          } else if (code === 1001) {
            this.dataList = {
              optionFundAccount: ''
            };
          } else {
            return Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },
    jumpPage() {
      if ($hvue.platform === '0') {
        window.location.href =
          $hvue.customConfig.thirdPartyUrl.businessDepartment;
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          params: {
            url: $hvue.customConfig.thirdPartyUrl.businessDepartment,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      }
    },
    toNext() {
      if (!this.szAccInfo.stockAccount && !this.shAccInfo.stockAccount) {
        this.szPrompt = true;
      } else {
        this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
          csdc_acode_account: this.dataList.acodeAccount,
          fund_account_data: JSON.stringify([
            {
              assetProp: this.dataList.assetProp,
              fundAccount: this.dataList.optionFundAccount
            }
          ])
        });
      }
    },
    toBizType(bizType) {
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType, initJumpMode: '0' });
      });
    }
  }
};
</script>

<style scoped>
.gray_bg {
  background-color: #f4f4f4;
}
</style>
