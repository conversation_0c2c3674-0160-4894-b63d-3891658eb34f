<template>
  <section class="main fixed white_bg" data-page="home" style="position: fixed">
    <t-header />
    <article class="content">
      <div class="notice_box risk">
        <div class="pic">
          <img src="@/assets/images/test_noticeimg01.png" />
        </div>
        <p ref="contentTxt" class="txt_left">
          尊敬的客户，<strong
            >若您的风险承受能力等级为C3（中风险承受），您的深圳期权交易权限将为一级；</strong
          ><br />若您的风险等级为C4（中高风险承受）及以上，您的深圳期权交易权限将匹配我司已开立的上海期权交易权限。<b style="font-weight: 600;color: black;">在为您开通深圳衍生品合约账户时，深圳期权持仓限额、买入额度及佣金收取标准将与我司已开立的上海期权账户保持一致。</b>
        </p>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn block">
        <a class="p_button" @click="pageResolve">同意</a>
        <a class="p_button border" @click="pageReject">不同意</a>
      </div>
    </footer>
  </section>
</template>

<script>
import {
  investProInfoQryV2,
  addClientCritMark,
  queryOptionRiskLevelInfo
} from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import { EXCHANGE_TYPE } from '@/common/enumeration';
export default {
  name: 'OptAccOpenConfirm',
  inject: ['eventMessage', 'tkFlowInfo'],
  data() {
    return {
      loading: true,
      corpRiskLevel: '',
      optLevel: ''
    };
  },
  created() {},
  mounted() {
    this.renderingView();
  },
  methods: {
    renderingView() {
      const { inProperty } = this.tkFlowInfo();
      const bizType = inProperty.bizType;
      const { fundAccountData, specialFlag } = inProperty;
      investProInfoQryV2({
        bizType,
        flowToken: sessionStorage.getItem('TKFlowToken')
      })
        .then(({ data }) => {
          this.corpRiskLevel = data.corpRiskLevel;
          return queryOptionRiskLevelInfo({
            optionFundAccount: JSON.parse(fundAccountData)[0].fundAccount,
            exchangeType: EXCHANGE_TYPE.SH,
            specialFlag,
            flowToken: sessionStorage.getItem('TKFlowToken')
          });
        })
        .then(({ data }) => {
          this.optLevel = data.optLevel;
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },
    pageResolve() {
      const alertTxt = this.$refs.contentTxt.innerText;
      addClientCritMark({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        markType: '5',
        markContent: alertTxt,
        confirmFlag: '1'
      });
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        corpRiskLevel: this.corpRiskLevel,
        optLevel: this.optLevel
      });
    },
    pageReject() {
      this.$TAlert({
        hasCancel: true,
        title: '温馨提示',
        tips: '股票期权交易风险较高，交易规则复杂。如您对交易规则有疑问，可以咨询客服95310，或亲临国金证券营业部申请开通。',
        confirmBtn: '退出开户',
        cancelBtn: '返回',
        confirm: this.toIndex
      });
    },
    toIndex() {
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    }
  }
};
</script>

<style></style>
