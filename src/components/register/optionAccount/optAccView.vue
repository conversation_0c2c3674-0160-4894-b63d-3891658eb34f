<template>
  <article class="content">
    <div v-for="(item, index) in dataList" :key="index">
      <div class="acct_status_item">
        <ul class="acct_list">
          <li>
            <p>
              股票期权资金账户<em class="acct_s_tag">{{
                item.fundAccountStatus
              }}</em>
            </p>
            <span class="state">{{ item.fundAccount }}</span>
          </li>
        </ul>
      </div>
      <div class="qq_acct_wrap">
        <div
          v-for="(it, idx) in item.optionAccountInfoList"
          :key="idx"
          class="qq_acct_item"
        >
          <h5>
            <strong
              >{{
                it.exchangeType === EXCHANGE_TYPE.SH
                  ? '上海衍生品合约账户'
                  : '深圳衍生品合约账户'
              }}<span class="acct_s_tag">{{
                it.holderStatusDesc
              }}</span></strong
            ><em>{{ it.stockAccount }}</em>
          </h5>
          <div class="info">
            <dl>
              <dt>期权交易级别</dt>
              <dd>{{ getOptLevel(it.optLevel) }}</dd>
            </dl>
            <dl>
              <dt>期权买入额度(万元)</dt>
              <dd>{{ moneyFormat(it.purApplyQuota, false, 2) }}</dd>
            </dl>
            <dl>
              <dt>已用买入额度(万元)</dt>
              <dd>{{ moneyFormat(it.usedPurQuota, false, 2) }}</dd>
            </dl>
            <dl>
              <dt>买入额度到期日期</dt>
              <dd>{{ dateFormat(it.endDate) }}</dd>
            </dl>
            <dl>
              <dt>权利仓(张)</dt>
              <dd>{{ mathCeil(it.rightHoldQuota) }}</dd>
            </dl>
            <dl>
              <dt>总持仓(张)</dt>
              <dd>{{ mathCeil(it.totalHoldQuota) }}</dd>
            </dl>
            <dl>
              <dt>单日买入开仓(张)</dt>
              <dd>{{ mathCeil(it.todayBuyQuota) }}</dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
    <div class="tip_txtbox">
      <p>
        温馨提示：关于期权买入额度调整标准请点击
        <a class="com_link" @click="clickInput('compareTable')">查看详情</a>
      </p>
    </div>
    <compareTable ref="compareTable" />
  </article>
</template>

<script>
import compareTable from './components/compareTable.vue';
import { optionAccountInfoQuery } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import { EXCHANGE_TYPE } from '@/common/enumeration';
import { moneyFormat, dateFormat } from '@/common/util';

export default {
  name: 'OptAccView',
  components: { compareTable },
  inject: ['eventMessage'],
  data() {
    return {
      dataList: [],
      EXCHANGE_TYPE
    };
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
      text: '调整买入额度',
      btnStatus: 2,
      data: this.checkAccount
    });
  },
  mounted() {
    optionAccountInfoQuery({
      flowToken: sessionStorage.getItem('TKFlowToken')
    })
      .then((res) => {
        this.dataList = res.data.fundAccountInfoList;
      })
      .catch((err) => {
        this.$TAlert({
          tips: err
        });
      });
  },
  methods: {
    moneyFormat,
    dateFormat,
    clickInput(propType) {
      this.$refs[propType].show();
    },
    checkAccount() {
      const optionAccountInfoList = this.dataList[0].optionAccountInfoList;
      const errorList = optionAccountInfoList
        .filter(({ holderStatusDesc }) => holderStatusDesc !== '正常')
        .map(({ exchangeType }) =>
          exchangeType === EXCHANGE_TYPE.SZ ? '深圳' : '上海'
        );
      if (optionAccountInfoList.length === errorList.length) {
        this.$TAlert({
          tips: `您的${errorList.join(
            ''
          )}衍生品合约账户状态异常，请先去规范账户后再来办理此业务`
        });
        return;
      }
      const flowParam = {
        selectedAccountsData: JSON.stringify([
          {
            fundAccount: this.dataList[0].fundAccount,
            assetProp: this.dataList[0].assetProp
          }
        ])
      };
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, flowParam);
    },
    mathCeil(numString) {
      if (numString !== '') {
        return Math.ceil(parseFloat(numString));
      } else {
        return '';
      }
    },
    getOptLevel(num) {
      const levelName = [
        '零',
        '一',
        '二',
        '三',
        '四',
        '五',
        '六',
        '七',
        '八',
        '九'
      ];
      if (!levelName[num]) {
        return '无';
      } else {
        return `${levelName[num]}级`;
      }
    }
  }
};
</script>

<style></style>
