<template>
  <article class="content">
    <ul class="com_infolist">
      <li>
        <span class="tit">您在我司自有资产余额 (万元)</span>
        <p>{{ moneyFormat(dataList.enableBalance, false, 2) }}</p>
      </li>
      <li>
        <span class="tit">前6个月日均沪深证券市值20% (万元)</span>
        <p>{{ moneyFormat(dataList.optMarketValue, false, 2) }}</p>
      </li>
    </ul>
    <div v-if="shViewData" class="com_title">
      <h5>上海</h5>
    </div>
    <div v-if="shViewData" class="amount_info">
      <h5 class="tit">投资者买入额度申请 (万元)</h5>
      <div class="amount_input">
        <i class="icon"></i>
        <input
          v-model="purQuotaSH"
          class="t1"
          type="text"
          placeholder="请输入拟申请的额度"
          maxlength="30"
          @input="handleSH"
        />
        <a
          class="com_link"
          @click="purQuotaSH = mathFloor(shViewData.maxBuyLimit)"
          >最高额度</a
        >
      </div>
      <div class="tips">
        <p v-if="shViewData.error" class="error_span">
          最高可调整到{{ shViewData.maxBuyLimit }}万元，请重新输入
        </p>
        <p v-else class="imp_span">
          最高买入额度: {{ shViewData.maxBuyLimit }}万元
        </p>
        <div class="now_num">
          当前额度: {{ shViewData.currentBuyLimit }}万元
        </div>
      </div>
      <p class="zs">
        您在我司自有资产余额{{ shViewData.n }}:
        {{ shViewData.marketEnableBalance }}万元
      </p>
    </div>
    <div v-if="szViewData" class="com_title">
      <h5>深圳</h5>
    </div>
    <div v-if="szViewData" class="amount_info">
      <h5 class="tit">投资者买入额度申请 (万元)</h5>
      <div class="amount_input">
        <i class="icon"></i>
        <input
          v-model="purQuotaSZ"
          class="t1"
          type="text"
          placeholder="请输入拟申请的额度"
          maxlength="30"
          @input="handleSZ"
        />
        <a
          class="com_link"
          @click="purQuotaSZ = mathFloor(szViewData.maxBuyLimit)"
          >最高额度</a
        >
      </div>
      <div class="tips">
        <p v-if="szViewData.error" class="error_span">
          最高可调整到{{ szViewData.maxBuyLimit }}万元，请重新输入
        </p>
        <p v-else class="imp_span">
          最高买入额度: {{ szViewData.maxBuyLimit }}万元
        </p>
        <div class="now_num">
          当前额度: {{ szViewData.currentBuyLimit }}万元
        </div>
      </div>
      <p class="zs">
        您在我司自有资产余额{{ szViewData.n }}:
        {{ szViewData.marketEnableBalance }}万元
      </p>
    </div>
  </article>
</template>

<script>
import {
  addClientCritMark,
  optionMarketQuotaInfoQuery
} from '@/service/service';
import { EXCHANGE_TYPE } from '@/common/enumeration';
import { EVENT_NAME } from '@/common/formEnum';
import { moneyFormat, mathFloor } from '@/common/util';

export default {
  name: 'OptfundLimitMod',
  inject: ['eventMessage'],
  props: {
    selectedAccountsData: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dataList: [],
      szViewData: null,
      shViewData: null,
      EXCHANGE_TYPE,
      purQuotaSZ: '',
      purQuotaSH: ''
    };
  },
  watch: {
    selectedAccountsData: {
      handler(newVal) {
        if (newVal !== '') {
          this.renderingView();
        }
      },
      immediate: true
    },
    purQuotaSZ: {
      handler(v) {
        this.setBtnStatus(this.handlerInput());
      },
      immediate: true
    },
    purQuotaSH: {
      handler(v) {
        this.setBtnStatus(this.handlerInput());
      },
      immediate: true
    }
  },
  methods: {
    moneyFormat,
    mathFloor,
    renderingView() {
      const { fundAccount } = JSON.parse(this.selectedAccountsData)[0];
      optionMarketQuotaInfoQuery({
        optionFundAccount: fundAccount,
        flowToken: sessionStorage.getItem('TKFlowToken')
      })
        .then(({ data }) => {
          this.dataList = data;
          let { optionAccountInfoList } = this.dataList;
          optionAccountInfoList = optionAccountInfoList.map((it) => {
            this.$set(it, 'errorContent', false);
            it.currentBuyLimit = this.mathCeil(
              this.moneyFormat(it.currentBuyLimit)
            );
            it.maxBuyLimit = this.mathCeil(this.moneyFormat(it.maxBuyLimit));
            it.marketEnableBalance = this.mathCeil(
              this.moneyFormat(it.marketEnableBalance)
            );
            return it;
          });
          this.szViewData = optionAccountInfoList.filter(
            ({ exchangeType }) => exchangeType === EXCHANGE_TYPE.SZ
          )[0];
          this.shViewData = optionAccountInfoList.filter(
            ({ exchangeType }) => exchangeType === EXCHANGE_TYPE.SH
          )[0];
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },
    handlerInput() {
      const regExpNum = /^(?!0\d)([1-9]\d{0,29})$/;
      let szPassFlag = false;
      let shPassFlag = false;
      if (this.szViewData && this.purQuotaSZ !== '') {
        this.szViewData.error = false;
        if (regExpNum.test(this.purQuotaSZ)) {
          if (parseFloat(this.purQuotaSZ) > this.szViewData.maxBuyLimit) {
            this.szViewData.error = true;
          } else {
            this.szViewData.error = false;
            szPassFlag = true;
          }
        } else {
          return false;
        }
      }
      if (this.shViewData && this.purQuotaSH !== '') {
        this.shViewData.error = false;
        if (regExpNum.test(this.purQuotaSH)) {
          if (parseFloat(this.purQuotaSH) > this.shViewData.maxBuyLimit) {
            this.shViewData.error = true;
          } else {
            this.shViewData.error = false;
            shPassFlag = true;
          }
        } else {
          return false;
        }
      }
      if (this.shViewData?.error || this.szViewData?.error) {
        return false;
      }
      return szPassFlag || shPassFlag;
    },
    setBtnStatus(display) {
      this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
        btnStatus: display ? 2 : 0,
        data: this.checkInput
      });
    },
    checkInput() {
      /*需求说明:
      1.单市场（仅上海/深圳）输入的额度低于客户对应市场（上海/深圳）的已有买入额度
      2.单市场（仅上海/深圳）输入的额度超过客户对应市场（上海/深圳）已有买入额度
      3	单市场（仅上海/深圳）输入的额度与对应市场（上海/深圳）已有的买入额度一致
      4	单市场（仅上海/深圳）输入额度大于对应市场（上海/深圳）的最高额度；（点击最高额度按钮，输入框内自动填充可申请的最高额度）
      5	双市场（上海+深圳）上海：输入额度大于对应市场已有的买入额度；深圳：输入额度大于对应市场已有的买入额度；
      6	双市场（上海+深圳）上海：输入额度大于对应市场已有的买入额度；深圳：输入额度等于对应市场已有的买入额度；
      7	双市场（上海+深圳）上海：输入额度大于对应市场已有的买入额度；深圳：输入额度小于对应市场已有的买入额度；
      8	双市场（上海+深圳）上海：输入额度等于对应市场已有的买入额度；深圳：输入额度大于对应市场已有的买入额度；
      9	双市场（上海+深圳）上海：输入额度等于对应市场已有的买入额度；深圳：输入额度等于对应市场已有的买入额度；
      10 双市场（上海+深圳）上海：输入额度等于对应市场已有的买入额度；深圳：输入额度小于对应市场已有的买入额度；
      11 双市场（上海+深圳）上海：输入额度小于对应市场已有的买入额度；深圳：输入额度大于对应市场已有的买入额度；
      12 双市场（上海+深圳）上海：输入额度小于对应市场已有的买入额度；深圳：输入额度等于对应市场已有的买入额度；
      13 双市场（上海+深圳）上海：输入额度小于对应市场已有的买入额度；深圳：输入额度小于对应市场已有的买入额度；*/
      let errorText = '';
      if (this.purQuotaSZ !== '' && this.purQuotaSH !== '') {
        errorText = this.getErrorText().all();
      } else if (this.purQuotaSZ !== '') {
        errorText = this.getErrorText().onlySZ();
      } else if (this.purQuotaSH !== '') {
        errorText = this.getErrorText().onlySH();
      } else {
        return;
      }
      if (errorText !== '') {
        this.$TAlert({
          title: '温馨提示',
          tips: errorText,
          hasCancel: true,
          cancelBtn: '取消',
          confirmBtn: '确认',
          confirm: () => {
            addClientCritMark({
              flowToken: sessionStorage.getItem('TKFlowToken'),
              markType: '4',
              markContent: errorText,
              confirmFlag: '1'
            })
              .then((res) => {
                this.submitInfo();
              })
              .catch((err) => {
                this.submitInfo();
              });
          },
          cancel: () => {}
        });
      } else {
        this.submitInfo();
      }
    },
    submitInfo() {
      let purQuotaData = [];
      if(!this.handlerInput()) return false;
      if (this.szViewData && this.purQuotaSZ !== '') {
        purQuotaData.push({
          exchange_type: EXCHANGE_TYPE.SZ,
          pur_quota: this.moneyFormat(this.purQuotaSZ, true)
        });
      }
      if (this.shViewData && this.purQuotaSH !== '') {
        purQuotaData.push({
          exchange_type: EXCHANGE_TYPE.SH,
          pur_quota: this.moneyFormat(this.purQuotaSH, true)
        });
      }
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        purQuotaData: JSON.stringify(purQuotaData)
      });
    },
    getErrorText() {
      const _this = this;
      return {
        onlySH() {
          if (
            parseFloat(_this.purQuotaSH) ===
            parseFloat(_this.shViewData.currentBuyLimit)
          ) {
            return '您申请上海的额度与当前买入额度—致。请您确认是否知晓相关风险并确认继续调整?';
          } else if (
            parseFloat(_this.purQuotaSH) >
            parseFloat(_this.shViewData.maxBuyLimit)
          ) {
            return '';
          } else if (
            parseFloat(_this.purQuotaSH) >
            parseFloat(_this.shViewData.currentBuyLimit)
          ) {
            return '您申请上海的额度高于当前买入额度。请您确认是否知晓相关风险并确认继续调整?';
          } else if (
            parseFloat(_this.purQuotaSH) <
            parseFloat(_this.shViewData.currentBuyLimit)
          ) {
            return '您申请上海的额度低于当前买入额度，调整后，若您的持仓超过已用买入额度，您可选择将现有持仓持有到期，但不能新增权利仓持仓。请您确认是否知晓相关风险并确认继续调整?';
          } else {
            return '';
          }
        },
        onlySZ() {
          if (
            parseFloat(_this.purQuotaSZ) ===
            parseFloat(_this.szViewData.currentBuyLimit)
          ) {
            return '您申请深圳的额度与当前买入额度—致。请您确认是否知晓相关风险并确认继续调整?';
          } else if (
            parseFloat(_this.purQuotaSZ) >
            parseFloat(_this.szViewData.maxBuyLimit)
          ) {
            _this.szViewData.error = true;
            return '';
          } else if (
            parseFloat(_this.purQuotaSZ) >
            parseFloat(_this.szViewData.currentBuyLimit)
          ) {
            return '您申请深圳的额度高于当前买入额度。请您确认是否知晓相关风险并确认继续调整?';
          } else if (
            parseFloat(_this.purQuotaSZ) <
            parseFloat(_this.szViewData.currentBuyLimit)
          ) {
            return '您申请深圳的额度低于当前买入额度，调整后，若您的持仓超过已用买入额度，您可选择将现有持仓持有到期，但不能新增权利仓持仓。请您确认是否知晓相关风险并确认继续调整?';
          } else {
            return '';
          }
        },
        all() {
          if (
            parseFloat(_this.purQuotaSZ) >
            parseFloat(_this.szViewData.maxBuyLimit)
          ) {
            _this.szViewData.error = true;
            return '';
          } else if (
            parseFloat(_this.purQuotaSH) >
            parseFloat(_this.shViewData.maxBuyLimit)
          ) {
            _this.shViewData.error = true;
            return '';
          } else if (
            parseFloat(_this.purQuotaSZ) >
              parseFloat(_this.szViewData.currentBuyLimit) &&
            parseFloat(_this.purQuotaSH) >
              parseFloat(_this.shViewData.currentBuyLimit)
          ) {
            return '您申请上海和深圳的额度高于当前买入额度。请您确认是否知晓相关风险并确认继续调整?';
          } else if (
            parseFloat(_this.purQuotaSZ) ===
              parseFloat(_this.szViewData.currentBuyLimit) &&
            parseFloat(_this.purQuotaSH) >
              parseFloat(_this.shViewData.currentBuyLimit)
          ) {
            return '您申请上海的额度高于当前买入额度。您申请深圳的额度与当前买入额度—致。请您确认是否知晓相关风险并确认继续调整?';
          } else if (
            parseFloat(_this.purQuotaSZ) <
              parseFloat(_this.szViewData.currentBuyLimit) &&
            parseFloat(_this.purQuotaSH) >
              parseFloat(_this.shViewData.currentBuyLimit)
          ) {
            return '您申请上海的额度高于当前买入额度。您申请深圳的额度低于当前买入额度，调整后，若您的持仓超过已用买入额度，您可选择将现有持仓持有到期，但不能新增权利仓持仓。请您确认是否知晓相关风险并确认继续调整?';
          } else if (
            parseFloat(_this.purQuotaSZ) >
              parseFloat(_this.szViewData.currentBuyLimit) &&
            parseFloat(_this.purQuotaSH) ===
              parseFloat(_this.shViewData.currentBuyLimit)
          ) {
            return '您申请上海的额度与当前买入额度—致。您申请深圳的额度高于当前买入额度。请您确认是否知晓相关风险并确认继续调整?';
          } else if (
            parseFloat(_this.purQuotaSZ) ===
              parseFloat(_this.szViewData.currentBuyLimit) &&
            parseFloat(_this.purQuotaSH) ===
              parseFloat(_this.shViewData.currentBuyLimit)
          ) {
            return '您申请上海和深圳的额度与当前买入额度—致。请您确认是否知晓相关风险并确认继续调整?';
          } else if (
            parseFloat(_this.purQuotaSZ) <
              parseFloat(_this.szViewData.currentBuyLimit) &&
            parseFloat(_this.purQuotaSH) ===
              parseFloat(_this.shViewData.currentBuyLimit)
          ) {
            return '您申请上海的额度与当前买入额度—致。您申请深圳的额度低于当前买入额度，调整后，若您的持仓超过已用买入额度，您可选择将现有持仓持有到期，但不能新增权利仓持仓。请您确认是否知晓相关风险并确认继续调整?';
          } else if (
            parseFloat(_this.purQuotaSZ) >
              parseFloat(_this.szViewData.currentBuyLimit) &&
            parseFloat(_this.purQuotaSH) <
              parseFloat(_this.shViewData.currentBuyLimit)
          ) {
            return '您申请上海的额度低于当前买入额度，调整后，若您的持仓超过已用买入额度，您可选择将现有持仓持有到期，但不能新增权利仓持仓。您申请深圳的额度高于当前买入额度。请您确认是否知晓相关风险并确认继续调整?';
          } else if (
            parseFloat(_this.purQuotaSZ) ===
              parseFloat(_this.szViewData.currentBuyLimit) &&
            parseFloat(_this.purQuotaSH) <
              parseFloat(_this.shViewData.currentBuyLimit)
          ) {
            return '您申请上海的额度低于当前买入额度，调整后，若您的持仓超过已用买入额度，您可选择将现有持仓持有到期，但不能新增权利仓持仓。您申请深圳的额度与当前买入额度—致。请您确认是否知晓相关风险并确认继续调整?';
          } else if (
            parseFloat(_this.purQuotaSZ) <
              parseFloat(_this.szViewData.currentBuyLimit) &&
            parseFloat(_this.purQuotaSH) <
              parseFloat(_this.shViewData.currentBuyLimit)
          ) {
            return '您申请上海和深圳的额度低于当前买入额度，调整后，若您的持仓超过已用买入额度，您可选择将现有持仓持有到期，但不能新增权利仓持仓。请您确认是否知晓相关风险并确认继续调整?';
          } else {
            return '';
          }
        }
      };
    },
    handleSH() {
      if (this.purQuotaSH !== '') {
        this.purQuotaSH = this.mathCeil(this.purQuotaSH.replace(/[^\d]/g, ''));
      }
    },
    handleSZ() {
      if (this.purQuotaSZ !== '') {
        this.purQuotaSZ = this.mathCeil(this.purQuotaSZ.replace(/[^\d]/g, ''));
      }
    },
    mathCeil(numString) {
      if (
        numString !== '' &&
        parseFloat(numString)?.constructor?.name === 'Number'
      ) {
        return Math.ceil(parseFloat(numString));
      } else {
        return '';
      }
    }
  }
};
</script>

<style></style>
