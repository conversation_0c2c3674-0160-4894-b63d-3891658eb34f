<template>
  <article class="content">
    <div class="com_title">
      <h5>拍摄并上传机动车驾驶证或社会保障卡</h5>
    </div>
    <div class="upload_com_box spel">
      <div class="assist_upload_wrap">
        <div class="tips">
          系统未能查询到您的公安头像，请补充上传能够证明您身份，带身份证号及头像的驾驶证、社保卡等图片资料。
        </div>
        <div
          v-for="({ base64, uploaded }, index) in auxiIdImgData"
          :key="index"
          class="assist_upload_item"
        >
          <div class="pic">
            <i v-show="uploaded" class="watermark" />
            <img :src="base64" />
          </div>
          <div v-if="!uploaded" class="tit">
            <span>驾驶证/社会保障卡</span>
          </div>
          <a v-if="!uploaded" class="btn" @click.stop="updateImg(index)"
            >点击上传</a
          >
          <a v-else class="reset_btn" @click.stop="updateImg(index)"
            >重新上传</a
          >
        </div>
        <a
          v-show="auxiIdImgData.length < maxImgLength"
          class="upload_addbtn"
          @click="addImg"
          ><i />增加补充证明</a
        >
      </div>
      <div class="photo_tips">
        <h5 class="title">请注意照片拍摄规范</h5>
        <ul class="list">
          <li>
            <div class="pic">
              <img src="@/assets/images/sl_img05_1.png" />
            </div>
            <span class="ok">合规照片</span>
          </li>
          <li>
            <div class="pic">
              <img src="@/assets/images/sl_img05_2.png" />
            </div>
            <span class="error">边角缺失</span>
          </li>
          <li>
            <div class="pic">
              <img src="@/assets/images/sl_img05_3.png" />
            </div>
            <span class="error">照片模糊</span>
          </li>
          <li>
            <div class="pic">
              <img src="@/assets/images/sl_img05_4.png" />
            </div>
            <span class="error">反光强烈</span>
          </li>
        </ul>
        <p>1. 拍摄时请将证件平放，手机横向拍摄</p>
        <p>2. 确保证明材料 <span class="imp">边框完整、文字清晰可见</span></p>
      </div>
    </div>

    <no-ocr-upload ref="getImgBoxThinkive" @getImgCallBack="getImgCallBack" />
    <get-img-alipay ref="getImgAlipay" @getImgCallBack="getImgCallBack" />
    <get-img-box-browser
      ref="getImgBoxBrowser"
      :scan="true"
      @getImgCallBack="getImgCallBack"
    />
  </article>
</template>
<script>
import { EVENT_NAME } from '@/common/formEnum';
import noOcrUpload from '@/components/uploadImage/noOcrUpload.vue';
import getImgBoxBrowser from '@/components/getImg_browser.vue';
import { imageUpload } from '@/service/service';
import GetImgAlipay from '@/components/getImg_alipay';
import AlipayUtil from '@/common/AlipayUtil';

export default {
  name: 'AuxiIdCardUpload',
  inject: ['eventMessage'],
  components: {
    getImgBoxBrowser,
    noOcrUpload,
    GetImgAlipay
  },
  data() {
    return {
      maxImgLength: 2,
      uploadIndex: 0,
      auxiIdImgData: []
    };
  },
  computed: {
    showNextBtn() {
      return this.auxiIdImgData.some(({ uploaded }) => uploaded);
    }
  },
  watch: {
    showNextBtn: {
      handler(bool) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          btnStatus: bool ? 2 : 0,
          data: this.submitInfo
        });
      },
      immediate: true
    }
  },
  created() {
    this.$store.commit('flow/setWhiteBg', true);
    this.addImg();
  },
  methods: {
    addImg() {
      this.auxiIdImgData.push({
        base64: require('@/assets/images/sl_img12.png'),
        uploaded: false,
        path: ''
      });
    },
    updateImg(index) {
      this.uploadIndex = index;
      if ($hvue.platform === '0') {
        console.log('alipayFlag=' + new AlipayUtil().checkAlipay);
        if (new AlipayUtil().checkAlipay) {
          this.$refs.getImgAlipay.getImg();
        } else {
          this.$refs.getImgBoxBrowser.getImg();
        }
      } else {
        this.$refs.getImgBoxThinkive.getImg();
      }
    },
    getImgCallBack(imgInfo) {
      imageUpload({
        imgContent: imgInfo.base64Image || imgInfo.base64,
        flowToken: sessionStorage.getItem('TKFlowToken')
      }).then(({ data }) => {
        this.$set(this.auxiIdImgData, this.uploadIndex, {
          base64: `data:image/jpeg;base64,${
            imgInfo.base64Image || imgInfo.base64
          }`,
          uploaded: true,
          path: data
        });
      });
    },
    submitInfo() {
      const pathData = this.auxiIdImgData
        .map(({ path }) => {
          return {
            image_file_path: path //图像文件地址
          };
        })
        .filter(({ image_file_path }) => image_file_path !== '');
      const auxiIdImgData = JSON.stringify([
        {
          image_no: '', //图像编号
          image_files: [...pathData]
        }
      ]);
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        auxiIdImgData
      });
    }
  }
};
</script>
<style scoped>
.watermark {
  width: 0.5rem;
  height: 0.5rem;
  background: url(../../../assets/images/ic_watermark.png) no-repeat center;
  background-size: 100%;
  position: absolute;
  top: 0.12rem;
  right: 0.12rem;
  z-index: 50;
}
</style>
