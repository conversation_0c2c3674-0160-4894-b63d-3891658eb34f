<template>
  <fragment>
    <div class="fc_basebox">
      <h5>请拍摄客户头像</h5>
      <p>请保证脸部清晰无遮挡无反光</p>
      <div class="pic"><img src="@/assets/images/video_face3.png" /></div>
    </div>
    <div class="lz_tipbox">
      <h5 class="title">请注意以下事项：</h5>
      <ul>
        <li>
          <i><img src="@/assets/images/p_fc_tp01.png" /></i
          ><span>确保光线清晰</span>
        </li>
        <li>
          <i><img src="@/assets/images/p_fc_tp05.png" /></i
          ><span>不能遮挡面部</span>
        </li>
        <li>
          <i><img src="@/assets/images/p_fc_tp03.png" /></i
          ><span>不能戴帽子</span>
        </li>
      </ul>
    </div>
    <avatar-upload ref="getImgBoxThinkive" @getImgCallBack="getImgCallBack" />
    <get-img-alipay
      ref="getImgAlipay"
      front="1"
      @getImgCallBack="getImgCallBack"
    />
    <getImgBoxBrowser
      ref="getImgBoxBrowser"
      :scan="true"
      :userCapture="true"
      @getImgCallBack="getImgCallBack"
    ></getImgBoxBrowser>
  </fragment>
</template>
<script>
import { EVENT_NAME } from '@/common/formEnum';
import avatarUpload from '@/components/uploadImage/avatarUpload.vue';
import getImgBoxBrowser from '@/components/getImg_browser.vue';
import { imageUpload } from '@/service/service';
import GetImgAlipay from '@/components/getImg_alipay';
import AlipayUtil from '@/common/AlipayUtil';

export default {
  name: 'FileIdPhotoUpload',
  inject: ['eventMessage'],
  components: {
    getImgBoxBrowser,
    avatarUpload,
    GetImgAlipay
  },
  props: {
    clientId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {};
  },
  created() {
    this.$store.commit('flow/setWhiteBg', true);
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
      text: '开始拍照',
      btnStatus: 2,
      data: this.startUpload
    });
  },
  methods: {
    startUpload() {
      if ($hvue.platform === '0') {
        console.log('alipayFlag=' + new AlipayUtil().checkAlipay);
        if (new AlipayUtil().checkAlipay) {
          this.$refs.getImgAlipay.getImg();
        } else {
          this.$refs.getImgBoxBrowser.getImg();
        }
      } else {
        this.$refs.getImgBoxThinkive.getImg();
      }
    },
    getImgCallBack(imgInfo) {
      imageUpload({
        imgContent: imgInfo.base64,
        flowToken: sessionStorage.getItem('TKFlowToken')
      }).then(({ data }) => {
        const bareheadedPic = JSON.stringify([
          {
            image_no: '', //图像编号
            image_files: [
              //图像文件
              {
                image_file_path: data //图像文件地址
              }
            ]
          }
        ]);
        this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
          bareheadedPic
        });
      });
    }
  }
};
</script>

<style scoped>
div.pic >>> img {
  max-width: 100%;
  width: auto;
  margin: 0 auto;
  height: 2.5rem;
}
</style>
