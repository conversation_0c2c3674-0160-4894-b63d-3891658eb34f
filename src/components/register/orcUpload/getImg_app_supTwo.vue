<template>
  <div v-show="showSelImgBox">
    <!-- 遮罩层 -->
    <div
      class="dialog_overlay"
      style="display: block"
      @click="close"
    />
    <!-- 弹出层 -->
    <div class="upload_select show">
      <h5>请选择上传方式</h5>
      <ul>
        <li @click="selImg(1)">
          <a>{{ scan ? '拍照扫描' : '拍照' }}</a>
        </li>
        <li @click="selImg(2)">
          <a>从相册上传</a>
        </li>
      </ul>
      <a
        class="cancel"
        @click="close"
      >取消</a>
    </div>
  </div>
</template>

<script>
import '@/nativeShell/nativeCallH5';

export default {
  name: 'GetImgApp',

  props: {
    scan: {
      type: Boolean,
      default: false
    },
    isNeedTwo: {
      type: String,
      default: '1' //1是 0否
    },
    hmtMark: {
      //是否港澳台证件标识
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showSelImgBox: false,
      imgType: ''
    };
  },

  methods: {
    getImg(imgType) {
      this.imgType = imgType;
      this.showSelImgBox = true;
    },
    close() {
      this.showSelImgBox = false;
    },
    selImg(selType) {
      window.imgCallBack = this.getImgCallBack;
      let imgType = '';
      if (selType === 1 && this.isNeedTwo === '1') {
        imgType = '4,5';
      } else {
        imgType = this.imgType === 'idfrontimg' ? '4' : '5';
      }
      var phoneConfig = {
        mainColor: '#fa443a',
        isNeedSample: '1',
        isOpenHHKVQuality: '0',//合合深度学习KV版本SDK，是否开启质检 //1开启
        requestParam: 'test', // h5上传不需要 但原生判断了是否为空
        imgType: imgType, // 需上传图片类型 4 身份证正面 5 反面
        url: $hvue.customConfig.serverUrl + '/servlet/json?',
        funcNo: (this.scan && selType) === 1 ? 60014 : 60013,
        action: selType !== 1 ? 'phone' : 'pai', // 60013  phone相册 pai拍照
        userId: '1', // h5上传不需要 但原生判断了是否为空
        isUpload: '0',
        isAlbum: '1', //是否显示相册 1表示显示相册，0或其他表示不显示 默认隐藏
        isTake: '1', //是否显示拍照按钮
        compressSize: '2048', //原生压缩大小 不传默认200k
        moduleName: $hvue.customConfig.moduleName // 必须为open
      };
      const hkMacTaiwanTypeMap = {
        idfrontimg_HMTpass: '60',
        idbackimg_HMTpass: '61',
        idfrontimg_HMTid: '70',
        idbackimg_HMTid: '71',
      }
      if(this.hmtMark){
        phoneConfig.imgType = hkMacTaiwanTypeMap[this.imgType];
        phoneConfig.funcNo = '60032';
        phoneConfig.isNeedSample = '0';
        phoneConfig.isOpenHHKVQuality = '0';
      }
      console.log('原生上传参数');
      console.log(phoneConfig)
      let result = $h.callMessageNative(phoneConfig);
      this.close();
      if (result.error_no !== '0') {
        console.log({ mes: result.error_info });
      }
    },
    getImgCallBack(data) {
      console.log('原生回调：')
      console.log(data)
      console.log(Object.keys(data))
      let result = {
        idNo: data.idNo,
        name: data.custName,
        address: data.native,
        ethnicname: data.ethnicName,
        idbegindate: data.idbeginDate,
        idenddate: data.idendDate,
        validity: data.idbeginDate + '-' + data.idendDate,
        issueAuthority: data.policeOrg,
        path: data.backFilePath || data.frontFilePath,
        secret: data.backSecret || data.frontSecret
      };
      if (data.frontBase64 && data.backBase64) {
        this.$emit('getImgCallBack', {
          base64: {
            frontBase64: this.filterBase64Pre(data.frontBase64),
            backBase64: this.filterBase64Pre(data.backBase64)
          },
          ocrInfo: result
        });
      } else {
        this.$emit('getImgCallBack', {
          base64: this.filterBase64Pre(data.frontBase64 || data.backBase64 || data.base64),
          ocrInfo: result
        });
      }
    },
    filterBase64Pre(ndata) {
      let arr = ndata.split('base64,');
      return arr[arr.length - 1];
    }
  }
};
</script>
