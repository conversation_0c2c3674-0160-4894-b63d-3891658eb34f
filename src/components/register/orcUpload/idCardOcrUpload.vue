<template>
  <div class="upload_com_box spel">
    <div class="upload_wrap">
      <div class="upload_item" @click="selImgClick()">
        <div class="pic">
          <img :src="imgDetail.src" />
        </div>
        <a v-if="imgDetail.uploaded" class="reset_btn">重拍</a>
        <a v-else class="btn">{{
          type === '1' ? '拍摄人像面' : '拍摄国徽面'
        }}</a>
      </div>
    </div>

    <getImgBoxApp
      ref="getImgBoxThinkive"
      :img-type="imgType"
      :scan="true"
      @getImgCallBack="getImgCallBack"
    ></getImgBoxApp>
    <getImgBoxBrowser
      ref="getImgBoxBrowser"
      :scan="true"
      @getImgCallBack="getImgCallBack"
    ></getImgBoxBrowser>
  </div>
</template>

<script>
import getImgBoxApp from './getImg_app';
import getImgBoxBrowser from '@/components/getImg_browser';
import {
  uploadFile
  // idCardToSex,
  // idCardToBirthday,
  // getAge
} from '@/common/util';
export default {
  name: 'IdCardOcrUpload',
  inject: ['tkFlowInfo', 'setPropsByForm'],
  components: {
    getImgBoxApp,
    getImgBoxBrowser
  },
  model: {
    props: 'value',
    event: 'change'
  },
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fileUrl: $hvue.customConfig.fileUrl,
      imgDetail: {
        src:
          this.type === '1'
            ? require('@/assets/images/sl_img02.png')
            : require('@/assets/images/sl_img03.png'),
        uploaded: false
      },
      imgType: 'idfrontimg'
    };
  },
  mounted() {
    const { outProperty } = this.tkFlowInfo();
    if (this.type === '2') {
      this.imgType = 'idNationalimg';
    }
    if (outProperty.idCardPortrait) {
      this.imgDetail = {
        src:
          this.type === '1'
            ? `${this.fileUrl}${outProperty.idCardPortrait}`
            : `${this.fileUrl}${outProperty.idCardNational}`,
        uploaded:
          outProperty.idCardPortrait || outProperty.idCardNational
            ? true
            : false
      };
    }
    if (outProperty.idCardNational) {
      this.imgDetail = {
        src:
          this.type === '1'
            ? `${this.fileUrl}${outProperty.idCardPortrait}`
            : `${this.fileUrl}${outProperty.idCardNational}`,
        uploaded:
          outProperty.idCardPortrait || outProperty.idCardNational
            ? true
            : false
      };
    }
  },
  methods: {
    selImgClick() {
      console.log(this.imgType);
      if ($hvue.platform == 0) {
        this.$refs.getImgBoxBrowser.getImg();
      } else {
        this.$refs.getImgBoxThinkive.getImg();
      }
    },

    echoOrcInfo(base64, result) {
      if (result.idNo) {
        if (this.type !== '1') {
          _hvueToast({
            mes: '请上传身份证国徽面'
          });
          return;
        }
        this.imgDetail.uploaded = true;
        this.imgDetail.src = 'data:image/jpeg;base64,' + base64;
        this.$emit('change', {
          idCardPortrait: result.path,
          clientName: result.custName,
          idNo: result.idNo,
          idAddress: result.idAddress
        });
      } else if (result.policeorg || result.idenddate) {
        if (this.type !== '2') {
          _hvueToast({
            mes: '请上传身份证人像面'
          });
          return;
        }
        this.imgDetail.uploaded = true;
        this.imgDetail.src = 'data:image/jpeg;base64,' + base64;
        this.$emit('change', {
          idCardNational: result.path,
          issuedDepart: result.policeorg,
          idBegindate: result.idbegindate.replace(/-/g, ''),
          idEnddate:
            result.idenddate === '长期'
              ? '30001231'
              : result.idenddate.replace(/-/g, '')
        });
      } else {
        _hvueToast({ mes: '未识别的身份证图片' });
      }
    },

    getImgCallBack(imgInfo, imageType) {
      imageType = imageType || this.imgType;
      _hvueLoading.open();
      let ocrMode = 0;
      if (
        imgInfo &&
        imgInfo.ocrInfo &&
        (imgInfo.ocrInfo.idNo || imgInfo.ocrInfo.issueAuthority)
      ) {
        ocrMode = 1;
      } else {
        ocrMode = 0;
      }
      uploadFile(
        `${$hvue.customConfig.serverUrl}/client/ocrParseIDCard?ocrMode=${ocrMode}`,
        // $hvue.customConfig.serverUrl + '/client/ocrParseIDCard',
        imgInfo.base64,
        {
          success: (result) => {
            _hvueLoading.close();
            if (ocrMode == 0) {
              result = {
                ...result.data.ocrMsgInfo,
                path: result.data.path
              };
            } else {
              result = {
                custName: imgInfo.ocrInfo.name,
                idNo: imgInfo.ocrInfo.idNo,
                idAddress: imgInfo.ocrInfo.address,
                policeorg: imgInfo.ocrInfo.issueAuthority,
                idBegindate: imgInfo.ocrInfo.idbegindate,
                idEnddate: imgInfo.ocrInfo.idenddate,
                path: result.data.path
              };
            }
            let ocrInfo = Object.assign({}, imgInfo.ocrInfo, result);
            this.echoOrcInfo(imgInfo.base64, ocrInfo);
          },
          progress: (count) => {
            console.log(count);
          },
          error: (e) => {
            _hvueLoading.close();
            console.log(e);
          }
        },
        {}
      );
    }
  }
};
</script>
