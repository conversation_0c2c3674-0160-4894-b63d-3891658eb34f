<template>
  <div class="upload_com_box spel">
    <div class="upload_wrap">
      <div class="upload_item" @click="selImgClick('idfrontimg')">
        <div class="pic">
          <img :src="positive.src" />
        </div>
        <a v-if="positive.uploaded" class="reset_btn">重拍</a>
        <a v-else class="btn">拍摄人像面</a>
      </div>
      <div class="upload_item" @click="selImgClick('idbackimg')">
        <div class="pic">
          <img :src="negative.src" />
        </div>
        <a v-if="negative.uploaded" class="reset_btn">重拍</a>
        <a v-else class="btn">拍摄国徽面</a>
      </div>
    </div>

    <getImgBoxApp
      ref="getImgBoxThinkive"
      :img-type="imgType"
      :is-need-two="isNeedTwo"
      :scan="true"
      @getImgCallBack="getImgCallBack"
    ></getImgBoxApp>
    <getImgBoxBrowser
      ref="getImgBoxBrowser"
      :scan="true"
      @getImgCallBack="getImgCallBack"
    ></getImgBoxBrowser>
  </div>
</template>

<script>
import getImgBoxApp from './getImg_app_supTwo';
import getImgBoxBrowser from '@/components/getImg_browser';
import { uploadFile } from '@/common/util';
export default {
  name: 'IdCardOcrUploadSupTwo',
  inject: ['tkFlowInfo', 'setPropsByForm'],
  components: {
    getImgBoxApp,
    getImgBoxBrowser
  },
  model: {
    props: 'value',
    event: 'change'
  },
  props: {
    isNeedTwo: {
      type: String,
      default: '1' //1是 0否
    }
  },
  data() {
    return {
      fileUrl: $hvue.customConfig.fileUrl,
      positive: {
        src: require('@/assets/images/sl_img02.png'),
        uploaded: false
      },
      negative: {
        src: require('@/assets/images/sl_img03.png'),
        uploaded: false
      },
      imgType: 'idfrontimg'
    };
  },
  mounted() {
    const { inProperty } = this.tkFlowInfo();
    if (inProperty.idCardPortrait) {
      this.positive = {
        src: `${this.fileUrl}${inProperty.idCardPortrait}`,
        uploaded: true
      };
    }
    if (inProperty.idCardNational) {
      this.negative = {
        src: `${this.fileUrl}${inProperty.idCardNational}`,
        uploaded: true
      };
    }
  },
  methods: {
    selImgClick(type) {
      this.imgType = type;
      if ($hvue.platform == 0) {
        this.$refs.getImgBoxBrowser.getImg();
      } else {
        if (this.isNeedTwo === '1') {
          this.$refs.getImgBoxThinkive.getImg(this.imgType);
        } else {
          this.$refs.getImgBoxThinkive.getImg(this.imgType);
        }
      }
    },

    echoOrcInfo(base64, result) {
      if (result.idNo) {
        this.positive.uploaded = true;
        this.positive.src = 'data:image/jpeg;base64,' + base64;
        let data = this.$attrs.value;
        this.$emit('change', {
          idCardPortrait: result.path,
          clientName: result.custName,
          idNo: result.idNo,
          idAddress: result.idAddress,
          idCardNational: data.idCardNational ? data.idCardNational : '',
          issuedDepart: data.issuedDepart ? data.issuedDepart : '',
          idBegindate: data.idBegindate ? data.idBegindate : '',
          idEnddate: data.idEnddate ? data.idEnddate : ''
        });
      } else if (result.policeorg || result.idenddate) {
        this.negative.uploaded = true;
        this.negative.src = 'data:image/jpeg;base64,' + base64;
        let data = this.$attrs.value;
        this.$emit('change', {
          idCardPortrait: data.idCardPortrait ? data.idCardPortrait : '',
          clientName: data.clientName ? data.clientName : '',
          idNo: data.idNo ? data.idNo : '',
          idAddress: data.idAddress ? data.idAddress : '',
          idCardNational: result.path,
          issuedDepart: result.policeorg,
          idBegindate: result.idbegindate.replace(/-/g, ''),
          idEnddate:
            result.idenddate === '长期'
              ? '30001231'
              : result.idenddate.replace(/-/g, '')
        });
      } else {
        _hvueToast({ mes: '未识别的身份证图片' });
      }
    },

    toUploadFile(base64) {
      _hvueLoading.open();
      uploadFile(
        $hvue.customConfig.serverUrl + '/client/ocrParseIDCard',
        base64,
        {
          success: (data) => {
            _hvueLoading.close();
            if (data.code == 0) {
              let ocrInfo = Object.assign({}, data.data.ocrMsgInfo, {
                path: data.data.path
              });
              this.echoOrcInfo(base64, ocrInfo);
            } else {
              _hvueAlert({ mes: data.msg });
            }
          },
          progress: (count) => {
            console.log(count);
          },
          error: (e) => {
            _hvueLoading.close();
            console.log(e);
          }
        },
        {}
      );
    },

    getImgCallBack(imgInfo) {
      console.log($hvue.platform);
      if (this.isNeedTwo === '1' && $hvue.platform !== '0') {
        _hvueLoading.open();
        uploadFile(
          $hvue.customConfig.serverUrl + '/client/ocrParseIDCard',
          imgInfo.base64.frontBase64,
          {
            success: (data) => {
              if (data.code == 0) {
                this.positive.uploaded = true;
                this.positive.src =
                  'data:image/jpeg;base64,' + imgInfo.base64.frontBase64;
                let ocrInfo = Object.assign({}, data.data.ocrMsgInfo, {
                  frontPath: data.data.path
                });
                uploadFile(
                  $hvue.customConfig.serverUrl + '/client/ocrParseIDCard',
                  imgInfo.base64.backBase64,
                  {
                    success: (data) => {
                      _hvueLoading.close();
                      if (data.code == 0) {
                        this.negative.uploaded = true;
                        this.negative.src =
                          'data:image/jpeg;base64,' + imgInfo.base64.backBase64;
                        Object.assign(ocrInfo, data.data.ocrMsgInfo, {
                          backPath: data.data.path
                        });
                        this.$emit('change', {
                          idCardPortrait: ocrInfo.frontPath,
                          clientName: ocrInfo.custName,
                          idNo: ocrInfo.idNo,
                          idAddress: ocrInfo.idAddress,
                          idCardNational: ocrInfo.backPath,
                          issuedDepart: ocrInfo.policeorg,
                          idBegindate: ocrInfo.idbegindate.replace(/-/g, ''),
                          idEnddate: ocrInfo.idenddate.replace(/-/g, '')
                        });
                      } else {
                        _hvueAlert({ mes: data.msg });
                      }
                    },
                    progress: (count) => {
                      console.log(count);
                    },
                    error: (e) => {
                      _hvueLoading.close();
                      console.log(e);
                    }
                  },
                  {}
                );
              } else {
                _hvueAlert({ mes: data.msg });
              }
            },
            progress: (count) => {
              console.log(count);
            },
            error: (e) => {
              _hvueLoading.close();
              console.log(e);
            }
          },
          {}
        );
      } else {
        this.toUploadFile(imgInfo.base64);
      }
    }
  }
};
</script>
