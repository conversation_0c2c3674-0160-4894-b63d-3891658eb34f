<template>
  <fragment>
    <div class="com_title">
      <h5>{{ pageTitle }}</h5>
    </div>
    <div class="upload_com_box spel">
      <div class="upload_wrap">
        <div class="upload_item" @click="selImgClick('idfrontimg')">
          <div class="pic">
            <i v-show="positive.uploaded" class="watermark" />
            <img :src="positive.src" />
          </div>
          <a v-if="positive.uploaded" class="reset_btn">重新上传</a>
          <a v-else class="btn"
            >上传/拍摄{{ hkMacTaiwanPass ? '' : '人像面' }}</a
          >
        </div>
        <div class="upload_item" @click="selImgClick('idbackimg')">
          <div class="pic">
            <i v-show="negative.uploaded" class="watermark" />
            <img :src="negative.src" />
          </div>
          <a v-if="negative.uploaded" class="reset_btn">重新上传</a>
          <a v-else class="btn"
            >上传/拍摄{{ hkMacTaiwanPass ? '' : '国徽照' }}</a
          >
        </div>
      </div>
      <div v-if="!hkMacTaiwanPass && !hkMacTaiwanId">
        <div
          v-show="positive.uploaded && negative.uploaded"
          class="upload_infobox"
        >
          <div class="com_title">
            <h5>请核对您的个人信息，若有误请手动修改</h5>
          </div>
          <div class="input_form">
            <div class="input_text text">
              <span class="tit active">姓名</span>
              <input
                v-model="formData.clientName"
                class="t1"
                type="text"
                maxlength="60"
                placeholder="请输入姓名"
              />
            </div>
            <div class="input_text text">
              <span class="tit active">证件号码</span>
              <input
                v-model="formData.idNo"
                class="t1"
                type="text"
                maxlength="18"
                placeholder="请输入证件号码"
              />
            </div>
            <div class="input_text text">
              <span class="tit active">开始日期</span>
              <h-datetime
                v-model="startDate"
                class="dropdown"
                title="请选择开始日期"
                placeholder="请选择开始日期"
                type="date"
                start-year="1900"
                end-year="2100"
              />
            </div>
            <div class="input_text text">
              <span class="tit active">结束日期</span>
              <h-datetime
                v-if="!longTimeChecked"
                v-model="endDateTime"
                class="dropdown"
                title="请选择结束日期"
                placeholder="请选择结束日期"
                type="date"
                start-year="1900"
                end-year="2100"
              />
              <input
                v-else
                class="t1"
                type="text"
                maxlength="32"
                disabled
                value="3000-12-31"
              />
              <span
                :class="{ checked: longTimeChecked }"
                class="icon_check long_span"
                @click.stop="triggerLongTime()"
                >长期</span
              >
            </div>
            <div class="input_text text">
              <span class="tit active">证件地址</span>
              <multLineInput
                v-model="formData.idAddress"
                class="tarea1 needsclick"
                :maxlength="60"
                placeholder="请输入您的详细地址"
                autocomplete="off"
              />
            </div>
            <div class="input_text text">
              <span class="tit active">发证机关</span>
              <input
                v-model="formData.issuedDepart"
                class="t1"
                type="text"
                placeholder="请输入发证机关"
              />
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <div
          v-show="positive.uploaded && negative.uploaded"
          class="upload_infobox"
        >
          <div class="com_title">
            <h5>请核对您的个人信息，若有误请手动修改</h5>
          </div>
          <div class="input_form">
            <div class="input_text text">
              <span class="tit active">姓名</span>
              <input
                v-model="formData.clientName"
                class="t1"
                type="text"
                maxlength="60"
                placeholder="请输入姓名"
              />
            </div>
            <div class="input_text text">
              <span class="tit active">证件号码</span>
              <input
                v-model="formData.idNo"
                class="t1"
                type="text"
                maxlength="18"
                placeholder="请输入证件号码"
              />
            </div>
            <div class="input_text text">
              <span class="tit active">证件类型</span>
              <div class="t1">
                {{ idKindeName }}
              </div>
            </div>
            <div class="input_text text">
              <span class="tit active">开始日期</span>
              <h-datetime
                v-model="startDate"
                class="dropdown"
                title="请选择开始日期"
                placeholder="请选择开始日期"
                type="date"
                start-year="1900"
                end-year="2100"
              />
            </div>
            <div class="input_text text">
              <span class="tit active">结束日期</span>
              <h-datetime
                v-if="!longTimeChecked"
                v-model="endDateTime"
                class="dropdown"
                title="请选择结束日期"
                placeholder="请选择结束日期"
                type="date"
                start-year="1900"
                end-year="2100"
              />
              <input
                v-else
                class="t1"
                type="text"
                maxlength="32"
                disabled
                value="3000-12-31"
              />
              <span
                :class="{ checked: longTimeChecked }"
                class="icon_check long_span"
                @click.stop="triggerLongTime()"
                >长期</span
              >
            </div>
          </div>
        </div>
      </div>
      <div v-show="!showNextBtn" class="photo_tips">
        <h5 class="title">请注意照片拍摄规范</h5>
        <ul class="list">
          <li>
            <div class="pic">
              <img src="@/assets/images/sl_img02_1.png" />
            </div>
            <span class="ok">合规照片</span>
          </li>
          <li>
            <div class="pic">
              <img src="@/assets/images/sl_img02_2.png" />
            </div>
            <span class="error">边角缺失</span>
          </li>
          <li>
            <div class="pic">
              <img src="@/assets/images/sl_img02_3.png" />
            </div>
            <span class="error">照片模糊</span>
          </li>
          <li>
            <div class="pic">
              <img src="@/assets/images/sl_img02_4.png" />
            </div>
            <span class="error">反光强烈</span>
          </li>
        </ul>
        <p>1. 拍摄时请将证件平放，手机横向拍摄</p>
        <p>2. 确保证明材料 <span class="imp">边框完整、文字清晰可见</span></p>
      </div>
    </div>
    <getImgBoxApp
      ref="getImgBoxThinkive"
      :img-type="imgType"
      :hmt-mark="hkMacTaiwanPass || hkMacTaiwanId"
      :is-need-two="isNeedTwo"
      @getImgCallBack="getImgCallBack"
    />
    <get-img-alipay
      ref="getImgAlipay"
      :floatingLayer="
        imgType === 'idfrontimg' || imgType === 'idfrontimg_HMTid' ? '1' : '0'
      "
      @getImgCallBack="getImgCallBack"
    />
    <getImgBoxBrowser
      ref="getImgBoxBrowser"
      :scan="hkMacTaiwanPass || hkMacTaiwanId"
      @getImgCallBack="getImgCallBack"
    />
  </fragment>
</template>

<script>
import AlipayUtil from '@/common/AlipayUtil';
import getImgBoxApp from './getImg_app_supTwo';
import getImgBoxBrowser from '@/components/getImg_browser';
import GetImgAlipay from '@/components/getImg_alipay';
import {
  checkHKLiveCard,
  checkHKPassCard,
  idCardToBirthday,
  uploadFile,
  computeGetYears,
  dateFormat,
  getId18
} from '@/common/util';
import { EVENT_NAME } from '@/common/formEnum';
import { ID_KIND, MARK_TYPE } from '@/common/enumeration';
import multLineInput from '@/components/multLineInput.vue';
import {
  addClientCritMark,
  confirmClientInfo,
  customerImageDownload,
  clientInfoQry,
  securityAvatarQry,
  imageUpload
} from '@/service/service';
import dayjs from 'dayjs';

export default {
  name: 'IdCardUpload',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {
    multLineInput,
    getImgBoxApp,
    getImgBoxBrowser,
    GetImgAlipay
  },
  props: {
    // 证件类别
    idKind: {
      type: String,
      default: ''
    },
    //是否校验免冠照
    check_bareheaded: {
      type: Boolean,
      default: true
    },
    /**柜台校验处理
      1：不处理（默认空同不处理）
      2：与柜台一致拦截（身份证更新使用）
      3：与柜台不拦截（身份证认证场景使用）
    */
    cs_check_todo: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showPrompt: false,
      ID_KIND,
      longTimeChecked: false,
      formData: {},
      imgType: '',
      ocrIdNo: '', //证件号码,用于对比修改位数
      startDate: '',
      endDate: '',
      pageTitle: '',
      positive: {
        src: require('@/assets/images/idcard_ic01.png'),
        uploaded: false
      },
      negative: {
        src: require('@/assets/images/idcard_ic02.png'),
        uploaded: false
      },
      csUserInfo: {}
    };
  },
  computed: {
    showNextBtn() {
      return this.positive.uploaded === true && this.negative.uploaded === true;
    },
    endDateTime: {
      get() {
        return this.longTimeChecked ? '3000-12-31' : this.endDate;
      },
      set(val) {
        this.endDate = val;
      }
    },
    hkMacTaiwanPass() {
      const { HK_MACAU_PASS, TAIWAN_PASS } = this.ID_KIND; // 港澳台通行证
      return [HK_MACAU_PASS, TAIWAN_PASS].includes(this.idKind);
    },
    hkMacTaiwanId() {
      const { HK_ID, MACAU_ID, HK_MACAU_TAIWAN_ID } = this.ID_KIND; // 港澳台居民居住证
      return [HK_ID, MACAU_ID, HK_MACAU_TAIWAN_ID].includes(this.idKind);
    },
    idKindeName() {
      if (this.hkMacTaiwanPass) {
        return '港澳台居民来往内地通行证';
      } else if (this.hkMacTaiwanId) {
        return '港澳台居民居住证';
      } else {
        return '二代居民身份证';
      }
    },
    //是否15位身份证证号
    oldIdCard() {
      const regExp = /^[\d]{15}$/;
      return (
        this.idKind === this.ID_KIND.PERSONAL_ID &&
        regExp.test(this.csUserInfo?.idNo)
      );
    },
    isNeedTwo() {
      if (!this.hkMacTaiwanPass && !this.hkMacTaiwanId) {
        if (!this.positive.uploaded && !this.negative.uploaded) {
          return '1';
        } else {
          return '0';
        }
      } else {
        return '0';
      }
    }
  },
  watch: {
    showNextBtn: {
      handler(bool) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          btnStatus: bool ? 2 : 0,
          data: this.confirmInfo
        });
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.$store.commit('flow/setWhiteBg', true);
    this.enterPopup().then(() => {
      const alertTxt =
        '根据中国人民银行、中国证券监督管理委员会、中国证券登记结算有限责任公司、沪深交易所及三方存管银行的相关要求，为履行适当性义务、反洗钱义务、实名制要求等法定义务，我们需要收集您的身份证件信息，用于实名验证。我们将严格按照个人信息保护相关法律法规保护您的个人隐私安全。请确认您已知晓并同意提供以上信息。';
      this.$TAlert({
        title: '温馨提示',
        tips: alertTxt,
        confirmBtn: '同意',
        cancelBtn: '不同意',
        hasCancel: true,
        confirm: () => {
          addClientCritMark({
            flowToken: sessionStorage.getItem('TKFlowToken'),
            markType: MARK_TYPE.ID_CARD_REAL_NAME_CHECK,
            markContent: alertTxt,
            confirmFlag: '1'
          })
            .then(() => {
              $h.setSession('clientCritMarkIdCard', true);
            })
            .then(() => {
              $h.setSession('clientCritMarkIdCard', true);
              this.queryCustomerImage();
            })
            .catch(() => {});
        },
        cancel: () => {
          this.eventMessage(this, EVENT_NAME.TO_INDEX);
        }
      });
    });
  },
  mounted() {
    this.$nextTick(() => {
      this.renderingView();
    });
  },
  methods: {
    enterPopup() {
      const {
        inProperty: { bizType }
      } = this.tkFlowInfo();
      return new Promise((resolve) => {
        // 开通股票期权业务个性化弹窗
        if (bizType === '010129') {
          const alertTxt =
            '股票期权交易风险较高，为了您的交易安全，请您上传本人身份证件并与客服录制确认视频后。再正式提交开户申请。';
          this.$TAlert({
            title: '温馨提示',
            tips: alertTxt,
            confirmBtn: '继续申请',
            confirm: () => {
              addClientCritMark({
                flowToken: sessionStorage.getItem('TKFlowToken'),
                markType: MARK_TYPE.PDPA,
                markContent: alertTxt,
                confirmFlag: '1'
              })
                .then(() => {
                  resolve();
                })
                .catch(() => {
                  resolve();
                });
            }
          });
        } else {
          resolve();
        }
      });
    },
    renderingView() {
      if (this.hkMacTaiwanPass) {
        this.pageTitle = '拍摄并上传本人港澳台居民来往内地通行证';
        this.positive.src = require('@/assets/images/sl_img15.png');
        this.negative.src = require('@/assets/images/sl_img16.png');
      } else if (this.hkMacTaiwanId) {
        this.pageTitle = '拍摄并上传本人港澳台居民居住证';
        this.positive.src = require('@/assets/images/sl_img13.png');
        this.negative.src = require('@/assets/images/sl_img14.png');
      } else {
        this.pageTitle = '拍摄并上传本人身份证件原件照片';
        this.positive.src = require('@/assets/images/idcard_ic01.png');
        this.negative.src = require('@/assets/images/idcard_ic02.png');
      }
    },
    selImgClick(type) {
      if (this.hkMacTaiwanPass) {
        this.imgType = type + '_HMTpass';
      } else if (this.hkMacTaiwanId) {
        this.imgType = type + '_HMTid';
      } else {
        this.imgType = type;
      }
      if ($hvue.platform === '0') {
        if (new AlipayUtil().checkAlipay) {
          this.$nextTick(() => {
            this.$refs.getImgAlipay.getImg();
          });
        } else {
          this.$refs.getImgBoxBrowser.getImg();
        }
      } else {
        this.$refs.getImgBoxThinkive.getImg(this.imgType);
      }
    },

    getImgCallBack(imgInfo) {
      //h5上传
      if ($hvue.platform === '0') {
        const { base64 } = imgInfo;
        if (this.imgType === 'idbackimg_HMTpass') {
          this.uploadNoOcrFile(base64);
        } else {
          this.uploadFile(base64);
        }
      } else {
        const { base64 } = imgInfo;
        if (base64?.frontBase64 && base64?.backBase64) {
          Object.keys(base64).forEach((k) => {
            const imageType = k === 'frontBase64' ? 'idfrontimg' : 'idbackimg';
            this.uploadFile(base64[k], imageType);
          });
        } else {
          if (this.imgType === 'idbackimg_HMTpass') {
            this.uploadNoOcrFile(base64);
          } else {
            this.uploadFile(base64);
          }
        }
      }
    },
    uploadFile(base64, imageType = false) {
      _hvueLoading.open();
      let imgType = this.imgType;
      if (imageType) imgType = imageType;
      imgType = imgType.replace('_HMTpass', '').replace('_HMTid', '');
      uploadFile(
        $hvue.customConfig.serverUrl + '/client/ocrParseIDCard',
        base64,
        {
          success: (data) => {
            _hvueLoading.close();
            if (data.code === 0) {
              let ocrInfo = Object.assign({}, data.data.ocrMsgInfo, {
                path: data.data.ocrImgPath
              });
              if (
                this.idKind === ID_KIND.PERSONAL_ID ||
                this.idKind === ID_KIND.HK_MACAU_TAIWAN_ID
              ) {
                this.echoOrcInfo(base64, ocrInfo);
              } else {
                this.hkMacTaiwanOcr(base64, ocrInfo);
              }
            } else {
              _hvueToast({ mes: '未识别的身份证件图片' });
            }
          },
          progress: (count) => {
            console.log(count);
          },
          error: (e) => {
            _hvueLoading.close();
            console.log(e);
          }
        },
        { idKind: this.idKind, imgType }
      );
    },
    uploadNoOcrFile(base64) {
      imageUpload({
        imgContent: base64,
        flowToken: sessionStorage.getItem('TKFlowToken')
      }).then(({ data }) => {
        this.negative.uploaded = true;
        this.negative.src = 'data:image/jpeg;base64,' + base64;
        const ocrInfo = {
          idCardNational: data
        };
        Object.assign(this.formData, ocrInfo);
        this.$emit('change', ocrInfo);
      });
    },
    echoOrcInfo(base64, result) {
      if (result.idNo) {
        this.positive.uploaded = true;
        this.positive.src = 'data:image/jpeg;base64,' + base64;
        const ocrInfo = {
          idCardPortrait: result.path,
          clientName: result.custName,
          idNo: result.idNo,
          idAddress: result.idAddress,
          clientGender: result.userSex === '男' ? '0' : '1',
          birthday: this.formatOcrDate(result.birthday)
        };
        this.ocrIdNo = result.idNo;
        Object.assign(this.formData, ocrInfo);
      } else if (result.policeorg || result.idenddate) {
        if (result.idenddate !== '') {
          const regExp = /\d{4}-\d{2}-\d{2}/;
          let idBegindate = result.idenddate.split('-')[0];
          let idEnddate = result.idenddate.split('-')[1];
          if (!idBegindate || !idEnddate) {
            _hvueToast({ mes: '未识别的身份证件图片' });
            return;
          }
          const startDate = dateFormat(idBegindate)
            .replace(/\./g, '-')
            .replace(/\s+/g, '');
          const endDate = dateFormat(idEnddate)
            .replace(/\./g, '-')
            .replace(/\s+/g, '');
          if (
            !regExp.test(startDate) ||
            (endDate !== '长期' && !regExp.test(endDate))
          ) {
            _hvueToast({ mes: '未识别的身份证件图片' });
            return;
          } else {
            this.startDate = startDate;
            this.endDate = endDate;
          }
          if (this.endDate === '长期' || this.endDate === '3000-12-31') {
            this.longTimeChecked = true;
            this.endDate = '3000-12-31';
          } else {
            this.longTimeChecked = false;
          }
        } else {
          _hvueToast({ mes: '未识别的身份证件图片' });
          return;
        }
        this.negative.uploaded = true;
        this.negative.src = 'data:image/jpeg;base64,' + base64;
        const ocrInfo = {
          idCardNational: result.path,
          issuedDepart: result.policeorg,
          idBegindate: this.startDate.replace(/[.-]/g, ''),
          idEnddate: this.endDate.replace(/[.-]/g, '')
        };
        console.log(ocrInfo);
        Object.assign(this.formData, ocrInfo);
      } else {
        _hvueToast({ mes: '未识别的身份证件图片' });
      }
    },
    hkMacTaiwanOcr(base64, result) {
      if (this.imgType === 'idfrontimg_HMTpass') {
        this.positive.uploaded = true;
        this.positive.src = 'data:image/jpeg;base64,' + base64;
        if (result.idenddate?.split('-')[0]) {
          this.startDate = result.idenddate
            .split('-')[0]
            .replace(/\./g, '-')
            .replace(/\s+/g, '');
        }
        if (result.idenddate?.split('-')[1]) {
          this.endDate = result.idenddate
            .split('-')[1]
            .replace(/\./g, '-')
            .replace(/\s+/g, '');
        }
        if (this.endDate === '长期' || this.endDate === '3000-12-31') {
          this.longTimeChecked = true;
          this.endDate = '3000-12-31';
        } else {
          this.longTimeChecked = false;
        }
        const ocrInfo = {
          idCardPortrait: result.path,
          clientName: result.custName,
          clientNameEnglish: result.englishName,
          idNo: result.idNo,
          issuedDepart: result.policeorg,
          idBegindate: this.startDate.replace(/[.-]/g, ''),
          clientGender: result.userSex === '男' ? '0' : '1',
          birthday: this.formatOcrDate(result.birthday),
          idEnddate:
            this.endDate === '长期'
              ? '30001231'
              : this.endDate.replace(/[.-]/g, '')
        };
        this.ocrIdNo = result.idNo;
        Object.assign(this.formData, ocrInfo);
      } else if (this.imgType === 'idbackimg_HMTpass') {
        this.negative.uploaded = true;
        this.negative.src = 'data:image/jpeg;base64,' + base64;
        const ocrInfo = {
          idCardNational: result.path
        };
        Object.assign(this.formData, ocrInfo);
      } else {
        _hvueToast({ mes: '未识别的身份证件图片' });
      }
    },
    checkInput() {
      const { clientName, idNo } = this.formData;
      let matchFlag = true;
      if (idNo === '' || clientName === '') {
        this.$TAlert({
          title: '温馨提示',
          tips: '请完善身份信息后再提交变更申请。'
        });
        return false;
      }
      let idNoRegex = /^([\d]{17}[\dXx]|[\d]{15})$/;
      let idNoLenTest = idNo.length === 18;
      let idNoTest = idNoRegex.test(idNo);
      let idNoTestErrMsg =
        '您的身份证号码位数不正确，第二代有效身份证号码由18位数字或字母组成，请核实。';
      if (this.hkMacTaiwanId) {
        idNoTest = checkHKLiveCard(idNo);
        idNoTestErrMsg = '您的居住证号码输入不正确，请核实。';
        if (idNo.length !== 18) {
          idNoLenTest = false;
          idNoTestErrMsg = '您的居住证位数输入不正确，请核实。';
        } else {
          idNoLenTest = true;
        }
      } else if (this.hkMacTaiwanPass) {
        idNoTest = checkHKPassCard(idNo);
        idNoTestErrMsg = '您的通行证号码输入不正确，请核实。';
        if (this.idKind === this.ID_KIND.HK_MACAU_PASS && idNo.length !== 9) {
          idNoTestErrMsg = '您的通行证位数输入不正确，请核实。';
          idNoLenTest = false;
        } else if (
          this.idKind === this.ID_KIND.TAIWAN_PASS &&
          idNo.length !== 8
        ) {
          idNoTestErrMsg = '您的通行证位数输入不正确，请核实。';
          idNoLenTest = false;
        } else {
          idNoLenTest = true;
        }
      }
      if (!idNoTest || !idNoLenTest) {
        this.$TAlert({
          title: '温馨提示',
          tips: idNoTestErrMsg
        });
        return false;
      }

      /* let updateCount = 0;
      let newIDC = idNo.split('');
      let oldIDC = this.ocrIdNo.split('');
      oldIDC.forEach(function (a, i) {
        a !== newIDC[i] && updateCount++;
      });
      updateCount += Math.abs(newIDC.length - oldIDC.length);
      let maxChange = 5;
      if (this.hkMacTaiwanPass) {
        maxChange = 3;
      }
      if (!this.oldIdCard && this.ocrIdNo !== '' && updateCount > maxChange) {
        this.$TAlert({
          title: '温馨提示',
          tips: '证件号号码位数修改过多'
        });
        return false;
      } */

      let beginDate = this.startDate;
      let endDate = this.longTimeChecked ? '3000-12-31' : this.endDate;
      let nowDate = new Date().format('yyyy-MM-dd');
      let dateExp = /\d{4}-\d{2}-\d{2}$/;
      // 判断有效期格式是否正确
      if (
        !dateExp.test(beginDate) ||
        !dateExp.test(endDate) ||
        Date.parse(beginDate.replace(/\./g, '-')) > Date.now() ||
        beginDate === endDate ||
        computeGetYears(endDate, nowDate, 0)
      ) {
        this.$TAlert({
          title: '温馨提示',
          tips: '您的证件有效期有误或即将到期，请核实。'
        });
        return false;
      }
      // 判断身份证是否过期
      if (
        !this.longTimeChecked &&
        Date.parse(endDate.replace(/\./g, '-')) < Date.now()
      ) {
        this.$TAlert({
          title: '温馨提示',
          tips: '您的证件有效期有误或即将到期，请核实。'
        });
        return false;
      }
      if (this.hkMacTaiwanId) {
        /** 规则:通行证有效期非5年，提示：请设置正确的证件有效期
         * 截止日期只能与开始日期相同或者前一天，比如起始日期是20121212，截止日期可以是20221212或者20221211，但不能是1213或其他日期
         */
        if (!computeGetYears(beginDate, endDate, 5)) {
          matchFlag = {
            alert: '港澳台居民居住证的有效期应为五年，请核实',
            markType: MARK_TYPE.HK_MAC_TAIWAN_CARD_DATE_CHECK
          };
        }
      } else if (this.hkMacTaiwanPass) {
        /** 规则:通行证有效期非5年或者10年，提示：请设置正确的证件有效期
         * 截止日期只能与开始日期相同或者前一天，比如起始日期是20121212，截止日期可以是20221212或者20221211，但不能是1213或其他日期
         */
        if (
          this.idKind === this.ID_KIND.HK_MACAU_PASS &&
          !computeGetYears(beginDate, endDate, 10)
        ) {
          matchFlag = {
            alert: '港澳居民来往内地通行证有效期应为十年，请核实',
            markType: MARK_TYPE.HK_MAC_TAIWAN_CARD_DATE_CHECK
          };
        } else if (
          this.idKind === this.ID_KIND.TAIWAN_PASS &&
          !computeGetYears(beginDate, endDate, 5)
        ) {
          matchFlag = {
            alert: '台湾居民来往内地通行证有效期应为五年，请核实',
            markType: MARK_TYPE.HK_MAC_TAIWAN_CARD_DATE_CHECK
          };
        }
      } else {
        //16 周岁至 25 周岁,有效期 为 10 年;26 周岁至 45 周岁有效期为 20 年;46 周岁以上的公民,有效期为长期。小 于 16 周岁的情况下申领身份证，有效期 为 5 年
        let birthday = idCardToBirthday(idNo);
        let birthdayTime = new Date(birthday.replace(/\-/g, '/'));
        let beginDateTime = new Date(beginDate.replace(/\-/g, '/'));
        //办理身份证时的年龄
        let handleAge =
          beginDateTime.getFullYear() -
          birthdayTime.getFullYear() -
          (beginDateTime.getMonth() < birthdayTime.getMonth() ||
          (beginDateTime.getMonth() == birthdayTime.getMonth() &&
            beginDateTime.getDate() < birthdayTime.getDate())
            ? 1
            : 0);
        let beginYear = beginDateTime.getFullYear();
        if (
          ((beginYear % 4 == 0 && beginYear % 100 != 0) ||
            beginYear % 400 == 0) &&
          beginDate.replace(/\./g, '-').substring(5) == '02-29'
        ) {
          //闰年2月29办理
          if (
            (endDate.replace(/\./g, '-').substring(5) != '02-29' &&
              handleAge >= 26 &&
              handleAge < 46) ||
            (handleAge < 26 &&
              endDate.replace(/\./g, '-').substring(5) != '02-28')
          ) {
            matchFlag = {
              alert: '请确认您上传的证件正反面为同一张',
              markType: MARK_TYPE.ID_CARD_CONFIRM
            };
          }
        }
        if (
          (handleAge < 16 &&
            handleAge >= 0 &&
            !computeGetYears(beginDate, endDate, 5)) ||
          (handleAge >= 16 &&
            handleAge < 26 &&
            !computeGetYears(beginDate, endDate, 10)) ||
          (handleAge >= 26 &&
            handleAge < 46 &&
            !computeGetYears(beginDate, endDate, 20)) ||
          (handleAge >= 46 && endDate != '3000-12-31')
        ) {
          matchFlag = {
            alert: '请确认您上传的证件正反面为同一张',
            markType: MARK_TYPE.ID_CARD_CONFIRM
          };
        }
        if (
          !/^([\u2E80-\uFE4F](?![\u3000-\u303F])){2,4}([·•]?[\u2E80-\uFE4F](?![\u3000-\u303F]))*$/.test(
            clientName
          )
        ) {
          this.$TAlert({
            title: '温馨提示',
            tips: '您的姓名格式不正确，请核实。'
          });
          return false;
        }
        if (this.formData.idAddress === '') {
          this.$TAlert({
            title: '温馨提示',
            tips: '请完善身份信息后再提交变更申请。'
          });
          return false;
        }
        if (
          this.formData.idAddress.length < 8 ||
          this.formData.idAddress.length > 60
        ) {
          this.$TAlert({
            title: '温馨提示',
            tips: '您的地址信息不正确，请核实。'
          });
          return false;
        }
        if (this.formData.issuedDepart === '') {
          this.$TAlert({
            title: '温馨提示',
            tips: '请完善身份信息后再提交变更申请。'
          });
          return false;
        }
        if (
          !/^[\u4E00-\u9FA5\w\d\-\s/]{3,32}$/.test(this.formData.issuedDepart)
        ) {
          this.$TAlert({
            title: '温馨提示',
            tips: '您的签发机关不正确，请核实。'
          });
          return false;
        }
        if (this.formData.issuedDepart.indexOf('局') === -1) {
          matchFlag = {
            alert: '请确认您上传的证件正反面为同一张',
            markType: MARK_TYPE.ID_CARD_CONFIRM
          };
        }
      }
      return matchFlag;
    },
    confirmInfo() {
      const matchFlag = this.checkInput();
      if (matchFlag?.alert) {
        this.$TAlert({
          title: '温馨提示',
          tips: matchFlag.alert,
          confirmBtn: '确认无误',
          cancelBtn: '返回修改',
          hasCancel: true,
          confirm: () => {
            addClientCritMark({
              flowToken: sessionStorage.getItem('TKFlowToken'),
              markType: matchFlag.markType,
              markContent: matchFlag.alert,
              confirmFlag: '1'
            })
              .then(() => {
                this.idMatch();
              })
              .catch(() => {
                this.idMatch();
              });
          },
          cancel: () => {}
        });
      } else if (matchFlag) {
        this.idMatch();
      }
    },
    idMatch() {
      let _this = this;
      const { idNo } = this.formData;
      const idMatchList = idNo
        .toString()
        .split('')
        .filter((a, i) => {
          return this.ocrIdNo.toString().charAt(i) !== a;
        });
      let maxChange = 3;
      console.log('识别后号码：' + this.ocrIdNo);
      console.log('表单输入号码：' + idNo);
      console.log('修改位数' + idMatchList.length);
      console.log('最大修改数' + maxChange);
      if (this.hkMacTaiwanPass) {
        maxChange = 3;
      }
      if (!this.oldIdCard && idMatchList.length > maxChange) {
        this.$TAlert({
          title: '温馨提示',
          tips: '系统检测到您提交的身份信息与上传的身份证件照片差别较大，是否需要重新上传身份证件照片？',
          confirmBtn: '确认',
          cancelBtn: '取消',
          hasCancel: true,
          confirm: () => {
            _this.emptyIdCardInput();
          }
        });
      } else {
        this.submitInfo();
      }
    },
    sameIdMatch() {
      const { idNo } = this.formData;
      const { idNo: csIdNo } = this.csUserInfo;
      return new Promise((resolve, reject) => {
        if (idNo !== getId18(csIdNo)) {
          reject({
            errorMsg: '您的身份证件号码与留存的身份证件号码不一致，请核实。'
          });
        } else {
          resolve(true);
        }
      });
    },
    sameNameMatch() {
      const { clientName } = this.formData;
      const { clientName: csClientName } = this.csUserInfo;
      return new Promise((resolve, reject) => {
        if (clientName !== csClientName) {
          reject({
            errorMsg:
              '您当前姓名与原身份证姓名不一致，请确认是否确有变更。如确有变更，请您致电95310或联系在线客服咨询身份证更新办理事宜。'
          });
        } else {
          resolve(true);
        }
      });
    },
    submitInfo() {
      this.sameIdMatch()
        .then(() => this.sameNameMatch())
        .then(() => {
          let changedClientInfoKey = '0';
          let excludeList = ['clientName', 'idNo', 'idBegindate', 'idEnddate'];
          if (!this.hkMacTaiwanId && !this.hkMacTaiwanPass) {
            excludeList.push('idAddress', 'issuedDepart');
          }
          this.formData.idBegindate = this.startDate.replace(/[.-]/g, '');
          this.formData.idEnddate = this.longTimeChecked
            ? '30001231'
            : this.endDate.replace(/[.-]/g, '');
          for (const key in this.formData) {
            if (
              excludeList.includes(key) &&
              this.formData[key] !== this.csUserInfo[key]
            ) {
              if (key === 'idBegindate' || key === 'idEnddate') {
                if (
                  this.formData[key].replace(/[.-]/g, '') ===
                  this.csUserInfo[key].replace(/[.-]/g, '')
                )
                  return;
              }
              changedClientInfoKey = '1';
              break;
            }
          }

          if (this.cs_check_todo === '2') {
            if (
              changedClientInfoKey === '0' &&
              this.formData.bareheadedExist !== ''
            ) {
              this.$TAlert({
                title: '温馨提示',
                tips: '您的身份信息与系统中留存信息完全一致，无需重新变更。',
                confirmBtn: '我知道了'
              });
              return;
            }
          }
          this.formData.changedClientInfoKey = changedClientInfoKey;
          console.log(this.formData.changedClientInfoKey);
          console.log(this.formData);
          console.log(this.csUserInfo);
          if (this.hkMacTaiwanId || this.hkMacTaiwanPass) {
            this.checkEmptyHead();
            return;
          }
          const reqParams = {
            clientName: this.formData.clientName,
            idNo: this.formData.idNo,
            idEnddate: this.longTimeChecked ? '3000-12-31' : this.endDate
          };
          confirmClientInfo(reqParams)
            .then((data) => {
              if (data.code === 0) {
                this.checkEmptyHead();
              } else {
                return Promise.reject(data.msg);
              }
            })
            .catch((err) => {
              this.$TAlert({
                title: '温馨提示',
                tips: err
              });
            });
        })
        .catch((err) => {
          if (err?.errorMsg) {
            this.$TAlert({
              title: '温馨提示',
              tips: err.errorMsg
            });
          }
        });
    },
    checkEmptyHead() {
      const { rejectFields } = this.tkFlowInfo();
      const rejectFlag = rejectFields ? rejectFields.length !== 0 : false;
      if (
        !rejectFlag &&
        this.formData.bareheadedExist === '' &&
        this.check_bareheaded
      ) {
        const title =
          this.formData.changedClientInfoKey === '0'
            ? '您上传的身份证无需更新'
            : '温馨提示';
        this.$TAlert({
          title,
          tips:
            (this.formData.changedClientInfoKey === '0' ? '但' : '') +
            '您缺失客户头像会影响您办理其他业务，请确认是否上传客户头像。',
          hasCancel: true,
          confirmBtn: '确认',
          cancelBtn: '返回',
          confirm: () => {
            this.checkPublicSecurity();
          }
        });
      } else {
        this.checkPublicSecurity();
      }
    },
    checkPublicSecurity() {
      const { clientName, idNo, idEnddate, clientGender, birthday } =
        this.formData;
      let pubilcSecPicFlag,
        _this = this,
        citizenship = 'CHN',
        reqParams;
      // 需求备注 https://jira.gjzq.cn/browse/KFSBYWBLCG-2881
      if (this.hkMacTaiwanPass || this.hkMacTaiwanId) {
        if (this.hkMacTaiwanPass) {
          if (/[H]\d{8}/.test(idNo)) {
            citizenship = 'HKG';
          } else if (/^[M]\d{8}$/.test(idNo)) {
            citizenship = 'MAC';
          } else {
            citizenship = 'CTN';
          }
        } else if (this.hkMacTaiwanId) {
          if (/^81000\d*$/.test(idNo)) {
            citizenship = 'HKG';
          } else if (/^82000\d*$/.test(idNo)) {
            citizenship = 'MAC';
          } else {
            citizenship = 'CTN';
          }
        }
      }
      reqParams = {
        flowToken: sessionStorage.getItem('TKFlowToken'),
        clientName,
        idKind: this.idKind,
        idNo,
        idEnddate,
        clientGender,
        birthday,
        citizenship,
        idCardPortrait: this.formData.idCardPortrait
      };
      securityAvatarQry(reqParams, { filter: true })
        .then(({ data, code }) => {
          let publicSecurityAvatar;
          let policeImageFlag = '0'; // 公安照是否存在||人脸对比是否一致标识，不一致走上传辅证
          if (code === 0) {
            pubilcSecPicFlag = data.flag;
            publicSecurityAvatar =
              data.securityAvatarPic?.length > 0 ? data.securityAvatarPic : '0';

            if (data.policeImageFlag === '1' || data.faceCompareFlag === '1') {
              policeImageFlag = '1';
            }
          } else {
            // 公安校验接口异常情况，默认通过（针对港澳台）
            if (_this.hkMacTaiwanId || _this.hkMacTaiwanPass) {
              pubilcSecPicFlag = '1';
            } else {
              pubilcSecPicFlag = '0';
            }
            publicSecurityAvatar = '0';
          }
          if (pubilcSecPicFlag !== '1') {
            let errorTips =
              '您的身份信息未通过公安校验检查，请核实并填写正确的证件信息。';
            if (_this.hkMacTaiwanId || _this.hkMacTaiwanPass)
              errorTips =
                '您的身份信息未通过出入境检查，请核实并填写正确的证件信息。';
            _this.$TAlert({
              title: '温馨提示',
              tips: errorTips,
              confirmBtn: '我知道了'
            });
          } else {
            // 需求备注 https://jira.gjzq.cn/browse/KFSBYWBLCG-5922
            const checkBizTypeList = ['010208', '010197', '010276', '010262']; //忘记密码，关联关系确认，预约业务-补充档案，预约业务-其他业务  这些业务如果监听到用户修改了证件信息，进行弹窗提示
            const { idNo: csIdNo } = this.csUserInfo;
            const {
              inProperty: { bizType }
            } = _this.tkFlowInfo();
            _this.$set(_this, 'formData', {
              ..._this.formData,
              idNoDiffer: csIdNo !== idNo ? '1' : '0',
              publicSecurityAvatar,
              policeImageFlag
            });
            if (
              checkBizTypeList.includes(bizType) &&
              _this.formData.changedClientInfoKey === '1'
            ) {
              _this.$TAlert({
                title: '温馨提示',
                tips: '您的身份证件信息有更新，我司将会自动进行更新，您无需单独提交身份证件更新业务申请。',
                confirmBtn: '我知道了',
                confirm: _this.submitFormData
              });
            } else {
              _this.submitFormData();
            }
            // _this.eventMessage(_this, EVENT_NAME.NEXT_STEP, _this.formData);
          }
        })
        .catch((err) => {
          _this.$TAlert({
            title: '温馨提示',
            tips: err
          });
        });
    },
    submitFormData() {
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, this.formData);
    },
    triggerLongTime() {
      this.longTimeChecked = !this.longTimeChecked;
    },
    queryCustomerImage() {
      let _this = this;
      clientInfoQry()
        .then(({ data }) => {
          _this.csUserInfo = { ...data };
          return customerImageDownload({});
        })
        .then(({ data }) => {
          const results = data[0];
          let bareheadedExist = '';
          if (results) {
            bareheadedExist =
              results.imageData?.length > 0 || results.existFlag === '1'
                ? '1'
                : '';
          }
          _this.$set(_this, 'formData', { ..._this.formData, bareheadedExist });
        })
        .catch((err) => {
          _this.$TAlert({
            title: '温馨提示',
            tips: err
          });
        });
    },
    emptyIdCardInput() {
      this.formData = {};
      this.imgType = '';
      this.ocrIdNo = ''; //证件号码,用于对比修改位数
      this.startDate = '';
      this.endDate = '';
      this.renderingView();
      this.positive.uploaded = false;
      this.negative.uploaded = false;
    },
    formatOcrDate(ocrDate) {
      ocrDate = ocrDate.replace(/\./g, '-').replace(/\s+/g, '');
      ocrDate = ocrDate.replace(
        /(\d{4})(\.|年)(\d{1,2})(\.|月)(\d{1,2})(日)?/,
        '$1/$3/$5'
      );
      return dayjs(new Date(ocrDate)).format('YYYYMMDD');
    }
  }
};
</script>

<style scoped>
.layer_box {
  position: fixed;
}
</style>
<style scoped>
.watermark {
  width: 0.5rem;
  height: 0.5rem;
  background: url(../../../assets/images/ic_watermark.png) no-repeat center;
  background-size: 100%;
  position: absolute;
  top: 0.12rem;
  right: 0.12rem;
  z-index: 50;
}
</style>
