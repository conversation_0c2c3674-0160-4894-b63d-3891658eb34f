<template>
  <section class="main fixed" style="position: fixed">
    <t-header></t-header>
    <article class="content">
      <div
        v-if="accountList.length === 0 && showPage"
        class="acct_nodata"
        style="background: #ffffff"
      >
        <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
        <h5>未查询到您名下存在非T日开通的中登TA账户请先进行开户的操作</h5>
      </div>
      <div v-if="accountList.length > 0 && showPage" class="com_title">
        <h5>请选择您需要处理对应关系的中登TA账户</h5>
      </div>
      <div v-if="accountList.length > 0 && showPage" class="acct_status_item">
        <ul class="acct_list">
          <li
            v-for="(item, index) in accountList"
            :key="index"
            @click="chooseItem(item)"
          >
            <span
              class="icon_radio"
              :class="{
                checked: item.checked
              }"
              >{{ item.fundCompanyStr }}：{{ item.fundCompanyAccount
              }}<em class="acct_s_tag">{{
                item.fundCompanyStatusDesc
              }}</em></span
            ><span
              v-if="
                item.stockAccountRel &&
                item.fundCompanyStatus === '0' &&
                !item.doing
              "
              class="state"
              >可解绑</span
            >
            <span
              v-if="
                !item.stockAccountRel &&
                item.fundCompanyStatus === '0' &&
                !item.doing
              "
              class="state"
              >可绑定</span
            >
            <span v-if="item.doing" class="state">办理中</span>
            <div v-if="item.stockAccountRel" class="txt_cont">
              对应账户：{{
                getAccountName('', item.exchangeTypeRel, item.holderKindRel)
              }}（{{ item.stockAccountRel }}）
            </div>
          </li>
        </ul>
      </div>
      <div class="tip_txtbox">
        <p>温馨提示:</p>
        <p>1、业务办理时间︰交易日9:00-16:00。</p>
        <p>
          2、添加证券基金账户对应关系：场外沪深TA账户和场内证券账户/封闭式基金账户建立对应关系，以便客户持有的基金份额进行跨系统转托管操作。
        </p>
        <p>
          3、取消证券基金账户对应关系：指取消现存的场外沪深TA账户和场内证券账户/封闭式基金账户的对应关系，以便建立新的证券基金账户对应关系。
        </p>
      </div>
    </article>
    <footer v-if="accountList.length === 0 && showPage" class="footer">
      <div class="ce_btn block">
        <a class="p_button" @click="toPage">新开场外TA账户</a>
        <a class="p_button border" @click="toIndex">返回首页</a>
      </div>
    </footer>
    <footer
      v-if="canSelectAccount.length > 0 && accountList.length > 0 && showPage"
      class="footer"
    >
      <div class="ce_btn block">
        <a class="p_button" :class="{ disabled: !canNext }" @click="next"
          >下一步</a
        >
      </div>
    </footer>
    <footer
      v-if="canSelectAccount.length === 0 && accountList.length > 0 && showPage"
      class="footer"
    >
      <div class="ce_btn block">
        <a class="p_button" @click="toIndex">返回首页</a>
      </div>
    </footer>
    <div v-if="toShow">
      <div class="dialog_overlay"></div>
      <div class="dialog_box">
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>温馨提示</h3>
          <div>
            <div style="text-align: left">
              您正在取消
              <span style="color: red"
                >{{ openAccount[0].fundCompanyStr }}({{
                  openAccount[0].fund_company_account
                }})</span
              >
              与
              <span style="color: red"
                >{{ accountName }}({{ openAccount[0].stock_account }})</span
              >的对应关系，请确保相关账户不存在跨系统转托管等在途业务。
            </div>
          </div>
        </div>
        <div class="dialog_btn">
          <a class="cancel" @click="toShow = false">取消</a>
          <a @click="confirmAlert">确认</a>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import { taAccountSelect, taOnRoadFormQuery } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

const exchangeTypeName = [
  { text: '深', value: '2' },
  { text: '沪', value: '1' }
];
const holderKindTypeName = [
  { text: 'A', value: '0' },
  { text: '市场内基金', value: '1' }
];
export default {
  name: 'OutProdtaAccSelection',
  inject: ['clearKeepAlive', 'eventMessage'],
  data() {
    return {
      toShow: false,
      accountName: '',
      showPage: false,
      chooseType: '', // 0解绑 1绑定
      accountList: [],
      canNext: false
    };
  },
  computed: {
    canSelectAccount() {
      return this.accountList;
    },
    openAccount() {
      // 已选择TA账号
      return this.accountList
        .filter((a) => a.checked == true)
        .map((item) => {
          return {
            fundCompanyStr: item.fundCompanyStr,
            asset_prop: item.assetPropRel,
            exchange_type: item.exchangeTypeRel,
            fund_account: '',
            holder_kind: item.holderKindRel,
            fund_company_account: item.fundCompanyAccount,
            stock_account: item.stockAccountRel,
            acode_account: item.acodeAccount,
            stock_type: ''
          };
        });
    }
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
  },
  mounted() {
    this.showPage = false;
    // this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
    //   text: '下一步',
    //   display: true,
    //   btnStatus: 2,
    //   data: () => {
    //     console.log(this.chooseType);
    //     if (this.chooseType === '0') {
    //       console.log(this.openAccount[0]);
    //       // 解绑
    //       this.$TAlert({
    //         tips: `您正在取消${this.openAccount[0].fund_company_account}与${this.openAccount[0].stock_account}的对应关系，请确保相关账户不存在跨系统转托管等在途业务。`
    //       });
    //     } else {
    //       this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
    //         outProdtaAccData: JSON.stringify(this.openAccount),
    //         maintFlag: this.chooseType
    //       });
    //     }
    //   }
    // });
    taAccountSelect().then((res) => {
      this.accountList = res.data;
      taOnRoadFormQuery({
        flowToken: sessionStorage.getItem('TKFlowToken')
      }).then((data) => {
        let doList = data.data?.mapList;
        this.showPage = true;
        this.accountList.forEach((item) => {
          this.$set(item, 'doing', false);
          doList?.length > 0 &&
            doList.forEach((it) => {
              let itData = JSON.parse(it.outProdtaAccData).map(
                (a) => a.fund_company_account
              );
              if (itData.includes(item.fundCompanyAccount)) {
                this.$set(item, 'doing', true);
              }
            });
        });
      });
    });
  },
  methods: {
    getAccountName(assetProp, exchangeType, holderKind) {
      let exchangeName = exchangeTypeName.filter(
        (item) => item.value === exchangeType
      )[0].text;
      let holderKindName = holderKindTypeName.filter(
        (item) => item.value === holderKind
      )[0].text;
      return `${exchangeName}${holderKindName}`;
    },

    toPage() {
      // 跳转到新开场外TA账号
      import('@/common/flowMixin.js').then((a) => {
        a.initFlow.call(this, '010089');
      });
    },

    toIndex() {
      // 返回首页
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    },

    next() {
      if (!this.canNext) {
        return;
      }
      if (this.chooseType === '0') {
        // 解绑
        console.log(this.openAccount[0]);
        this.accountName = this.getAccountName(
          this.openAccount[0],
          this.openAccount[0].exchange_type,
          this.openAccount[0].holder_kind
        );
        // 解绑
        this.toShow = true;
        // this.$TAlert({
        //   tips: `您正在取消${this.openAccount[0].fundCompanyStr}（${this.openAccount[0].fund_company_account}）与${accountName}（${this.openAccount[0].stock_account}）的对应关系，请确保相关账户不存在跨系统转托管等在途业务。`,
        //   hasCancel: true,
        //   confirmBtn: '确认',
        //   confirm: () => {
        //     this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        //       outProdtaAccData: JSON.stringify(this.openAccount),
        //       maintFlag: this.chooseType
        //     });
        //   }
        // });
      } else {
        this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
          outProdtaAccData: JSON.stringify(this.openAccount),
          maintFlag: this.chooseType
        });
      }
    },
    confirmAlert() {
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        outProdtaAccData: JSON.stringify(this.openAccount),
        maintFlag: this.chooseType
      });
    },
    chooseItem(item) {
      // if (item.fundCompanyStatus !== '0') {
      //   return;
      // }
      // if (item.doing) {
      //   return;
      // }
      this.accountList.forEach((it) => {
        this.$set(it, 'checked', false);
      });
      this.accountList.forEach((it) => {
        if (item.fundCompanyAccount === it.fundCompanyAccount) {
          this.chooseType = it.stockAccountRel ? '0' : '1';
          this.$set(it, 'checked', true);
        }
      });
      this.$emit('change', {
        outProdtaAccData: JSON.stringify(this.openAccount),
        maintFlag: this.chooseType
      });
      this.canNext = true;
    }
  }
};
</script>

<style scoped>
.tip_txtbox{
  background-color: transparent;
}
.acct_list {
    padding: 0 0.16rem;
    background: #ffffff;
}
.acct_list li .icon_radio, .acct_list li .icon_check {
    display: block;
    padding: 0.16rem 0 0.16rem 0.3rem;
    font-size: 0.16rem;
    line-height: 1.5;
    color: #333333;
}
.acct_list li .icon_radio:before, .acct_list li .icon_check:before {
    left: 0;
    right: auto;
}
.icon_radio.checked:before {
    content: "\E61F";
    border-color:  #F93838;
    background:  #F93838;
}
.icon_radio:before {
    content: '';
    box-sizing: border-box;
    width: 0.18rem;
    height: 0.18rem;
    border: 1px solid #BBBBBB;
    border-radius: 50%;
    font-size: 0.16rem;
    line-height: 1;
    color: #ffffff;
    font-family: "wt-iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    top: 50%;
    left: 0;
    margin: -0.09rem 0 0 0;
}
.acct_s_tag {
    display: inline-block;
    padding: 0 0.03rem;
    border-radius: 0.02rem;
    border: 1px solid #E0953F;
    color: #E0953F;
    font-size: 0.12rem;
    line-height: 1.33333;
    vertical-align: middle;
    margin-left: 0.08rem;
}
</style>
