<template>
  <section class="main fixed" style="position: fixed">
    <t-header></t-header>
    <article class="content">
      <div
        v-if="accountList.length === 0 && showPage"
        class="acct_nodata"
        style="background: #ffffff"
      >
        <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
        <h5>未查询到您名下存在可关联的证券账户请先去补开股东户后再来办理</h5>
      </div>

      <div v-if="accountList.length > 0 && showPage" class="com_title">
        <h5>请选择您需要进来对应关系的证券账户</h5>
      </div>
      <div v-if="accountList.length > 0 && showPage" class="acct_status_item">
        <ul class="acct_list">
          <li
            v-for="(item, index) in accountList"
            :key="index"
            @click="chooseItem(item)"
          >
            <span
              class="icon_radio"
              :class="{
                checked: item.checked,
                disabled: item.holderStatus !== '0'
              }"
              >{{ item.accountTypeName }}: {{ item.stockAccount }}
              <em v-if="item.holderStatus === '0'" class="acct_s_tag">{{
                item.holderStatusDesc
              }}</em>
              <em v-if="item.holderStatus !== '0'" class="acct_s_tag abnormal"
                >异常</em
              >
            </span>
            <!-- <span class="state">未绑定</span> -->
          </li>
        </ul>
      </div>
      <div class="tip_txtbox" v-if="canSelectAccount.length === 0 && showPage">
        <p>您本地还没有可办理业务的账户，请先前往规范或开通。</p>
      </div>
    </article>
    <footer v-if="canSelectAccount.length === 0 && showPage" class="footer">
      <div class="ce_btn block">
        <a class="p_button" @click="toPage">补开股东账户</a>
        <a class="p_button border" @click="toIndex">返回首页</a>
      </div>
    </footer>
    <footer v-if="canSelectAccount.length > 0 && showPage" class="footer">
      <div class="ce_btn block">
        <a class="p_button" :class="{ disabled: !canNext }" @click="next"
          >下一步</a
        >
      </div>
    </footer>
  </section>
</template>

<script>
import { stockAccountListV1 } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

const exchangeTypeName = [
  { text: '深', value: '2' },
  { text: '沪', value: '1' }
];
const holderKindTypeName = [
  { text: 'A', value: '0' },
  { text: '市场内基金', value: '1' }
];
export default {
  name: 'OutProdtaMaintStock',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      showPage: false,
      canNext: false,
      accountList: []
    };
  },
  computed: {
    canSelectAccount() {
      return this.accountList.filter((item) => item.holderStatus === '0');
    },
    openAccount() {
      // 已选择TA账号
      return this.accountList
        .filter((a) => a.checked == true)
        .map((item) => {
          return {
            asset_prop: item.assetProp,
            exchange_type: item.exchangeType,
            fund_account: item.fundAccount,
            holder_kind: item.holderKind,
            stock_account: item.stockAccount,
            acode_account: item.acodeAccount,
            stock_type: ''
          };
        });
    }
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    window.viewShowCallBack = this.viewShowCallBack;
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  mounted() {
    const { inProperty } = this.tkFlowInfo();
    let outProdtaAccData = JSON.parse(inProperty.outProdtaAccData);
    console.log(outProdtaAccData);
    stockAccountListV1({
      filterExchangeType: outProdtaAccData[0].exchange_type,
      filterAssetProp: '0,1', //普通，基金
      filterHolderKind: '0,1', // 普通，基金
      fundCompanyAccount: outProdtaAccData[0].fund_company_account
    }).then((res) => {
      this.showPage = true;
      this.accountList = res.data.map((item) => {
        return {
          ...item,
          accountTypeName: this.getAccountName(
            item.assetProp,
            item.exchangeType,
            item.holderKind
          )
        };
      });
      /* if (this.accountList.length > 0) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
      } */
    });
  },
  methods: {
    viewShowCallBack() {
      const { inProperty } = this.tkFlowInfo();
      let outProdtaAccData = JSON.parse(inProperty.outProdtaAccData);
      console.log(outProdtaAccData);
      stockAccountListV1({
        filterExchangeType: outProdtaAccData[0].exchange_type,
        filterAssetProp: '0,1', //普通，基金
        filterHolderKind: '0,1', // 普通，基金
        fundCompanyAccount: outProdtaAccData[0].fund_company_account
      }).then((res) => {
        this.showPage = true;
        this.accountList = res.data.map((item) => {
          return {
            ...item,
            accountTypeName: this.getAccountName(
              item.assetProp,
              item.exchangeType,
              item.holderKind
            )
          };
        });
        /* if (this.accountList.length > 0) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
        } */
      });
    },

    toPage() {
      // 跳转至补开股东户
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010044', initJumpMode: '0' });
      });
      /* if ($hvue.platform == 0) {
        window.location.href =
          window.$hvue.customConfig.targetUrl +
          '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=stockAForWeb';
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          // targetModule: 'open',
          params: {
            url:
              window.$hvue.customConfig.targetUrl +
              '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=stockAForWeb',
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      } */
    },

    toIndex() {
      // 返回首页
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    },

    next() {
      if (!this.canNext) {
        return;
      }
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        selectedAccountsData: JSON.stringify(this.openAccount)
      });
    },

    getAccountName(assetProp, exchangeType, holderKind) {
      let exchangeName = exchangeTypeName.filter(
        (item) => item.value === exchangeType
      )[0].text;
      let holderKindName = holderKindTypeName.filter(
        (item) => item.value === holderKind
      )[0].text;
      return `${exchangeName}${holderKindName}`;
    },

    chooseItem(item) {
      if (item.holderStatus !== '0') {
        return;
      }
      this.accountList.forEach((it) => {
        this.$set(it, 'checked', false);
      });
      this.accountList.forEach((it) => {
        if (item.stockAccount === it.stockAccount) {
          this.$set(it, 'checked', true);
        }
      });
      this.$emit('change', {
        selectedAccountsData: JSON.stringify(this.openAccount)
      });
      this.canNext = true;
    }
  }
};
</script>

<style></style>
