<template>
  <div style="margin-bottom: 0.08rem">
    <van-field
      v-model="fieldValue"
      type="password"
      :label="$attrs.label"
      :placeholder="$attrs.placeholder"
    />
    <van-field
      v-if="needConfirm === '1'"
      v-model="confirmFieldValue"
      type="password"
      :label="'确认' + $attrs.label"
      :placeholder="$attrs.placeholder"
    />
  </div>
</template>

<script>
import { getPwdEncryption } from '@/common/util.js';
export default {
  name: 'PasswordInput',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    needConfirm: {
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      fieldValue: '',
      confirmFieldValue: ''
    };
  },
  watch: {
    fieldValue: {
      handler(newVal) {
        if (this.needConfirm !== '1' || newVal === this.confirmFieldValue) {
          this.$emit('change', getPwdEncryption(newVal));
        }
      },
      deep: false,
      immediate: false
    },
    confirmFieldValue: {
      handler(newVal) {
        if (newVal === this.fieldValue) {
          this.$emit('change', getPwdEncryption(newVal));
        }
      },
      deep: false,
      immediate: false
    }
  },
  methods: {}
};
</script>

<style lang="less" scope></style>
