<template>
  <article class="content">
    <ul class="com_infolist">
      <li>
        <span class="tit">{{ assetPropTitle }}资金账户</span>
        <p class="txt_left">{{ fundAccount }}</p>
      </li>
    </ul>
    <div class="com_title">
      <h5>请输入{{ assetPropTitle }}交易密码进行校验</h5>
    </div>
    <div class="com_box">
      <div class="input_form spel">
        <div class="input_text pword">
          <h-keypanel
            v-model="password"
            type="tel2"
            :mask="true"
            :is-head-icon="true"
            extra-parent-el=".hui-flexview"
            placeholder="请输入交易密码"
          >
            <div slot="head" class="safe-head">
              <img src="../../../assets/images/logo.png" alt="" />
              <span>佣金宝安全输入</span>
            </div>
          </h-keypanel>
        </div>
      </div>
    </div>
  </article>
</template>

<script>
import { creditPwdCheck, fundAccountListQry } from '@/service/service';
import { getPwdEncryption } from '@/common/util';
import { EVENT_NAME } from '@/common/formEnum';
import { ASSET_PROP } from '@/common/enumeration';

export default {
  inject: ['tkFlowInfo', 'eventMessage'],
  name: 'ClientPwdCheck',
  data() {
    return {
      assetPropTitle: '',
      password: '',
      fundAccount: '', //资金账户
      ifExistCreditAccount: '0',
      specialFlag: '0'
    };
  },
  computed: {
    canNext() {
      return this.password !== '';
    }
  },
  watch: {
    canNext: {
      handler(bool) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          btnStatus: bool ? 2 : 0,
          data: () => {
            const { asset_prop, password_type } = this.$attrs;
            creditPwdCheck({
              flowToken: sessionStorage.getItem('TKFlowToken'),
              account: this.fundAccount,
              password: 'encrypt:' + getPwdEncryption(this.password),
              assetProp: asset_prop,
              clientPwdType: password_type
            })
              .then(({ code }) => {
                if (code === 0) {
                  this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
                    specialFlag: this.specialFlag
                  });
                }
              })
              .catch((err) => {
                this.$TAlert({
                  tips: err
                });
              });
          }
        });
      },
      immediate: true
    }
  },
  created() {
    this.$store.commit('flow/setWhiteBg', true);
  },
  mounted() {
    this.renderingView();
  },
  methods: {
    renderingView() {
      const { asset_prop } = this.$attrs;
      fundAccountListQry({})
        .then(({ data }) => {
          const filterList =
            data.fundAccountList.filter(
              ({ assetProp }) => assetProp === asset_prop
            ) || [];
          if (filterList.length !== 0) {
            const { specialFlag, fundAccount, assetProp } = filterList[0];
            this.assetPropTitle = this.getAssetPropMap(assetProp);
            this.fundAccount = fundAccount;
            this.specialFlag = specialFlag;
            if (this.specialFlag === '0') {
              this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
                specialFlag: this.specialFlag
              });
            }
          } else {
            return Promise.reject('没有查询到账户信息');
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },
    getAssetPropMap(v) {
      let getMap = new Map();
      getMap.set(ASSET_PROP.DERIVATIVES_ACCOUNT, '期权');
      getMap.set(ASSET_PROP.ORDINARY_ACCOUNT, '普通');
      getMap.set(ASSET_PROP.CREDIT_ACCOUNT, '信用');
      getMap.set(ASSET_PROP.OPTIONS_ACCOUNT, '期权');
      getMap.set(ASSET_PROP.FUND_ACCOUNT, '基金');
      return getMap.get(v) || '';
    }
  }
};
</script>

<style></style>
