<template>
  <section class="main fixed" style="position: fixed">
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" href="javascript:void(0);"></a>
        <h1 class="title">修改密码</h1>
      </div>
    </header>
    <article class="content">
      <div class="input_form mt10">
        <div class="input_text text">
          <span class="tit">资金账户</span>
          <input
            class="t1"
            type="text"
            placeholder="请输入"
            disabled
            :value="fundAccount"
          />
        </div>
      </div>

      <!-- 交易密码优先 -->
      <div v-if="!$attrs.fundPasswordFirst">
        <div class="set_pword_item">
          <div class="com_title">
            <h5>设置交易密码<em class="small">交易密码用于账户登录</em></h5>
          </div>
          <div class="input_form">
            <div class="input_text pword">
              <input
                class="t1"
                :type="show_trade_password ? 'text' : 'password'"
                placeholder="旧交易密码"
                v-model="trade_password"
              />
              <a
                class="icon_eye"
                :class="show_trade_password ? 'show' : ''"
                @click="show_trade_password = !show_trade_password"
              ></a>
            </div>
            <div class="input_text pword">
              <input
                class="t1"
                maxlength="6"
                :type="show_trade_password ? 'text' : 'password'"
                placeholder="设置6位交易数字密码"
                v-model="new_trade_password"
              />
            </div>
            <div class="input_text">
              <input
                class="t1"
                maxlength="6"
                :type="show_trade_password ? 'text' : 'password'"
                placeholder="重复6位交易数字密码"
                v-model="new_trade_password_confirm"
              />
            </div>
          </div>
        </div>
        <div class="set_pword_item" v-if="!pwd_linkage_flag">
          <div class="com_title">
            <h5>设置资金密码<em class="small">资金密码用于转账枚验</em></h5>
            <div class="switch">
              <input type="checkbox" v-model="is_set_fundpassword" />
              <div class="switch-inner">
                <div class="switch-arrow"></div>
              </div>
            </div>
          </div>
          <div class="input_form" v-if="is_set_fundpassword">
            <div class="input_text pword">
              <input
                class="t1"
                :type="show_fund_password ? 'text' : 'password'"
                placeholder="旧资金密码"
                v-model="fund_password"
              />
              <a
                class="icon_eye"
                :class="show_fund_password ? 'show' : ''"
                @click="show_fund_password = !show_fund_password"
              ></a>
            </div>
            <div class="input_text pword">
              <input
                class="t1"
                maxlength="6"
                :type="show_fund_password ? 'text' : 'password'"
                placeholder="设置6位资金数字密码"
                v-model="new_fund_password"
              />
            </div>
            <div class="input_text">
              <input
                class="t1"
                maxlength="6"
                :type="show_fund_password ? 'text' : 'password'"
                placeholder="重复6位资金数字密码"
                v-model="new_fund_password_confirm"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 优先资金密码 -->
      <div v-if="$attrs.fundPasswordFirst">
        <div class="set_pword_item">
          <div class="com_title">
            <h5>设置资金密码<em class="small">资金密码用于转账枚验</em></h5>
          </div>
          <div class="input_form">
            <div class="input_text pword">
              <input
                class="t1"
                :type="show_fund_password ? 'text' : 'password'"
                placeholder="旧资金密码"
                v-model="fund_password"
              />
              <a
                class="icon_eye"
                :class="show_fund_password ? 'show' : ''"
                @click="show_fund_password = !show_fund_password"
              ></a>
            </div>
            <div class="input_text pword">
              <input
                class="t1"
                maxlength="6"
                :type="show_fund_password ? 'text' : 'password'"
                placeholder="设置6位资金数字密码"
                v-model="new_fund_password"
              />
            </div>
            <div class="input_text">
              <input
                class="t1"
                maxlength="6"
                :type="show_fund_password ? 'text' : 'password'"
                placeholder="重复6位资金数字密码"
                v-model="new_fund_password_confirm"
              />
            </div>
          </div>
        </div>
        <div v-if="!pwd_linkage_flag" class="set_pword_item">
          <div class="com_title">
            <h5>设置交易密码<em class="small">交易密码用于账户登录</em></h5>
            <div class="switch">
              <input type="checkbox" v-model="is_set_fundpassword" />
              <div class="switch-inner">
                <div class="switch-arrow"></div>
              </div>
            </div>
          </div>
          <div class="input_form" v-if="is_set_fundpassword">
            <div class="input_text pword">
              <input
                class="t1"
                :type="show_trade_password ? 'text' : 'password'"
                placeholder="旧交易密码"
                v-model="trade_password"
              />
              <a
                class="icon_eye"
                :class="show_trade_password ? 'show' : ''"
                @click="show_trade_password = !show_trade_password"
              ></a>
            </div>
            <div class="input_text pword">
              <input
                class="t1"
                maxlength="6"
                :type="show_trade_password ? 'text' : 'password'"
                placeholder="设置6位交易数字密码"
                v-model="new_trade_password"
              />
            </div>
            <div class="input_text">
              <input
                class="t1"
                maxlength="6"
                :type="show_trade_password ? 'text' : 'password'"
                placeholder="重复6位交易数字密码"
                v-model="new_trade_password_confirm"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="pwors_select">
        <p>密码联动</p>
        <div class="switch">
          <input type="checkbox" v-model="pwd_linkage_flag" />
          <div class="switch-inner">
            <div class="switch-arrow"></div>
          </div>
        </div>
        <div class="imp_c_tips">
          <p>
            <span class="imp"
              >密码联动指将交易密码和资金密码设置为同一密码</span
            >
          </p>
        </div>
      </div>
      <div class="pword_tips">
        <p>为了保障您的资金安全，以下密码将不能使用</p>
        <ul>
          <li>密码为同一数字，如111111</li>
          <li>数字顺序递增，如123456</li>
          <li>密码数字前后三位递增重复，如123123</li>
        </ul>
        <p>密码设置完成后，请妥善保管</p>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="confirm">确认修改</a>
      </div>
    </footer>
  </section>
</template>

<script>
import { PROCESS_STATUS } from '@/common/enumeration';
import {
  flowQueryIns,
  flowSubmit,
  getJwtToken,
  updateFlowForm,
  pwdSyncTypeQry
} from '@/service/service.js';
import { getPwdEncryption } from '@/common/util.js';
import { exitApp, wakeLoginApp } from '@/common/util';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'PasswordModify',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      fundAccount: '',
      show_trade_password: false,
      show_fund_password: false,
      trade_password: '',
      new_trade_password: '',
      new_trade_password_confirm: '',
      fund_password: '',
      new_fund_password: '',
      new_fund_password_confirm: '',
      is_set_fundpassword: false,
      pwd_linkage_flag: null
    };
  },
  computed: {
    isApp() {
      return $hvue.platform !== '0';
    }
  },
  created() {
    this.renderingView();
  },
  watch: {
    pwd_linkage_flag(val) {
      console.log(val);
    }
  },
  methods: {
    renderingView() {
      const { inProperty } = this.tkFlowInfo();
      this.fundAccount = inProperty.fundAccount;
      pwdSyncTypeQry().then((res) => {
        this.is_only_tradepwd = res.data.pwdSynchronizationType;
        if (this.is_only_tradepwd === '1') {
          this.pwd_linkage_flag = true;
        } else {
          this.pwd_linkage_flag = false;
        }
      });
    },

    sameCheck() {
      if (this.pwd_linkage_flag) {
        this.fund_password = this.trade_password;
        this.new_fund_password = this.new_trade_password;
        this.new_fund_password_confirm = this.new_trade_password_confirm;
      }
    },

    verifyYes(pwd, type, old) {
      let txt = '';
      if (type === 'trade') {
        txt = '交易密码';
        if (old === 'old') {
          txt = '旧' + txt;
        } else {
          txt = '新' + txt;
        }
      } else if (type === 'fund') {
        txt = '资金密码';
        if (old === 'old') {
          txt = '旧' + txt;
        } else {
          txt = '新' + txt;
        }
      }
      if (pwd.trim() === '' && old !== 'old') {
        this.errTxt = '请输入六位' + txt;
      } else if (!/^\d{6}$/.test(pwd) && old !== 'old') {
        this.errTxt = '请输入六位数字' + txt;
      } else if (/(\d)\d*\1\d*\1/.test(pwd) && old !== 'old') {
        this.errTxt = txt + '有数字重复出现三次';
      } else if (/(\d{2,})\1|(\d)\2{2,}/.test(pwd) && old !== 'old') {
        this.errTxt = txt + '连续出现两组相同数字';
      } else if (
        /(?:1234|2345|3456|4567|5678|6789|7890|9876|8765|7654|6543|5432|4321)/.test(
          pwd
        ) &&
        old !== 'old'
      ) {
        this.errTxt = txt + '出现四位及以上连续数字';
      } else {
        this.errTxt = 'pass';
      }
      if (this.errTxt !== 'pass') {
        _hvueToast({
          timeout: 1500,
          mes: this.errTxt
        });
        return false;
      }
      return true;
    },

    verifySameOld(type) {
      let txt = '';
      let pwd1;
      let pwd2;
      if (type === 'trade') {
        txt = '交易';
        pwd1 = this.trade_password;
        pwd2 = this.new_trade_password;
      } else if (type === 'fund') {
        txt = '资金';
        pwd1 = this.fund_password;
        pwd2 = this.new_fund_password;
      }
      if (pwd1 === pwd2) {
        _hvueToast({
          timeout: 1200,
          mes: '新' + txt + '密码不能与旧' + txt + '密码一致'
        });
        return false;
      }
      return true;
    },

    verifySame(type) {
      let txt = '';
      let pwd1;
      let pwd2;
      if (type === 'trade') {
        txt = '交易';
        pwd1 = this.new_trade_password;
        pwd2 = this.new_trade_password_confirm;
      } else if (type === 'fund') {
        txt = '资金';
        pwd1 = this.new_fund_password;
        pwd2 = this.new_fund_password_confirm;
      }
      if (pwd1 !== pwd2) {
        _hvueToast({
          timeout: 1200,
          mes: '两次新' + txt + '密码不一致'
        });
        return false;
      }
      return true;
    },

    confirm() {
      const { flowName } = this.tkFlowInfo();
      if (
        !this.trade_password &&
        !this.new_trade_password &&
        !this.new_trade_password_confirm &&
        !this.fund_password &&
        !this.new_fund_password &&
        !this.new_fund_password_confirm
      ) {
        _hvueToast({
          timeout: 1200,
          mes: '请输入交易密码或资金密码'
        });
        return;
      }
      if (
        this.trade_password ||
        this.new_trade_password ||
        this.new_trade_password_confirm
      ) {
        // 校验交易密码
        if (
          !this.verifyYes(this.trade_password, 'trade', 'old') ||
          !this.verifyYes(this.new_trade_password, 'trade', 'new') ||
          !this.verifyYes(this.new_trade_password_confirm, 'trade', 'new') ||
          !this.verifySame('trade') ||
          !this.verifySameOld('trade')
        ) {
          return;
        }
      }
      if (
        this.fund_password ||
        this.new_fund_password ||
        this.new_fund_password_confirm
      ) {
        // 校验资金密码
        if (
          !this.verifyYes(this.fund_password, 'fund', 'old') ||
          !this.verifyYes(this.new_fund_password, 'fund', 'new') ||
          !this.verifyYes(this.new_fund_password_confirm, 'fund', 'new') ||
          !this.verifySame('fund') ||
          !this.verifySameOld('fund')
        ) {
          return;
        }
      }
      updateFlowForm({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        source: `${flowName}-初始化`,
        formParam: {
          trade_password: getPwdEncryption(this.trade_password),
          new_trade_password: getPwdEncryption(this.new_trade_password),
          fund_password: getPwdEncryption(this.fund_password),
          new_fund_password: getPwdEncryption(this.new_fund_password),
          pwd_linkage_flag: this.pwd_linkage_flag ? '1' : '0'
        }
      }).then(() => {
        const flowToken = sessionStorage.getItem('TKFlowToken');
        const accArr = [PROCESS_STATUS.ACCEPT_COMPLETED]; // 配置需要提交受理结果的状态
        flowQueryIns({ flowToken }).then((res) => {
          if (accArr.includes(res.data.status)) {
          } else {
            flowSubmit({ flowToken }).then();
          }
        });
        flowSubmit({ flowToken })
          .then((res) => {
            // this.eventMessage(this, 'toIndex');
            let tradeMsg = res.data[0].tradeMsg || res.data[0].bandleMsg;
            let fundMsg = res.data[0].fundMsg || res.data[0].bandleMsg;
            this.$TAlert({
              tips: [tradeMsg, fundMsg].join(','),
              confirm: () => {
                if (
                  this.new_trade_password &&
                  tradeMsg === '交易密码修改成功'
                ) {
                  this.$store.commit('user/setUserInfo', null);
                  localStorage.removeItem('vuex');
                  sessionStorage.clear();
                  if (this.isApp) {
                    // exitApp();
                    wakeLoginApp();
                  } else {
                    this.$router.replace({
                      path: '/login'
                    });
                  }
                } else {
                  // this.eventMessage(this, EVENT_NAME.TO_INDEX);
                }
              }
            });
          })
          .catch((err) => {
            _hvueToast({
              mes: err
            });
          });
      });
    }
  }
};
</script>

<style lang="less" scope></style>
