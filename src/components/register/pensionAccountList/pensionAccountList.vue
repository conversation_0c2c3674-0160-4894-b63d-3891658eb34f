<template>
  <section class="main fixed backWhite_box" data-page="home">
    <div class="com_box">
      <span>普通资金账号</span>
      <span>{{ fundAccount }}</span>
    </div>
    <div class="pension_bank_card">
      <div class="pension_info">
        <div class="pension_title">养老金账户</div>
        <div v-if="pensionOpenStatus">
          <span class="pension_account">{{
            pensionFundAccountInfo[0].fundAccount
          }}</span>
          <span class="status">{{
            pensionFundAccountInfo[0].fundAccountStatus === '0'
              ? '正常'
              : '异常'
          }}</span>
        </div>
        <div v-else class="pension_account">未开通</div>
      </div>
      <div class="bank_info" :class="{ open_account_status: !pensionOpenStatus }">
        <div v-if="pensionOpenStatus && currentCsdcBankList.length > 0">
          <img
            class="bankImg"
            :src="`data:image/jpeg;base64,${currentCsdcBankList[0].bankLogo}`"
          />
          <span>{{ currentCsdcBankList[0].bankName }}</span>
        </div>
        <div class="open_account" v-else @click="openAccount">开通账户</div>
      </div>
    </div>
  </section>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
import { getOpStation } from '@/common/util';
import {
  fundAccountListQry,
  pensionAccountQry,
  csdcBankQry
} from '@/service/service';

export default {
  name: 'pensionAccountList',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {},
  data() {
    return {
      fundAccount: '', //普通资金账号
      pensionFundAccountInfo: [], //养老金资金账号
      currentCsdcBankList: [],
      pensionAccountBankInfo: {},
      pensionOpenStatus: false,
      tempPensionFundAccountInfo: []
    };
  },
  computed: {
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo?.fundAccount;
    }
  },
  watch: {
    ssoLoginFlag: {
      handler: function (fundAccount) {
        console.log(fundAccount);
        if (fundAccount) {
          this.fundAccount = fundAccount;
          this.renderingView();
        }
      },
      immediate: true
    }
  },
  created() {
    console.log('test-----=============================================');
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    window.viewShowCallBack = this.viewShowCallBack;
  },

  destroyed() {
    window.viewShowCallBack = null;
  },
  mounted() {},
  methods: {
    viewShowCallBack() {
      this.renderingView();
    },
    renderingView() {
      this.getFundAccountList();
    },
    // 资产账户列表查询
    getFundAccountList() {
      fundAccountListQry().then((res) => {
        const { code, data } = res;
        if (code == '0') {
          const pensionItemArr = data.fundAccountList.filter((item) => {
            return item.assetProp === '8';
          });
          this.tempPensionFundAccountInfo = pensionItemArr;
          // 有养老金账号继续获取绑定银行信息，没有则为未开通状态
          if (pensionItemArr.length > 0) {
            this.pensionOpenStatus = true;
            this.getPensionAccount();
          }
        }
      });
    },
    // 获取养老金本地资金账号
    getPensionAccount() {
      let params = {
        clientId: this.$store.state.user?.userInfo?.clientId,
        fundAccount: this.tempPensionFundAccountInfo[0].fundAccount,
        branchNo: this.$store.state.user?.userInfo?.branchNo,
        opEntrustWay: $hvue.customConfig.opEntrustWay,
        opStation: getOpStation()
      };
      pensionAccountQry(params).then((res) => {
        const { code, data } = res;
        if (code == 0) {
          if (data?.bankNo) {
            // 当前养老金账户已绑定对应的银行，展示对应银行
            this.getCsdcBank(data.bankNo);
          } else {
            // 当前账户未绑定对应的银行，展示未开通状态
            this.pensionOpenStatus = false;
          }
        }
      });
    },
    // 获取养老金银行列表
    getCsdcBank(bankNo) {
      csdcBankQry().then((res) => {
        console.log('银行列表', res);
        const { code, data } = res;
        if (code == 0 && data.length > 0) {
          this.pensionFundAccountInfo = this.tempPensionFundAccountInfo
          this.currentCsdcBankList = data.filter((item) => {
            return item.bankNo === bankNo;
          });
        }
      });
    },
    // 开通账户
    openAccount() {
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {});
    }
  }
};
</script>

<style scoped lang="less">
.com_box {
  padding: 0.16rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  line-height: 24px;
  color: #0f0f1b;
}
.pension_bank_card {
  margin: 0.16rem;
  .pension_info {
    padding: 0.16rem;
    color: #0f0f1b;
    font-size: 18px;
    font-weight: 500;
    line-height: 24px;
    background: linear-gradient(303deg, #fff5f5 2.04%, #fff 96.45%);
    .pension_title {
      margin-bottom: 0.11rem;
    }
    .pension_account {
      color: #87878d;
      font-size: 14px;
      line-height: 22px;
      margin-right: 0.09rem;
    }
    .status {
      color: #ff2840;
      font-size: 10px;
      line-height: 12px;
      border-radius: 3px;
      border: 0.5px solid var(--Primary-B-500, #ff2840);
      padding: 0.02rem 0.04rem;
    }
  }
  .bank_info {
    padding: 0.16rem;
    background-color: #fff;
    color: var(--Typography-333, #0f0f1b);
    font-size: 16px;
    line-height: 24px;
    .open_account {
      padding: 0.04rem 0.12rem;
      border-radius: 18px;
      background: var(--Primary-B-50, #fff0f0);
      color: var(--Primary-B-500, #ff2840);
      text-align: center;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
    }
  }
  .open_account_status {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .bankImg {
    width: 0.25rem;
    height: 0.25rem;
    margin-right: 0.15rem;
  }
}
</style>
