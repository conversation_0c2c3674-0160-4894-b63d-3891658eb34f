<template>
  <section class="main fixed container" data-page="home">
    <div class="pension_bank_card">
      <div class="text">
        <div class="text_title">已有养老金银行卡</div>
        <div class="text_tip">
          已在银行开立个人养老金银行卡，可在绑卡后进行养老产品的投资
        </div>
      </div>
      <div class="button" @click="toBind">去绑定</div>
    </div>
    <div class="pension_bank_card">
      <div class="text">
        <div class="text_title">新开养老金银行卡</div>
        <div class="text_tip">
          未在银行开立过个人养老金银行卡的大陆个人客户可跳转银行线上渠道开户
        </div>
      </div>
      <div class="button" @click="openBankAccount">去开户</div>
    </div>
    <div class="tip">
      养老金银行卡由参加人在规定商业银行开立，并与个人养老金账户绑定后用于参与个人养老金投资基金业务的专用资金账户，缴存资金可享受每年最高12000元的个人所得税延优惠。
    </div>
  </section>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'pensionBankStatus',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {};
  },
  created() {
    console.log('test===================');
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
  },
  methods: {
    //绑定已有银行卡
    toBind() {
      console.log('去绑定');
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, { holdPensionBank: 1 });
    },
    // 新开银行卡
    openBankAccount() {
      console.log('去开户');
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, { holdPensionBank: 0 });
    }
  }
};
</script>

<style scoped lang="less">
.container {
  padding: 0.16rem;
}
.pension_bank_card {
  background: #fff;
  padding: 0.16rem 0.12rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.08rem;
  min-height: 1rem;
  .text_title {
    color: #0f0f1b;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
  }
  .text_tip {
    color: #87878d;
    font-size: 14px;
    line-height: 22px;
  }
  .button {
    padding: 0.04rem 0.12rem;
    border-radius: 0.14rem;
    margin-left: 0.16rem;
    border: 1px solid #ff2840;
    white-space: nowrap;
    color: #ff2840;
    text-align: center;
    font-weight: 500;
    line-height: 20px;
  }
}
.tip {
  margin-top: 0.08rem;
  color: #55555e;
  font-size: 14px;
  line-height: 22px;
}
</style>
