<template>
  <article class="content">
    <!-- <div class="com_title">
      <h5>办理此业务需要满足以下条件</h5>
    </div> -->
    <ul class="com_infolist">
      <li>
        <span class="tit">您的投资者类型</span>
        <p v-if="profFlag === '0'">普通投资者</p>
        <p v-if="profFlag === '1'">专业投资者</p>
      </li>
    </ul>
    <div class="tip_txtbox">
      <p>投资小贴士</p>
      <p>
        申请转化成为专业投资者或者个人申请成为专业投资者，根据《证券期货投资者适当性管理办法》中第十一条规定:同时符合下列条件的自然人普通投资者可以申请转化成为专业投资者
      </p>
      <p>1、金融资产不低于300万元或者最近3年个人年均收入不低于30万元;</p>
      <p>
        2、具有1年以上证券、基金、期货、黄金、外汇等投资经验或者1年以上金融产品设计、投资、风险管理及相关工作经验。
      </p>
    </div>
  </article>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
import { whetherProfExtDate, judgeTodayOpenDate } from '@/service/service';

export default {
  name: 'ProfApply',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      profFlag: ''
    };
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
      display: false
    });
    this.renderingView();
  },
  methods: {
    renderingView() {
      const { inProperty } = this.tkFlowInfo();
      const profFlag = inProperty.profFlag;
      this.profFlag = profFlag;

      if (profFlag === '1') {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '返回首页',
          display: true,
          btnStatus: 2,
          data: () => {
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
        });
        // whetherProfExtDate().then((res) => {
        //   if (res.data.ifExtDate) {
        //     this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
        //       text: '专业投资者续认定',
        //       display: true,
        //       btnStatus: 2,
        //       data: () => {
        //         this.eventMessage(this, EVENT_NAME.NEXT_STEP);
        //       }
        //     });
        //   } else {
        //     this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
        //       text: '返回首页',
        //       display: true,
        //       btnStatus: 2,
        //       data: () => {
        //         this.eventMessage(this, EVENT_NAME.TO_INDEX);
        //       }
        //     });
        //   }
        // });
      } else {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '申请成为专业投资者',
          display: true,
          btnStatus: 2,
          data: () => {
            judgeTodayOpenDate().then((res) => {
              if (res.data.todayOpenFlag) {
                // 当天开户
                this.$TAlert({
                  title: '温馨提示',
                  tips: '对不起，当日新开账户无法提交专业投资者申请，请下个交易日再来',
                  confirmBtn: '确定',
                  confirm: () => {}
                });
              } else {
                this.eventMessage(this, EVENT_NAME.NEXT_STEP);
              }
            });
          }
        });
      }
    }
  }
};
</script>

<style></style>
