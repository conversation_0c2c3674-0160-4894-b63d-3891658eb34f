<template>
  <fragment>
    <div class="com_title">
      <h5>以下资金账户需要进行结息转账，请输入资金密码</h5>
    </div>
    <div v-for="(acc, index) in list" :key="index" class="xh_pwrod_item">
      <div class="tit">{{ acc.name }}：{{ acc.account }}</div>
      <div class="input_text">
        <input v-model="acc.password" class="t1" :type="acc.show ? 'text' : 'password'" placeholder="请输入6~8位资金数字密码"
          maxlength="8">
        <a :class="{ show: acc.show }" @click="acc.show = !acc.show" class="icon_eye"></a>
      </div>
    </div>
  </fragment>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
import { creditPwdCheck, recordOfAccountClosure } from '@/service/service'
import { getPwdEncryption } from '@/common/util';

export default {
  name: 'accountPwdCheck',
  inject: ['tkFlowInfo', 'clearKeepAlive', 'eventMessage'],
  components: {
  },
  data() {
    return {
      list: [],
      enumAccountType: {
        mainFundAccount: 'C', // 主资金账号
        assistFundAccount: 'CF0M0', // 辅资金账号
        creditFundAccount: 'CF7M1', // 信用资金账号
        optionFundAccount: 'CFBM1', // 衍生品资金账号
      }
    };
  },
  props: {
    accountNeedPwdList: {
      type: String,
      default: () => ''
    }
  },
  watch: {
    accountNeedPwdList: {
      handler(val) {
        if (val) {
          try {
            this.list = JSON.parse(val).map(t => {
              return { ...t, show: false, password: null, checked: false }
            });
          } catch (e) {
            console.error(e);
          }
        }
      },
      immediate: true
    },

    nextBtnSatus: {
      handler(nv) {
        if (nv) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 2,
            data: this.verifyPwd
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
        }
      },
      immediate: true
    }
  },
  computed: {
    nextBtnSatus() {
      if (this.list.length === 0) return false;
      return this.list.every(t => t.password?.length >= 6);
    }
  },
  created() {
  },
  methods: {
    getAssetProp(accountType) {
      if (accountType == this.enumAccountType.mainFundAccount) {
        return { assetProp: '0', mainFlag: '1' }
      } else if (accountType == this.enumAccountType.creditFundAccount) {
        return { assetProp: '7', mainFlag: '1' }
      } else if (accountType == this.enumAccountType.optionFundAccount) {
        return { assetProp: 'E', mainFlag: '1' }
      } else if (accountType == this.enumAccountType.mainFundAccount) {
        return { assetProp: '0', mainFlag: '0' }
      }
    },
    async verifyPwd() {
      try {
        let flag = true;
        for (let i = 0; i < this.list.length; i++) {
          // 资金账号
          const { account, accountType, password } = this.list[i];
          // assetProp 0-普通 7-信用 E-期权
          // mainFlag 0辅 1主
          const { assetProp, mainFlag } = this.getAssetProp(accountType);
          const clientPwdType = '1'; // 1-资金 2-交易
          const { code } = await creditPwdCheck({
            flowToken: sessionStorage.getItem('TKFlowToken'),
            account,
            password: 'encrypt:' + getPwdEncryption(password),
            assetProp,
            clientPwdType,
            mainFlag
          })
          if (code === 0) {
          } else {
            this.$TAlert({
              tips: err
            });
            flag = false;
          }
        }
        flag && this.submitPwd();
      } catch (err) {
        this.$TAlert({
          tips: err
        });
      }
    },

    submitPwd() {
      const preFlowInsId = this.$attrs.preFlowInsId;
      const flowToken = sessionStorage.getItem('TKFlowToken');
      let { branchNo, clientId } = this.$store.state.user?.userInfo;
      const { bizType } = this.tkFlowInfo().inProperty;
      recordOfAccountClosure({
        clientId,
        branchNo,
        bizType,
        flowToken,
        preFlowInsId,
        cancelAccountDataList: JSON.stringify(this.list.map((acc) => {
          return {...acc, fundPassword: 'encrypt:' + getPwdEncryption(acc.password), password: null}
        })),
      })
        .then(({ code, msg }) => {
          if (code === 0) {
            this.eventMessage(this, EVENT_NAME.NEXT_STEP)
          } else {
            Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    }
  },
};
</script>

<style scoped></style>
