<template>
  <fragment>
    <div class="com_title">
      <h5>请输入以下银行密码，以便为您进行存管银行解绑</h5>
    </div>
    <!-- <div v-for="(acc, index) in list" :key="index">
      <div class="xh_act_title">
        <p>{{ acc.name }}：{{ acc.account }}</p>
      </div>
      <div class="xh_pwrod_item">
        <div class="tit">{{ acc.bankName }}：{{ formatBankCardNoFn(acc.bankAccount) }}</div>
        <div class="input_text">
          <input v-model="acc.password" class="t1" :type="acc.show ? 'text' : 'password'" placeholder="请输入银行卡密码">
          <a :class="{ show: acc.show }" @click="acc.show = !acc.show" class="icon_eye"></a>
        </div>
      </div>
    </div> -->
    <div v-for="(acc, index) in list" :key="index" class="xh_pwrod_item">
      <div class="tit">{{ acc.name }}：{{ acc.account }}</div>
      <div class="tit">{{ acc.bankName }}：{{ formatBankCardNoFn(acc.bankAccount) }}</div>
      <div>
        <!-- <input v-model="acc.password" class="t1" :type="acc.show ? 'text' : 'password'" placeholder="请输入银行卡密码">
        <a :class="{ show: acc.show }" @click="acc.show = !acc.show" class="icon_eye"></a> -->
        <h-keypanel v-model="acc.password" ref="keyPanel" type="tel2" class="t1" :length="6" :mask="true"
          :is-head-icon="true" extra-parent-el=".hui-flexview" :placeholder="'请输入' + passwordType[acc.cancelPsdInd]"
          @on-show="showKeypanel">
          <div slot="head" class="safe-head">
            <img src="@/assets/images/logo.png" alt="" />
            <span>佣金宝安全输入</span>
          </div>
        </h-keypanel>
      </div>
    </div>
  </fragment>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
import { recordOfAccountClosure } from '@/service/service'
import { getPwdEncryption } from '@/common/util';

export default {
  name: 'bankPwdCheck',
  inject: ['tkFlowInfo', 'clearKeepAlive', 'eventMessage'],
  components: {
  },
  data() {
    return {
      passwordType: {
        0: '',
        1: '取款密码',
        2: '手机密码',
        3: '查询密码'
      },
      list: []
    };
  },
  props: {
    bankNeedPwdList: {
      type: String,
      default: () => ''
    }
  },
  watch: {
    bankNeedPwdList: {
      handler(val) {
        if (val) {
          try {
            this.list = JSON.parse(val).map(t => {
              return { ...t, show: false, password: null, checked: false }
            });
          } catch (e) {
            console.error(e);
          }
        }
      },
      immediate: true
    },

    nextBtnSatus: {
      handler(nv) {
        if (nv) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 2,
            data: this.submitPwd
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
        }
      },
      immediate: true
    }
  },
  computed: {
    nextBtnSatus() {
      if (this.list.length === 0) return false;
      return this.list.every(t => t.password?.length === 6);
    }
  },
  created() {
  },
  methods: {
    showKeypanel() {
    },

    submitPwd() {
      const preFlowInsId = this.$attrs.preFlowInsId;
      const flowToken = sessionStorage.getItem('TKFlowToken');
      let { branchNo, clientId } = this.$store.state.user?.userInfo;
      const { bizType } = this.tkFlowInfo().inProperty;
      recordOfAccountClosure({
        clientId,
        branchNo,
        bizType,
        flowToken,
        preFlowInsId,
        cancelAccountDataList: JSON.stringify(this.list.map((acc) => {
          return { ...acc, bankPassword: 'encrypt:' + getPwdEncryption(acc.password), password: null }
        })),
      })
        .then(({ code, msg }) => {
          if (code === 0) {
            this.eventMessage(this, EVENT_NAME.NEXT_STEP)
          } else {
            Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },

    formatBankCardNoFn(value) {
      console.log('value', value);
      if (value === '' || value === null || value === undefined) {
        console.log(1);
        return '';
      } else {
        console.log(2);
        let str =
          '**** **** ****' + value.substr(value.length - 4, value.length);
        return str;
      }
    }
  },
};
</script>

<style scoped>
.hui-keypanel {
  padding: 0 !important;
}
</style>
