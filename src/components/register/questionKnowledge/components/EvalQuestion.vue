<template>
  <section class="main fixed">
    <t-header title="知识测评"></t-header>
    <article
      class="content top_border"
      style="overflow-y: hidden; display: flex; flex-direction: column"
    >
      <div class="test_numbox" style="overflow: initial">
        <span
          v-for="(item, index) in questionList"
          :key="index"
          :class="{ off: icon(item).off, error: icon(item).error }"
          @click="jump(index)"
          >{{ index + 1 }}</span
        >
      </div>
      <div
        ref="questionList"
        class="test_list"
        style="
          overflow-y: scroll;
          margin: 0;
          height: 100%;
          position: relative;
          -webkit-overflow-scrolling: touch;
        "
      >
        <div
          v-for="(item, index) in questionList"
          :ref="`question${index}`"
          :key="index"
          class="test_list"
        >
          <div class="test_box">
            <h5 class="title">
              {{ index + 1 }}、{{ item[0].questionContent }}
              <span
                v-show="item[0].selectFailTip"
                class="ques_icon"
                @click="showIconTips(item[0].selectFailTip)"
              ></span>
            </h5>
            <div class="radio_list">
              <span
                v-for="(m, i) in item"
                :key="'m' + i"
                :class="{
                  icon_check: m.questionKind === QUESTION_KIND.MULTI,
                  icon_radio: m.questionKind !== QUESTION_KIND.MULTI,
                  checked: m.checked,
                  disabled: m.degreeCode && m.degreeCode !== ''
                }"
                @click="selectItem(m, i, index)"
                >{{ m.answerContent }}</span
              >
            </div>
          </div>
        </div>
      </div>
    </article>
    <footer class="footer" style="background: #ffffff">
      <div class="ce_btn">
        <a class="p_button" @click="toNext">提交</a>
      </div>
    </footer>
  </section>
</template>

<script>
import { questionSubmitV2, questionQryV3 } from '@/service/service';
import { QUESTION_KIND } from '@/common/enumeration';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {
    // 问卷编号
    subjectNo: {
      type: String,
      default: '0'
    },
    // 通过分数
    passScore: {
      type: String,
      default: ''
    },
    // 是否重置答案
    resetFlag: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      questionList: [],
      answerList: [],
      subjectVersion: '',
      QUESTION_KIND,
      reAnswer: false
    };
  },
  created() {
    this.getQuestionList();
  },
  methods: {
    getQuestionList() {
      let _this = this;
      let { subjectNo } = this;
      questionQryV3({
        subjectNo,
        flowToken: sessionStorage.getItem('TKFlowToken')
      })
        .then((data) => {
          this.subjectVersion = data.data.subjectVersion;
          const {
            limitList: [{ limitTimesSubmit, limitTimes } = {}]
          } = data.data;
          if (limitTimesSubmit >= limitTimes) {
            this.$TAlert({
              title: '温馨提示',
              tips: '你当日测评次数已达上限',
              confirm: this.toIndex
            });
          }
          let _resArr = data.data.ques;
          let _queId = '';
          let _queNum = -1;
          let i = 0;
          _resArr.forEach((item) => {
            if (item.questionNo !== _queId) {
              _queId = item.questionNo;
              _queNum++;
              i = 0;
              item.extName =
                item.questionKind === QUESTION_KIND.MULTI ? '（多选）' : '';
              _this.$set(_this.questionList, _queNum, []);
            }
            // 增加checked属性来判定是否选中当前选项
            if (item.isAlter) {
              item.checked = true;
              _this.$set(
                _this.answerList,
                _queNum,
                `${item.questionNo}_${item.answerNo}`
              );
            } else {
              item.checked = false;
            }
            _this.$set(_this.questionList[_queNum], i++, item);
          });
        })
        .catch((error) => {
          _hvueToast({
            mes: error
          });
        });
    },
    toIndex() {
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    },
    jump(index) {
      let quesIndex = `question${index}`;
      this.smoothScroll(quesIndex, 400);
      // this.$refs['questionList'].scrollTop = this.$refs[quesIndex][0].offsetTop;
    },
    // 实现滑动效果
    smoothScroll(target, duration) {
      const start = this.$refs['questionList'].scrollTop;
      const end = this.$refs[target][0].offsetTop;
      const change = end - start;
      const startTime = performance.now();

      const animateScroll = (currentTime) => {
        const timeElapsed = currentTime - startTime;
        const progress = Math.min(timeElapsed / duration, 1);
        const ease = this.easeInOutQuad(progress);

        this.$refs['questionList'].scrollTop = start + change * ease;

        if (timeElapsed < duration) {
          if (window.requestAnimationFrame) {
            requestAnimationFrame(animateScroll);
          } else {
            setTimeout(animateScroll, 1000 / 60); //60帧
          }
        }
      };
      if (window.requestAnimationFrame) {
        requestAnimationFrame(animateScroll);
      } else {
        setTimeout(animateScroll, 1000 / 60); //60帧
      }
    },
    easeInOutQuad(t) {
      return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
    },
    verifyAnswer() {
      let arr = [];
      for (let quesIndex of this.questionList.keys()) {
        if (!this.answerList[quesIndex]) {
          arr.push(quesIndex + 1);
        }
      }
      if (arr.length > 0) {
        let msg = `第${arr.join(',')}题尚未选择，请确认后重新提交`;
        _hvueToast({
          mes: msg
        });
        return false;
      } else {
        return true;
      }
    },
    getErrorQuesList() {
      return this.questionList.reduce((a, list, i) => {
        const score = list.reduce((b, { checked, questionScore }) => {
          if (checked) {
            b = b + Number(questionScore);
          }
          return b;
        }, 0);
        if (score === 0) {
          a.push(`第${i + 1}题`);
        }
        return a;
      }, []);
    },
    toNext() {
      if (this.reAnswer === true) {
        const errorQuesList = this.getErrorQuesList();
        if (errorQuesList.length > 0) {
          this.$TAlert({
            title: '温馨提示',
            tips: `您的${errorQuesList.join('，')}还未更正`
          });
          return;
        }
      }
      this.submitQuestion();
    },
    async submitQuestion() {
      const _this = this;
      //校验答案是否选择完成
      if (!_this.verifyAnswer()) return;
      const flowToken = sessionStorage.getItem('TKFlowToken');
      let { subjectNo } = this;
      questionSubmitV2({
        flowToken,
        subjectNo,
        subjectVersion: this.subjectVersion,
        paperAnswer: this.answerList.join('|') // 试卷答案对象数据
      })
        .then(({ data }) => {
          if (data.limitFlag) {
            this.$TAlert({
              title: '已超重新测评次数',
              tips: '今日已经超过重新测评次数，需要重测请您明天再来办理'
            });
          } else {
            this.reAnswer = true;
            if (
              this.resetFlag ||
              parseInt(data.paperScore) >= parseInt(this.passScore)
            ) {
              _this.$emit('finish', { data });
            } else {
              const paperScore = data.paperScore;
              const [{ limitTimesSubmit, limitTimes } = {}] = data.limitList;
              const errorQuesList = this.getErrorQuesList();
              console.log('getErrorQuesList callback' + errorQuesList);
              this.$TAlert({
                title: '测评不通过',
                tips: `客户评测结果${paperScore}分，错误题号包括有${errorQuesList.join(
                  '，'
                )}，剩余测评次数${limitTimes - limitTimesSubmit}次数`
              });
            }
          }
        })
        .catch((err) => {
          if (err) {
            _hvueToast({
              mes: err
            });
          }
        });
    },
    selectItem(item, aIndex, quesIndex) {
      const _this = this;
      let ansAtr = [];
      let quesNo = '';
      if (this.questionList[quesIndex].find((item) => item.degreeCode)) {
        return;
      }
      _this.questionList[quesIndex].forEach((a) => {
        if (a.questionKind === QUESTION_KIND.MULTI) {
          if (item.answerNo === a.answerNo) {
            a.checked = !a.checked;
          }
          if (a.checked) {
            quesNo = a.questionNo;
            ansAtr.push(a.answerNo);
          }
        } else {
          if (a.answerNo === item.answerNo) {
            a.checked = true;
            quesNo = a.questionNo;
            ansAtr.push(a.answerNo);
          } else {
            a.checked = false;
          }
        }
      });
      // 选择后更改答案列表
      let a = '';
      if (ansAtr.length > 0) {
        a = `${quesNo}_${ansAtr.join('&')}`;
      }
      this.$set(this.answerList, quesIndex, a);
      if (
        this.questionList.every((question) => question.some((a) => a.checked))
      ) {
      } else if (
        this.questionList[quesIndex][0].questionKind != QUESTION_KIND.MULTI &&
        quesIndex < this.questionList.length - 1
      ) {
        setTimeout(() => {
          this.jump(quesIndex + 1);
        }, 300);
      }
    },
    showIconTips(tipsHtml) {
      this.$TAlert({
        tipsHtml,
        confirmBtn: '确定'
      });
    },
    icon(it) {
      return {
        off: it.some(({ checked }) => checked),
        error: it.some(
          ({ checked, questionScore }) =>
            this.reAnswer && !this.resetFlag && checked && questionScore === '0'
        )
      };
    }
  }
};
</script>

<style scoped lang="less">
h5.title {
  .ques_icon {
    display: inline-block;
    width: 0.16rem;
    height: 0.16rem;
    background-image: url('../../../../assets/images/ques_icon.png');
    background-size: contain;
  }
}
</style>
