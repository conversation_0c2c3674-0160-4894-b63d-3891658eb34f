<template>
  <section class="main fixed white_bg" style="position: fixed">
    <header class="header">
      <div class="header_inner">
        <h1 class="title">知识测评</h1>
      </div>
    </header>
    <article class="content">
      <div class="zs_test_result">
        <div class="zs_test_level">
          <div class="txt">
            <h5
              :class="{
                error: parseInt(resData.paperScore) < parseInt(passScore)
              }"
            >
              <strong id="testData">{{ resData.paperScore }}</strong
              ><span>分</span>
            </h5>
            <p>测评得分</p>
          </div>
          <div class="zs_test_canvas">
            <canvas id="testCanvas" width="360" height="360"></canvas>
          </div>
        </div>
        <h3 v-if="parseInt(resData.paperScore) >= parseInt(passScore)">
          恭喜您测评通过！
        </h3>
        <p v-if="parseInt(resData.paperScore) >= parseInt(passScore)">
          客户评测结果{{
            resData.paperScore
          }}分，评测等级为准入，测评结果一经提交，不可更改，请确认填写无误后提交。
        </p>
        <h3 v-if="parseInt(resData.paperScore) < parseInt(passScore)">
          很遗憾测评未通过
        </h3>
        <p
          v-if="
            parseInt(resData.paperScore) < parseInt(passScore) &&
            resData.limitTimes !== 0
          "
        >
          每日可重新测评<span class="com_span">3</span>次，当前剩余<span
            class="com_span"
            >0</span
          >次
        </p>
      </div>
    </article>
    <footer class="footer" style="background: #ffffff">
      <div
        v-if="parseInt(resData.paperScore) < parseInt(passScore)"
        class="ce_btn block"
      >
        <a class="p_button" @click="riskAfresh">重新评测</a>
        <a class="p_button border" @click="toIndex">返回首页</a>
      </div>
      <div
        v-if="parseInt(resData.paperScore) >= parseInt(passScore)"
        class="ce_btn"
      >
        <a class="p_button" @click="nextFlow">下一步</a>
      </div>
    </footer>
  </section>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
export default {
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {
    passScore: {
      // 通过分数
      type: String,
      default: ''
    },
    resData: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    resData: {
      handler({ paperScore }) {
        if (paperScore !== '') {
          this.$nextTick(() => {
            this.initCanvans();
          });
        }
      },
      immediate: true,
      deep: true
    }
  },
  data() {
    return {};
  },
  methods: {
    initCanvans() {
      var canvas1 = document.getElementById('testCanvas');
      var ctx1 = canvas1.getContext('2d');
      var W1 = canvas1.width;
      var H1 = canvas1.height;
      var deg1 = 0,
        new_deg1 = 0,
        dif1 = 0;
      var loop1;
      var t_data1 = document.getElementById('testData').innerHTML;
      var deColor1 = '#f0f0f0',
        dotColor1,
        dotColorOk = '#FF2840',
        dotColorError = '#FF7015';
      if (parseInt(t_data1) < parseInt(this.passScore)) {
        dotColor1 = dotColorError;
      } else {
        dotColor1 = dotColorOk;
      }

      function init1() {
        ctx1.clearRect(0, 0, W1, H1);
        ctx1.beginPath();
        ctx1.strokeStyle = deColor1;
        ctx1.lineWidth = 32;
        ctx1.arc(
          W1 / 2,
          H1 / 2 + 30,
          130,
          (Math.PI * 5) / 6,
          (Math.PI * 39) / 18,
          false
        );
        ctx1.stroke();

        var r2 = (2.4 * 1 * Math.PI) / 180 + (Math.PI * 1) / 3;
        ctx1.beginPath();
        ctx1.fillStyle = dotColor1;
        ctx1.arc(
          W1 / 2 - 130 * Math.sin(r2),
          H1 / 2 + 30 + 130 * Math.cos(r2),
          16,
          -180,
          true
        );
        ctx1.fill();

        var r3 = (2.4 * 100 * Math.PI) / 180 + (Math.PI * 1) / 3;
        ctx1.beginPath();
        ctx1.fillStyle = deColor1;
        ctx1.arc(
          W1 / 2 - 130 * Math.sin(r3),
          H1 / 2 + 30 + 130 * Math.cos(r3),
          16,
          -180,
          true
        );
        ctx1.fill();

        var r4 = (2.4 * deg1 * Math.PI) / 180 + (Math.PI * 1) / 3;
        ctx1.beginPath();
        ctx1.fillStyle = dotColor1;
        ctx1.arc(
          W1 / 2 - 130 * Math.sin(r4),
          H1 / 2 + 30 + 130 * Math.cos(r4),
          16,
          -180,
          true
        );
        ctx1.fill();

        var r1 = (2.4 * deg1 * Math.PI) / 180;
        ctx1.beginPath();
        ctx1.strokeStyle = dotColor1;
        ctx1.lineWidth = 32;
        ctx1.arc(
          W1 / 2,
          H1 / 2 + 30,
          130,
          (Math.PI * 5) / 6,
          r1 + (Math.PI * 5) / 6,
          false
        );
        ctx1.stroke();

        var r5 = (2.4 * deg1 * Math.PI) / 180 + (Math.PI * 1) / 3;
        ctx1.beginPath();
        ctx1.fillStyle = '#fff';
        ctx1.arc(
          W1 / 2 - 130 * Math.sin(r5),
          H1 / 2 + 30 + 130 * Math.cos(r5),
          7,
          -180,
          true
        );
        ctx1.fill();
      }

      function draw1() {
        new_deg1 = t_data1;
        dif1 = new_deg1 - deg1;
        loop1 = setInterval(to1, 500 / dif1);
      }
      function to1() {
        if (deg1 == new_deg1) {
          clearInterval(loop1);
        }
        if (deg1 < new_deg1) {
          deg1++;
        }
        init1();
      }
      draw1();
    },
    toIndex() {
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    },
    riskAfresh() {
      this.$emit('reset');
    },
    nextFlow() {
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        paperAnswer: this.resData.riskQuestion,
        paperScore: this.resData.paperScore,
        paperSno: this.resData.paperSno,
        paperVersion: this.subjectVersion,
        paperNo: this.$attrs.subjectNo
      });
    }
  }
};
</script>
