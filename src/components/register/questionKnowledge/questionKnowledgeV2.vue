<template>
  <div>
    <section v-show="pageStep === 0" class="main fixed" style="position: fixed">
      <t-header title="知识测评" @back="back"></t-header>
      <article
        class="content top_border"
        style="overflow-y: hidden; display: flex; flex-direction: column"
      >
        <div class="test_numbox" style="overflow: initial">
          <span
            v-for="(item, index) in questionList"
            :key="index"
            :class="{ off: item.some((a) => a.checked) }"
            @click="toJump(index)"
            >{{ index + 1 }}</span
          >
        </div>
        <div
          ref="questionList"
          class="test_list"
          style="
            overflow-y: scroll;
            margin: 0;
            height: 100%;
            position: relative;
            -webkit-overflow-scrolling: touch;
          "
        >
          <div
            v-for="(item, index) in questionList"
            :ref="`question${index}`"
            :key="index"
            class="test_list"
          >
            <div class="test_box">
              <h5 class="title">
                {{ index + 1 }}、{{ item[0].questionContent }}
                <span
                  v-show="item[0].selectFailTip"
                  class="ques_icon"
                  @click="showIconTips(item[0].selectFailTip)"
                ></span>
              </h5>
              <div class="radio_list">
                <span
                  v-for="(m, i) in item"
                  :key="'m' + i"
                  :class="{
                    icon_check: m.questionKind === QUESTION_KIND.MULTI,
                    icon_radio: m.questionKind !== QUESTION_KIND.MULTI,
                    checked: m.checked,
                    disabled: m.degreeCode && m.degreeCode !== ''
                  }"
                  @click="selectItem(m, i, index)"
                  >{{ m.answerContent }}</span
                >
              </div>
            </div>
          </div>
        </div>
      </article>
      <footer class="footer" style="background: #ffffff">
        <div class="ce_btn">
          <!-- <a class="p_button border" @click="riskAfresh">重新评测</a> -->
          <a class="p_button" @click="toNext">提交</a>
        </div>
      </footer>
    </section>

    <section
      v-show="pageStep === 1"
      class="main fixed white_bg"
      style="position: fixed"
    >
      <header class="header">
        <div class="header_inner">
          <a class="icon_back" href="javascript:void(0);"></a>
          <h1 class="title">知识测评</h1>
        </div>
      </header>
      <article class="content">
        <div class="zs_test_result">
          <div class="zs_test_level">
            <div class="txt">
              <h5
                :class="{
                  error: parseInt(testResult.paperScore) < parseInt(passScore)
                }"
              >
                <strong id="testData">{{ testResult.paperScore }}</strong
                ><span>分</span>
              </h5>
              <p>测评得分</p>
            </div>
            <div class="zs_test_canvas">
              <canvas id="testCanvas" width="360" height="360"></canvas>
            </div>
          </div>
          <h3 v-if="parseInt(testResult.paperScore) >= parseInt(passScore)">
            恭喜您测评通过！
          </h3>
          <p v-if="parseInt(testResult.paperScore) >= parseInt(passScore)">
            客户评测结果{{
              testResult.paperScore
            }}分，评测等级为准入，测评结果一经提交，不可更改，请确认填写无误后提交。
          </p>
          <h3 v-if="parseInt(testResult.paperScore) < parseInt(passScore)">
            很遗憾测评未通过
          </h3>
          <p
            v-if="
              parseInt(testResult.paperScore) < parseInt(passScore) &&
              testResult.limitTimes !== 0
            "
          >
            每日可重新测评<span class="com_span">3</span>次，当前剩余<span
              class="com_span"
              >0</span
            >次
          </p>
        </div>
      </article>
      <footer class="footer" style="background: #ffffff">
        <div
          v-if="parseInt(testResult.paperScore) < parseInt(passScore)"
          class="ce_btn block"
        >
          <a class="p_button" @click="riskAfresh">重新评测</a>
          <a class="p_button border" @click="toIndex">返回首页</a>
        </div>
        <div
          v-if="parseInt(testResult.paperScore) >= parseInt(passScore)"
          class="ce_btn"
        >
          <a class="p_button" @click="nextFlow">下一步</a>
        </div>
      </footer>
    </section>
  </div>
</template>

<script>
import {
  questionQryV3,
  questionSubmitV2
  // getQuestionNumber,
} from '@/service/service';
import { QUESTION_KIND } from '@/common/enumeration';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'QuestionKnowledgeV2',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {},
  props: {
    paperType: {
      type: String,
      default: '1'
    },
    isObtainLoc: {
      // 0 柜台 非0 本地
      type: String,
      default: '0'
    },
    passScore: {
      // 通过分数
      type: String,
      default: ''
    }
  },
  data() {
    return {
      QUESTION_KIND,
      testResult: {},
      questionList: [],
      answerList: [],
      subjectNo: '',
      subjectVersion: '',
      riskQuestion: '',
      pageStep: 0
    };
  },
  computed: {
    totalQuestionLength() {
      return this.questionList.length;
    }
  },
  async mounted() {
    const { outProperty } = this.tkFlowInfo();
    if (outProperty.paperAnswer) {
      this.pageStep = 1;
      this.testResult.paperScore = outProperty.paperScore;
    }
    if (this.questionList.length === 0) {
      this.getQuestionList();
    }
    this.$nextTick(() => {
      this.initCanvans();
    });
  },
  methods: {
    initCanvans() {
      var canvas1 = document.getElementById('testCanvas');
      var ctx1 = canvas1.getContext('2d');
      var W1 = canvas1.width;
      var H1 = canvas1.height;
      var deg1 = 0,
        new_deg1 = 0,
        dif1 = 0;
      var loop1;
      var t_data1 = document.getElementById('testData').innerHTML;
      var deColor1 = '#f0f0f0',
        dotColor1,
        dotColorOk = '#FF2840',
        dotColorError = '#FF7015';
      if (parseInt(t_data1) < parseInt(this.passScore)) {
        dotColor1 = dotColorError;
      } else {
        dotColor1 = dotColorOk;
      }

      function init1() {
        ctx1.clearRect(0, 0, W1, H1);
        ctx1.beginPath();
        ctx1.strokeStyle = deColor1;
        ctx1.lineWidth = 32;
        ctx1.arc(
          W1 / 2,
          H1 / 2 + 30,
          130,
          (Math.PI * 5) / 6,
          (Math.PI * 39) / 18,
          false
        );
        ctx1.stroke();

        var r2 = (2.4 * 1 * Math.PI) / 180 + (Math.PI * 1) / 3;
        ctx1.beginPath();
        ctx1.fillStyle = dotColor1;
        ctx1.arc(
          W1 / 2 - 130 * Math.sin(r2),
          H1 / 2 + 30 + 130 * Math.cos(r2),
          16,
          -180,
          true
        );
        ctx1.fill();

        var r3 = (2.4 * 100 * Math.PI) / 180 + (Math.PI * 1) / 3;
        ctx1.beginPath();
        ctx1.fillStyle = deColor1;
        ctx1.arc(
          W1 / 2 - 130 * Math.sin(r3),
          H1 / 2 + 30 + 130 * Math.cos(r3),
          16,
          -180,
          true
        );
        ctx1.fill();

        var r4 = (2.4 * deg1 * Math.PI) / 180 + (Math.PI * 1) / 3;
        ctx1.beginPath();
        ctx1.fillStyle = dotColor1;
        ctx1.arc(
          W1 / 2 - 130 * Math.sin(r4),
          H1 / 2 + 30 + 130 * Math.cos(r4),
          16,
          -180,
          true
        );
        ctx1.fill();

        var r1 = (2.4 * deg1 * Math.PI) / 180;
        ctx1.beginPath();
        ctx1.strokeStyle = dotColor1;
        ctx1.lineWidth = 32;
        ctx1.arc(
          W1 / 2,
          H1 / 2 + 30,
          130,
          (Math.PI * 5) / 6,
          r1 + (Math.PI * 5) / 6,
          false
        );
        ctx1.stroke();

        var r5 = (2.4 * deg1 * Math.PI) / 180 + (Math.PI * 1) / 3;
        ctx1.beginPath();
        ctx1.fillStyle = '#fff';
        ctx1.arc(
          W1 / 2 - 130 * Math.sin(r5),
          H1 / 2 + 30 + 130 * Math.cos(r5),
          7,
          -180,
          true
        );
        ctx1.fill();
      }

      function draw1() {
        new_deg1 = t_data1;
        dif1 = new_deg1 - deg1;
        loop1 = setInterval(to1, 500 / dif1);
      }
      function to1() {
        if (deg1 == new_deg1) {
          clearInterval(loop1);
        }
        if (deg1 < new_deg1) {
          deg1++;
        }
        init1();
      }
      draw1();
    },

    toIndex() {
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    },

    reset() {
      this.pageStep = 0;
      this.answerList = [];
      this.questionList.forEach((item, index) => {
        item.forEach((it) => {
          if (it.degreeCode !== '' && it.isAlter) {
            this.answerList[index] = `${it.questionNo}_${it.answerNo}`;
            return;
          } else {
            it.checked = false;
          }
        });
      });
      this.$nextTick(() => {
        this.jump(0);
        this.testResult = this.$options.data().testResult;
      });
    },

    toSubmit() {},

    // toIndex() {
    //   this.eventMessage(this, 'toIndex');
    // },

    toJump(index) {
      let quesIndex = `question${index}`;
      this.$refs['questionList'].scrollTop = this.$refs[quesIndex][0].offsetTop;
    },

    jump(index) {
      let quesIndex = `question${index}`;
      this.$refs['questionList'].scrollTop = this.$refs[quesIndex][0].offsetTop;
    },

    verifyAnswer() {
      let arr = [];
      for (let quesIndex of this.questionList.keys()) {
        if (!this.answerList[quesIndex]) {
          arr.push(quesIndex + 1);
        }
      }
      if (arr.length > 0) {
        let msg = `第${arr.join(',')}题尚未选择，请确认后重新提交`;
        _hvueToast({
          mes: msg
        });
        return false;
      } else {
        return true;
      }
    },

    getQuestionList() {
      let _this = this;
      let { subjectNo } = this.$attrs;
      questionQryV3({
        subjectNo,
        flowToken: sessionStorage.getItem('TKFlowToken')
      })
        .then((data) => {
          this.subjectVersion = data.data.subjectVersion;
          let _resArr = data.data.ques;
          let _queId = '';
          let _queNum = -1;
          let i = 0;
          _resArr.forEach((item) => {
            if (item.questionNo !== _queId) {
              _queId = item.questionNo;
              _queNum++;
              i = 0;
              item.extName =
                item.questionKind === QUESTION_KIND.MULTI ? '（多选）' : '';
              _this.$set(_this.questionList, _queNum, []);
            }
            // 增加checked属性来判定是否选中当前选项
            if (item.isAlter) {
              item.checked = true;
              _this.$set(
                _this.answerList,
                _queNum,
                `${item.questionNo}_${item.answerNo}`
              );
            } else {
              item.checked = false;
            }
            _this.$set(_this.questionList[_queNum], i++, item);
          });
        })
        .catch((error) => {
          _hvueToast({
            mes: error
          });
        });
    },

    selectItem(item, aIndex, quesIndex) {
      const _this = this;
      let ansAtr = [];
      let quesNo = '';
      if (this.questionList[quesIndex].find((item) => item.degreeCode)) {
        return;
      }
      _this.questionList[quesIndex].forEach((a) => {
        if (a.questionKind === QUESTION_KIND.MULTI) {
          if (item.answerNo === a.answerNo) {
            a.checked = !a.checked;
          }
          if (a.checked) {
            quesNo = a.questionNo;
            ansAtr.push(a.answerNo);
          }
        } else {
          if (a.answerNo === item.answerNo) {
            a.checked = true;
            quesNo = a.questionNo;
            ansAtr.push(a.answerNo);
          } else {
            a.checked = false;
          }
        }
      });
      // 选择后更改答案列表
      let a = '';
      if (ansAtr.length > 0) {
        a = `${quesNo}_${ansAtr.join('&')}`;
      }
      this.$set(this.answerList, quesIndex, a);
      if (
        this.questionList.every((question) => question.some((a) => a.checked))
      ) {
        // this.submitQuestion();
      } else if (
        this.questionList[quesIndex][0].questionKind != QUESTION_KIND.MULTI &&
        quesIndex < this.questionList.length - 1
      ) {
        setTimeout(() => {
          this.jump(quesIndex + 1);
        }, 300);
      }
    },

    async submitQuestion() {
      const _this = this;
      //校验答案是否选择完成
      if (!_this.verifyAnswer()) return;
      const flowToken = sessionStorage.getItem('TKFlowToken');
      let { subjectNo } = this.$attrs;
      questionSubmitV2({
        flowToken,
        subjectNo: subjectNo,
        subjectVersion: this.subjectVersion,
        paperAnswer: this.answerList.join('|') // 试卷答案对象数据
      })
        .then((data) => {
          Object.assign(_this.testResult, data.data);
          if (data.data.limitFlag) {
            // limitFlag为true
            this.$TAlert({
              title: '已超重新测评次数',
              tips: '今日已经超过重新测评次数，需要重测请您明天再来办理'
            });
          } else {
            this.pageStep = 1;
            this.$nextTick(() => {
              this.initCanvans();
            });
          }
        })
        .catch((err) => {
          if (err) {
            _hvueToast({
              mes: err
            });
          }
        });
    },

    riskAfresh() {
      // 重新测评
      this.answerList = [];
      this.questionList.forEach((item, index) => {
        item.forEach((it) => {
          if (it.degreeCode !== '' && it.isAlter) {
            this.answerList[index] = `${it.questionNo}_${it.answerNo}`;
            return;
          } else {
            it.checked = false;
          }
        });
      });
      this.pageStep = 0;
      this.$nextTick(() => {
        this.jump(0);
        this.testResult = this.$options.data().testResult;
      });
    },

    back() {},

    toNext() {
      this.submitQuestion();
    },

    nextFlow() {
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        paperAnswer: this.testResult.riskQuestion,
        paperScore: this.testResult.paperScore,
        paperSno: this.testResult.paperSno,
        paperVersion: this.subjectVersion,
        paperNo: this.$attrs.subjectNo
      });
    },

    showIconTips(tipsHtml) {
      this.$TAlert({
        tipsHtml,
        confirmBtn: '确定'
      });
    }
  }
};
</script>
<style scoped lang="less">
h5.title {
  .ques_icon {
    display: inline-block;
    width: 0.16rem;
    height: 0.16rem;
    background-image: url('../../../assets/images/ques_icon.png');
    background-size: contain;
  }
}
</style>
