<template>
  <fragment>
    <question
      v-show="pageStep === 0"
      v-bind="$attrs"
      @finish="quesFinish"
    />
    <result
      v-show="pageStep !== 0"
      v-bind="$attrs"
      :resData="testResult"
      @reset="resCallback"
    />
  </fragment>
</template>

<script>
import * as Components from './components/index';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'QuestionKnowledgeV3',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: { ...Components },
  data() {
    return {
      pageStep: 0, // 是否做过一次测评
      testResult: {
        limitFlag: false,
        limitTimes: 0,
        paperScore: '',
        paperSno: '',
        riskQuestion: ''
      }
    };
  },
  created() {
    this.eventMessage(this, EVENT_NAME.DISPLAY_HEADER, { display: false });
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
  },
  methods: {
    quesFinish({ data }) {
      this.testResult = data;
      this.pageStep = 1;
    },
    resCallback() {
      this.pageStep = 0;
      this.testResult = this.$options.data().testResult;
    }
  }
};
</script>
