<template>
  <section class="main fixed" style="position: fixed">
    <t-header></t-header>
    <article class="content">
      <div class="wx_tips">
        <p>{{ startTips }}</p>
      </div>
      <div
        class="visit_main"
        v-for="(item, index) in questionList"
        :ref="`question${index}`"
        :key="index"
      >
        <div class="visit_box">
          <h5>
            {{ index + 1 }}、{{ replaceQuesContent(item[0].questionContent) }}
          </h5>
          <ul>
            <li
              :class="{
                checked: m.checked,
                disabled: m.degreeCode && m.degreeCode !== ''
              }"
              @click="selectItem(m, i, index)"
              v-for="(m, i) in item"
              :key="'m' + i"
            >
              <span>{{ m.answerContent }}</span>
            </li>
          </ul>
        </div>
      </div>
      <div class="wx_tips">
        <p>{{ endTips }}</p>
      </div>
    </article>

    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" :class="{ disabled: verifyAnswer }" @click="submit"
          >提交</a
        >
      </div>
    </footer>
  </section>
</template>

<script>
import { questionQryV3, questionSubmitV2 } from '@/service/service';
import { QUESTION_KIND } from '@/common/enumeration';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'QuestionVisit',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      QUESTION_KIND,
      questionList: [],
      answerList: [],
      startTips: '',
      endTips: '',
      subjectVersion: ''
    };
  },
  props: {
    subjectNo: {
      // 问卷编号
      type: String,
      default: ''
    },
    clientName: {
      // 客户姓名
      type: String,
      default: ''
    },
    clientGender: {
      // 客户性别
      type: String,
      default: ''
    }
  },
  computed: {
    verifyAnswer() {
      let arr = [];
      for (let quesIndex of this.questionList.keys()) {
        if (!this.answerList[quesIndex]) {
          arr.push(quesIndex + 1);
        }
      }
      return arr.length !== 0;
    }
  },
  mounted() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    this.renderingView();
  },
  methods: {
    async renderingView() {
      let _this = this;
      questionQryV3({
        subjectNo: this.subjectNo,
        flowToken: sessionStorage.getItem('TKFlowToken')
      }).then((data) => {
        this.subjectVersion = data.data.subjectVersion;
        this.startTips = data.data.startTips;
        this.endTips = data.data.endTips;
        let _resArr = data.data.ques;
        let _queId = '';
        let _queNum = -1;
        let i = 0;
        _resArr.forEach((item) => {
          if (item.questionNo !== _queId) {
            _queId = item.questionNo;
            _queNum++;
            i = 0;
            item.extName =
              item.questionKind === QUESTION_KIND.MULTI ? '（多选）' : '';
            _this.$set(_this.questionList, _queNum, []);
          }
          // 增加checked属性来判定是否选中当前选项
          if (item.isAlter) {
            item.checked = true;
            _this.$set(
              _this.answerList,
              _queNum,
              `${item.questionNo}_${item.answerNo}`
            );
          } else {
            item.checked = false;
          }
          _this.$set(_this.questionList[_queNum], i++, item);
        });
      });
    },

    replaceQuesContent(content) {
      return content
        .replace('${clientName}', this.clientName)
        .replace(
          '${clientGender}',
          this.clientGender === '0' ? '先生' : '女士'
        );
    },

    selectItem(item, aIndex, quesIndex) {
      const _this = this;
      let ansAtr = [];
      let quesNo = '';
      const { isTrue } = item;

      _this.questionList[quesIndex].forEach((a) => {
        if (a.questionKind === QUESTION_KIND.MULTI) {
          if (item.answerNo === a.answerNo && isTrue !== '0') {
            a.checked = !a.checked;
          }
          if (a.checked) {
            quesNo = a.questionNo;
            ansAtr.push(a.answerNo);
          }
        } else {
          if (a.answerNo === item.answerNo && isTrue !== '0') {
            a.checked = true;
            quesNo = a.questionNo;
            ansAtr.push(a.answerNo);
          } else {
            a.checked = false;
          }
        }
      });

      if (isTrue === '0') {
        this.$set(this.answerList, quesIndex, '');
        return this.showFailTips(item.selectFailTip);
      }

      // 选择后更改答案列表
      let a = '';
      if (ansAtr.length > 0) {
        a = `${quesNo}_${ansAtr.join('&')}`;
      }
      this.$set(this.answerList, quesIndex, a);
    },

    showFailTips(tipsHtml) {
      this.$TAlert({
        title: '温馨提示',
        tipsHtml
      });
    },

    submit() {
      if (this.verifyAnswer) return;
      const flowToken = sessionStorage.getItem('TKFlowToken');
      let { subjectNo } = this;
      questionSubmitV2({
        flowToken,
        subjectNo,
        subjectVersion: this.subjectVersion,
        paperAnswer: this.answerList.join('|') // 试卷答案对象数据
      })
        .then(({ data }) => {
          const { riskQuestion, paperSno } = data;
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
            revisitPaperAnswer: riskQuestion,
            revisitPaperNo: subjectNo
          });
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    }
  }
};
</script>
