<template>
  <fragment>
    <van-field
      v-show="showDetail"
      v-model="value"
      readonly
      rows="1"
      type="textarea"
      :label="label"
    />
    <van-field
      v-model="fieldValue"
      is-link
      required
      label="选择地区"
      :placeholder="$attrs.placeholeder"
      :error-message="errorMessages[0] && errorMessages[0].area"
      @click="show = true"
      @focus="iptFocus"
    />
    <van-field
      v-model="detailValue"
      label="详细地址"
      rows="1"
      required
      type="textarea"
      autosize
      :error-message="errorMessages[0] && errorMessages[0].address"
      placeholder="请输入详细地址"
    />
    <div class="input_form form_tit_right">
      <div class="imp_c_tips">
        <p>
          <span class="imp"
            >详细地址需详细到 单元号、门牌号、楼层和房间号部门科室</span
          >
        </p>
      </div>
    </div>
    <van-popup v-model="show" round position="bottom">
      <van-cascader
        v-model="cascaderValue"
        :title="$attrs.placeholeder"
        :options="options"
        @close="show = false"
        @finish="onFinish"
      />
    </van-popup>
  </fragment>
</template>

<script>
import { getAdressTree } from '@/service/commonService';
import { addressParse } from '@/service/service';
export default {
  name: 'RegionSelector',
  inject: ['setPropsByForm'],
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    label: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: ''
    },
    showDetail: {
      type: Boolean,
      default: false
    },
    errorMessages: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      show: false,
      isInit: false,
      detailValue: '',
      cascaderValue: '',
      options: [],
      fieldValue: '',
      fieldArray: []
    };
  },
  watch: {
    fieldValue(newVal) {
      this.setPropsByForm(this.$attrs.propKey, 'fieldValue', newVal);
      let val1 = [...new Set(this.fieldArray)].join('');
      this.$emit('change', val1 + this.detailValue);
    },
    detailValue(newVal) {
      this.setPropsByForm(this.$attrs.propKey, 'detailValue', newVal);
      let val1 = [...new Set(this.fieldArray)].join('');
      this.$emit('change', val1 + newVal);
    },
    value: {
      handler: function (newVal) {
        if (newVal && !this.isInit) {
          addressParse({
            address: newVal
          }).then((res) => {
            this.fieldArray = [
              res.data.proRegionName,
              res.data.cityRegionName,
              res.data.areaRegionName
            ];
            this.fieldValue = `${res.data.proRegionName}${res.data.cityRegionName}${res.data.areaRegionName}`;
            this.detailValue = res.data.regionDetail;
            this.isInit = true;
          });
        }
      },
      immediate: false
    }
  },
  mounted() {
    getAdressTree()
      .then((res) => {
        this.options = res.data.map((item) => {
          return {
            text: item.provinceName,
            value: item.provinceCode,
            children: item.sysCityVOs
              ? item.sysCityVOs.map((it) => {
                  return {
                    text: it.cityName,
                    value: it.cityCode,
                    children: it.sysXzqyVOs
                      ? it.sysXzqyVOs.map((t) => {
                          return {
                            text: t.xzqyName,
                            value: t.xzqyCode
                          };
                        })
                      : []
                  };
                })
              : []
          };
        });
      })
      .catch((err) => {
        console.log(err);
      });
  },
  methods: {
    iptFocus() {
      // 禁止弹出手机键盘
      document.activeElement.blur();
    },

    onFinish({ selectedOptions }) {
      this.show = false;
      this.fieldValue = selectedOptions.map((option) => option.text).join('');
      this.fieldArray = selectedOptions.map((option) => option.text);
    }
  }
};
</script>

<style lang="less" scope>
.component {
  width: 100%;
  .van-cell {
    min-height: 0.24rem;
    line-height: 0.24rem;
    padding: 0;
  }
  .van-cell::after {
    display: none;
  }
}
</style>
