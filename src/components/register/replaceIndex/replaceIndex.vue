<template>
  <div></div>
</template>

<script>
import { dfFlowSave } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'ReplaceIndex',
  inject: ['eventMessage'],
  mounted() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    const { clientId } = this.$store.state.user.userInfo;
    dfFlowSave({
      clientId,
      // flowToken: sessionStorage.getItem('TKFlowToken'),
      dataCompleteFlag: '1'
    }).then((res) => {
      this.$router.replace({ name: 'chooseAppointmentOrTeach' });
    });
  }
};
</script>
