<template>
  <section class="main fixed white_bg" style="position: fixed">
    <t-header :show-back="false" />
    <article v-if="isSubmitCounter" class="content">
      <div class="result_page">
        <div class="result_tips">
          <div class="icon" :class="resultClass" />
          <h5>{{ resultTips }}</h5>
          <p v-if="dataResult.bandleState !== BANDLE_STATE.SUCCESS">
            {{ dataResult.bandleMsg }}
          </p>
          <p v-if="dataResult.bandleState">提交时间：{{ nowDate }}</p>
        </div>
        <div class="result_banner" v-if="displayBanner" @click="toBannerUrl">
          <img :src="bannerSrc" />
        </div>
      </div>
    </article>
    <article v-else class="content">
      <div class="result_page">
        <div class="result_tips">
          <div class="icon ing" />
          <h5><span v-if="showDetail">成功提交，</span>处理中</h5>
          <p v-if="showDetail">
            {{ resultDesc }}
          </p>
          <!-- <p v-if="showVisit" style="color: #fa443a">
            您有一份待完成的问卷，请及时填写，否则将影响业务办理结果。
          </p> -->
        </div>
        <div
          v-if="selectedAccountsData.length == 0 && showDetail"
          class="result_info"
        >
          <ul>
            <li>
              <span class="tit">{{ flowName || bizName }}</span>
              <p><span class="state">处理中</span></p>
            </li>
          </ul>
        </div>
        <div
          v-if="selectedAccountsData.length > 0 && showDetail"
          class="result_info"
        >
          <ul>
            <li v-for="(item, index) in selectedAccountsData" :key="index">
              <span class="tit"
                >{{ item.stockType }}：{{ item.stockAccount }}</span
              >
              <p><span class="state">处理中</span></p>
            </li>
          </ul>
        </div>
      </div>
      <div v-if="tips" class="result_bottom_tips" v-html="tips" />
      <div class="result_banner" v-if="displayBanner" @click="toBannerUrl">
        <img :src="bannerSrc" />
      </div>
    </article>
    <footer class="footer white_bg">
      <div class="ce_btn black">
        <!-- <a v-if="showVisit" class="p_button" @click="toQuestion"
          >前往完成问卷</a
        > -->
        <a class="p_button border" @click="back">返回</a>
      </div>
    </footer>
  </section>
</template>

<script>
import {
  BANDLE_STATE,
  PROCESS_STATUS,
  TASK_TYPE,
  TASK_STATUS
} from '@/common/enumeration';
import { EVENT_NAME } from '@/common/formEnum';
import {
  returnVisitGenerate,
  visitClientQuery,
  flowSubmit,
  flowQueryIns,
  addClientCritMark,
  critMarkQuery,
  flowEnd
} from '@/service/service';
import { jumpThirdPartyUrl, getInstantToken } from '@/common/util';

export default {
  name: 'Result',
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {
    resultDesc: {
      type: String,
      default: ''
    },
    isSubmitCounter: {
      // 是否直接提交柜台
      type: Boolean,
      default: true
    },
    needBackApp: {
      type: Boolean,
      default: false
    },
    isNeedVisit: {
      type: Boolean,
      default: false
    },
    subjectNo: {
      // 试卷编号
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      bizType: '',
      bizName: '',
      flowName: '',
      formId: '',
      BANDLE_STATE: BANDLE_STATE,
      TASK_TYPE,
      TASK_STATUS,
      dataResult: () => {},
      nowDate: '',
      showVisit: false,
      selectedAccountsData: [],
      showDetail: false,
      tips: this.$attrs.tips,
      banerConfig: $hvue.customConfig.bannerConfig
    };
  },
  computed: {
    isApp() {
      return $hvue.platform !== '0';
    },
    bannerSrc() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      const bannerData = this.banerConfig.filter(
        (a) => a.bizType === bizType
      )[0];
      if (bannerData?.imageName) {
        return require(`@/assets/images/${bannerData.imageName}.png`);
      } else {
        return '';
      }
    },

    displayBanner() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      const bannerData = this.banerConfig.filter(
        (a) => a.bizType === bizType
      )[0];
      return bannerData?.bizType;
    },
    resultClass() {
      if (this.dataResult.bandleState === BANDLE_STATE.SUCCESS) {
        return 'ok';
      } else if (this.dataResult.bandleState === BANDLE_STATE.FAIL) {
        return 'fail';
      } else {
        return 'ing';
      }
    },
    resultTips() {
      if (this.dataResult.bandleState === BANDLE_STATE.SUCCESS) {
        return '办理成功';
      } else if (this.dataResult.bandleState === BANDLE_STATE.FAIL) {
        return '办理失败';
      } else {
        return '办理中';
      }
    }
  },
  created() {
    $h.setSession('onResultPage', true);
  },
  mounted() {
    this.renderingView();
    if (this.isNeedVisit) {
      critMarkQuery({
        markType: '3',
        flowToken: sessionStorage.getItem('TKFlowToken')
      }).then((res) => {
        if (res.data.critMarkFlag === false) {
          this.$TAlert({
            title: '重要提示',
            tips: '权限开通30日内我司会向您推送回访问卷，请您务必配合完成。请您结合本人风险承受能力问卷测评结果，以及投资偏好、承受损失等因素，审慎参与。若我司从业人员存在接受全权委托、违规操作投资者账户等行为，请及时拨打95310反馈。',
            confirm: () => {
              addClientCritMark({
                flowToken: sessionStorage.getItem('TKFlowToken'),
                markType: '3',
                markContent:
                  '权限开通30日内我司会向您推送回访问卷，请您务必配合完成。请您结合本人风险承受能力问卷测评结果，以及投资偏好、承受损失等因素，审慎参与。若我司从业人员存在接受全权委托、违规操作投资者账户等行为，请及时拨打95310反馈。',
                confirmFlag: '1'
              }).then((res) => {});
            }
          });
        }
      });
    }
    this.eventMessage(this, EVENT_NAME.BACK_BTN, { display: false });
    // if (this.isNeedVisit) {
    //   visitClientQuery({
    //     flowToken: sessionStorage.getItem('TKFlowToken')
    //   }).then((res) => {
    //     let state = res.data.userVisitStatus;
    //     if (state === 0) {
    //       returnVisitGenerate({
    //         flowToken: sessionStorage.getItem('TKFlowToken'),
    //         subjectNo: this.subjectNo
    //       }).then(() => {
    //         this.showVisit = true;
    //       });
    //     } else if (state === 1) {
    //       this.showVisit = true;
    //     } else if (state === 2) {
    //       // 已完成
    //       this.showVisit = false;
    //     }
    //   });
    // }
  },
  methods: {
    toQuestion() {
      this.$router.replace({
        name: 'questionVisitDetail',
        query: {
          subjectNo: this.subjectNo,
          biz: this.bizType,
          formId: this.formId
        }
      });
    },
    preStep() {
      this.eventMessage(this, EVENT_NAME.PREV_FLOW);
    },
    back() {
      if (this.isApp && this.needBackApp) {
        this.$store.commit('user/setUserInfo', null);
        localStorage.removeItem('vuex');
        sessionStorage.clear();
        // wakeLoginApp();
        $h.callMessageNative({
          funcNo: '50114',
          moduleName: $hvue.customConfig.moduleName
        });
      } else {
        this.eventMessage(this, EVENT_NAME.TO_INDEX);
      }
    },

    renderingView() {
      // window.tkFlowNextCallback = (data) => {
      //   console.log(data);
      // };
      // this.eventMessage(this, EVENT_NAME.NEXT_STEP);
      // return;
      const { inProperty } = this.tkFlowInfo();
      if (inProperty?.selectedAccountsData) {
        this.selectedAccountsData =
          JSON.parse(inProperty?.selectedAccountsData) || [];
      }
      const flowToken = sessionStorage.getItem('TKFlowToken');
      // const accArr = [
      //   PROCESS_STATUS.ACCEPT_COMPLETED, //受理完成
      //   PROCESS_STATUS.COMPLETED //办理完成
      // ]; // 配置需要提交受理结果的状态
      flowQueryIns({ flowToken })
        .then((res) => {
          this.bizType = res.data.bizType;
          this.bizName = res.data.bizName;
          this.flowName = res.data.flowName;
          this.formId = res.data.formId;
          // 进入页面查询状态，只有当前流程状态为受理中的情况，进入业务，
          // 直接提交给柜台的调用flowSubmit，跑批同步提交调用流程下一步
          // 0为受理中状态
          if (res.data.status === '0') {
            // 流程受理中状态
            if (this.isSubmitCounter) {
              // 直接提交柜台
              flowSubmit({ flowToken }, { loading: false })
                .then((data) => {
                  if (data.code === 0) {
                    this.showDetail = true;
                    this.dataResult = data.data[0];
                    this.nowDate = new Date().format('yyyy-MM-dd hh:mm:ss');
                  } else {
                    return Promise.reject(data.msg);
                  }
                })
                .catch((err) => {
                  this.showDetail = false;
                });
            } else {
              // 跑批同步提交
              this.eventMessage(this, EVENT_NAME.NEXT_STEP);
              window.tkFlowNextCallback = (data) => {
                if (data.code === 0) {
                  this.showDetail = true;
                } else {
                  this.showDetail = false;
                  _hvueAlert({
                    mes: '提交失败，请稍后重试',
                    callback: () => {
                      $h.callMessageNative({
                        funcNo: '50114',
                        moduleName: $hvue.customConfig.moduleName
                      });
                    }
                  });
                }
              };
            }
          } else {
            this.showDetail = true;
            // 非受理中状态进入结果页
            return;
          }
        })
        .catch((err) => {
          this.showDetail = false;
          _hvueAlert({
            mes: '请求失败，请稍后重试',
            callback: () => {
              $h.callMessageNative({
                funcNo: '50114',
                moduleName: $hvue.customConfig.moduleName
              });
            }
          });
        });
    },
    async toBannerUrl() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      const bannerData = this.banerConfig.filter(
        (a) => a.bizType === bizType
      )[0];
      try {
        if (bannerData?.getToken) {
          const { appId, opStation, instantToken } = await getInstantToken();
          jumpThirdPartyUrl({
            url: `${bannerData.url}?app_id=${appId}&op_station=${opStation}&instant_token=${instantToken}`
          });
        } else {
          jumpThirdPartyUrl({
            url: `${bannerData.url}`
          });
        }
      } catch (err) {
        this.$TAlert({
          tips: err
        });
      }
    },
  }
};
</script>
<style scoped>
div.result_banner img {
  width: 100%;
  height: 100%;
  padding: 0.16rem;
}
</style>
