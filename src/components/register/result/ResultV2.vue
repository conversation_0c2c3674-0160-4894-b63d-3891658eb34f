<template>
  <section class="main fixed white_bg" style="position: fixed; z-index: 1490">
    <t-header :show-back="false" :title="tkFlowInfo().stepName" />
    <article class="content" v-if="isLoading" />
    <InProcess
      v-if="isInProcess"
      :isShowDetail="showDetail"
      :flowName="flowName"
      :bizName="bizName"
      :resultDesc="resultDesc"
      :dataResult="dataResult"
      :tips="tips"
    />
    <!-- 开户类 -->
    <OpenAccount
      v-else-if="viewWay == 1 && !isLoading"
      :dataResult="dataResult"
      :flowName="flowName"
      :bizName="bizName"
      :resultDesc="resultDesc"
    />
    <!-- 权限类 -->
    <AuthorityAccount
      v-else-if="viewWay == 2 && !isLoading"
      :dataResult="dataResult"
      :flowName="flowName"
      :bizName="bizName"
      :startTaskWay="startTaskWay"
      :resultDesc="resultDesc"
      :tips="tips"
      :successMessage="successMessage"
    />
    <!-- 新三板权限取消 -->
    <RovekAccount
      v-else-if="viewWay == 3 && !isLoading"
      :dataResult="dataResult"
      :flowName="flowName"
      :bizName="bizName"
      :resultDesc="resultDesc"
    />
    <!-- 预约结果页 -->
    <ReservationBusiness
      v-else-if="viewWay == 4 && !isLoading"
      :dataResult="dataResult"
      :flowName="flowName"
      :bizName="bizName"
      :resultDesc="resultDesc"
      :tipsNote="tipsNote"
    />
    <footer class="footer white_bg">
      <div class="ce_btn black">
        <a v-show="showXycybBtn" class="p_button" @click="goBizPage('010245')"
          >申请开通信用创业板</a
        >
        <a v-show="showBjsBtn" class="p_button" @click="goBizPage('010180')"
          >申请开通北交所权限</a
        >
        <a class="p_button border" @click="back">返回</a>
      </div>
    </footer>
  </section>
</template>

<script>
import {
  BANDLE_STATE,
  ASSET_PROP,
  EXCHANGE_TYPE,
  DICT_TYPE,
  SYS_CONFIG
} from '@/common/enumeration';
import { EVENT_NAME } from '@/common/formEnum';
import {
  flowSubmit,
  flowQueryIns,
  addClientCritMark,
  critMarkQuery,
  processTaskFlowSubmit,
  zqFundAccountQry,
  querySysConfig
} from '@/service/service';
import { wakeLoginAppGJ } from '@/common/util.js';

import InProcess from './component/InProcess.vue';
import OpenAccount from './component/OpenAccount.vue';
import AuthorityAccount from './component/AuthorityAccount.vue';
import RovekAccount from './component/RovekAccount.vue';
import ReservationBusiness from './component/ReservationBusiness.vue';

export default {
  name: 'ResultV2',
  inject: ['tkFlowInfo', 'eventMessage'],
  provide() {
    return {
      getLabelName: this.getLabelName,
      getExchangeTypeName: this.getExchangeTypeName,
      getSelectAccountList: this.getSelectAccountList
    };
  },
  components: {
    InProcess,
    OpenAccount,
    AuthorityAccount,
    RovekAccount,
    ReservationBusiness
  },
  props: {
    // 成功受理时，界面展示的描述信息
    resultDesc: { type: String, default: '' },
    // 是否直接提交柜台
    isSubmitCounter: { type: Boolean, default: true },
    // 是否直接返回App
    needBackApp: { type: Boolean, default: false },
    // 是否加载问卷信息
    isNeedVisit: { type: Boolean, default: false },
    // 问卷编号
    subjectNo: { type: String, default: '1' },
    // 发起任务方式：1.实时 2.异步 3.7*24
    startTaskWay: { type: String, default: '' },
    // 条件审核策略编号
    strategyNo: { type: String, default: '' },
    // 展示方式：1.开户类 2.权限类 3.新三板取消权限开通
    viewWay: { type: String, default: '' },
    // 是否通知交易端更新
    isTrade: { type: Boolean, default: '' },
    // 是否通知交易端更新
    tradeType: { type: String, default: () => [] },
    // 提示文案
    tipsNote: { type: String, default: '办理' },
    //系统编号
    sysNo: { type: String, default: '' },
    //办理成功提示语
    successMessage: { type: String, default: '' }
  },
  data() {
    return {
      // 业务类型
      bizType: '',
      // 业务名称
      bizName: '',
      // 流程名称
      flowName: '',
      // 表单唯一标识
      formId: '',
      // 是否展示处理中
      isInProcess: false,
      // 处理结果
      dataResult: {},
      // 处理中是否已成功提交
      showDetail: false,
      // 是否处于加载状态
      isLoading: false,
      tips: '',
      selectedAccountsData: ''
    };
  },
  computed: {
    flowToken() {
      return sessionStorage.getItem('TKFlowToken');
    },
    // 中签资金预冻结取消业务标识
    zqzzBizType() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      return bizType === '010284';
    },
    //创业板权限开通
    cybBizType() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      return bizType === '010061';
    },
    //启用/取消独立密码业务
    dlmmBizType() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      return ['010303', '010305'].includes(bizType);
    },
    // 开通Ptradet&QMT权限开通业务
    ptradeQmtBizType() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      return ['010734'].includes(bizType);
    },
    //普通、信用科创板权限开通
    kcbBizType() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      return bizType === '010086';
    },
    showXycybBtn() {
      return (
        this.cybBizType && this.dataResult.bandleState === BANDLE_STATE.SUCCESS
      );
    },
    // 普通和信用科创板开通结果页展示北交所按钮
    showBjsBtn() {
      return this.kcbBizType && this.dataResult.bandleState === BANDLE_STATE.SUCCESS
    }
  },
  created() {
    $h.setSession('onResultPage', true);
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    this.eventMessage(this, EVENT_NAME.BACK_BTN, { display: false });
  },
  mounted() {
    this.isLoading = true;
    this.init();
    this.selectedAccountsData = this.getSelectAccountList();
  },
  methods: {
    goBizPage(bizType) {
      $h.setSession('ResultV2_DataResult', this.dataResult);
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType, initJumpMode: '0' });
      });
    },
    getZqFundAccount() {
      zqFundAccountQry({
        needRemindFlag: '1', // 需要提醒标识： 1 需要；0 不需要（默认）
        flowToken: sessionStorage.getItem('TKFlowToken')
      }).then(({ data }) => {
        const frozen = this.selectedAccountsData.some(({ fundAccount }) => {
          return data.zqFundAccountList.some(
            ({ ipofrozenFlag, fundAccount: zqFundAccount }) =>
              fundAccount === zqFundAccount && ipofrozenFlag === '1'
          ); //已冻结
        });
        const unfrozen = this.selectedAccountsData.some(({ fundAccount }) => {
          return data.zqFundAccountList.some(
            ({ ipofrozenFlag, fundAccount: zqFundAccount }) =>
              fundAccount === zqFundAccount && ipofrozenFlag === '2'
          ); //未冻结
        });
        // 提交中
        let content = '中签预冻结资金权限关闭成功';
        if (this.isInProcess) {
          content = '关闭中签预冻结资金权限关闭中，预计在下一个交易日关闭成功';
        }
        if (frozen) {
          this.tips = `温馨提示：${content}。您的已冻结的资金不会自动解冻，您可前往中签资金查询页面进行解冻。如有疑问，可拨打客服热线95310。`;
        } else if (unfrozen) {
          this.tips = `温馨提示：${content}。您的已中签未冻结资金，将不会自动冻结。为了确保您在中签缴款日有足够资金可进行扣款，请提前准备好中签缴款资金哦～`;
        } else {
          this.tips = `温馨提示：${content}。若您后续中签，中签资金将不会预冻结。若您想中签的资金可以预冻结，可再次开通此权限哦~`;
        }
      });
    },
    visit() {
      if (this.isNeedVisit) {
        critMarkQuery({
          markType: '3',
          flowToken: this.flowToken
        }).then((res) => {
          // 是否存在问卷回访留痕记录
          if (res.data.critMarkFlag === false) {
            this.$TAlert({
              title: '重要提示',
              tips: '权限开通30日内我司会向您推送回访问卷，请您务必配合完成。请您结合本人风险承受能力问卷测评结果，以及投资偏好、承受损失等因素，审慎参与。若我司从业人员存在接受全权委托、违规操作投资者账户等行为，请及时拨打95310反馈。',
              confirm: () => {
                addClientCritMark({
                  flowToken: this.flowToken,
                  markType: '3',
                  markContent:
                    '权限开通30日内我司会向您推送回访问卷，请您务必配合完成。请您结合本人风险承受能力问卷测评结果，以及投资偏好、承受损失等因素，审慎参与。若我司从业人员存在接受全权委托、违规操作投资者账户等行为，请及时拨打95310反馈。',
                  confirmFlag: '1'
                }).then((res) => {});
              }
            });
          }
        });
      }
    },
    // 返回方法
    back() {
      if (
        this.dlmmBizType &&
        this.dataResult.bandleState === BANDLE_STATE.SUCCESS
      ) {
        const logoutType = this.getLogoutType();
        console.log(`***** logoutType = ${logoutType} ****`);
        if ($hvue.platform !== '0' && logoutType !== false) {
          wakeLoginAppGJ(logoutType);
        } else {
          this.eventMessage(this, EVENT_NAME.TO_INDEX);
        }
        return;
      }
      if ($hvue.platform !== '0' && this.needBackApp) {
        this.$store.commit('user/setUserInfo', null);
        localStorage.removeItem('vuex');
        sessionStorage.clear();
        $h.callMessageNative({
          funcNo: '50114',
          moduleName: $hvue.customConfig.moduleName
        });
      } else {
        this.eventMessage(this, EVENT_NAME.TO_INDEX);
      }
    },
    /*
     * 获取退出登录状态类型
     * @return {Number} 1普通登录 2信用登录 3担保品划转登录（普通+信用） 16期权
     */
    getLogoutType() {
      /* const credit = this.selectedAccountsData.some(
        ({ assetProp }) => assetProp === ASSET_PROP.CREDIT_ACCOUNT
      );
      const ordinary = this.selectedAccountsData.some(
        ({ assetProp }) => assetProp === ASSET_PROP.ORDINARY_ACCOUNT
      );
      const derivatives = this.selectedAccountsData.some(
        ({ assetProp }) => assetProp === ASSET_PROP.DERIVATIVES_ACCOUNT
      );
      return credit ? (ordinary ? 3 : 2) : derivatives ? 16 : 1; */
      //信用资金账户
      let logoutMap = new Map();
      logoutMap.set(ASSET_PROP.ORDINARY_ACCOUNT, 1);
      logoutMap.set(ASSET_PROP.CREDIT_ACCOUNT, 2);
      logoutMap.set(ASSET_PROP.DERIVATIVES_ACCOUNT, 16);
      let list = [];
      for (const { assetProp } of this.selectedAccountsData) {
        list.push(logoutMap.get(assetProp));
      }
      return list;
    },
    // 初始化方法
    async init() {
      // $h.callMessageNative({
      //   funcNo: '50106',
      //   content: '再按一次退出流程'
      // });
      // // 展示处理中页面
      // this.isLoading = false;
      // // 展示处理中页面
      // this.isInProcess = false;
      // this.dataResult = {
      //   bandleState: BANDLE_STATE.FAIL,
      //   bandleMsg:
      //     '{"accounts_proc_data":[{"stock_account":"E439739561","exchange_type":"1","fail_reason":"123123","proc_success":"0"},{"stock_account":"","exchange_type":"1","fail_reason":"123123","proc_success":"1"}]}'
      // };
      // return;
      // 展示遮罩层
      // _hvueLoading.open();
      // 获取当前流程状态
      const [error, data] = await this.request(flowQueryIns);
      if (!error) {
        // 业务类型
        this.bizType = data.bizType;
        // 业务名称
        this.bizName = data.bizName;
        // 流程名称
        this.flowName = data.flowName;

        // 受理状态为办理完成，并且存在业务提交结果，无需重复调用提交接口
        if (data.status === '3' && $h.getSession('ResultV2_DataResult')) {
          this.dataResult = $h.getSession('ResultV2_DataResult');
          $h.clearSession('ResultV2_DataResult');
          this.isLoading = false;
          return;
        }

        // 开通ptrade&QMT权限业务特殊处理，选择系统编号为底仓增强策略
        if (this.ptradeQmtBizType) {
          const [e, d] = await this.request(querySysConfig, {
            configKey: DICT_TYPE.PTRADEQMT_SYS_CONFIG
          });
          const { configValue = '' } = d[DICT_TYPE.PTRADEQMT_SYS_CONFIG];
          if (!e && this.sysNo === SYS_CONFIG.DCZQCL && configValue !== '') {
            const configValList = JSON.parse(configValue);
            this.flowName = configValList.reduce((str, item) => {
              if (item.sysCode === SYS_CONFIG.DCZQCL) {
                str = item.title;
              }
              return `${str}权限`;
            }, '');
          }
        }
        // 流程唯一标识
        this.formId = data.formId;
        // 从流程表单中获取相关的信息
        const { startTask } = this.tkFlowInfo();
        // 判断当前是否还处于受理中
        if (data.status === '0') {
          // 跑批同步提交
          this.eventMessage(this, EVENT_NAME.NEXT_STEP);
          // 展示遮罩层
          // _hvueLoading.open();
          window.tkFlowNextCallback = () => {
            // 判断当前结果页是否配置了任务流程
            startTask == '' ? this.current() : this.original();
          };
        } else if (data.status === '1' && startTask == '') {
          this.current();
        } else {
          // 关闭加载页
          this.isLoading = false;
          // 展示处理中页面
          this.isInProcess = true;
          // 非受理中表示提交完成
          this.showDetail = true;
        }
      } else this.errorMsg();
    },
    // 接口查询异常统一处理
    errorMsg() {
      this.showDetail = false;
      _hvueAlert({
        mes: '请求失败，请稍后重试',
        callback: () => {
          $h.callMessageNative({
            funcNo: '50114',
            moduleName: $hvue.customConfig.moduleName
          });
        }
      });
    },
    // 原始结果页流程
    async original() {
      // 是否直接提交柜台
      if (this.isSubmitCounter || this.startTaskWay === '1') {
        // 开启加载页
        this.isLoading = true;
        // 展示处理中页面
        this.isInProcess = false;
        // 展示遮罩层
        // _hvueLoading.open();
        // 获取当前流程状态
        const [error, data] = await this.request(flowSubmit, {
          flowToken: this.flowToken
        });
        this.showDetail = true;
        if (!error) {
          this.dataResult = {
            bandleState: BANDLE_STATE.SUCCESS,
            bandleMsg: data
          };
          // 成功后调用 交易段同步方法
          this.tradeMsg();
          this.visit();
        } else {
          this.dataResult = {
            bandleState: BANDLE_STATE.FAIL,
            bandleMsg: error
          };
        }
      } else {
        // 展示处理中页面
        this.isInProcess = true;
        this.showDetail = true;
        this.visit();
        /*  // 跑批同步提交
        this.eventMessage(this, EVENT_NAME.NEXT_STEP);
        // next接口出现异常后回调处理
        window.tkFlowNextCallback = (data) => {
          if (data.code === 0) {
            this.showDetail = true;
            this.visit();
          } else {
            this.errorMsg();
          }
        }; */
      }
      // 关闭加载页
      this.isLoading = false;
      // 展示遮罩层
      // _hvueLoading.close();
    },
    // 当前支持的流程
    async current() {
      // 开启加载页
      this.isLoading = true;
      // 实时提交使用该方法
      if (this.startTaskWay === '1') {
        this.original();
        return;
      }
      // 判断当前是否开启了7*24小时
      const alldayFlag = this.startTaskWay == '3' ? '1' : '0';
      // 展示遮罩层
      // _hvueLoading.open();
      // 查询无配置流程任务提交
      const [error, data] = await this.request(processTaskFlowSubmit, {
        strategyNo: this.strategyNo,
        alldayFlag
      });
      if (!error) {
        if (!data || (!!data && !data.status)) {
          this.dataResult = {
            bandleState: BANDLE_STATE.SUCCESS,
            bandleMsg: data
          };
          // 成功后调用 交易段同步方法
          this.tradeMsg();
        } else {
          if (!!data && data.status != 3) {
            // 展示处理中页面
            this.isInProcess = true;
            // 非受理中表示提交完成
            this.showDetail = true;
          } else {
            this.dataResult = {
              bandleState: BANDLE_STATE.SUCCESS,
              bandleMsg: data
            };
            // 成功后调用 交易段同步方法
            this.tradeMsg();
          }
        }
        this.visit();
      } else if (error === '处理中') {
        // 展示处理中页面
        this.isInProcess = true;
        // 非受理中表示提交完成
        this.showDetail = true;
      } else {
        this.dataResult = {
          bandleState: BANDLE_STATE.FAIL,
          bandleMsg: error
        };
      }
      this.isLoading = false;

      if (this.zqzzBizType) {
        this.getZqFundAccount();
      }
    },
    // 交易信息
    tradeMsg() {
      console.log('tradeMsg start *****');
      if (this.isTrade && this.tradeType) {
        const tradeType = this.tradeType.split(',');
        if (tradeType.length > 0) {
          tradeType.map((item) => {
            const reqParam60099 = {
              funcNo: '60099',
              actionType: '13',
              params: {
                yjbFuncId: '4021',
                yjbFuncParams: {
                  type: item
                }
              }
            };
            const { result } = $h.callMessageNative(reqParam60099);
            console.log(`60099 callback ******** type:${item}`, result);
          });
        }
      }
    },
    // 请求二次包装
    async request(promiseFunc, param = {}, config = {}) {
      try {
        // 查询无配置流程任务提交
        const { code, msg, data } = await promiseFunc(
          {
            flowToken: this.flowToken,
            ...param
          },
          { ...config }
        );
        return [null, data];
      } catch (e) {
        return [e, null];
      }
    },
    handelObjToCamel(obj) {
      const camelCaseObj = {};
      for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
          const camelCaseKey = key.replace(/_([a-z])/g, (match, letter) =>
            letter.toUpperCase()
          );
          camelCaseObj[camelCaseKey] = obj[key];
        }
      }
      return camelCaseObj;
    },
    getAssetPropMap(v) {
      let getMap = new Map();
      getMap.set(ASSET_PROP.DERIVATIVES_ACCOUNT, '衍生品资金账户');
      getMap.set(ASSET_PROP.ORDINARY_ACCOUNT, '普通资金账户');
      getMap.set(ASSET_PROP.CREDIT_ACCOUNT, '信用资金账户');
      getMap.set(ASSET_PROP.OPTIONS_ACCOUNT, '期权资金账户');
      getMap.set(ASSET_PROP.FUND_ACCOUNT, '基金资金账户');
      return getMap.get(v) || '';
    },
    getLabelName(data) {
      let { stockType, stockAccount, fundAccount, assetProp, desc } =
        this.handelObjToCamel(data);
      if (desc) {
        return desc;
      } else if (!stockAccount) {
        return `${this.getAssetPropMap(assetProp)}${
          fundAccount ? '：' : ''
        }${fundAccount}`;
      } else {
        return `${stockType}${stockAccount ? '：' : ''}${stockAccount}`;
      }
    },
    getExchangeTypeName(type) {
      return (
        {
          [EXCHANGE_TYPE.SH]: '沪A',
          [EXCHANGE_TYPE.SZ]: '深A',
          [EXCHANGE_TYPE.TZA]: '特转A'
        }[type] || ''
      );
    },
    getSelectAccountList() {
      const { inProperty } = this.tkFlowInfo();
      // 判断是否传入了已选账户
      if (inProperty && inProperty.selectedAccountsData) {
        // 解构获取当前的选中账户信息
        let selectAccData = JSON.parse(inProperty.selectedAccountsData);
        // Ptrade/QMT权限开通业务，数据格式特殊处理
        if (this.ptradeQmtBizType) {
          const generalFundAccList = [];
          for (const item of selectAccData?.permissionData?.permissionList) {
            if (item.checked === '1') {
              generalFundAccList.push({ desc: `普通资金账户：${item.desc}` });
            }
            if (item?.subPermission?.length > 0) {
              item.subPermission.forEach((it) => {
                if (it.checked === '1')
                  generalFundAccList.push({ desc: `普通资金账户：${it.desc}` });
              });
            }
          }
          /* ******** 需求确认，暂时取消展示信息资金账户逻辑 start */
          /* let creditFundAccList = [];
          if (creditPermFlag === '1') {
            creditFundAccList = permissionList.map(({ desc }) => {
              return { desc: `信用资金账户：${desc}` };
            });
          } */
          /* ******** 需求确认，暂时取消展示信息资金账户逻辑 end */
          return [...generalFundAccList];
        } else {
          return selectAccData;
        }
      }
    }
  }
};
</script>
