<template>
  <article class="content">
    <div class="result_page">
      <div class="result_tips">
        <div class="icon" :class="resultClass" />
        <h5>{{ resultTips }}</h5>
      </div>
      <div v-if="isAccount" class="result_info">
        <ul>
          <li>
            <span class="tit">{{ flowName || bizName }}</span>
            <p>
              <span class="state">{{ resultTips }}</span>
            </p>
          </li>
        </ul>
      </div>
      <div v-else class="result_info">
        <ul>
          <li v-for="(item, index) in selectAccount" :key="index">
            <span class="tit" v-if="!item.marketCompany">{{
              getListDetail(item)
            }}</span>
            <div class="company_box" v-else>
              <span class="tit">
                {{ item.marketCompany }}
              </span>
              <span class="company_tit">
                对应账户：
                {{ getExchangeType(item.exchangeType) }}
                （{{ item.stockAccount }}）
              </span>
            </div>
            <p>
              <span class="state">{{ item.resultTips }}</span>
            </p>
          </li>
        </ul>
      </div>
      <div class="reject_txtinfo" v-if="isFail">
        <h5 class="title">原因</h5>
        <p v-for="(item, i) in errorMsg" :key="i">
          {{ item ? `${i + 1}、${item}` : '' }}
        </p>
      </div>
      <div
        class="reject_txtinfo"
        v-else-if="startTaskWay === '1' && resultDesc"
      >
        <h5 class="title" v-html="resultDesc"></h5>
      </div>
      <div v-if="tips" class="result_tips pdt0">
        <p>{{ tips }}</p>
      </div>
      <div
        v-else-if="
          dataResult.bandleState === BANDLE_STATE.SUCCESS &&
          successMessage !== ''
        "
        class="result_tips pl016"
        v-html="successMessage"
      ></div>
      <div v-show="displayFrozenBtn" class="result_info" @click="toFrozenUrl">
        <p>解除本次冻结资金></p>
      </div>
      <div class="result_banner" v-if="displayBanner" @click="toBannerUrl">
        <img :src="bannerSrc" />
      </div>
    </div>
  </article>
</template>

<script>
import { BANDLE_STATE, ASSET_PROP, EXCHANGE_TYPE } from '@/common/enumeration';
import { jumpThirdPartyUrl, getInstantToken } from '@/common/util';
export default {
  inject: [
    'tkFlowInfo',
    'eventMessage',
    'getLabelName',
    'getExchangeTypeName',
    'getSelectAccountList'
  ],
  props: {
    // 流程名称
    flowName: { type: String, default: '' },
    // 业务名称
    bizName: { type: String, default: '' },
    // 发起任务方式：1.实时 2.异步 3.7*24
    startTaskWay: { type: String, default: '' },
    // 处理完成的结果集
    dataResult: {
      type: Object,
      default: () => ({
        // 处理状态
        bandleState: '',
        // 处理原因
        bandleMsg: ''
      })
    },
    resultDesc: String,
    tips: { type: String, default: '' },
    //办理成功提示语
    successMessage: { type: String, default: '' }
  },
  data() {
    return {
      // 当前已选的账户列表
      selectedAccountsData: [],
      EXCHANGE_TYPE,
      banerConfig: $hvue.customConfig.bannerConfig,
      BANDLE_STATE
    };
  },
  computed: {
    bannerSrc() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      const bannerData = this.banerConfig.filter(
        (a) => a.bizType === bizType
      )[0];
      if (bannerData?.imageName) {
        return require(`@/assets/images/${bannerData.imageName}.png`);
      } else {
        return '';
      }
    },
    displayBanner() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      const bannerData = this.banerConfig.filter(
        (a) => a.bizType === bizType
      )[0];
      return bannerData?.bizType;
    },
    // 中签资金预冻结取消个性化需求，佣金宝渠道展示
    displayFrozenBtn() {
      return $hvue.platform !== '0' && this.zqzzBizType;
    },
    // 中签资金预冻结取消业务标识
    zqzzBizType() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      return bizType === '010284';
    },
    // 判断处理中展示效果
    isAccount() {
      return this.selectedAccountsData.length == 0;
    },
    // 是否失败状态
    isFail() {
      return this.dataResult.bandleState === BANDLE_STATE.FAIL;
    },
    // 处理状态下样式
    resultClass() {
      if (this.dataResult.bandleState === BANDLE_STATE.SUCCESS) {
        return 'ok';
      } else if (this.dataResult.bandleState === BANDLE_STATE.FAIL) {
        return 'fail';
      } else {
        return '';
      }
    },
    // 当前处理状态
    resultTips() {
      if (this.dataResult.bandleState === BANDLE_STATE.SUCCESS) {
        return '办理成功';
      } else if (this.dataResult.bandleState === BANDLE_STATE.FAIL) {
        return '办理失败';
      } else {
        return '';
      }
    },
    // 当前选中账户结果页处理
    selectAccount() {
      const bandleMsg = this.dataResult.bandleMsg;
      const account = this.selectedAccountsData;
      if (!this.isJSON(bandleMsg)) {
        return account.map((item) => ({
          ...item,
          resultTips: this.resultTips
        }));
      }
      // 格式化消息内容
      const msg = !bandleMsg ? [] : JSON.parse(bandleMsg);
      if (!Array.isArray(msg) && msg.accounts_proc_data) {
        return account.map((item) => {
          const account = item.stockAccount || item.fundAccount;
          const msgInfo = msg.accounts_proc_data.find((num) => {
            // 权限类会存在多个账户但单个账户唯一，开户类市场唯一
            if (num.stock_account) {
              return num.stock_account == account;
            } else if (num.fund_account) {
              return num.fund_account == account;
            } else {
              return (
                num.exchange_type == item.exchangeType ||
                num.exchangeType == item.exchangeType
              );
            }
          });
          if (Array.isArray(msgInfo)) {
            const err = msgInfo
              .map((item) => {
                if (item.proc_success != undefined) return item.proc_success;
                return this.dataResult.bandleState;
              })
              .filter((item) => item != '0');
            if (err.length > 0) {
              return {
                ...item,
                resultTips: '办理失败'
              };
            }
          }
          const tips =
            msgInfo && msgInfo.proc_success
              ? msgInfo.proc_success == '0'
                ? '办理失败'
                : '办理成功'
              : this.resultTips;
          // 拼接返回对应的错误信息
          return { ...item, resultTips: tips };
        });
      }
      return account.map((item) => {
        const account = item.stockAccount || item.fundAccount;
        const msgInfo = msg.find((num) => {
          // 权限类会存在多个账户但单个账户唯一，开户类市场唯一
          if (num.stock_account) {
            return num.stock_account == account;
          } else if (num.fund_account) {
            return num.fund_account == account;
          } else {
            return (
              num.exchange_type == item.exchangeType ||
              num.exchangeType == item.exchangeType
            );
          }
        });
        if (Array.isArray(msgInfo)) {
          const err = msgInfo
            .map((item) => {
              if (item.result != undefined) return item.result;
              return this.dataResult.bandleState;
            })
            .filter((item) => item != '0');
          if (err.length > 0) {
            return {
              ...item,
              resultTips: '办理失败'
            };
          }
        }
        const tips =
          msgInfo && msgInfo.result
            ? msgInfo.result == '0'
              ? '办理失败'
              : '办理成功'
            : this.resultTips;
        return {
          ...item,
          resultTips: tips
        };
      });
    },
    // 错误原因
    errorMsg() {
      const bandleMsg = this.dataResult.bandleMsg;
      const account = this.selectedAccountsData;
      if (!this.isJSON(bandleMsg)) {
        const errorMsg = bandleMsg.split('\n');
        return account.map((_item, i) => errorMsg[i]);
      }
      console.info(account);
      const msg = !bandleMsg ? [] : JSON.parse(bandleMsg);
      if (!Array.isArray(msg) && msg.accounts_proc_data) {
        return account
          .map((item) => {
            const account = item.stockAccount || item.fundAccount;
            const msgInfo = msg.accounts_proc_data.find((num) => {
              // 权限类会存在多个账户但单个账户唯一，开户类市场唯一
              if (num.stock_account) {
                return num.stock_account == account && num.fail_reason;
              } else if (num.fund_account) {
                return num.fund_account == account && num.fail_reason;
              } else {
                return (
                  num.exchange_type == item.exchangeType ||
                  num.exchangeType == item.exchangeType
                );
              }
            });
            if (
              msgInfo &&
              !!msgInfo.proc_success &&
              msgInfo.proc_success == '0'
            ) {
              // 拼接返回对应的错误信息
              return `[${item.stockAccount || item.stockType}]${
                msgInfo.fail_reason
              }`;
            } else if (Array.isArray(msgInfo)) {
              const key = `[${item.stockAccount || item.stockType}]`;
              const info = msgInfo
                .map((item) => item.fail_reason || '')
                .filter((item) => !!item);
              return key + info.join(';\n');
            }
            return '';
          })
          .filter((item) => !!item);
      }
      return account
        .map((item) => {
          const account = item.stockAccount || item.fundAccount;
          const msgInfo = msg.find((num) => {
            // 权限类会存在多个账户但单个账户唯一，开户类市场唯一
            if (num.stock_account) {
              return num.stock_account == account;
            } else if (num.fund_account) {
              return num.fund_account == account;
            } else {
              return (
                num.exchange_type == item.exchangeType ||
                num.exchangeType == item.exchangeType
              );
            }
          });
          if (msgInfo && !!msgInfo.result && msgInfo.result == 0) {
            // 拼接返回对应的错误信息
            return `[${item.stockAccount || item.stockType}]${msgInfo.msg}`;
          } else if (Array.isArray(msgInfo)) {
            const key = `[${item.stockAccount || item.stockType}]`;
            const info = msgInfo
              .map((item) => item.msg || '')
              .filter((item) => !!item);
            return key + info.join(';\n');
          }
          return '';
        })
        .filter((item) => !!item);
    }
  },
  created() {},
  mounted() {
    // 从流程表单中获取相关的信息
    const { inProperty } = this.tkFlowInfo();
    // 判断是否传入了已选账户
    if (inProperty && inProperty.selectedAccountsData) {
      // 解构获取当前的选中账户信息
      this.selectedAccountsData = this.getSelectAccountList();
    }
  },
  methods: {
    // 判断是否能够正常进行解析
    isJSON(str) {
      try {
        JSON.parse(str);
        return true;
      } catch (e) {
        return false;
      }
    },
    async toBannerUrl() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      const bannerData = this.banerConfig.filter(
        (a) => a.bizType === bizType
      )[0];
      try {
        if (bannerData?.getToken) {
          const { appId, opStation, instantToken } = await getInstantToken();
          jumpThirdPartyUrl({
            url: `${bannerData.url}?app_id=${appId}&op_station=${opStation}&instant_token=${instantToken}`
          });
        } else {
          jumpThirdPartyUrl({
            url: `${bannerData.url}`
          });
        }
      } catch (err) {
        this.$TAlert({
          tips: err
        });
      }
    },
    getListDetail(item) {
      return this.getLabelName(item);
    },
    getExchangeType(type) {
      return this.getExchangeTypeName(type);
    },
    toFrozenUrl() {
      let login_type; //1 普通交易登录 2信用交易登录
      let url = 'trans/build/lucky_query.html';
      const creaditFlag = this.selectedAccountsData.some(
        ({ assetProp }) => assetProp === ASSET_PROP.CREDIT_ACCOUNT
      );
      const ordinaryFlag = this.selectedAccountsData.some(
        ({ assetProp }) => assetProp === ASSET_PROP.ORDINARY_ACCOUNT
      );
      if (creaditFlag && !ordinaryFlag) {
        login_type = '2';
        let reqParams = {
          funcNo: '60099',
          actionType: '13',
          params: {
            yjbFuncId: '4004',
            yjbFuncParams: {
              keys: ['loginStatus']
            }
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res4004 = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res4004)}`);
        //0为未登录，1为普通交易，2为信用交易, 3为普通交易+信用交易。
        if ([2, 3].includes(res4004.loginStatus)) {
          url = 'margintrading/build/lucky_query.html';
        } else {
          let reqParams4000 = {
            funcNo: '60099',
            actionType: '13',
            params: {
              yjbFuncId: '4000',
              yjbFuncParams: {
                type: '2'
              }
            }
          };
          console.log(`请求参数为: ~~${JSON.stringify(reqParams4000)}`);
          const res4000 = $h.callMessageNative(reqParams4000);
          console.log(`请求结果为: ~~${JSON.stringify(res4000)}`);
          return;
        }
      } else {
        login_type = '1';
      }
      jumpThirdPartyUrl({
        url,
        fullScreen: 0
      });
    }
  }
};
</script>
<style scoped>
div.result_banner img {
  width: 100%;
  height: 100%;
  padding: 0.16rem;
}
div.result_info > p {
  color: #4f7cff;
  margin-top: 0.2rem;
  text-align: center;
}
div.pdt0 {
  padding-top: 0;
  text-align: left;
}
div.pl016 {
  padding-left: 0.16rem;
  text-align: left;
}
div.result_banner img {
  width: 100%;
  height: 100%;
  padding: 0.16rem;
}
div.result_info > p {
  color: #4f7cff;
  margin-top: 0.2rem;
  text-align: center;
}
div.company_box {
  -webkit-box-flex: 3;
  -webkit-flex: 3;
  -ms-flex: 3;
  flex: 3;
}
div.company_box > span.company_tit {
  font-size: 0.14rem;
  color: #969799;
  position: relative;
  top: 0.01rem;
}
</style>
