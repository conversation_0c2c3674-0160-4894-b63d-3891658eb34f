<template>
  <article class="content">
    <div class="result_page">
      <div class="result_tips">
        <div class="icon ing" />
        <h5><span v-if="isShowDetail">成功提交，</span>处理中</h5>
        <p v-if="isShowDetail">{{ resultDesc }}</p>
      </div>
      <div v-if="isAccount" class="result_info">
        <ul>
          <li>
            <span class="tit">{{ flowName || bizName }}</span>
            <p><span class="state">处理中</span></p>
          </li>
        </ul>
      </div>
      <div v-else class="result_info">
        <ul>
          <li v-for="(item, index) in selectedAccountsData" :key="index">
            <span class="tit" v-if="!item.marketCompany">{{
              getListDetail(item)
            }}</span>
            <div class="company_box" v-else>
              <span class="tit">
                {{ item.marketCompany }}
              </span>
              <span class="company_tit">
                对应账户：
                {{ getExchangeType(item.exchangeType) }}
                （{{ item.stockAccount }}）
              </span>
            </div>
            <p><span class="state">处理中</span></p>
          </li>
        </ul>
      </div>
      <div v-if="tips" class="result_tips pdt0">
        <p>{{ tips }}</p>
      </div>
      <div class="result_info" v-show="displayFrozenBtn" @click="toFrozenUrl">
        <p>解除本次冻结资金></p>
      </div>
      <div class="result_banner" v-if="displayBanner" @click="toBannerUrl">
        <img :src="bannerSrc" />
      </div>
    </div>
  </article>
</template>

<script>
import { jumpThirdPartyUrl, getInstantToken } from '@/common/util';
import { ASSET_PROP, EXCHANGE_TYPE } from '@/common/enumeration';
export default {
  inject: [
    'tkFlowInfo',
    'eventMessage',
    'getLabelName',
    'getExchangeTypeName',
    'getSelectAccountList'
  ],
  props: {
    // 是否展示“成功办理”
    isShowDetail: { type: Boolean, default: false },
    // 流程名称
    flowName: { type: String, default: '' },
    // 业务名称
    bizName: { type: String, default: '' },
    // 界面展示的描述信息
    resultDesc: { type: String, default: '' },
    tips: { type: String, default: '' }
  },
  data() {
    return {
      // 当前已选的账户列表
      selectedAccountsData: [],
      EXCHANGE_TYPE,
      banerConfig: $hvue.customConfig.bannerConfig
    };
  },
  computed: {
    // 判断处理中展示效果
    isAccount() {
      return this.selectedAccountsData.length == 0 && this.isShowDetail;
    },
    bannerSrc() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      const bannerData = this.banerConfig.filter(
        (a) => a.bizType === bizType
      )[0];
      if (bannerData?.imageName) {
        return require(`@/assets/images/${bannerData.imageName}.png`);
      } else {
        return '';
      }
    },
    displayBanner() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      const bannerData = this.banerConfig.filter(
        (a) => a.bizType === bizType
      )[0];
      return bannerData?.bizType;
    },
    // 中签资金预冻结取消个性化需求，佣金宝渠道展示
    displayFrozenBtn() {
      return $hvue.platform !== '0' && this.zqzzBizType;
    },
    // 中签资金预冻结取消业务标识
    zqzzBizType() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      return bizType === '010284';
    }
  },
  created() {},
  mounted() {
    // 从流程表单中获取相关的信息
    const { inProperty } = this.tkFlowInfo();
    // 判断是否传入了已选账户
    if (inProperty && inProperty.selectedAccountsData) {
      // 解构获取当前的选中账户信息
      this.selectedAccountsData = this.getSelectAccountList();
    }
  },
  methods: {
    async toBannerUrl() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      const bannerData = this.banerConfig.filter(
        (a) => a.bizType === bizType
      )[0];
      try {
        if (bannerData?.getToken) {
          const { appId, opStation, instantToken } = await getInstantToken();
          jumpThirdPartyUrl({
            url: `${bannerData.url}?app_id=${appId}&op_station=${opStation}&instant_token=${instantToken}`
          });
        } else {
          jumpThirdPartyUrl({
            url: `${bannerData.url}`
          });
        }
      } catch (err) {
        this.$TAlert({
          tips: err
        });
      }
    },
    toFrozenUrl() {
      let login_type; //1 普通交易登录 2信用交易登录
      let url = 'trans/build/lucky_query.html';
      const creaditFlag = this.selectedAccountsData.some(
        ({ assetProp }) => assetProp === ASSET_PROP.CREDIT_ACCOUNT
      );
      const ordinaryFlag = this.selectedAccountsData.some(
        ({ assetProp }) => assetProp === ASSET_PROP.ORDINARY_ACCOUNT
      );
      if (creaditFlag && !ordinaryFlag) {
        login_type = '2';
        let reqParams = {
          funcNo: '60099',
          actionType: '13',
          params: {
            yjbFuncId: '4004',
            yjbFuncParams: {
              keys: ['loginStatus']
            }
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res4004 = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res4004)}`);
        //0为未登录，1为普通交易，2为信用交易, 3为普通交易+信用交易。
        if ([2, 3].includes(res4004.loginStatus)) {
          url = 'margintrading/build/lucky_query.html';
        } else {
          let reqParams4000 = {
            funcNo: '60099',
            actionType: '13',
            params: {
              yjbFuncId: '4000',
              yjbFuncParams: {
                type: '2'
              }
            }
          };
          console.log(`请求参数为: ~~${JSON.stringify(reqParams4000)}`);
          const res4000 = $h.callMessageNative(reqParams4000);
          console.log(`请求结果为: ~~${JSON.stringify(res4000)}`);
          return;
        }
      } else {
        login_type = '1';
      }
      jumpThirdPartyUrl({
        url,
        fullScreen: 0
      });
    },
    getListDetail(item) {
      return this.getLabelName(item);
    },
    getExchangeType(type) {
      return this.getExchangeTypeName(type);
    }
  }
};
</script>
<style scoped>
div.result_banner img {
  width: 100%;
  height: 100%;
  padding: 0.16rem;
}
div.result_info > p {
  color: #4f7cff;
  margin-top: 0.2rem;
  text-align: center;
}
div.pdt0 {
  padding-top: 0;
  text-align: left;
}
div.company_box {
  -webkit-box-flex: 3;
  -webkit-flex: 3;
  -ms-flex: 3;
  flex: 3;
}
div.company_box > span.company_tit {
  font-size: 0.14rem;
  color: #969799;
  position: relative;
  top: 0.01rem;
}
</style>
