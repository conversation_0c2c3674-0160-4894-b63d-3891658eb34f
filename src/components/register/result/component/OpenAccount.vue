<template>
  <article class="content">
    <div class="result_page">
      <div class="result_tips">
        <div class="icon" :class="resultClass" />
        <h5>{{ resultTips }}</h5>
      </div>
      <div v-if="isAccount" class="result_info">
        <ul>
          <li>
            <span class="tit">{{ flowName || bizName }}</span>
            <p>
              <span class="state">{{ resultTips }}</span>
            </p>
          </li>
        </ul>
      </div>
      <div v-else class="result_info">
        <ul>
          <li v-for="(item, index) in selectAccount" :key="index">
            <span class="tit">
              {{ item.stockType }}
              {{ item.stockAccount ? ':' : '' }}
              {{ item.stockAccount }}
            </span>
            <p>
              <span class="state">{{ item.resultTips }}</span>
            </p>
          </li>
        </ul>
      </div>
      <div class="reject_txtinfo" v-if="isFail">
        <h5 class="title">原因</h5>
        <p v-for="(item, i) in errorMsg" :key="i">
          {{ `${i + 1}、` }}{{ item }}
        </p>
      </div>
      <div class="reject_txtinfo" v-else-if="resultDesc">
        <h5 class="title">温馨提示：{{ resultDesc }}</h5>
      </div>
    </div>
  </article>
</template>

<script>
import { BANDLE_STATE } from '@/common/enumeration';
export default {
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {
    // 流程名称
    flowName: { type: String, default: '' },
    // 业务名称
    bizName: { type: String, default: '' },
    // 受理成功温馨提示
    resultDesc: { type: String, default: '' },
    // 处理完成的结果集
    dataResult: {
      type: Object,
      default: () => ({
        // 处理状态
        bandleState: '',
        // 处理原因
        bandleMsg: ''
      })
    }
  },
  data() {
    return {
      // 当前已选的账户列表
      selectedAccountsData: []
    };
  },
  computed: {
    // 判断处理中展示效果
    isAccount() {
      return this.selectedAccountsData.length == 0;
    },
    // 是否失败状态
    isFail() {
      return this.dataResult.bandleState === BANDLE_STATE.FAIL;
    },
    // 处理状态下样式
    resultClass() {
      if (this.dataResult.bandleState === BANDLE_STATE.SUCCESS) {
        return 'ok';
      } else if (this.dataResult.bandleState === BANDLE_STATE.FAIL) {
        return 'fail';
      } else {
        return '';
      }
    },
    // 当前处理状态
    resultTips() {
      if (this.dataResult.bandleState === BANDLE_STATE.SUCCESS) {
        return '办理成功';
      } else if (this.dataResult.bandleState === BANDLE_STATE.FAIL) {
        return '办理失败';
      } else {
        return '';
      }
    },
    // 当前选中账户结果页处理
    selectAccount() {
      const bandleMsg = this.dataResult.bandleMsg;
      const account = this.selectedAccountsData;
      if (!this.isJSON(bandleMsg)) {
        return account.map((item) => ({
          ...item,
          resultTips: this.resultTips
        }));
      }
      // 格式化消息内容
      const msg = !bandleMsg ? [] : JSON.parse(bandleMsg);
      if (!Array.isArray(msg) && msg.accounts_proc_data) {
        return account.map((item) => {
          const account = item.stockAccount || item.fundAccount;
          const msgInfo = msg.accounts_proc_data.find((num) => {
            // 权限类会存在多个账户但单个账户唯一，开户类市场唯一
            if (num.stock_account) {
              return num.stock_account == account;
            } else if (num.stockAccount) {
              return num.stockAccount == account;
            } else if (num.fund_account) {
              return num.fund_account == account;
            } else {
              return (
                num.exchange_type == item.exchangeType ||
                num.exchangeType == item.exchangeType
              );
            }
          });
          if (Array.isArray(msgInfo)) {
            const err = msgInfo
              .map((item) => {
                if (item.proc_success != undefined) return item.proc_success;
                return this.dataResult.bandleState;
              })
              .filter((item) => item != '0');
            if (err.length > 0) {
              return {
                ...item,
                resultTips: '办理失败'
              };
            }
          }
          const tips =
            msgInfo && msgInfo.proc_success
              ? msgInfo.proc_success == '0'
                ? '办理失败'
                : '办理成功'
              : this.resultTips;
          // 拼接返回对应的错误信息
          return {
            ...item,
            resultTips: tips
          };
        });
      }
      return account.map((item) => {
        const account = item.stockAccount || item.fundAccount;
        const msgInfo = msg.find((num) => {
          // 权限类会存在多个账户但单个账户唯一，开户类市场唯一
          if (num.stock_account) {
            return num.stock_account == account;
          } else if (num.fund_account) {
            return num.fund_account == account;
          } else {
            return (
              num.exchange_type == item.exchangeType ||
              num.exchangeType == item.exchangeType
            );
          }
        });
        if (Array.isArray(msgInfo)) {
          const err = msgInfo
            .map((item) => {
              if (item.result != undefined) return item.result;
              return this.dataResult.bandleState;
            })
            .filter((item) => item != '0');
          if (err.length > 0) {
            return {
              ...item,
              resultTips: '办理失败'
            };
          }
        }
        const tips =
          msgInfo && msgInfo.result
            ? msgInfo.result == '0'
              ? '办理失败'
              : '办理成功'
            : this.resultTips;
        return {
          ...item,
          stockAccount: msgInfo?.creditStockAccount ?? item.stockAccount,
          resultTips: tips
        };
      });
    },
    // 错误原因
    errorMsg() {
      const bandleMsg = this.dataResult.bandleMsg;
      // 格式化消息内容
      const account = this.selectedAccountsData;
      if (!this.isJSON(bandleMsg)) {
        const errorMsg = bandleMsg.split('\n');
        if (!account || !account.length) {
          return errorMsg;
        }
        return account.map((_item, i) => errorMsg[i]);
      }
      const msg = !bandleMsg ? [] : JSON.parse(bandleMsg);
      if (!Array.isArray(msg) && msg.accounts_proc_data) {
        return account
          .map((item) => {
            const account = item.stockAccount || item.fundAccount;
            const msgInfo = msg.accounts_proc_data.find((num) => {
              // 权限类会存在多个账户但单个账户唯一，开户类市场唯一
              if (num.stock_account) {
                return num.stock_account == account && num.fail_reason;
              } else if (num.fund_account) {
                return num.fund_account == account && num.fail_reason;
              } else {
                return (
                  num.exchange_type == item.exchangeType ||
                  num.exchangeType == item.exchangeType
                );
              }
            });
            if (
              msgInfo &&
              !!msgInfo.proc_success &&
              msgInfo.proc_success == '0'
            ) {
              // 拼接返回对应的错误信息
              return `[${item.stockAccount || item.stockType}]${
                msgInfo.fail_reason
              }`;
            } else if (Array.isArray(msgInfo)) {
              const key = `[${item.stockAccount || item.stockType}]`;
              const info = msgInfo
                .map((item) => item.fail_reason || '')
                .filter((item) => !!item);
              return key + info.join(';\n');
            }
            return '';
          })
          .filter((item) => !!item);
      }
      return account
        .map((item) => {
          const msgInfo = msg.find((num) => {
            // 权限类会存在多个账户但单个账户唯一，开户类市场唯一
            if (num.stockAccount) {
              return num.stockAccount == item.stockAccount;
            } else {
              return num.exchangeType == item.exchangeType;
            }
          });
          if (msgInfo && !!msgInfo.result && msgInfo.result == 0) {
            // 拼接返回对应的错误信息
            return `[${item.stockAccount || item.stockType}]${msgInfo.msg}`;
          } else if (Array.isArray(msgInfo)) {
            const key = `[${item.stockAccount || item.stockType}]`;
            const info = msgInfo
              .map((item) => item.msg || '')
              .filter((item) => !!item);
            return key + info.join(';\n');
          }
          return '';
        })
        .filter((item) => !!item);
    }
  },
  created() {},
  mounted() {
    // 从流程表单中获取相关的信息
    const { inProperty } = this.tkFlowInfo();
    // 判断是否传入了已选账户
    if (inProperty && inProperty.selectedAccountsData) {
      // 解构获取当前的选中账户信息
      this.selectedAccountsData = JSON.parse(inProperty.selectedAccountsData);
    }
  },
  methods: {
    // 判断是否能够正常进行解析
    isJSON(str) {
      try {
        JSON.parse(str);
        return true;
      } catch (e) {
        return false;
      }
    }
  }
};
</script>
