<template>
  <article class="content">
    <div class="result_page">
      <div class="result_tips">
        <div class="icon" :class="resultClass" />
        <h5>{{ resultTips }}</h5>
      </div>
      <div v-if="isAccount" class="result_info">
        <ul>
          <li>
            <span class="tit">{{ flowName || bizName }}</span>
            <p>
              <span class="state">{{ resultTips }}</span>
            </p>
          </li>
        </ul>
      </div>
      <div v-else class="result_info">
        <ul>
          <li v-for="(item, index) in selectAccount" :key="index">
            <span class="tit"
              >{{ getListDetail(item) }}<span class="tit-desc">{{spanCli(item.holder_rights)}}</span></span
            >
            <p>
              <span class="state">{{ item.resultTips }}</span>
            </p>
          </li>
        </ul>
      </div>
      <div class="reject_txtinfo" v-if="isFail">
        <h5 class="title">原因</h5>
        <p v-for="(item, i) in errorMsg" :key="i">
          {{ item ? `${i + 1}、${item}` : '' }}
        </p>
      </div>
      <div v-show="displayFrozenBtn" class="result_info" @click="toFrozenUrl">
        <p>解除本次冻结资金></p>
      </div>
      <div class="result_banner" v-show="displayBanner" @click="toBannerUrl">
        <img :src="require('@/assets/images/result_banner.png')" />
      </div>
    </div>
  </article>
</template>

<script>
import { BANDLE_STATE, ASSET_PROP } from '@/common/enumeration';
import { jumpThirdPartyUrl, getInstantToken } from '@/common/util';
export default {
  inject: ['tkFlowInfo', 'eventMessage', 'getLabelName'],
  props: {
    // 流程名称
    flowName: { type: String, default: '' },
    // 业务名称
    bizName: { type: String, default: '' },
    // 处理完成的结果集
    dataResult: {
      type: Object,
      default: () => ({
        // 处理状态
        bandleState: '',
        // 处理原因
        bandleMsg: ''
      })
    }
  },
  data() {
    return {
      // 当前已选的账户列表
      selectedAccountsData: []
    };
  },
  computed: {
    // 报价回购权限开通个性化需求，佣金宝渠道展示图片
    displayBanner() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      return $hvue.platform !== '0' && bizType === '010254';
    },
    // 中签资金预冻结取消个性化需求，佣金宝渠道展示
    displayFrozenBtn() {
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      return $hvue.platform !== '0' && bizType === '010284';
    },
    // 判断处理中展示效果
    isAccount() {
      return this.selectedAccountsData.length == 0;
    },
    // 是否失败状态
    isFail() {
      return this.dataResult.bandleState === BANDLE_STATE.FAIL;
    },
    // 处理状态下样式
    resultClass() {
      if (this.dataResult.bandleState === BANDLE_STATE.SUCCESS) {
        return 'ok';
      } else if (this.dataResult.bandleState === BANDLE_STATE.FAIL) {
        return 'fail';
      } else {
        return '';
      }
    },
    // 当前处理状态
    resultTips() {
      if (this.dataResult.bandleState === BANDLE_STATE.SUCCESS) {
        return '办理成功';
      } else if (this.dataResult.bandleState === BANDLE_STATE.FAIL) {
        return '办理失败';
      } else {
        return '';
      }
    },
    // 当前选中账户结果页处理
    selectAccount() {
      const bandleMsg = this.dataResult.bandleMsg;
      const account = this.selectedAccountsData;
      if (!this.isJSON(bandleMsg)) {
        return account.map((item) => ({
          ...item,
          resultTips: this.resultTips
        }));
      }
      // 格式化消息内容
      const msg = !bandleMsg ? [] : JSON.parse(bandleMsg);
      if (!Array.isArray(msg) && msg.accounts_proc_data) {
        return msg.accounts_proc_data.map((item) => {
          // 获取到对应错误账户的选中账户
          const accountInfo = account.find((num) => {
            const account = num.stockAccount || num.fundAccount;
            // 权限类会存在多个账户但单个账户唯一，开户类市场唯一
            if (item.stock_account) {
              return item.stock_account == account;
            } else if (item.fund_account) {
              return item.fund_account == account;
            } else {
              return (
                item.exchange_type == num.exchangeType ||
                item.exchangeType == num.exchangeType
              );
            }
          });
          const tips = item.proc_success
            ? item.proc_success == '0'
              ? '办理失败'
              : '办理成功'
            : this.resultTips;
          return { ...item, ...accountInfo, resultTips: tips };
        });
      }
      return account.map((item) => {
        const account = item.stockAccount || item.fundAccount;
        const msgInfo = msg.find((num) => {
          // 权限类会存在多个账户但单个账户唯一，开户类市场唯一
          if (num.stock_account) {
            return num.stock_account == account;
          } else if (num.fund_account) {
            return num.fund_account == account;
          } else {
            return (
              num.exchange_type == item.exchangeType ||
              num.exchangeType == item.exchangeType
            );
          }
        });
        const tips =
          msgInfo && msgInfo.result
            ? msgInfo.result == '0'
              ? '办理失败'
              : '办理成功'
            : this.resultTips;
        return {
          ...item,
          resultTips: tips
        };
      });
    },
    // 错误原因
    errorMsg() {
      const bandleMsg = this.dataResult.bandleMsg;
      const account = this.selectedAccountsData;
      if (!this.isJSON(bandleMsg)) {
        const errorMsg = bandleMsg.split('\n');
        return account.map((_item, i) => errorMsg[i]);
      }
      const msg = !bandleMsg ? [] : JSON.parse(bandleMsg);
      if (!Array.isArray(msg) && msg.accounts_proc_data) {
        return msg.accounts_proc_data
          .map((item) => {
            // 获取到对应错误账户的选中账户
            const accountInfo = account.find((num) => {
              const account = num.stockAccount || num.fundAccount;
              // 权限类会存在多个账户但单个账户唯一，开户类市场唯一
              if (item.stock_account) {
                return item.stock_account == account;
              } else if (item.fund_account) {
                return item.fund_account == account;
              } else {
                return (
                  item.exchange_type == num.exchangeType ||
                  item.exchangeType == num.exchangeType
                );
              }
            });
            if (item.proc_success == '0') {
              return `[${accountInfo.stockAccount || accountInfo.stockType}]${
                item.fail_reason
              }`;
            }
            return '';
          })
          .filter((item) => !!item);
      }
      return account
        .map((item) => {
          const account = item.stockAccount || item.fundAccount;
          const msgInfo = msg.find((num) => {
            // 权限类会存在多个账户但单个账户唯一，开户类市场唯一
            if (num.stock_account) {
              return num.stock_account == account;
            } else if (num.fund_account) {
              return num.fund_account == account;
            } else {
              return (
                num.exchange_type == item.exchangeType ||
                num.exchangeType == item.exchangeType
              );
            }
          });
          if (msgInfo && !!msgInfo.result && msgInfo.result == 0) {
            // 拼接返回对应的错误信息
            return `[${item.stockAccount || item.stockType}]${msgInfo.msg}`;
          }
          return '';
        })
        .filter((item) => !!item);
    }
  },
  created() {},
  mounted() {
    // 从流程表单中获取相关的信息
    const { inProperty } = this.tkFlowInfo();
    // 判断是否传入了已选账户
    if (inProperty && inProperty.selectedAccountsData) {
      // 解构获取当前的选中账户信息
      this.selectedAccountsData = JSON.parse(inProperty.selectedAccountsData);
    }
  },
  methods: {
    // 判断是否能够正常进行解析
    isJSON(str) {
      try {
        JSON.parse(str);
        return true;
      } catch (e) {
        return false;
      }
    },
    async toBannerUrl() {
      try {
        const { appId, opStation, instantToken } = await getInstantToken();
        jumpThirdPartyUrl({
          url: `${$hvue.customConfig.thirdPartyUrl.bjhgFinancialList}?app_id=${appId}&op_station=${opStation}&instant_token=${instantToken}`
        });
      } catch (err) {
        this.$TAlert({
          tips: err
        });
      }
    },
    getListDetail(item) {
      return this.getLabelName(item);
    },
    spanCli(val) {
      if (val == '7') return '一类权限';
      if (val == '8') return '二类权限';
      return '';
    },
    toFrozenUrl() {
      let login_type; //1 普通交易登录 2信用交易登录
      let url = 'trans/build/lucky_query.html';
      const creaditFlag = this.selectedAccountsData.some(
        ({ assetProp }) => assetProp === ASSET_PROP.CREDIT_ACCOUNT
      );
      const ordinaryFlag = this.selectedAccountsData.some(
        ({ assetProp }) => assetProp === ASSET_PROP.ORDINARY_ACCOUNT
      );
      if (creaditFlag && !ordinaryFlag) {
        login_type = '2';
        url = 'margintrading/build/lucky_query.html';
      } else {
        login_type = '1';
      }
      jumpThirdPartyUrl({
        url,
        fullScreen: 0
      });
    }
  }
};
</script>
<style scoped>
div.result_banner img {
  width: 100%;
  height: 100%;
  padding: 0.16rem;
}
div.result_info > p {
  color: #4f7cff;
  margin-top: 0.2rem;
  text-align: center;
}
.tit-desc {
  margin-left: 4px;
  color: #d1cccc;
}
</style>
