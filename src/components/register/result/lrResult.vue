<template>
  <section class="main fixed white_bg" style="position: fixed">
    <t-header :show-back="false" />
    <article class="content">
      <div class="result_page">
        <div class="result_tips">
          <div v-if="isAuditIng" class="icon ing" />
          <h5 v-if="isAuditIng">您的预约资料已提交审核</h5>
          <div v-if="isAppointment" class="icon ok" />
          <h5 v-if="isAppointment">恭喜您预约成功</h5>
          <div class="result_step">
            <ul v-if="isAuditIng">
              <li class="s1 off"><i></i><span>申请提交</span></li>
              <li class="s2 on"><i></i><span>审核中</span></li>
              <li class="s3"><i></i><span>预约成功</span></li>
            </ul>
            <ul v-if="isAppointment">
              <li class="s1 off"><i></i><span>申请提交</span></li>
              <li class="s2 off"><i></i><span>审核中</span></li>
              <li class="s3 on"><i></i><span>预约成功</span></li>
            </ul>
          </div>
          <p v-if="isAppointment">
            您的两融开户预约资料已审核通过，请在<strong class="ared">{{
              dateTime
            }}</strong
            >前持有效身份证件亲临我司营业部办理正式开户手续，过期需重新预约。特别提醒您，在办理正式开户时，仍需满足我司融资融券开户准入要求。如有疑问可拨打客服热线95310进行咨询。
          </p>
          <p v-else>
            您的两融开户预约资料已提交审核，审核通过后请您前往线下营业部进行开户，审核结果会在1个交易日内通过短信通知您。特别提醒您，在办理正式开户时，仍需满足我司融资融券开户准入要求。如有疑问可拨打客服热线95310进行咨询。
          </p>
        </div>
      </div>
    </article>
    <footer class="footer white_bg">
      <div class="ce_btn black">
        <a v-if="isAppointment" class="p_button" @click="toYYB">查找营业部</a>
        <a class="p_button border" @click="back">返回</a>
      </div>
    </footer>
  </section>
</template>

<script>
import {
  BANDLE_STATE,
  PROCESS_STATUS,
  TASK_TYPE,
  TASK_STATUS
} from '@/common/enumeration';
import { EVENT_NAME } from '@/common/formEnum';
import {
  returnVisitGenerate,
  visitClientQuery,
  flowSubmit,
  flowQueryIns,
  addClientCritMark,
  critMarkQuery,
  flowEnd
} from '@/service/service';
import { QueryExpireDate } from '@/service/lrService';
import { wakeLoginApp } from '@/common/util';

export default {
  name: 'lrResult',
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {},
  data() {
    return {
      showDetail: false,
      bizType: '',
      bizName: '',
      flowName: '',
      formId: '',
      taskStatus: '',
      taskType: '',
      BANDLE_STATE: BANDLE_STATE,
      TASK_TYPE,
      TASK_STATUS,
      dateTime: '',
      dataResult: () => {}
    };
  },
  computed: {
    isApp() {
      return $hvue.platform !== '0';
    },

    isAuditIng() {
      // 是否未审核中
      if (
        this.taskType === '2' ||
        this.taskType === '4' ||
        this.taskType === '1' ||
        this.taskType === ''
      ) {
        return true;
      } else {
        return false;
      }
    },

    isAppointment() {
      // 是否发起跑配审核完成了
      if (this.taskType === '3') {
        return true;
      } else {
        return false;
      }
    }
  },
  created() {
    $h.setSession('onResultPage', true);
  },
  mounted() {
    this.renderingView();
    this.eventMessage(this, EVENT_NAME.BACK_BTN, { display: false });
  },
  methods: {
    preStep() {
      this.eventMessage(this, EVENT_NAME.PREV_FLOW);
    },
    back() {
      if (this.isApp && this.needBackApp) {
        this.$store.commit('user/setUserInfo', null);
        localStorage.removeItem('vuex');
        sessionStorage.clear();
        // wakeLoginApp();
        $h.callMessageNative({
          funcNo: '50114',
          moduleName: $hvue.customConfig.moduleName
        });
      } else {
        this.eventMessage(this, EVENT_NAME.TO_INDEX);
      }
    },

    toYYB() {
      if ($hvue.platform === '0') {
        window.location.href =
          $hvue.customConfig.thirdPartyUrl.businessDepartment;
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          params: {
            url: $hvue.customConfig.thirdPartyUrl.businessDepartment,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      }
    },
    // 查询介绍文案
    queryExpireDate() {
      const flowToken = sessionStorage.getItem('TKFlowToken');
      QueryExpireDate({ flowToken })
        .then((res) => {
          res.code == 0 && res.data && (this.dateTime = res.data.expireDate);
        })
        .catch((err) => {
          _hvueAlert({
            mes: '请求失败，请稍后重试',
            callback: () => {
              $h.callMessageNative({
                funcNo: '50114',
                moduleName: $hvue.customConfig.moduleName
              });
            }
          });
        });
    },
    renderingView() {
      const { inProperty } = this.tkFlowInfo();
      const flowToken = sessionStorage.getItem('TKFlowToken');
      // const accArr = [w
      //   PROCESS_STATUS.ACCEPT_COMPLETED, //受理完成
      //   PROCESS_STATUS.COMPLETED //办理完成
      // ]; // 配置需要提交受理结果的状态
      flowQueryIns({ flowToken })
        .then((res) => {
          this.bizType = res.data.bizType;
          this.bizName = res.data.bizName;
          this.flowName = res.data.flowName;
          this.formId = res.data.formId;
          this.taskStatus = res.data.taskStatus;
          this.taskType = res.data.taskType || '';
          if (this.isAppointment) this.queryExpireDate();
          // 进入页面查询状态，只有当前流程状态为受理中的情况，进入业务，
          // 直接提交给柜台的调用flowSubmit，跑批同步提交调用流程下一步
          // 0为受理中状态
          if (res.data.status === '0') {
            // 流程受理中状态
            // 跑批同步提交
            this.eventMessage(this, EVENT_NAME.NEXT_STEP);
            window.tkFlowNextCallback = (data) => {
              if (data.code === 0) {
                this.showDetail = true;
              } else {
                this.showDetail = false;
                _hvueAlert({
                  mes: '提交失败，请稍后重试',
                  callback: () => {
                    $h.callMessageNative({
                      funcNo: '50114',
                      moduleName: $hvue.customConfig.moduleName
                    });
                  }
                });
              }
            };
          } else {
            this.showDetail = true;
            // 非受理中状态进入结果页
            return;
          }
        })
        .catch((err) => {
          this.showDetail = false;
          _hvueAlert({
            mes: '请求失败，请稍后重试',
            callback: () => {
              $h.callMessageNative({
                funcNo: '50114',
                moduleName: $hvue.customConfig.moduleName
              });
            }
          });
        });
    }
  }
};
</script>
