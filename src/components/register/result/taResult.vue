<template>
  <section
    class="main fixed white_bg"
    style="position: fixed"
  >
    <t-header :show-back="false" />
    <article
      v-if="isSubmitCounter"
      class="content"
    >
      <div class="result_page">
        <div class="result_tips">
          <div
            class="icon"
            :class="resultClass"
          />
          <h5>{{ resultTips }}</h5>
          <p v-if="dataResult.bandleState && nowDate !== ''">
            提交时间：{{ nowDate }}
          </p>
        </div>
        <div class="result_info">
          <ul>
            <li style="width: 100%">
              <div
                class="tit"
                style="width: 100%"
              >
                <div
                  style="
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                  "
                >
                  <span style="font-weight: 500">{{ TAaccount }}</span><span class="state">{{
                    maintFlag === '0' ? '解绑' : '绑定'
                  }}</span>
                </div>
                <div class="state">
                  对应账户（{{ account }}）
                </div>
                <!-- <p style="font-weight: bold;">{{ TAaccount }} </p>
                <p style="text-align: left">对应账户：</p>{{ account }}</p> -->
              </div>
            </li>
          </ul>
        </div>
        <div
          v-show="failReason !== ''"
          class="reject_txtinfo"
        >
          <h5 class="title">
            原因
          </h5>
          <p>{{ failReason }}</p>
        </div>
      </div>
    </article>
    <article
      v-else
      class="content"
    >
      <div class="result_page">
        <div class="result_tips">
          <div class="icon ing" />
          <h5><span v-if="showDetail">成功提交，</span>处理中</h5>
          <p v-if="showDetail">
            {{ resultDesc }}
          </p>
          <!-- <p v-if="showVisit" style="color: #fa443a">
            您有一份待完成的问卷，请及时填写，否则将影响业务办理结果。
          </p> -->
        </div>
        <div
          v-if="showDetail"
          class="result_info"
        >
          <ul>
            <li style="width: 100%">
              <div
                class="tit"
                style="width: 100%"
              >
                <div
                  style="
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                  "
                >
                  <span style="font-weight: 500">{{ TAaccount }}</span><span class="state">{{
                    maintFlag === '0' ? '解绑中' : '绑定中'
                  }}</span>
                </div>
                <div class="state">
                  对应账户（{{ account }}）
                </div>
                <!-- <p style="font-weight: bold;">{{ TAaccount }} </p>
                <p style="text-align: left">对应账户：</p>{{ account }}</p> -->
              </div>
            </li>
          </ul>
        </div>
      </div>
    </article>
    <footer class="footer white_bg">
      <div class="ce_btn black">
        <a
          class="p_button border"
          @click="back"
        >返回</a>
      </div>
    </footer>
  </section>
</template>

<script>
import {
  BANDLE_STATE,
  PROCESS_STATUS,
  TASK_TYPE,
  TASK_STATUS
} from '@/common/enumeration';
import { EVENT_NAME } from '@/common/formEnum';
import {
  returnVisitGenerate,
  visitClientQuery,
  flowSubmit,
  flowQueryIns,
  addClientCritMark,
  critMarkQuery,
  flowEnd
} from '@/service/service';
import { wakeLoginApp } from '@/common/util';

const exchangeTypeName = [
  { text: '深', value: '2' },
  { text: '沪', value: '1' }
];
const holderKindTypeName = [
  { text: 'A', value: '0' },
  { text: '市场内基金', value: '1' }
];

export default {
  name: 'TaResult',
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {
    resultDesc: {
      type: String,
      default: ''
    },
    isSubmitCounter: {
      // 是否直接提交柜台
      type: Boolean,
      default: true
    },
    needBackApp: {
      type: Boolean,
      default: false
    },
    isNeedVisit: {
      type: Boolean,
      default: false
    },
    subjectNo: {
      // 试卷编号
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      bizType: '',
      bizName: '',
      formId: '',
      BANDLE_STATE: BANDLE_STATE,
      TASK_TYPE,
      TASK_STATUS,
      dataResult: {},
      nowDate: '',
      showVisit: false,
      maintFlag: '0',
      outProdtaAccData: [],
      selectedAccountsData: [],
      TAaccount: '',
      account: '',
      failReason: '',
      showDetail: false
    };
  },
  computed: {
    isApp() {
      return $hvue.platform !== '0';
    },
    resultClass() {
      if (this.dataResult.bandleState === BANDLE_STATE.SUCCESS) {
        return 'ok';
      } else if (
        this.dataResult &&
        this.dataResult.bandleState === BANDLE_STATE.FAIL
      ) {
        return 'fail';
      } else {
        return 'ing';
      }
    },
    resultTips() {
      if (
        this.dataResult &&
        this.dataResult.bandleState === BANDLE_STATE.SUCCESS
      ) {
        return '办理成功';
      } else if (
        this.dataResult &&
        this.dataResult.bandleState === BANDLE_STATE.FAIL
      ) {
        return '办理失败';
      } else {
        return '提交中';
      }
    }
  },
  created() {
    $h.setSession('onResultPage', true);
  },
  mounted() {
    this.renderingView();
    if (this.isNeedVisit) {
      critMarkQuery({
        markType: '3',
        flowToken: sessionStorage.getItem('TKFlowToken')
      }).then((res) => {
        if (res.data.critMarkFlag === false) {
          this.$TAlert({
            title: '重要提示',
            tips: '权限开通30日内我司会向您推送回访问卷，请您务必配合完成。请您结合本人风险承受能力问卷测评结果，以及投资偏好、承受损失等因素，审慎参与。若我司从业人员存在接受全权委托、违规操作投资者账户等行为，请及时拨打95310反馈。',
            confirm: () => {
              addClientCritMark({
                flowToken: sessionStorage.getItem('TKFlowToken'),
                markType: '3',
                markContent:
                  '权限开通30日内我司会向您推送回访问卷，请您务必配合完成。请您结合本人风险承受能力问卷测评结果，以及投资偏好、承受损失等因素，审慎参与。若我司从业人员存在接受全权委托、违规操作投资者账户等行为，请及时拨打95310反馈。',
                confirmFlag: '1'
              }).then((res) => {});
            }
          });
        }
      });
    }
    this.eventMessage(this, EVENT_NAME.BACK_BTN, { display: false });
    // if (this.isNeedVisit) {
    //   visitClientQuery({
    //     flowToken: sessionStorage.getItem('TKFlowToken')
    //   }).then((res) => {
    //     let state = res.data.userVisitStatus;
    //     if (state === 0) {
    //       returnVisitGenerate({
    //         flowToken: sessionStorage.getItem('TKFlowToken'),
    //         subjectNo: this.subjectNo
    //       }).then(() => {
    //         this.showVisit = true;
    //       });
    //     } else if (state === 1) {
    //       this.showVisit = true;
    //     } else if (state === 2) {
    //       // 已完成
    //       this.showVisit = false;
    //     }
    //   });
    // }
  },
  methods: {
    toQuestion() {
      this.$router.replace({
        name: 'questionVisitDetail',
        query: {
          subjectNo: this.subjectNo,
          biz: this.bizType,
          formId: this.formId
        }
      });
    },
    preStep() {
      this.eventMessage(this, EVENT_NAME.PREV_FLOW);
    },
    back() {
      if (this.isApp && this.needBackApp) {
        this.$store.commit('user/setUserInfo', null);
        localStorage.removeItem('vuex');
        sessionStorage.clear();
        // wakeLoginApp();
        $h.callMessageNative({
          funcNo: '50114',
          moduleName: $hvue.customConfig.moduleName
        });
      } else {
        this.eventMessage(this, EVENT_NAME.TO_INDEX);
      }
    },

    getAccountName(assetProp, exchangeType, holderKind) {
      let exchangeName = exchangeTypeName.filter(
        (item) => item.value === exchangeType
      )[0].text;
      let holderKindName = holderKindTypeName.filter(
        (item) => item.value === holderKind
      )[0].text;
      return `${exchangeName}${holderKindName}`;
    },

    renderingView() {
      // window.tkFlowNextCallback = (data) => {
      //   console.log(data);
      // };
      // this.eventMessage(this, EVENT_NAME.NEXT_STEP);
      // return;
      const { inProperty } = this.tkFlowInfo();
      if (inProperty?.selectedAccountsData) {
        this.selectedAccountsData =
          JSON.parse(inProperty?.selectedAccountsData) || [];
      }
      if (inProperty?.outProdtaAccData) {
        this.outProdtaAccData = JSON.parse(inProperty?.outProdtaAccData) || [];
      }
      if (inProperty?.maintFlag) {
        this.maintFlag = inProperty?.maintFlag;
      }
      if (this.maintFlag === '0') {
        // 解绑
        this.TAaccount =
          this.outProdtaAccData[0].fundCompanyStr +
          '：' +
          this.outProdtaAccData[0].fund_company_account;
        let accountName = this.getAccountName(
          this.outProdtaAccData[0].asset_prop,
          this.outProdtaAccData[0].exchange_type,
          this.outProdtaAccData[0].holder_kind
        );
        this.account =
          accountName + '：' + this.outProdtaAccData[0].stock_account;
      } else {
        // 开通
        this.TAaccount =
          this.outProdtaAccData[0].fundCompanyStr +
          '：' +
          this.outProdtaAccData[0].fund_company_account;
        let accountName = this.getAccountName(
          this.selectedAccountsData[0].asset_prop,
          this.selectedAccountsData[0].exchange_type,
          this.selectedAccountsData[0].holder_kind
        );
        this.account =
          accountName + '：' + this.selectedAccountsData[0].stock_account;
      }
      const flowToken = sessionStorage.getItem('TKFlowToken');
      flowSubmit({ flowToken })
        .then(({ code }) => {
          if (code === 0) {
            this.$set(this.dataResult, 'bandleState', BANDLE_STATE.SUCCESS);
            this.nowDate = new Date().format('yyyy-MM-dd hh:mm:ss');
          } else {
            return Promise.reject('办理失败');
          }
        })
        .catch((err) => {
          this.showDetail = true;
          this.$set(this.dataResult, 'bandleState', BANDLE_STATE.FAIL);
          this.failReason = err;
          // _hvueToast({ mes: err });
        });

      // const accArr = [
      //   PROCESS_STATUS.ACCEPT_COMPLETED, //受理完成
      //   PROCESS_STATUS.COMPLETED //办理完成
      // ]; // 配置需要提交受理结果的状态
      /* flowQueryIns({ flowToken })
        .then((res) => {
          this.bizType = res.data.bizType;
          this.bizName = res.data.bizName;
          this.formId = res.data.formId;
          // 进入页面查询状态，只有当前流程状态为受理中的情况，进入业务，
          // 直接提交给柜台的调用flowSubmit，跑批同步提交调用流程下一步
          // 0为受理中状态
          if (res.data.status === '0') {
            // 流程受理中状态
            if (this.isSubmitCounter) {
              // 直接提交柜台
              this.showDetail = true;
              flowSubmit({ flowToken })
                .then((data) => {
                  this.showDetail = true;
                  this.dataResult = data.data[0];
                  this.nowDate = new Date().format('yyyy-MM-dd hh:mm:ss');
                })
                .catch((err) => {});
            } else {
              // 跑批同步提交
              this.eventMessage(this, EVENT_NAME.NEXT_STEP);
              window.tkFlowNextCallback = (data) => {
                if (data.code === 0) {
                  this.showDetail = true;
                } else {
                  this.showDetail = false;
                  _hvueAlert({
                    mes: '提交失败，请稍后重试',
                    callback: () => {
                      $h.callMessageNative({
                        funcNo: '50114',
                        moduleName: 'open'
                      });
                    }
                  });
                }
              };
            }
          } else {
            this.showDetail = true;
            // 非受理中状态进入结果页
            return;
          }
        })
        .catch((err) => {
          this.showDetail = false;
          _hvueAlert({
            mes: '请求失败，请稍后重试',
            callback: () => {
              $h.callMessageNative({
                funcNo: '50114',
                moduleName: 'open'
              });
            }
          });
        });*/
    }
  }
};
</script>
