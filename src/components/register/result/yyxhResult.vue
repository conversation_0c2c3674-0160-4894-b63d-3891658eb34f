<template>
  <section class="main fixed white_bg" style="position: fixed">
    <t-header />
    <article class="content">
      <div class="result_page">
        <div class="result_tips">
          <div class="icon ing" />
          <h5><span v-if="showDetail">成功提交，</span>处理中</h5>
          <p v-if="showDetail" ref="resultDesc"></p>
        </div>
        <div class="result_info">
          <ul>
            <li>
              <span class="tit">预约转销户</span>
              <p><span class="state">处理中</span></p>
            </li>
          </ul>
        </div>
      </div>
    </article>
    <footer class="footer white_bg">
      <div class="ce_btn black">
        <a class="p_button" @click="taskCannel">撤销转销户预约</a>
        <a class="p_button border" @click="back">返回</a>
      </div>
    </footer>
  </section>
</template>

<script>
import { BANDLE_STATE } from '@/common/enumeration';
import { EVENT_NAME } from '@/common/formEnum';
import {
  flowQueryIns,
  detainTaskCancel,
  detainTaskInfoQry
} from '@/service/service';

export default {
  name: 'yyxhResult',
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {
    resultDesc: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dataResult: {},
      showDetail: false,
      BANDLE_STATE,
      flowInsId: ''
    };
  },
  watch: {},
  computed: {
    isApp() {
      return $hvue.platform !== '0';
    },
    resultClass() {
      // 0挽留失败，1挽留成功，2无需挽留
      if (['1'].includes(this.dataResult.conclusion)) {
        return 'ok';
      } else if (['0', '2'].includes(this.dataResult.conclusion)) {
        return 'ok';
      } else {
        return 'ing';
      }
    },
    resultTips() {
      // 0挽留失败，1挽留成功，2无需挽留
      if (['1'].includes(this.dataResult.conclusion)) {
        return '转销户预约撤销成功';
      } else if (['0', '2'].includes(this.dataResult.conclusion)) {
        return '转销户预约初审通过';
      } else {
        return '办理中';
      }
    }
  },
  created() {
    $h.setSession('onResultPage', true);
    this.$store.commit('flow/setWhiteBg', true);
  },
  mounted() {
    this.renderingView();
  },
  methods: {
    renderingView() {
      const { inProperty } = this.tkFlowInfo();
      if (inProperty?.selectedAccountsData) {
        this.selectedAccountsData =
          JSON.parse(inProperty?.selectedAccountsData) || [];
      }
      const flowToken = sessionStorage.getItem('TKFlowToken');
      // 配置需要提交受理结果的状态
      flowQueryIns({ flowToken })
        .then((res) => {
          this.flowInsId = res.data.id;
          // 进入页面查询状态，只有当前流程状态为受理中的情况，进入业务，
          // 直接提交给柜台的调用flowSubmit，跑批同步提交调用流程下一步
          // 0为受理中状态
          if (res.data.status === '0') {
            // 流程受理中状态
            this.eventMessage(this, EVENT_NAME.NEXT_STEP);
            window.tkFlowNextCallback = (data) => {
              if (data.code === 0) {
                this.showDetail = true;
              } else {
                this.showDetail = false;
                _hvueAlert({
                  mes: '提交失败，请稍后重试',
                  callback: () => {
                    $h.callMessageNative({
                      funcNo: '50114',
                      moduleName: $hvue.customConfig.moduleName
                    });
                  }
                });
              }
            };
          } else {
            // 非受理中状态进入结果页
            this.showDetail = true;
          }

          this.$nextTick(() => {
            this.$refs.resultDesc.innerHTML = this.resultDesc;
          });
        })
        .catch((err) => {
          this.showDetail = false;
          _hvueAlert({
            mes: '请求失败，请稍后重试',
            callback: () => {
              $h.callMessageNative({
                funcNo: '50114',
                moduleName: $hvue.customConfig.moduleName
              });
            }
          });
        });
    },
    taskCannel() {
      const _this = this;
      _this.$TAlert({
        title: '提示',
        tips: '点击"确认"，将为您撤销本次转销户预约。如果您有任何意见或建议，欢迎致电95310向我们反馈！',
        hasCancel: true,
        confirmBtn: '确认',
        cancelBtn: '取消',
        confirm: () => {
          detainTaskCancel({
            preFlowInsId: this.flowInsId,
            flowToken: sessionStorage.getItem('TKFlowToken')
          })
            .then(({ code, msg }) => {
              if (code === 0) {
                _this.eventMessage(_this, EVENT_NAME.TO_INDEX);
              } else {
                return Promise.reject(msg);
              }
            })
            .catch((err) => {
              _this.$TAlert({
                tips: err
              });
            });
        }
      });
    },
    back() {
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    }
  }
};
</script>
