<template>
  <article class="content">
    <div class="com_title">
      <h5>
        您当前在我司的前10日日均资产为：<span class="imp_span">{{
          averageAsset
        }}</span
        >。综合评估您的资产状况后，您可以申请开通以下类别的交易权限：
      </h5>
    </div>
    <ul class="select_levellist">
      <li @click="chooseRights('7', classOneOptional)">
        <div class="tit">
          <span
            class="icon_check"
            style="margin: 0 20px 0 0"
            :class="[
              chooseHolderRights === '7' ? 'checked' : '',
              classOneOptional === '0' ? 'disabled' : ''
            ]"
          ></span>
          <div>
            <h5>一类：可交易北交所、创新层、基础层股票</h5>
            <div class="cont">
              申请权限开通前10个交易日，本人名下证券账户和资金账户内的资产达到日均人民币200万元以上且满足其他适当性要求，并通过我司综合资产评估。
            </div>
          </div>
        </div>
      </li>
      <li @click="chooseRights('8', classTwoOptional)">
        <div class="tit">
          <span
            class="icon_check"
            style="margin: 0 20px 0 0"
            :class="[
              chooseHolderRights === '8' ? 'checked' : '',
              classTwoOptional === '0' ? 'disabled' : ''
            ]"
          ></span>
          <div>
            <h5>二类：可交易北交所、创新层股票</h5>
            <div class="cont">
              申请权限开通前10个交易日，本人名下证券账户和资金账户内的资产达到日均人民币100万元以上且满足其他适当性要求，并通过我司综合资产评估。
            </div>
          </div>
        </div>
      </li>
    </ul>
  </article>
</template>

<script>
import { accPermissionSelect } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'rightSelectionXSB',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      chooseHolderRights: '',
      averageAsset: '',
      classOneAuthorityOpen: '0', // 是否已开通 1 已开通 0 未开通
      classOneOptional: '0', // 是否可选 1 可选 0 不可选
      classTwoAuthorityOpen: '0',
      classTwoOptional: '0'
    };
  },
  computed: {},
  mounted() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
    this.renderingView();
  },
  methods: {
    renderingView() {
      accPermissionSelect({
        flowToken: sessionStorage.getItem('TKFlowToken')
      }).then((res) => {
        this.averageAsset = res.data.averageAsset;
        this.classOneAuthorityOpen = res.data.classOneAuthorityOpen;
        this.classOneOptional = res.data.classOneOptional;
        this.classTwoAuthorityOpen = res.data.classTwoAuthorityOpen;
        this.classTwoOptional = res.data.classTwoOptional;
      });
    },

    chooseRights(val, disabled) {
      if (disabled === '0') {
        return;
      }
      this.chooseHolderRights = val;
      if (this.chooseHolderRights) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 1 });
        this.$emit('change', {
          choose_holder_rights: this.chooseHolderRights
        });
      } else {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
      }
    }
  }
};
</script>
