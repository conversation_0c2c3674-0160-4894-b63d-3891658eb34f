<template>
  <div v-show="loadingEd">
    <article v-if="profFlag === '1'" class="content">
      <div class="notice_box risk">
        <div class="pic">
          <img src="@/assets/images/test_noticeimg01.png" />
        </div>
        <h5>专业投资者无需测评</h5>
      </div>
    </article>

    <article v-else class="content top_border">
      <div class="com_title">
        <h5>尊敬的投资者，您的风险承受能力等级为</h5>
      </div>
      <div class="test_rsult">
        <div class="test_infobox">
          <!-- <div class="test_inner">
            <div class="info">
              <h5>{{ riskResult.riskLevelName }}</h5>
              <p>
                本次风险承受能力评定结果有效期至:{{ riskResult.corpEndDate }}
              </p>
            </div>
            <div class="test_level_2">
              <div class="txt">
                <h5>
                  <strong>C{{ riskResult.corpRiskLevel }}</strong>
                </h5>
              </div>
            </div>
          </div> -->
          <div class="test_inner">
            <div class="info">
              <h5>{{ riskResult.riskLevelName }}</h5>
              <p v-if="minRankFlag !== '1' && riskResult.corpEndDate !== '0'">
                您适合购买或接受{{
                  riskResult.enServiceRiskLevel
                }}风险等级的产品或服务
              </p>
              <p v-if="minRankFlag === '1' && riskResult.corpEndDate !== '0'">
                您仅允许购买或接受{{
                  riskResult.enServiceRiskLevel
                }}风险等级的产品或服务
              </p>
              <p
                v-if="riskResult.corpEndDate === '0'"
                style="font-size: 12px; color: #999999"
              >
                非最新版本，请重新测评
              </p>
              <p
                v-if="riskResult.corpEndDate === '0'"
                style="font-size: 12px; color: #999999"
              >
                本次风险承受能力评定结果有效期为2年
              </p>
            </div>
            <div class="test_level_2">
              <div class="txt">
                <h5>
                  <strong>C{{ riskResult.corpRiskLevel }}</strong>
                </h5>
              </div>
            </div>
          </div>
        </div>
        <div v-if="riskResult.corpEndDate !== '0'" class="appro_info">
          <ul>
            <li>
              <span class="tit">有效期至</span>
              <p :class="{ ared: expired }">
                {{ riskResult.corpEndDate }}
                <span v-if="expired">{{ expiredTips }}</span>
              </p>
            </li>
          </ul>
          <div class="s_form_tips">
            <p>
              如您参与了公募基金投顾业务，需确保风险测评的剩余有效期超过1年。
            </p>
          </div>
        </div>
        <div class="appro_tips">
          <p>温馨提示:</p>
          <p>
            1.请仔细阅读<a class="com_link" @click="clickInput('compareTable')"
              >《风险等级对照表》</a
            >中产品或服务风险目录及适配规定。
          </p>
          <p>
            2.为确保投资者适当性测评结果的有效性，投资者在参与适当性测评时，一个交易日内测评次数仅限{{
              dailyPermitTimes
            }}次，任意连续{{ limitTimeDict }}个自然日内累计测评次数不超过{{
              maxTimesDict
            }}次。请您根据自身实际情况如实填写，审慎测评。
          </p>
        </div>
      </div>
    </article>
    <div v-show="showQusetion">
      <div class="dialog_overlay" style="display: block"></div>
      <div class="dialog_box" style="display: block">
        <div class="dialog_cont">
          <div>
            <div class="test_box">
              <h5 class="title">
                {{ questionTitle }}
              </h5>
              <div class="radio_list">
                <span
                  v-for="(item, index) in qusetion"
                  :key="index"
                  class="icon_radio"
                  :class="{ checked: item.checked }"
                  @click="chooseQuestion(item)"
                  >{{ item.optionContent }}</span
                >
              </div>
            </div>
          </div>
        </div>
        <div class="dialog_btn">
          <a class="cancel" @click="showQusetion = false">取消</a>
          <a @click="submitQuestion">确认</a>
        </div>
      </div>
    </div>
    <compareTable ref="compareTable" />
  </div>
</template>

<script>
import { riskQuery } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import compareTable from './components/compareTable.vue';
import {
  // answerHisDetail,
  questionQuery,
  questionSave
  // getQuestionNumber,
} from '@/service/service';
import {
  riskFlagQuery,
  answerHisDetail
  // getQuestionNumber,
} from '@/service/riskService';
import HmosUtil from '@/common/HmosUtil';
const hmosUtil = new HmosUtil({});

function formatDate(dateString) {
  var year = dateString.substring(0, 4);
  var month = dateString.substring(4, 6);
  var day = dateString.substring(6, 8);
  return year + '-' + month + '-' + day;
}
export default {
  name: 'RiskResultPreview',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: { compareTable },
  data() {
    return {
      loadingEd: false,
      profFlag: '',
      questionId: '',
      questionTitle: '',
      qusetion: [],
      dayTime: 0,
      showQusetion: false,
      paperNo: '',
      paperVersion: '',
      minRankFlag: '',
      riskResult: {
        investAdvice: '',
        riskLevelName: '',
        corpEndDate: '',
        limitList: []
      },

      oldPaperAnswer: '',
      oldVersion: '',

      validDateFlag: '',
      validTimesFlag: '',
      limitTime: '',
      maxTimes: '',

      dailyPermitTimes: '',
      limitTimeDict: '',
      maxTimesDict: ''
    };
  },
  computed: {
    expired() {
      if (this.validDateFlag === '1' || this.validDateFlag === '2') {
        console.log('日期已过期');
        return true;
      } else {
        console.log('日期未过期');
        return false;
      }
    },

    expiredTips() {
      if (this.validDateFlag === '2') {
        console.log('日期已过期');
        return '已过期';
      } else if (this.validDateFlag === '1') {
        console.log('日期即将过期');
        return '即将过期';
      } else {
        return '';
      }
    }
  },
  mounted() {
    const { from = '' } = $h.getSession('pageParams') || {};
    console.log('from', from);
    hmosUtil.setTitle();
    this.renderingView();
    const { inProperty } = this.tkFlowInfo();
    const profFlag = inProperty.profFlag;
    this.profFlag = profFlag;
    if (this.profFlag === '1') {
      if ($hvue.platform === '0' && $h.getSession('appId') !== 'yjbwx') {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
        return;
      }
      this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
        text: '返回',
        display: true,
        btnStatus: 2,
        data: () => {
          if ($hvue.platform != 0) {
            // 原生APP
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          } else {
            if ($h.getSession('channelType') === '2005000000000') {
              callNativeHandler(
                'JSWangTingEvent',
                {
                  action: 'CloseWebVC',
                  param: {}
                },
                function () {}
              );
              return;
            } else if ($h.getSession('channelType') === '2095000000000') {
              // 支付宝渠道
              this.eventMessage(this, EVENT_NAME.TO_INDEX);
              return;
            } else if (hmosUtil.checkHM) {
              hmosUtil.closePage({ valid: 0 });
              return;
            } else if ($h.getSession('from') === 'pc') {
              this.$TAlert({
                title: '温馨提示',
                tips: '您已完成当前业务操作,请退出本业务或继续办理其他业务',
                hasConfirm: false
              });
              return;
            } else {
              console.log('toCheckOtherChannelType');
              window.history.go(-1);
              // if (new AlipayUtil().checkAlipay) {
              //   alipayUtil.toIndex();
              //   return;
              // } else {
              //   window.history.go(-1);
              // }
              // if (alipayUtil.checkAlipay) {
              //   alipayUtil.toIndex();
              //   return;
              // }
              // this.eventMessage(this, EVENT_NAME.PREV_FLOW);
              // window.history.go(-1);
            }
          }
          // this.eventMessage(this, EVENT_NAME.PREV_FLOW);
          // this.eventMessage(this, EVENT_NAME.TO_INDEX);
        }
      });
    } else {
      this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
        text: '重新测评',
        display: true,
        btnStatus: 2,
        data: () => {
          this.submit();
        }
      });
    }
  },
  methods: {
    clickInput(propType) {
      this.$refs[propType].show();
    },

    renderingView() {
      const { inProperty } = this.tkFlowInfo();
      let { subjectNo } = this.$attrs;
      riskFlagQuery({
        paperType: subjectNo,
        userType: inProperty.userType
      }).then((res) => {
        let { data } = res;
        this.validDateFlag = data.validDateFlag; //即将过期，过期flag
        this.validTimesFlag = data.validTimesFlag; //问卷答题是否超过次数flag

        this.limitTime = data.limitTime;
        this.maxTimes = data.maxTimes;

        this.dailyPermitTimes = data.dailyPermitTimes;
        this.limitTimeDict = data.limitTimeDict;
        this.maxTimesDict = data.maxTimesDict;
      });
      riskQuery({
        paperType: subjectNo,
        userType: inProperty.userType
      }).then((res) => {
        this.riskResult = res.data;
        this.riskResult.corpEndDate =
          res.data.corpEndDate === '0' ? '0' : formatDate(res.data.corpEndDate);
        this.minRankFlag = res.data.minRankFlag ? res.data.minRankFlag : '0';
        answerHisDetail({
          paperType: subjectNo
        }).then((data) => {
          if (data.data) {
            let paperAnswer = data.data.paperAnswer.split('|');
            paperAnswer.pop();
            paperAnswer = paperAnswer.map((item) => {
              let quseNo = item.split('#')[0];
              let answerNo = item.split('&')[1].split('^').join('&');
              return `${quseNo}_${answerNo}`;
            });
            let version = data.data.version;

            this.oldPaperAnswer = paperAnswer.join('|');
            this.oldPaperVersion = version;
          }
          if (res.data.corpRiskLevel === '0') {
            // 风测未评级
            this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
              paperNo: this.paperNo,
              paperVersion: this.paperVersion,
              oldPaperAnswer: this.oldPaperAnswer,
              oldPaperVersion: this.oldPaperVersion
            });
          } else {
            this.loadingEd = true;
          }
        });
        // questionQry({
        //   subjectNo,
        //   userType: '1'
        // }).then((data) => {
        //   this.limitList = data.data.limitList;
        //   this.dayTime = data.data.limitList[0].limitTimesSubmit;
        //   this.paperNo = data.data.subjectCode;
        //   this.paperVersion = data.data.subjectVersion;
        // });
      });
    },

    checkLimit() {
      console.log(this.validTimesFlag);
      if (this.validTimesFlag === '1') {
        let Tips = this.limitTime === '1' ? '日' : this.limitTime + '日';
        let limitTips = `我司规定每${Tips}测评次数不能超过${this.maxTimes}次，您已达到上限，不能继续测评`;
        this.$TAlert({
          tips: limitTips,
          confirmBtn: '确定'
        });
        return false;
      } else {
        return true;
      }
    },

    chooseQuestion(item) {
      this.qusetion.forEach((it) => {
        this.$set(it, 'checked', false);
      });
      this.qusetion.forEach((it) => {
        if (it.optionId === item.optionId) {
          // it.checked = true;
          this.$set(it, 'checked', true);
        }
      });
    },

    submit() {
      if (!this.checkLimit()) {
        return;
      }
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        paperNo: this.paperNo,
        paperVersion: this.paperVersion,
        oldPaperAnswer: this.oldPaperAnswer,
        oldPaperVersion: this.oldPaperVersion
      });
    },

    submitQuestion() {
      if (this.qusetion.filter((item) => item.checked).length <= 0) {
        return;
      }
      questionSave({
        questionId: this.questionId,
        optionId: this.qusetion.filter((item) => item.checked)[0].optionId,
        questionnaireType: 'riskQuestionMultipleSubmit'
      }).then((res) => {
        // 流水res.data.serialNumber
        this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
          paperNo: this.paperNo,
          paperVersion: this.paperVersion
        });
      });
    }
  }
};
</script>

<style lang="less" scoped>
.s_form_tips {
  p {
    color: var(--tColorLightgray, #87878d);
  }
}
</style>>
