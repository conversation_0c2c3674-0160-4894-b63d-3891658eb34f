<template>
  <div>
    <section v-show="pageStep === 0" class="main fixed" style="position: fixed">
      <t-header title="风险测评" @back="back"></t-header>
      <article
        class="content top_border"
        style="overflow-y: hidden; display: flex; flex-direction: column"
      >
        <div class="test_numbox" style="overflow: initial">
          <span
            v-for="(item, index) in questionList"
            :key="index"
            :class="{ off: item.some((a) => a.checked) }"
            @click="toJump(index)"
            >{{ index + 1 }}</span
          >
        </div>
        <div
          ref="questionList"
          class="test_list"
          style="overflow-y: auto; margin: 0; height: 100%; position: relative"
        >
          <div
            v-for="(item, index) in questionList"
            :ref="`question${index}`"
            :key="index"
            class="test_list"
          >
            <div class="test_box">
              <h5 class="title">
                {{ index + 1 }}、{{ item[0].questionContent }}
              </h5>
              <div class="radio_list">
                <span
                  v-for="(m, i) in item"
                  :key="'m' + i"
                  :class="{
                    icon_check: m.questionKind === QUESTION_KIND.MULTI,
                    icon_radio: m.questionKind !== QUESTION_KIND.MULTI,
                    checked: m.checked,
                    disabled: m.degreeCode && m.degreeCode !== ''
                  }"
                  @click="selectItem(m, i, index)"
                  >{{ m.answerContent }}</span
                >
              </div>
            </div>
          </div>
        </div>
      </article>
      <footer class="footer" style="background: #ffffff">
        <div class="ce_btn">
          <a class="p_button border" @click="riskAfresh">重新评测</a>
          <a class="p_button" @click="toNext">下一步</a>
        </div>
      </footer>
    </section>

    <section v-show="pageStep === 3" class="main fixed" style="position: fixed">
      <header class="header">
        <div class="header_inner">
          <a class="icon_back" href="javascript:void(0);"></a>
          <h1 class="title">风险测评</h1>
        </div>
      </header>
      <article class="content top_border">
        <div class="protocol_box">
          <h2 class="title">个人投资者风险承受能力评估问卷</h2>
          <div class="protocol_cont">
            <p>
              本问卷旨在了解您可承受的风险程度等情况借此协助您选择合适的金融产品或金融服务类别以符合您的风险承受能力。
            </p>
            <p>
              风险承受能力评估是本公司向客户履行适当性职责的一个环节，其目的是使本公司所提供的金融产品或金融服务与您的风险承受能力等级相匹配。
            </p>
            <p>
              本公司特别提醒您:本公司向客户履行风险承受能力评估等适当性职责，并不能取代您自己的投资判断，也不会降低金融产品或金融服务的固有风险:我公司向您提供的适当性匹配意见不表明对产品或者服务的风险和收益做出实质性判断或者保证。同时，与金融产品或金融服务相关的投资风险、履约责任以及费用等将由您自行承担。
            </p>
            <p>
              本公司提示您:本公司根据您提供的信息对您进行风险承受能力评估，开展适当性工作。您应当如实提供相关信息及证明材料，并对所提供的信息和证明材料的真实性、准确性、完整性负责。
            </p>
            <p>
              本公司建议:当您的各项状况发生重大变化时，需对您所投资的金融产品及时进行重新审视以确保您的投资决定与您可承受的投资风险程度等实际情况一致。
            </p>
            <p>
              本公司在此承诺，对于您在本问卷中所提供的一切信息，本公司将严格按照法律法规要求承担保
            </p>
          </div>
        </div>
      </article>
      <footer class="footer">
        <div class="ce_btn">
          <a class="p_button" @click="pageStep = 0">开始测评</a>
        </div>
      </footer>
    </section>

    <section v-show="pageStep === 1" class="main fixed" style="position: fixed">
      <header class="header">
        <div class="header_inner">
          <a class="icon_back" href="javascript:void(0);"></a>
          <h1 class="title">风险测评</h1>
        </div>
      </header>
      <article class="content top_border">
        <div class="com_title">
          <h5>尊敬的投资者，您的风险承受能力等级为</h5>
        </div>
        <div class="test_rsult">
          <div class="test_infobox">
            <div class="test_inner">
              <div class="info">
                <h5>{{ testResult.riskLevelName }}</h5>
                <p>{{ testResult.investAdvice }}</p>
              </div>
              <div class="test_level">
                <div class="txt">
                  <h5>
                    <strong id="testData" style="font-size: 0.24rem">{{
                      testResult.paperScore
                    }}</strong
                    ><span>分</span>
                  </h5>
                  <p>测评得分</p>
                </div>
                <div class="test_canvas">
                  <canvas id="testCanvas" width="320" height="320"></canvas>
                </div>
              </div>
            </div>
          </div>
          <div class="appro_info">
            <ul>
              <li>
                <span class="tit">拟投资期限为</span>
                <p>根据评估结果，您的投资期限为{{ testResult.enInvestTerm }}</p>
              </li>
              <li>
                <span class="tit">拟投资品种为</span>
                <p>{{ testResult.enInvestKind }}</p>
              </li>
              <!-- <li>
                <span class="tit">建议办理业务</span>
                <p></p>
              </li> -->
            </ul>
          </div>
          <div v-if="riskMatchRightInfoList.length > 0" class="pro_appro_wrap">
            <p>
              根据上述测评情况，您已参与或已开通的产品或服务的适当性匹配结果如下
            </p>
            <div class="pro_appro_item">
              <h3 class="title on">适当性匹配情况说明</h3>
              <div class="cont">
                <div class="pro_appro_tbbox">
                  <table
                    class="pro_appro_table"
                    width="100%"
                    cellpadding="0"
                    cellspacing="0"
                  >
                    <tr>
                      <th width="50%">业务名称</th>
                      <th width="25%">业务等级</th>
                      <th width="25%">是否匹配</th>
                    </tr>
                    <tr
                      v-for="(item, index) in riskMatchRightInfoList"
                      :key="index"
                    >
                      <td>{{ item.bizName }}</td>
                      <td>{{ item.bizRiskLevelName }}</td>
                      <td>{{ item.isMatch === '0' ? '否' : '是' }}</td>
                    </tr>
                  </table>
                </div>
              </div>
            </div>
            <div class="com_imp_tips">
              建议您审慎评判自身风险承受能力、结合自身投资行为，审慎参与以上产品或服务。
            </div>
          </div>
          <div class="appro_tips">
            <p>确认声明</p>
            <p>
              本人经贵公司提示，已充分知晓贵公司向本人销售的产品或提供的服务将以本人此次确认的风险承受能力等级为基础。若本人提供的信息发生任何重大变化，本人都会及时书面通知贵公司。本人已知晓并确认上述适当性评估结果，将自主承担购买或接受相关产品或服务可能引起的损失和其他后果。本确认函系本人独立、自主、真实的意思表示，特此确认。
            </p>
          </div>
        </div>
        <div class="rule_check">
          <span class="icon_check checked"></span>
          <label
            >本人已详细阅读并同意签署以下协议<a @click="openAgree"
              >《{{ agreeDetail.agreementName }}》</a
            ></label
          >
        </div>
      </article>
      <footer class="footer" style="background: #f5f6fa">
        <div class="ce_btn">
          <a class="p_button border" @click="reset">重新测评</a>
          <a class="p_button" @click="toSubmit">确认</a>
        </div>
      </footer>
    </section>
    <agreementDetail
      :show="showAgreeDetail"
      :info="agreeDetail"
      :is-count="true"
      @callback="agreeCallBack"
    />
  </div>
</template>

<script>
import {
  questionQry,
  questionSubmit,
  // getQuestionNumber,
  updateFlowForm,
  flowQueryIns,
  flowSubmit,
  riskMatchRight,
  queryAgreement,
  getJwtToken
} from '@/service/service';
import { QUESTION_KIND, PROCESS_STATUS } from '@/common/enumeration';
import agreementDetail from '@/components/agreementDetail';
import { signAgree } from '@/common/util';

export default {
  name: 'RiskSurvey',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {
    agreementDetail
  },
  props: {
    paperType: {
      type: String,
      default: '1'
    },
    isObtainLoc: {
      // 0 柜台 非0 本地
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      QUESTION_KIND,
      testResult: {},
      questionList: [],
      answerList: [],
      riskMatchRightInfoList: [],
      subjectNo: '',
      riskQuestion: '',
      pageStep: 3,
      agreeDetail: {},
      showAgreeDetail: false,
      checkAgree: false,
      groupId: this.$attrs.groupId,
      contractType: this.$attrs.contractType,
      epaperSignJson: []
    };
  },
  computed: {
    totalQuestionLength() {
      return this.questionList.length;
    }
  },
  async mounted() {
    if (this.questionList.length === 0) {
      this.getQuestionList();
    }
    this.$nextTick(() => {
      this.initCanvans();
    });
  },
  methods: {
    initCanvans() {
      var canvas1 = document.getElementById('testCanvas');
      var ctx1 = canvas1.getContext('2d');
      var W1 = canvas1.width;
      var H1 = canvas1.height;
      var deg1 = 0,
        new_deg1 = 0,
        dif1 = 0;
      var loop1;
      var t_data1 = document.getElementById('testData').innerHTML;
      var deColor1 = '#f0f0f0',
        dotColor1 = '#FA443A';

      function init1() {
        ctx1.clearRect(0, 0, W1, H1);
        ctx1.beginPath();
        ctx1.strokeStyle = deColor1;
        ctx1.lineWidth = 32;
        ctx1.arc(
          W1 / 2,
          H1 / 2 + 30,
          130,
          (Math.PI * 5) / 6,
          (Math.PI * 39) / 18,
          false
        );
        ctx1.stroke();

        var r2 = (2.4 * 1 * Math.PI) / 180 + (Math.PI * 1) / 3;
        ctx1.beginPath();
        ctx1.fillStyle = dotColor1;
        ctx1.arc(
          W1 / 2 - 130 * Math.sin(r2),
          H1 / 2 + 30 + 130 * Math.cos(r2),
          16,
          -180,
          true
        );
        ctx1.fill();

        var r3 = (2.4 * 100 * Math.PI) / 180 + (Math.PI * 1) / 3;
        ctx1.beginPath();
        ctx1.fillStyle = deColor1;
        ctx1.arc(
          W1 / 2 - 130 * Math.sin(r3),
          H1 / 2 + 30 + 130 * Math.cos(r3),
          16,
          -180,
          true
        );
        ctx1.fill();

        var r4 = (2.4 * deg1 * Math.PI) / 180 + (Math.PI * 1) / 3;
        ctx1.beginPath();
        ctx1.fillStyle = dotColor1;
        ctx1.arc(
          W1 / 2 - 130 * Math.sin(r4),
          H1 / 2 + 30 + 130 * Math.cos(r4),
          16,
          -180,
          true
        );
        ctx1.fill();

        var r1 = (2.4 * deg1 * Math.PI) / 180;
        ctx1.beginPath();
        ctx1.strokeStyle = dotColor1;
        ctx1.lineWidth = 32;
        ctx1.arc(
          W1 / 2,
          H1 / 2 + 30,
          130,
          (Math.PI * 5) / 6,
          r1 + (Math.PI * 5) / 6,
          false
        );
        ctx1.stroke();

        var r5 = (2.4 * deg1 * Math.PI) / 180 + (Math.PI * 1) / 3;
        ctx1.beginPath();
        ctx1.fillStyle = '#fff';
        ctx1.arc(
          W1 / 2 - 130 * Math.sin(r5),
          H1 / 2 + 30 + 130 * Math.cos(r5),
          7,
          -180,
          true
        );
        ctx1.fill();
      }

      function draw1() {
        new_deg1 = t_data1;
        dif1 = new_deg1 - deg1;
        loop1 = setInterval(to1, 500 / dif1);
      }
      function to1() {
        if (deg1 == new_deg1) {
          clearInterval(loop1);
        }
        if (deg1 < new_deg1) {
          deg1++;
        }
        init1();
      }
      draw1();
    },

    openAgree() {
      this.showAgreeDetail = true;
    },

    agreeCallBack() {
      this.showAgreeDetail = false;
    },

    reset() {
      this.pageStep = 0;
      this.answerList = [];
      this.questionList.forEach((item, index) => {
        item.forEach((it) => {
          if (it.degreeCode !== '' && it.isAlter) {
            this.answerList[index] = `${it.questionNo}_${it.answerNo}`;
            return;
          } else {
            it.checked = false;
          }
        });
      });
      this.$nextTick(() => {
        this.jump(0);
        this.testResult = this.$options.data().testResult;
      });
    },

    toSubmit() {
      const flowToken = sessionStorage.getItem('TKFlowToken');
      const accArr = [PROCESS_STATUS.ACCEPT_COMPLETED]; // 配置需要提交受理结果的状态
      flowQueryIns({ flowToken }).then((res) => {
        if (accArr.includes(res.data.status)) {
          console.log();
        } else {
          flowSubmit({ flowToken }).then();
        }
      });
      flowSubmit({ flowToken }).then(() => {
        if ($h.getSession('noHome') === true) {
          // todo 关闭webview
          $h.callMessageNative({
            funcNo: '50114',
            moduleName: $hvue.customConfig.moduleName
          });
        } else {
          this.eventMessage(this, 'toIndex');
        }
      });
    },

    toJump(index) {
      let quesIndex = `question${index}`;
      this.$refs['questionList'].scrollTop = this.$refs[quesIndex][0].offsetTop;
    },

    jump(index) {
      let quesIndex = `question${index}`;
      this.$refs['questionList'].scrollTop = this.$refs[quesIndex][0].offsetTop;
    },

    verifyAnswer() {
      let arr = [];
      for (let quesIndex of this.questionList.keys()) {
        if (!this.answerList[quesIndex]) {
          arr.push(quesIndex + 1);
        }
      }
      if (arr.length > 0) {
        let msg = `第${arr.join(',')}题尚未选择，请确认后重新提交`;
        _hvueToast({
          mes: msg
        });
        return false;
      } else {
        return true;
      }
    },

    getQuestionList() {
      let _this = this;
      let { paperType, isObtainLoc } = _this;
      const { inProperty } = this.tkFlowInfo();
      let userType = inProperty.userType;
      this.paperType = paperType;
      questionQry({
        paperType,
        userType,
        isObtainLoc,
        flowToken: sessionStorage.getItem('TKFlowToken')
      })
        .then((data) => {
          _hvueLoading.close();
          let _resArr = data.data;
          let _queId = '';
          let _queNum = -1;
          let i = 0;
          // 取柜台数据时paperType用接口返回的，取本地数据时paperType用私有属性配置的
          _this.pagePaperType =
            isObtainLoc === '1' ? paperType : _resArr[0].paperType;
          _resArr.forEach((item) => {
            if (item.questionNo !== _queId) {
              _queId = item.questionNo;
              _queNum++;
              i = 0;
              item.extName =
                item.questionKind === QUESTION_KIND.MULTI ? '（多选）' : '';
              _this.$set(_this.questionList, _queNum, []);
            }
            // 增加checked属性来判定是否选中当前选项
            if (item.degreeCode !== '' && item.isAlter) {
              item.checked = true;
              _this.$set(
                _this.answerList,
                _queNum,
                `${item.questionNo}_${item.answerNo}`
              );
            } else {
              item.checked = false;
            }
            _this.$set(_this.questionList[_queNum], i++, item);
          });
        })
        .catch((error) => {
          _hvueLoading.close();
          _hvueToast({
            mes: error
          });
        });
    },

    selectItem(item, aIndex, quesIndex) {
      const _this = this;
      let ansAtr = [];
      let quesNo = '';
      if (this.questionList[quesIndex].find((item) => item.degreeCode)) {
        return;
      }
      _this.questionList[quesIndex].forEach((a) => {
        if (a.questionKind === QUESTION_KIND.MULTI) {
          if (item.answerNo === a.answerNo) {
            a.checked = !a.checked;
          }
          if (a.checked) {
            quesNo = a.questionNo;
            ansAtr.push(a.answerNo);
          }
        } else {
          if (a.answerNo === item.answerNo) {
            a.checked = true;
            quesNo = a.questionNo;
            ansAtr.push(a.answerNo);
          } else {
            a.checked = false;
          }
        }
      });
      // 选择后更改答案列表
      let a = '';
      if (ansAtr.length > 0) {
        a = `${quesNo}_${ansAtr.join('&')}`;
      }
      this.$set(this.answerList, quesIndex, a);
      if (
        this.questionList.every((question) => question.some((a) => a.checked))
      ) {
        // this.submitQuestion();
      } else if (
        this.questionList[quesIndex][0].questionKind != QUESTION_KIND.MULTI &&
        quesIndex < this.questionList.length - 1
      ) {
        setTimeout(() => {
          this.jump(quesIndex + 1);
        }, 300);
      }
    },

    async submitQuestion() {
      const _this = this;
      // const info = _this.flowOutputInfo.inProperty;
      //校验答案是否选择完成
      if (!_this.verifyAnswer()) return;
      // let { isObtainLoc } = _this.flowOutputInfo.privProperty;
      const flowToken = sessionStorage.getItem('TKFlowToken');
      if (_this.isObtainLoc === '1' && this.testResult.ifCorpRiskTimes <= 0) {
        // 本地问卷提交前看根据剩余次数能否提交
        let errMsg =
          '尊敬的投资者，您今天的测评次数已用完，请充分学习业务知识，下个交易日再进行测评，谢谢';
        _hvueToast({
          mes: errMsg
        });
        return;
      }
      questionSubmit({
        flowToken,
        paperType: this.paperType,
        paperAnswer: this.answerList.join('|'), // 试卷答案对象数据
        isObtainLoc: this.isObtainLoc
      })
        .then((data) => {
          Object.assign(_this.testResult, data.data);
          riskMatchRight({
            corpRiskLevel: data.data.corpRiskLevel
          }).then((res) => {
            console.log(res);
            this.riskMatchRightInfoList = res.data.riskMatchRightInfoList;
            updateFlowForm({
              flowToken: sessionStorage.getItem('TKFlowToken'),
              formParam: {
                paper_answer: data.data.riskQuestion,
                corp_risk_core: data.data.paperScore,
                corp_risk_level: data.data.corpRiskLevel,
                invest_advice: data.data.investAdvice,
                risk_level_name: data.data.riskLevelName,
                en_prod_risk_level: data.data.enProdRiskLevel,
                un_prod_risk_level: data.data.unProdRiskLevel,
                en_user_services: JSON.stringify(
                  this.riskMatchRightInfoList.filter(
                    (item) => item.isMatch === '1'
                  )
                ),
                un_user_services: JSON.stringify(
                  this.riskMatchRightInfoList.filter(
                    (item) => item.isMatch === '0'
                  )
                )
              }
            }).then(async () => {
              const { flowNodeNo, inProperty } = this.tkFlowInfo();
              const tokenRes = await getJwtToken({
                flowNo: flowNodeNo,
                businessType: inProperty.bizType
              });
              $h.setSession('jwtToken', tokenRes.data);
              queryAgreement({
                flowToken: sessionStorage.getItem('TKFlowToken'),
                groupId: this.groupId,
                contractType: this.contractType
              }).then((res) => {
                this.agreeDetail = res.data[0];
                const tkFlowInfo = this.tkFlowInfo();
                signAgree(tkFlowInfo, res.data).then((res) => {
                  this.epaperSignJson = res;
                  updateFlowForm({
                    flowToken: sessionStorage.getItem('TKFlowToken'),
                    formParam: {
                      epaper_sign_json: this.epaperSignJson
                    }
                  });
                });
              });
              this.pageStep = 1;
              this.$nextTick(() => {
                this.initCanvans();
              });
            });
          });
        })
        .catch((err) => {
          if (err) {
            _hvueToast({
              mes: err
            });
          }
        });
    },

    riskAfresh() {
      if (
        this.testResult.ifCorpRiskTimes === '0' ||
        this.testResult.ifCorpRiskTimes <= 0
      ) {
        let errMsg =
          '尊敬的投资者，您今天的测评次数已用完，请充分学习业务知识，下个交易日再进行测评，谢谢';
        _hvueToast({
          mes: errMsg
        });
      } else {
        // 重新测评
        this.answerList = [];
        this.questionList.forEach((item, index) => {
          item.forEach((it) => {
            if (it.degreeCode !== '' && it.isAlter) {
              this.answerList[index] = `${it.questionNo}_${it.answerNo}`;
              return;
            } else {
              it.checked = false;
            }
          });
        });
        this.pageStep = 0;
        this.$nextTick(() => {
          this.jump(0);
          this.testResult = this.$options.data().testResult;
        });
      }
    },

    back() {},

    toNext() {
      this.submitQuestion();
    }
  }
};
</script>
