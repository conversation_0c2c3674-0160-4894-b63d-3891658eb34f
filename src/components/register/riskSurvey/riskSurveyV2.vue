<template>
  <div>
    <section v-if="pageStep == 0" class="main fixed" style="position: fixed">
      <t-header title="风险测评" @back="back"></t-header>
      <article class="content top_border">
        <div class="protocol_box">
          <h2 class="title">风险承受能力评估问卷须知</h2>
          <div v-if="userType === '0'" class="protocol_cont">
            <p>
              本问卷旨在了解您可承受的风险程度等情况，借此协助您选择合适的产品或服务类别，以符合您的风险承受能力。
            </p>
            <p>
              风险承受能力评估是本公司向投资者履行适当性义务的一个环节，其目的是使本公司所提供的产品或服务与您的风险承受能力等级相匹配。
            </p>
            <p>
              <span style="font-weight: bold"
                >本公司特别提醒您：本公司向投资者履行风险承受能力评估等适当性义务，并不能取代您自己的投资判断，也不会降低产品或服务的固有风险</span
              >。同时，与产品或服务相关的投资风险、履约责任以及费用等将由您自行承担。
            </p>
            <p>
              本公司提示您：本公司将根据您的测评结果评估您的风险承受能力等级，该风险承受能力等级将影响您购买产品或接受服务的范围。<span
                style="font-weight: bold"
                >您应当如实提供测评相关信息，并对所提供信息的真实性、准确性、完整性负责，否则您应当自行承担相应法律责任，同时本公司有权拒绝向您提供服务或者销售产品</span
              >。您对风险测评问卷某一问题的具体回答不构成本公司向您提供服务或销售产品时风险评估的唯一依据，也不构成您与本公司就本公司向您提供服务或销售产品之间达成任何约定或协议安排。
            </p>
            <p>
              本公司建议：当您的各项状况发生重大变化时，需对您所投资的产品及时进行重新审视，以确保您的投资决定与您可承受的投资风险程度等实际情况一致。
            </p>
            <p>
              本公司在此承诺，对于您在本问卷中所提供的一切信息，本公司将严格按照法律法规要求承担保密义务。除法律法规规定的有权机关依法定程序进行查询以外，本公司保证不会将涉及您的任何信息提供、泄露给任何第三方，或者将相关信息用于违法、不当用途。
            </p>
          </div>

          <div
            v-if="userType === '1' || userType === '3'"
            class="protocol_cont"
          >
            <p>
              本问卷旨在了解贵单位可承受的风险程度等情况，借此协助贵单位选择合适的产品或服务类别，以符合贵单位的风险承受能力。
            </p>
            <p>
              风险承受能力评估是本公司向投资者履行适当性义务的一个环节，其目的是使本公司所提供的产品或服务与贵单位的风险承受能力等级相匹配。
            </p>
            <p>
              <span style="font-weight: bold"
                >本公司特别提醒贵单位：本公司向投资者履行风险承受能力评估等适当性义务，并不能取代贵单位自己的投资判断，也不会降低产品或服务的固有风险</span
              >。同时，与产品或服务相关的投资风险、履约责任以及费用等将由贵单位自行承担。
            </p>
            <p>
              本公司提示贵单位：本公司将根据贵单位的测评结果评估贵单位的风险承受能力等级，该风险承受能力等级将影响贵单位购买产品或接受服务的范围。<span
                style="font-weight: bold"
                >贵单位应当如实提供测评相关信息，并对所提供信息的真实性、准确性、完整性负责，否则贵单位应当自行承担相应法律责任，同时本公司有权拒绝向贵单位提供服务或者销售产品</span
              >。贵单位对风险测评问卷某一问题的具体回答不构成本公司向贵单位提供服务或销售产品时风险评估的唯一依据，也不构成贵单位与本公司就本公司向贵单位提供服务或销售产品之间达成任何约定或协议安排。
            </p>
            <p>
              本公司建议：当贵单位的各项状况发生重大变化时，需对贵单位所投资的产品及时进行重新审视，以确保贵单位的投资决定与贵单位可承受的投资风险程度等实际情况一致。
            </p>
            <p>
              本公司在此承诺，对于贵单位在本问卷中所提供的一切信息，本公司将严格按照法律法规要求承担保密义务。除法律法规规定的有权机关依法定程序进行查询以外，本公司保证不会将涉及贵单位的任何信息提供、泄露给任何第三方，或者将相关信息用于违法、不当用途。
            </p>
          </div>
        </div>
      </article>
      <footer class="footer">
        <div class="ce_btn">
          <a class="p_button" @click="toStart">开始测评</a>
        </div>
      </footer>
    </section>

    <section v-if="pageStep == 1" class="main fixed" style="position: fixed">
      <t-header title="风险测评" @back="back"></t-header>
      <article
        class="content top_border"
        style="overflow-y: hidden; display: flex; flex-direction: column"
      >
        <div class="test_numbox" style="overflow: initial">
          <span
            v-for="(item, index) in questionList"
            :key="index"
            :class="{ off: item.some((a) => a.checked) }"
            @click="toJump(index)"
            >{{ index + 1 }}</span
          >
        </div>
        <div
          ref="questionList"
          class="test_list"
          style="
            overflow-y: scroll;
            margin: 0;
            height: 100%;
            position: relative;
            -webkit-overflow-scrolling: touch;
          "
        >
          <div
            v-for="(item, index) in questionList"
            :ref="`question${index}`"
            :key="index"
            class="test_list"
          >
            <div class="test_box">
              <h5 class="title">
                {{ index + 1 }}、{{ item[0].questionContent }}
              </h5>
              <div class="radio_list">
                <span
                  v-for="(m, i) in item"
                  :key="'m' + i"
                  :class="{
                    icon_check: m.questionKind === QUESTION_KIND.MULTI,
                    icon_radio: m.questionKind !== QUESTION_KIND.MULTI,
                    checked: m.checked,
                    disabled: m.disabled
                  }"
                  @click="selectItem(m, i, index)"
                  >{{ m.answerContent }}</span
                >
              </div>
            </div>
          </div>
        </div>
      </article>
      <footer class="footer">
        <div class="ce_btn">
          <a
            class="p_button"
            :class="{ disabled: submitDisable }"
            @click="toNextbefore"
            >提交测评</a
          >
        </div>
      </footer>
    </section>

    <section
      v-if="pageStep == 2"
      class="main fixed white_bg"
      style="position: fixed"
    >
      <header class="header">
        <div class="header_inner">
          <a class="icon_back" href="javascript:void(0);"></a>
          <h1 class="title">风险测评</h1>
        </div>
      </header>
      <article class="content top_border">
        <div class="com_title">
          <h5>尊敬的投资者，您的风险承受能力等级为</h5>
        </div>
        <div class="test_rsult">
          <div class="test_infobox">
            <!-- <div class="test_inner">
              <div class="info">
                <h5>{{ testResult.riskBear }}</h5>
                <p>本次风险承受能力评定结果有效期至:2024.03.20</p>
              </div>
              <div class="test_level_2">
                <div class="txt">
                  <h5>
                    <strong
                      >C{{
                        testResult.questionSubmitPreVo.corpRiskLevel
                      }}</strong
                    >
                  </h5>
                </div>
              </div>
            </div> -->
            <div class="test_inner">
              <div class="info">
                <h5>{{ testResult.riskBear }}</h5>
                <p v-if="testResult.questionSubmitPreVo.minRankFlag !== '1'">
                  您适合购买或接受{{
                    testResult.suitService
                  }}风险等级的产品或服务
                </p>
                <p v-if="testResult.questionSubmitPreVo.minRankFlag === '1'">
                  您仅允许购买或接受{{
                    testResult.suitService
                  }}风险等级的产品或服务
                </p>
              </div>
              <div class="test_level_2">
                <div class="txt">
                  <h5>
                    <strong
                      >C{{
                        testResult.questionSubmitPreVo.corpRiskLevel
                      }}</strong
                    >
                  </h5>
                </div>
              </div>
            </div>
          </div>
          <div class="appro_info">
            <ul>
              <li>
                <span class="tit">到期日</span>
                <p>{{ testResult.failureDate }}</p>
              </li>
            </ul>
          </div>
          <div v-show="testResult.tipIndexes !== ''" class="test_opeabox">
            <p ref="tips1">
              您本次在第<span class="ared">{{ testResult.tipIndexes }}</span
              >题中选择的选项较上次测评结果变化较大，请您确认本次提供的信息是否真实准确。若不准确，请重新测评。
            </p>
            <div class="ce_btn">
              <a class="p_button border" @click="reset">重新测评</a>
            </div>
            <!-- <div v-if="agreeList.length > 0" class="rule_check spel">
              <span class="icon_check checked"></span>
              <label
                >我已阅读并同意<a href="javascript:void(0);"
                  >《{{ agreeList[0].agreementNoName }}》</a
                ></label
              >
            </div> -->
          </div>
          <div class="appro_txt_wrap">
            <div ref="tips2" class="item">
              <h5>风险承受能力评估结果及适当性匹配意见</h5>
              <p>
                本公司对您风险承受能力评估结果为：C{{
                  testResult.questionSubmitPreVo.corpRiskLevel
                }}
                （{{ testResult.riskBear }}）
              </p>
              <p v-if="testResult.questionSubmitPreVo.minRankFlag !== '1'">
                根据您的风险承受能力， 您适合购买或接受
                {{ testResult.suitService }}风险等级的产品或服务
              </p>
              <p v-if="testResult.questionSubmitPreVo.minRankFlag === '1'">
                根据您的风险承受能力，您仅允许购买或接受R1风险等级的产品或服务。
              </p>
              <p>
                本公司对此郑重提醒，
                我司向您销售的产品或提供的服务将以您的风险承受能力等级和投资目标为基础，请您如实完成风险承受能力的自我测评，并结合自身投资行为，作出审慎的投资判断。
              </p>
              <p>
                若您购买或接受超过了您的风险承受能力的产品或服务，可能会给您带来超出您风险承受能力的损失，请您谨慎参与。
              </p>
            </div>
            <div ref="tips3" class="item">
              <h5 style="font-weight: bold; color: #ff2840">特别提示</h5>
              <p v-show="testResult.isHigher === '1'">
                1、尊敬的客户，由于您在我司本次风险承受能力测评等级较之前有所提高，根据我司《风险等级对照表》中产品或服务风险目录及适配规定，与您适配的产品或服务风险级别将会更高，请确保您本次提供的各题测评答案是您本人真实信息且准确无误；若确认提交，建议您在购买产品或服务前充分了解其相关特征与风险，谨慎参与。
              </p>
              <p>
                {{
                  testResult.isHigher === '1' ? '2' : '1'
                }}、本次风险承受能力评定结果有效期为2年。在此期间或有效期满时您的信息发生任何重大变化，您都应当及时书面通知本公司或立即更新您的《投资者风险承受能力评估问卷》。若您提供的信息不及时、不真实、不准确、不完整，将导致公司不能有效评估您的真实风险承受能力，您应当自行承担由此造成的一切后果，同时公司有权拒绝向您销售产品或提供服务。
              </p>
            </div>
            <div ref="tips4" class="item">
              <h5 v-if="userType === '0'">投资者确认</h5>
              <h5 v-if="userType === '1' || userType === '3'">机构客户确认</h5>
              <p>
                {{
                  userTypeTips
                }}在贵公司的提示下，已经审慎考虑自身的风险承受能力在此确认
              </p>
              <p>
                1、{{
                  userTypeTips
                }}已经了解并愿意遵守国家有关证券市场管理的法律、法规、规章及相关业务规则，{{
                  userTypeTips
                }}在此郑重承诺以上填写的内容真实、准确、完整；
              </p>
              <p>
                2、{{ userTypeTips }}的风险承受能力为C{{
                  testResult.questionSubmitPreVo.corpRiskLevel
                }}（{{ testResult.riskBear }}）
              </p>
              <p>
                3、{{ userTypeTips }}经贵公司提示，已充分知晓贵公司向{{
                  userTypeTips
                }}销售的产品或提供的服务将以{{
                  userTypeTips
                }}此次确认的风险承受能力等级和投资品种、期限为基础。若{{
                  userTypeTips
                }}提供的信息发生任何重大变化，{{
                  userTypeTips
                }}都会及时书面通知贵公司。若{{
                  userTypeTips
                }}违反上述承诺，将自行承担由此引起的相应后果。本确认函系{{
                  userTypeTips
                }}独立、自主、真实的意思表示，特此确认。
              </p>
            </div>

            <div class="appro_tips">
              <p>温馨提示:</p>
              <p>
                1、C1 (最低类别)、C1(低风险承受) 以及C2
                (中低风险承受)的投资者不能在我司开立A股、B股及封闭式基金账户。C1
                (最低类别)的投资者将只能购买或接受公司产品或服务风险目录中的低风险产品或服务，不能购买或接受超过低风险等级的产品或服务，其他类型的投资者，请您根据<a
                  class="com_link"
                  @click="clickInput('compareTable')"
                  >《风险等级对照表》</a
                >适配规则了解您适合购买的产品或接受的服务。
              </p>
              <p>
                2、有效期满或期间内您的信息发生重大变化，请您及时更新您的《投资者适当性评估表》中申报的信息。若您提供的信息不及时、不真实、不准确、不完整，将导致公司不能有效评估您的真实风险承受能力，您应当自行承担由此造成的一切后果，同时公司有权拒绝向您销售产品或提供服务。
              </p>
            </div>
          </div>
        </div>
      </article>
      <footer class="footer">
        <div
          v-if="
            agreeList.filter((it) => it.agreementNo === '100030').length > 0
          "
          class="rule_check"
        >
          <span
            class="icon_check"
            :class="{ checked: signed }"
            @click="signed = !signed"
          ></span>
          <label
            >我已阅读并同意<a
              v-for="(item, index) in agreeList.filter(
                (it) => it.agreementNo === '100030'
              )"
              :key="index"
              @click="toDetail(index)"
              >《{{ item.agreementName }}》</a
            ></label
          >
        </div>
        <div class="ce_btn">
          <!-- <a class="p_button border" @click="reset">重新测评</a> -->
          <a class="p_button" @click="nextFlow">确认并下一步</a>
        </div>
      </footer>
    </section>

    <!-- 问卷回访题 -->
    <div v-if="showVisit">
      <div class="dialog_overlay" style="display: block"></div>
      <div class="dialog_box" style="display: block">
        <div class="dialog_cont">
          <div
            v-for="(item, index) in visitQuestionList"
            :ref="`visitQuestion${index}`"
            :key="index"
          >
            <div class="test_box">
              <h5 class="title">
                {{ item[0].questionContent }}
              </h5>
              <div class="radio_list">
                <span
                  v-for="(m, i) in item"
                  :key="'m' + i"
                  :class="{
                    icon_check: m.questionKind === QUESTION_KIND.MULTI,
                    icon_radio: m.questionKind !== QUESTION_KIND.MULTI,
                    checked: m.checked,
                    disabled: m.disabled
                  }"
                  class="icon_radio"
                  @click="selectVisitItem(m, i, index)"
                  >{{ m.answerContent }}</span
                >
              </div>
            </div>
          </div>
        </div>
        <div class="dialog_btn">
          <a class="cancel" @click="showVisit = false">取消</a>
          <a :class="{ disabled: visitSubmitDisable }" @click="toVisitNext"
            >确认</a
          >
        </div>
      </div>
    </div>

    <!-- 是否有工作人员引导提示 -->
    <div v-if="showSelect && compareRiskLevel >= 2">
      <div class="dialog_overlay" style="display: block"></div>
      <div class="dialog_box" style="display: block">
        <div class="dialog_cont">
          尊敬的投资者，您提交的测评结果与之前变动较大，请问我司工作人员是否存在诱导您填写问卷答案等不当行为？如果您选择“是”，公司后续会及时与您取得联系
        </div>
        <div class="radio_list" style="display: flex; justify-content: center">
          <van-radio-group v-model="userSelectRes" direction="horizontal">
            <van-radio name="1">否</van-radio>
            <van-radio name="2">是（有诱导行为）</van-radio>
          </van-radio-group>
        </div>
        <div class="dialog_btn">
          <a
            @click="handleConfirmSubmit"
            :style="{ color: userSelectRes == '0' ? '#ccc' : '#FF2840' }"
            >确认并提交</a
          >
        </div>
      </div>
    </div>
    <protocol-detail
      v-if="showAgreeDetail"
      v-model="agreeIndex"
      :agree-list="agreeList"
      :read-list="readList"
      :is-count="!isCount"
      @callback="agreeCallBack"
      @signAgree="detailSignAgree"
    />
    <compareTable ref="compareTable" />
  </div>
</template>

<script>
import {
  addClientCritMark,
  // riskFlagQuery,
  questionQry,
  updateFlowForm,
  // questionPreSubmit,
  flowSubmit
} from '@/service/service';
import {
  // addClientCritMark,
  riskFlagQuery,
  // questionQry,
  // updateFlowForm,
  questionPreSubmit
  // flowSubmit
} from '@/service/riskService';
import ProtocolDetail from './components/ProtocolDetail';
import { queryAgreementExtV3 } from '@/service/service';
import { QUESTION_KIND } from '@/common/enumeration';
import { EVENT_NAME } from '@/common/formEnum';
import { signAgreeV2 } from '@/common/util';
import { ruleList } from './rule.js';
import compareTable from './components/compareTable.vue';
import HmosUtil from '@/common/HmosUtil';
const hmosUtil = new HmosUtil({});

export default {
  name: 'RiskSurveyV2',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: { compareTable, ProtocolDetail },
  props: {
    paperType: {
      type: String,
      default: '1'
    },
    isObtainLoc: {
      // 0 柜台 非0 本地
      type: String,
      default: '0'
    },
    passScore: {
      // 通过分数
      type: String,
      default: ''
    }
  },
  data() {
    return {
      userSelectRes: '0',
      compareRiskLevel: 0,
      getRiskLevel: 0,
      alertList: ['2056000000000', '2054000000000'], //提交成功渠道列表
      agreeIndex: 0,
      showAgreeDetail: false,
      signed: false,
      showVisit: false,
      showSelect: false,
      userType: '0',
      readList: [],
      agreeList: [],
      pageStep: 0,
      version: '',
      QUESTION_KIND,
      testResult: {},
      questionList: [],
      answerList: [],
      visitQuestionList: [],
      visitAnswerList: [],
      subjectNo: '',
      riskQuestion: '',
      ruleList,
      quickRiskFlag: '0',
      questionShowFlag: '0',

      dailyPermitTimes: '',
      limitTimeDict: '',
      maxTimesDict: '',
      epaperESignedFlag: ''
    };
  },
  computed: {
    isCount() {
      return this.readList.includes(this.agreeId);
    },

    totalQuestionLength() {
      return this.questionList.length;
    },

    userTypeTips() {
      if (this.userType === '0') {
        return '本人';
      } else {
        return '本机构';
      }
    },

    submitDisable() {
      let arr = [];
      for (let quesIndex of this.questionList.keys()) {
        if (!this.answerList[quesIndex]) {
          arr.push(quesIndex + 1);
        }
      }
      if (arr.length > 0) {
        let msg = `第${arr.join(',')}题尚未选择，请确认后重新提交`;
        // _hvueToast({
        //   mes: msg
        // });
        return true;
      } else {
        return false;
      }
    },

    visitSubmitDisable() {
      let arr = [];
      for (let quesIndex of this.visitQuestionList.keys()) {
        if (!this.visitAnswerList[quesIndex]) {
          arr.push(quesIndex + 1);
        }
      }
      if (arr.length > 0) {
        let msg = `第${arr.join(',')}题尚未选择，请确认后重新提交`;
        // _hvueToast({
        //   mes: msg
        // });
        return true;
      } else {
        return false;
      }
    }
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
  },
  async mounted() {
    let { subjectNo } = this.$attrs;
    const { inProperty, outProperty } = this.tkFlowInfo();
    this.userType = inProperty.userType;
    if (this.questionList.length === 0) {
      this.getQuestionList();
    }
    this.getVisitQuestionList();

    riskFlagQuery({
      paperType: subjectNo,
      userType: inProperty.userType
    }).then((res) => {
      let { data } = res;

      this.quickRiskFlag = data.quickRiskFlag; //快速测评弹窗flag
      this.questionShowFlag = data.questionShowFlag; //问卷回访弹窗flag
      this.getRiskLevel = data.corpRiskLevel; // 获取当前问卷等级
      this.dailyPermitTimes = data.dailyPermitTimes;
      this.limitTimeDict = data.limitTimeDict;
      this.maxTimesDict = data.maxTimesDict;
    });
  },
  methods: {
    handleConfirmSubmit() {
      if (this.userSelectRes == '0') {
        // let errMsg = '请您选择是或否';
        // _hvueToast({
        //   mes: errMsg
        // });
        return;
      } else if (this.userSelectRes == '2') {
        // 点击否 关闭弹窗 留痕 并往后走流程
        let text =
          '尊敬的投资者，您提交的测评结果与之前变动较大，请问我司工作人员是否存在引导您填写问卷答案等行为？';
        addClientCritMark({
          flowToken: sessionStorage.getItem('TKFlowToken'),
          markType: '23',
          markContent: `${text}</br>客户回答：是`,
          confirmFlag: '1'
        }).then((res) => {
          let desc = '基于您的前条反馈，我司建议您结合自身真实信息重新测评。';
          this.$TAlert({
            title: '',
            hasCancel: true,
            tips: desc,
            confirmBtn: '重新测评',
            cancelBtn: '符合本人真实情况，确认提交',
            confirm: () => {
              // 弹窗关闭 重走风测流程
              // this.reset();
              import('@/common/flowMixinV2.js').then((a) => {
                // this.$router.history.current.name = null;
                a.initFlow.call(this, {
                  bizType: '010013',
                  flowNo: '0-3060',
                  isReset: null,
                  // isJump: true,
                  initJumpMode: '1'
                });
              });
            },
            cancel: () => {
              // 点击确认 关闭弹窗 留痕 并往后走流程
              let content = `${desc}</br>客户回答：符合本人真实情况，确认提交</br>`;
              addClientCritMark({
                flowToken: sessionStorage.getItem('TKFlowToken'),
                markType: '18',
                markContent: content,
                confirmFlag: '1',
                opEntrusWay: '7',
                merchantld: '0',
                opSource: '4',
                opStation: ''
              }).then((res) => {
                console.log(res);
                this.flowSubmitSucBack();
              });
            }
          });
        });
      } else if (this.userSelectRes == '1') {
        // 点击否 关闭弹窗 留痕 并往后走流程
        let desc =
          '尊敬的投资者，您提交的测评结果与之前变动较大，请问我司工作人员是否存在引导您填写问卷答案等行为？';
        addClientCritMark({
          flowToken: sessionStorage.getItem('TKFlowToken'),
          markType: '21',
          markContent: `${desc}</br>客户回答：否`,
          confirmFlag: '1'
        }).then((res) => {
          console.log(res);
          this.flowSubmitSucBack();
        });
      }
      this.showSelect = false;
    },
    clickInput(propType) {
      this.$refs[propType].show();
    },

    toIndex() {
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    },

    reset() {
      this.answerList = [];
      this.questionList.forEach((item, index) => {
        item.forEach((it) => {
          if (it.degreeCode !== '' && it.isAlter) {
            this.answerList[index] = `${it.questionNo}_${it.answerNo}`;
            return;
          } else {
            it.checked = false;
          }
        });
      });
      // 引导选项提示弹窗 重置选择
      if (this.visitQuestionList[0]) {
        this.visitQuestionList[0].forEach((item) => {
          item.checked = false;
        });
      }
      this.pageStep = 1;
      this.$nextTick(() => {
        this.jump(0);
        this.testResult = this.$options.data().testResult;
      });
    },

    toJump(index) {
      let quesIndex = `question${index}`;
      this.$refs['questionList'].scrollTop = this.$refs[quesIndex][0].offsetTop;
    },

    jump(index) {
      let quesIndex = `question${index}`;
      this.$refs['questionList'].scrollTop = this.$refs[quesIndex][0].offsetTop;
    },

    verifyAnswer() {
      let arr = [];
      for (let quesIndex of this.questionList.keys()) {
        if (!this.answerList[quesIndex]) {
          arr.push(quesIndex + 1);
        }
      }
      if (arr.length > 0) {
        let msg = `第${arr.join(',')}题尚未选择，请确认后重新提交`;
        _hvueToast({
          mes: msg
        });
        return false;
      } else {
        return true;
      }
    },

    toStart() {
      console.log(this.quickRiskFlag);
      if (this.quickRiskFlag === '1') {
        this.$TAlert({
          title: '',
          tips: '您已做过风险测评问卷，您可以点击下方快速测评按钮，在原测评问卷基础上修改答案；您也可以重新进行风险测评。',
          hasCancel: true,
          cancelBtn: '快速测评',
          confirmBtn: '重新测评',
          confirm: () => {
            this.pageStep = 1;
          },
          cancel: () => {
            const { inProperty, outProperty } = this.tkFlowInfo();
            let defaultResult = inProperty.oldPaperAnswer
              .split('|')
              .map((item) => {
                let questionId = item.split('_')[0];
                let answerIds = item.split('_')[1];
                return {
                  questionId,
                  answerIds
                };
              });
            this.questionList.forEach((item, queIndex) => {
              item.forEach((it) => {
                defaultResult &&
                  defaultResult.forEach((dfit) => {
                    if (
                      dfit.questionId === it.questionNo &&
                      dfit.answerIds.includes(it.answerNo)
                    ) {
                      item.checked = true;
                      this.$set(it, 'checked', true);
                      this.$set(
                        this.answerList,
                        queIndex,
                        `${it.questionNo}_${dfit.answerIds}`
                      );
                    }
                  });
              });
            });
            // 快速测评强互斥选项禁用
            // 遍历出所有checked为true的选项
            let arr = [];
            this.questionList.forEach((it) => {
              it.forEach((item) => {
                if (item.checked) {
                  let qzhcList = [];
                  // 此时这个it需要去检查强互斥
                  ruleList.forEach((ruleItem) => {
                    if (
                      item.questionNo === ruleItem.questionId &&
                      ruleItem.answerIdList.includes(item.answerNo)
                    ) {
                      if (ruleItem.ruleNo === '02') {
                        qzhcList = ruleItem.questionList;
                        // 强制互斥逻辑判断，禁用对应的选项
                        this.questionList.forEach((quesArr, qIndex) => {
                          let qusetionNo = quesArr[0].questionNo;
                          qzhcList.forEach((hcques) => {
                            if (hcques.questionId === qusetionNo) {
                              quesArr.forEach((quseItem) => {
                                if (
                                  hcques.answerIds.includes(quseItem.answerNo)
                                ) {
                                  this.$set(quseItem, 'disabled', true);
                                } else {
                                  this.$set(quseItem, 'disabled', false);
                                }
                              });
                            }
                          });
                        });
                      }
                    }
                  });
                }
              });
            });
            console.log(arr);

            this.pageStep = 1;
          }
        });
      } else {
        this.pageStep = 1;
      }
    },

    getVisitQuestionList() {
      let _this = this;
      let { subjectNo } = this.$attrs;
      const { inProperty, outProperty } = this.tkFlowInfo();
      questionQry({
        paperType: 'K',
        userType: inProperty.userType
      }).then((data) => {
        let _resArr = data.data;
        let _queId = '';
        let _queNum = -1;
        let i = 0;
        _resArr.forEach((item) => {
          if (item.questionNo !== _queId) {
            _queId = item.questionNo;
            _queNum++;
            i = 0;
            item.extName =
              item.questionKind === QUESTION_KIND.MULTI ? '（多选）' : '';
            _this.$set(_this.visitQuestionList, _queNum, []);
          }
          item.checked = false;
          _this.$set(_this.visitQuestionList[_queNum], i++, item);
        });
      });
    },

    getQuestionList() {
      let _this = this;
      let { subjectNo } = this.$attrs;
      const { inProperty, outProperty } = this.tkFlowInfo();
      questionQry({
        paperType: subjectNo,
        userType: inProperty.userType
      })
        .then((data) => {
          let _resArr = data.data;
          let _queId = '';
          let _queNum = -1;
          let i = 0;
          _resArr.forEach((item) => {
            this.version = item.version;
            if (item.questionNo !== _queId) {
              _queId = item.questionNo;
              _queNum++;
              i = 0;
              item.extName =
                item.questionKind === QUESTION_KIND.MULTI ? '（多选）' : '';
              _this.$set(_this.questionList, _queNum, []);
            }
            // 增加checked属性来判定是否选中当前选项
            if (item.isAlter) {
              item.checked = true;
              _this.$set(
                _this.answerList,
                _queNum,
                `${item.questionNo}_${item.answerNo}`
              );
            } else {
              item.checked = false;
            }
            _this.$set(_this.questionList[_queNum], i++, item);
          });
        })
        .catch((error) => {
          _hvueToast({
            mes: error
          });
        });
    },

    selectVisitItem(item, aIndex, quesIndex) {
      if (item.disabled) {
        return;
      }
      let ansAtr = [];
      let quesNo = '';
      this.visitQuestionList[quesIndex].forEach((a) => {
        if (a.questionKind === QUESTION_KIND.MULTI) {
          if (item.answerNo === a.answerNo) {
            a.checked = !a.checked;
          }
          if (a.checked) {
            quesNo = a.questionNo;
            ansAtr.push(a.answerNo);
          }
        } else {
          if (a.answerNo === item.answerNo) {
            a.checked = true;
            this.$set(a, 'checked', true);
            quesNo = a.questionNo;
            ansAtr.push(a.answerNo);
          } else {
            a.checked = false;
            this.$set(a, 'checked', false);
          }
        }
      });
      // 选择后更改答案列表
      let a = '';
      if (ansAtr.length > 0) {
        a = `${quesNo}_${ansAtr.join('&')}`;
      }
      this.$set(this.visitAnswerList, quesIndex, a);
    },

    selectItem(item, aIndex, quesIndex) {
      let _item = item;
      if (item.disabled) {
        return;
      }
      let flag = false;
      let hcQuestiondesc1 = item.questionContent;
      let hcQuestiondesc2 = '';
      let hcIndex1 = quesIndex + 1;
      let hcIndex2 = 0;
      let ruleNo = '';

      let hcList = [];

      let qzhcList = [];
      ruleList.forEach((ruleItem) => {
        if (
          item.questionNo === ruleItem.questionId &&
          ruleItem.answerIdList.includes(item.answerNo)
        ) {
          if (ruleItem.ruleNo === '02') {
            // 强制互斥逻辑需在答案确认选中后执行
            // qzhcList = ruleItem.questionList;
            // // 强制互斥逻辑判断，禁用对应的选项
            // this.questionList.forEach((quesArr, qIndex) => {
            //   let qusetionNo = quesArr[0].questionNo;
            //   qzhcList.forEach((hcques) => {
            //     if (hcques.questionId === qusetionNo) {
            //       quesArr.forEach((quseItem) => {
            //         if (hcques.answerIds.includes(quseItem.answerNo)) {
            //           this.$set(quseItem, 'disabled', true);
            //         } else {
            //           this.$set(quseItem, 'disabled', false);
            //         }
            //       });
            //     }
            //   });
            // });
          } else if (ruleItem.ruleNo === '01') {
            this.questionList.forEach((quesArr, qIndex) => {
              ruleItem.questionList.forEach((ruleQuesItem) => {
                quesArr.forEach((quseItem) => {
                  if (
                    quseItem.questionNo === ruleQuesItem.questionId &&
                    ruleQuesItem.answerIds.includes(quseItem.answerNo)
                  ) {
                    if (quseItem.checked) {
                      // 有互斥选择确认
                      ruleNo = ruleItem.ruleNo;
                      hcIndex2 = qIndex + 1;
                      hcQuestiondesc2 = quseItem.questionContent;
                      if (
                        hcList.filter((item) => item.hcIndex2 === hcIndex2)
                          .length === 0
                      ) {
                        let tips = `${hcQuestiondesc1}（第${hcIndex1}题）与${hcQuestiondesc2}（第${hcIndex2}题）可能不符，请确认勾选的信息准确无误`;
                        hcList.push({
                          hcIndex2,
                          hcQuestiondesc2,
                          tips
                        });
                      }
                      flag = true;
                    }
                  }
                });
              });
            });
          }
        } else if (
          item.questionNo === ruleItem.questionId &&
          !ruleItem.answerIdList.includes(item.answerNo)
        ) {
          // if (ruleItem.ruleNo === '02') {
          //   // 强制互斥选择其他选项清除禁用
          //   qzhcList = ruleItem.questionList;
          //   // 强制互斥逻辑判断，清除禁用对应的选项
          //   this.questionList.forEach((quesArr, qIndex) => {
          //     let qusetionNo = quesArr[0].questionNo;
          //     qzhcList.forEach((hcques) => {
          //       if (hcques.questionId === qusetionNo) {
          //         quesArr.forEach((quseItem) => {
          //           this.$set(quseItem, 'disabled', false);
          //         });
          //       }
          //     });
          //   });
          // }
        }
      });
      console.log(hcList);
      let tips = `${hcQuestiondesc1}（第${hcIndex1}题）与${hcQuestiondesc2}（第${hcIndex2}题）可能不符，请确认勾选的信息准确无误`;

      if (hcIndex1 === 1) {
        // 第1题年龄题互斥
        const { inProperty } = this.tkFlowInfo();
        if (
          item.answerNo === '6' &&
          ((parseInt(inProperty.clientAge || 0) < 50 &&
            inProperty.clientGender === '0') ||
            (parseInt(inProperty.clientAge || 0) < 45 &&
              inProperty.clientGender === '1'))
        ) {
          ruleNo = '01';
          flag = true;
          tips = `您的年龄与选项可能存在不相符，请再次确认信息真实准确或进行修改`;
          hcList.push({
            hcIndex2: '',
            hcQuestiondesc2: '',
            tips
          });
        }
      }
      if (hcIndex1 === 8) {
        // 第8题年龄题互斥
        const { inProperty } = this.tkFlowInfo();
        if (item.answerNo === '4' && parseInt(inProperty.clientAge || 0) < 26) {
          ruleNo = '01';
          flag = true;
          tips = `您的投资经验与您的年龄情况可能不符，请确认勾选的信息准确无误`;
          hcList.push({
            hcIndex2: '',
            hcQuestiondesc2: '',
            tips
          });
        }
      }
      if (flag) {
        if (ruleNo === '01') {
          if (item.checked && item.questionKind === QUESTION_KIND.MULTI) {
            this.toSelectItem(item, aIndex, quesIndex);
            return;
          }
          let taskList = [];
          hcList.forEach((__item) => {
            let tips = `${hcQuestiondesc1}（第${hcIndex1}题）与${__item.hcQuestiondesc2}（第${__item.hcIndex2}题）可能不符，请确认勾选的信息准确无误`;
            const promise = () =>
              new Promise((resolve, reject) => {
                this.$TAlert({
                  tips: __item.tips,
                  hasCancel: true,
                  cancelBtn: '确认无误',
                  confirmBtn: '重新选择',
                  confirm: () => {
                    reject({ result: false });
                    // 清除当前页面的选择
                    this.questionList.forEach((itemd, idx) => {
                      itemd.forEach((it) => {
                        if (it.questionNo === _item.questionNo) {
                          this.$set(it, 'checked', false);
                          this.$set(this.answerList, idx, null);
                        }
                      });
                    });
                    let qzhcList = [];
                    console.log(item);
                    ruleList.forEach((ruleItem) => {
                      if (item.questionNo === ruleItem.questionId) {
                        if (ruleItem.ruleNo === '02') {
                          // 强制互斥选择其他选项清除禁用
                          qzhcList = ruleItem.questionList;
                          // 强制互斥逻辑判断，清除禁用对应的选项
                          this.questionList.forEach((quesArr, qIndex) => {
                            let qusetionNo = quesArr[0].questionNo;
                            qzhcList.forEach((hcques) => {
                              if (hcques.questionId === qusetionNo) {
                                quesArr.forEach((quseItem) => {
                                  this.$set(quseItem, 'disabled', false);
                                });
                              }
                            });
                          });
                        }
                      }
                    });
                    return;
                  },
                  cancel: () => {
                    // 需要留痕，然后继续执行
                    addClientCritMark({
                      flowToken: sessionStorage.getItem('TKFlowToken'),
                      markType: '2',
                      markContent: tips,
                      confirmFlag: '1'
                    })
                      .then((res) => {
                        // this.toSelectItem(item, aIndex, quesIndex);
                        resolve({ item, aIndex, quesIndex, result: true });
                      })
                      .catch((err) => {
                        this.$TAlert({
                          tips: '很抱歉，受理失败了',
                          confirmBtn: '确定'
                        });
                        this.questionList.forEach((itemd, idx) => {
                          itemd.forEach((it) => {
                            if (it.questionNo === _item.questionNo) {
                              this.$set(it, 'checked', false);
                              this.$set(this.answerList, idx, null);
                            }
                          });
                        });
                        let qzhcList = [];
                        ruleList.forEach((ruleItem) => {
                          if (
                            item.questionNo === ruleItem.questionId &&
                            ruleItem.answerIdList.includes(item.answerNo)
                          ) {
                            if (ruleItem.ruleNo === '02') {
                              // 强制互斥选择其他选项清除禁用
                              qzhcList = ruleItem.questionList;
                              // 强制互斥逻辑判断，清除禁用对应的选项
                              this.questionList.forEach((quesArr, qIndex) => {
                                let qusetionNo = quesArr[0].questionNo;
                                qzhcList.forEach((hcques) => {
                                  if (hcques.questionId === qusetionNo) {
                                    quesArr.forEach((quseItem) => {
                                      this.$set(quseItem, 'disabled', false);
                                    });
                                  }
                                });
                              });
                            }
                          }
                        });
                        reject({ result: false });
                      });
                  }
                });
              });
            taskList.push(promise);
          });
          const lin = async () => {
            let result = '';
            console.log(taskList.length);
            while (taskList.length > 0) {
              try {
                await taskList[0]();
                taskList.splice(0, 1);
                result = true;
              } catch (err) {
                result = false;
                taskList = [];
              }
            }
            return result;
          };

          lin().then((res) => {
            if (res) {
              this.toSelectItem(item, aIndex, quesIndex);
            }
          });
        } else if (ruleNo === '02') {
          // 强互斥
          // this.$TAlert({
          //   tips,
          //   confirmBtn: '重新选择'
          // });
          // return;
        }
      } else {
        this.toSelectItem(item, aIndex, quesIndex);
      }
    },

    toSelectItem(item, aIndex, quesIndex) {
      if (item.questionNo === '8020' && item.answerNo === '1') {
        // 15题选A
        let tips =
          '尊敬的投资者，该题选择此选项，您的风险承受能力等级将被认定为C1（最低类别）。我司普通股票交易服务的风险等级为R3（中风险），为保护您的合法权益，我司将不会给您提供普通股票交易服务，您确定该选项反映您的真实情况吗？';
        this.$TAlert({
          tips,
          hasCancel: true,
          cancelBtn: '确认无误',
          confirmBtn: '重新选择',
          confirm: () => {
            this.questionList.forEach((itemd, idx) => {
              itemd.forEach((it) => {
                if (it.questionNo === item.questionNo) {
                  this.$set(it, 'checked', false);
                  this.$set(this.answerList, idx, null);
                }
              });
            });
            let qzhcList = [];
            ruleList.forEach((ruleItem) => {
              if (item.questionNo === ruleItem.questionId) {
                if (ruleItem.ruleNo === '02') {
                  // 强制互斥选择其他选项清除禁用
                  qzhcList = ruleItem.questionList;
                  // 强制互斥逻辑判断，清除禁用对应的选项
                  this.questionList.forEach((quesArr, qIndex) => {
                    let qusetionNo = quesArr[0].questionNo;
                    qzhcList.forEach((hcques) => {
                      if (hcques.questionId === qusetionNo) {
                        quesArr.forEach((quseItem) => {
                          this.$set(quseItem, 'disabled', false);
                        });
                      }
                    });
                  });
                }
              }
            });
            return;
          },
          cancel: () => {
            // 需要留痕，然后继续执行
            addClientCritMark({
              flowToken: sessionStorage.getItem('TKFlowToken'),
              markType: '2',
              markContent: tips,
              confirmFlag: '1'
            })
              .then((res) => {
                this.toSelectItemConfirm(item, aIndex, quesIndex);
              })
              .catch((err) => {
                this.$TAlert({
                  tips: '很抱歉，受理失败了',
                  confirmBtn: '确定'
                });
                this.questionList.forEach((itemd, idx) => {
                  itemd.forEach((it) => {
                    if (it.questionNo === _item.questionNo) {
                      this.$set(it, 'checked', false);
                      this.$set(this.answerList, idx, null);
                    }
                  });
                });
                let qzhcList = [];
                ruleList.forEach((ruleItem) => {
                  if (
                    item.questionNo === ruleItem.questionId &&
                    ruleItem.answerIdList.includes(item.answerNo)
                  ) {
                    if (ruleItem.ruleNo === '02') {
                      // 强制互斥选择其他选项清除禁用
                      qzhcList = ruleItem.questionList;
                      // 强制互斥逻辑判断，清除禁用对应的选项
                      this.questionList.forEach((quesArr, qIndex) => {
                        let qusetionNo = quesArr[0].questionNo;
                        qzhcList.forEach((hcques) => {
                          if (hcques.questionId === qusetionNo) {
                            quesArr.forEach((quseItem) => {
                              this.$set(quseItem, 'disabled', false);
                            });
                          }
                        });
                      });
                    }
                  }
                });
              });
          }
        });
      } else {
        this.toSelectItemConfirm(item, aIndex, quesIndex);
      }
    },

    toSelectItemConfirm(item, aIndex, quesIndex) {
      if (item.disabled) {
        return;
      }

      let qzhcList = [];
      // 强制互斥逻辑选项禁用
      ruleList.forEach((ruleItem) => {
        if (
          item.questionNo === ruleItem.questionId &&
          ruleItem.answerIdList.includes(item.answerNo)
        ) {
          if (ruleItem.ruleNo === '02') {
            // 强制互斥逻辑需在答案确认选中后执行
            qzhcList = ruleItem.questionList;
            // 强制互斥逻辑判断，禁用对应的选项
            this.questionList.forEach((quesArr, qIndex) => {
              let qusetionNo = quesArr[0].questionNo;
              qzhcList.forEach((hcques) => {
                if (hcques.questionId === qusetionNo) {
                  quesArr.forEach((quseItem) => {
                    if (hcques.answerIds.includes(quseItem.answerNo)) {
                      this.$set(quseItem, 'disabled', true);
                    } else {
                      this.$set(quseItem, 'disabled', false);
                    }
                  });
                }
              });
            });
          }
        }
      });
      // 强制互斥选择其他选项清除禁用
      ruleList.forEach((ruleItem) => {
        if (
          item.questionNo === ruleItem.questionId &&
          !ruleItem.answerIdList.includes(item.answerNo)
        ) {
          if (ruleItem.ruleNo === '02') {
            // 强制互斥选择其他选项清除禁用
            qzhcList = ruleItem.questionList;
            // 强制互斥逻辑判断，清除禁用对应的选项
            this.questionList.forEach((quesArr, qIndex) => {
              let qusetionNo = quesArr[0].questionNo;
              qzhcList.forEach((hcques) => {
                if (hcques.questionId === qusetionNo) {
                  quesArr.forEach((quseItem) => {
                    this.$set(quseItem, 'disabled', false);
                  });
                }
              });
            });
          }
        }
      });

      const _this = this;
      let ansAtr = [];
      let quesNo = '';
      _this.questionList[quesIndex].forEach((a) => {
        if (a.questionKind === QUESTION_KIND.MULTI) {
          if (item.answerNo === a.answerNo) {
            a.checked = !a.checked;
          }
          if (a.checked) {
            quesNo = a.questionNo;
            ansAtr.push(a.answerNo);
          }
        } else {
          if (a.answerNo === item.answerNo) {
            a.checked = true;
            quesNo = a.questionNo;
            ansAtr.push(a.answerNo);
          } else {
            a.checked = false;
          }
        }
      });
      // 选择后更改答案列表
      let a = '';
      if (ansAtr.length > 0) {
        a = `${quesNo}_${ansAtr.join('&')}`;
      }
      this.$set(this.answerList, quesIndex, a);
      if (
        this.questionList.every((question) => question.some((a) => a.checked))
      ) {
        // this.submitQuestion();
      } else if (
        this.questionList[quesIndex][0].questionKind != QUESTION_KIND.MULTI &&
        quesIndex < this.questionList.length - 1
      ) {
        setTimeout(() => {
          this.jump(quesIndex + 1);
        }, 300);
      }
    },

    toVisitNext() {
      if (this.visitSubmitDisable) {
        return;
      }
      if (this.visitAnswerList.length > 0) {
        this.toNext();
      } else {
        _hvueToast({
          mes: '请选择问卷回访答案'
        });
      }
    },

    async submitQuestion() {
      const _this = this;
      //校验答案是否选择完成
      if (!_this.verifyAnswer()) return;
      let flag = false;
      let curAnswerContent = '';
      // let desc =
      console.log('visitQuestionList', this.visitQuestionList);
      this.visitQuestionList[0].forEach((item, index) => {
        if (item.checked) {
          curAnswerContent = item.answerContent;
          if (index == 3) {
            // 选择的答案为D选项
            flag = true;
          }
        }
      });
      if (flag) {
        let desc =
          '因您选择D选项可能存在与您实际信息不符，请您结合自身真实信息选择是否提交或者重新答题';
        this.$TAlert({
          title: '',
          hasCancel: true,
          tips: desc,
          confirmBtn: '重新答题',
          cancelBtn: '符合本人真实情况，确认提交',
          confirm: () => {
            // this.riskAfresh();
            this.visitQuestionList[0].forEach((item) => {
              item.checked = false;
            });
            this.showVisit = true;
          },
          cancel: () => {
            addClientCritMark({
              flowToken: sessionStorage.getItem('TKFlowToken'),
              markType: '20',
              markContent: `${desc}</br>客户回答：${curAnswerContent}</br>`,
              confirmFlag: '1',
              opEntrusWay: '7',
              merchantld: '0',
              opSource: '4',
              opStation: ''
            }).then((res) => {
              console.log(res);
            });
            this.commonFunc(_this);
          }
        });
      } else {
        this.commonFunc(_this);
      }
    },
    commonFunc(_this) {
      const flowToken = sessionStorage.getItem('TKFlowToken');
      let { subjectNo } = this.$attrs;
      questionPreSubmit({
        flowToken,
        paperType: subjectNo,
        corpRiskLevel: '1',
        version: this.version,
        paperAnswer: this.answerList.join('|') // 试卷答案对象数据
      })
        .then((data) => {
          let { subjectNo } = this.$attrs;
          const { outProperty, flowName } = this.tkFlowInfo();
          Object.assign(_this.testResult, data.data);
          // 拿到返回的等级 减去当前等级 大于等于2时 需要弹窗提示
          // getRiskLevel为0说明是首次测评 不走弹窗判断
          if (this.getRiskLevel != '0') {
            this.compareRiskLevel =
              Number(data.data.questionSubmitPreVo.corpRiskLevel) -
              Number(this.getRiskLevel);
          }
          console.log('compareRiskLevel', this.compareRiskLevel);
          // 提交当前结果到上下文
          // this.pageStep = 2;
          updateFlowForm({
            source: `${flowName}-初始化`,
            flowToken: sessionStorage.getItem('TKFlowToken'),
            formParam: {
              paperAnswer: this.answerList.join('|'),
              paperVersion: this.version,
              paperType: subjectNo,
              revisitPaperType: 'K',
              revisitPaperAnswer: this.visitAnswerList.join('|'),
              corpRiskLevel: this.testResult.riskBear.includes('最低')
                ? '最低'
                : this.testResult.questionSubmitPreVo.corpRiskLevel

              // epaperSignJson,
              // epaperESignedFlag: haseEpaperESigned > 0 ? '1' : '0'
            }
          }).then(async () => {
            this._queryAgreement();
            // this.pageStep = 2;
          });
          // updateFlowForm({
          //   source: `${flowName}-初始化`,
          //   flowToken: sessionStorage.getItem('TKFlowToken'),
          //   formParam: {
          //     paperAnswer: this.answerList.join('|'),
          //     paperVersion: this.version,
          //     paperType: subjectNo,
          //     revisitPaperType: 'K',
          //     revisitPaperAnswer: '1077_4'
          //   }
          // }).then((res) => {
          //   this.pageStep = 2;
          // });
        })
        .catch((err) => {
          if (err) {
            _hvueToast({
              mes: err
            });
          }
        });
    },

    riskAfresh() {
      // 重新测评
      this.answerList = [];
      this.questionList.forEach((item, index) => {
        item.forEach((it) => {
          if (it.degreeCode !== '' && it.isAlter) {
            this.answerList[index] = `${it.questionNo}_${it.answerNo}`;
            return;
          } else {
            it.checked = false;
          }
        });
      });
      this.$nextTick(() => {
        this.jump(0);
        this.testResult = this.$options.data().testResult;
      });
    },

    // 协议相关
    getProtocolParam() {
      return {
        agreementBizType: this.$attrs.agreementNodeNo.split(':')[0],
        agreementNodeNo: this.$attrs.agreementNodeNo.split(':')[1],
        agreementExt: this.$attrs.agreementExt
      };
    },

    async _queryAgreement() {
      let param = this.getProtocolParam();
      let reqParams = {
        flowToken: sessionStorage.getItem('TKFlowToken'),
        ...param
      };
      queryAgreementExtV3(reqParams)
        .then((res) => {
          const agreementList = res.data.agreementList;
          this.epaperESignedFlag = res.data.epaperESignedFlag;
          this.agreeList = agreementList.map((it, i) => {
            it.agreementBizType = param.agreementBizType;
            return it;
          });
          this.pageStep = 2;
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    },

    back() {},

    agreeCallBack() {
      this.showAgreeDetail = false;
    },

    detailSignAgree() {},

    toNextbefore() {
      if (this.submitDisable) {
        return;
      }
      if (this.questionShowFlag === '1') {
        this.showVisit = true;
      } else {
        this.toNext();
      }
    },

    toNext() {
      this.showVisit = false;
      this.submitQuestion();
    },

    toDetail(index) {
      this.showAgreeDetail = true;
      this.agreeIndex = index;
    },
    nextFlow() {
      console.log(this.visitAnswerList);
      if (this.agreeList.length > 0) {
        if (
          !this.signed &&
          this.agreeList.filter((it) => it.agreementNo === '100030').length > 0
        ) {
          _hvueToast({
            mes: '请阅读并勾选协议'
          });
          return;
        }
        let haseEpaperESigned = this.agreeList.filter(
          (item) => item.agreementNo === '100030'
        ).length;
        const tkFlowInfo = this.tkFlowInfo();
        signAgreeV2(tkFlowInfo, this.agreeList, this.$attrs)
          .then((epaperSignJson) => {
            this.toSubmit(epaperSignJson, haseEpaperESigned);
          })
          .catch((error) => {
            _hvueToast({
              mes: error
            });
          });
      } else {
        this.toSubmit();
      }
    },
    flowSubmitSucBack() {
      if ($hvue.platform != 0) {
        // 原生APP
        this.eventMessage(this, EVENT_NAME.TO_INDEX);
      } else {
        if (hmosUtil.checkHM) {
          hmosUtil.closePage({ valid: 1 });
          return;
        } else if ($h.getSession('history_list').records.length > 1) {
          // 特殊处理，风测如果是通过准入跳转过来的，完成的时候返回准入
          this.eventMessage(this, EVENT_NAME.TO_INDEX);
          return;
        }
        if ($h.getSession('channelType') === '2005000000000') {
          callNativeHandler(
            'JSWangTingEvent',
            {
              action: 'CloseWebVC',
              param: {}
            },
            function () {}
          );
          return;
        } else if ($h.getSession('channelType') === '2095000000000') {
          // 支付宝渠道
          this.eventMessage(this, EVENT_NAME.TO_INDEX);
          return;
        } else if (
          $h.getSession('from') === 'pc' ||
          $h.getSession('appId') !== 'yjbwx'
        ) {
          this.$TAlert({
            title: '温馨提示',
            tips: '您已完成当前业务操作,请退出本业务或继续办理其他业务',
            hasConfirm: false
          });
          return;
        } else {
          console.log('toCheckOtherChannelType');
          if ($h.getSession('channelType') === '2095000000000') {
            // alipayUtil.toIndex();
            // 支付宝渠道
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
            return;
          } else {
            this.eventMessage(this, EVENT_NAME.TO_INDEX);
          }
          // this.eventMessage(this, EVENT_NAME.PREV_FLOW);
        }
      }
      // this.eventMessage(this, EVENT_NAME.TO_INDEX);
    },
    toSubmit(epaperSignJson, haseEpaperESigned) {
      let { subjectNo } = this.$attrs;
      const { outProperty, flowName } = this.tkFlowInfo();
      updateFlowForm({
        source: `${flowName}-初始化`,
        flowToken: sessionStorage.getItem('TKFlowToken'),
        formParam: {
          paperAnswer: this.answerList.join('|'),
          paperVersion: this.version,
          paperType: subjectNo,
          revisitPaperType: 'K',
          revisitPaperAnswer: this.visitAnswerList.join('|'),
          epaperSignJson,
          epaperESignedFlag: this.epaperESignedFlag
        }
      })
        .then(() => {
          // 提交前留痕
          let tips = '';
          let version = `问卷版本号:${this.version}</br>`;
          let tips1 =
            this.testResult.tipIndexes !== ''
              ? this.$refs.tips1.innerHTML + '</br>'
              : '';
          let tips2 = this.$refs.tips2.innerHTML + '</br>';
          let tips3 =
            this.testResult.isHigher === '1'
              ? this.$refs.tips3.innerHTML + '</br>'
              : '';
          let tips4 = this.$refs.tips4.innerHTML;
          tips = `${version}${tips1}${tips2}${tips3}${tips4}`;
          addClientCritMark({
            flowToken: sessionStorage.getItem('TKFlowToken'),
            markType: '13',
            markContent: tips,
            confirmFlag: '1'
          })
            .then(() => {
              // 调用flowSubmit
              // return;
              flowSubmit({
                flowToken: sessionStorage.getItem('TKFlowToken')
              })
                .then((res) => {
                  if (res.code === 0) {
                    // 走完流程后放出弹窗 具体展不展示弹窗需要看评级相差是否大于2
                    // 当showSelect为true并且compareRiskLevel大于等于2时 就会弹窗提示，不自动往下执行
                    this.showSelect = true;
                    console.log('compareRiskLevel', this.compareRiskLevel);
                    $h.setSession('optType', 'loginSuccess');
                    // 交易端登录信息同步
                    // let reqParams = {
                    //   funcNo: '60099',
                    //   actionType: '5',
                    //   params: {
                    //     optType: '1',
                    //     isSuccess: '1'
                    //   }
                    // };
                    // const res = $h.callMessageNative(reqParams);
                    if (this.compareRiskLevel < 2) this.flowSubmitSucBack();
                  } else {
                    this.$TAlert({
                      tips: '提交失败',
                      confirm: () => {
                        if ($hvue.platform != 0) {
                          // 原生APP
                          this.eventMessage(this, EVENT_NAME.TO_INDEX);
                        } else {
                          if (hmosUtil.checkHM) {
                            hmosUtil.closePage({ valid: 1 });
                            return;
                          } else if (
                            $h.getSession('channelType') === '2005000000000'
                          ) {
                            callNativeHandler(
                              'JSWangTingEvent',
                              {
                                action: 'CloseWebVC',
                                param: {}
                              },
                              function () {}
                            );
                            return;
                          } else if (
                            $h.getSession('channelType') === '2095000000000'
                          ) {
                            // 支付宝渠道
                            this.eventMessage(this, EVENT_NAME.TO_INDEX);
                            return;
                          } else {
                            if ($h.getSession('appId') !== 'yjbwx') {
                              this.$TAlert({
                                title: '温馨提示',
                                tips: '您已完成当前业务操作,请退出本业务或继续办理其他业务',
                                hasConfirm: false
                              });
                              return;
                            } else {
                              window.history.go(-1);
                            }
                          }
                        }
                      }
                    });
                  }
                })
                .catch((err) => {
                  this.$TAlert({
                    tips: '提交失败',
                    confirmBtn: '确定',
                    confirm: () => {
                      if ($hvue.platform != 0) {
                        // 原生APP
                        this.eventMessage(this, EVENT_NAME.TO_INDEX);
                      } else {
                        if ($h.getSession('channelType') === '2005000000000') {
                          callNativeHandler(
                            'JSWangTingEvent',
                            {
                              action: 'CloseWebVC',
                              param: {}
                            },
                            function () {}
                          );
                          return;
                        } else if (
                          $h.getSession('channelType') === '2095000000000'
                        ) {
                          // 支付宝渠道
                          this.eventMessage(this, EVENT_NAME.TO_INDEX);
                          return;
                        } else {
                          // this.eventMessage(this, EVENT_NAME.PREV_FLOW);
                          window.history.go(-1);
                        }
                        // this.eventMessage(this, EVENT_NAME.PREV_FLOW);
                        // this.eventMessage(this, EVENT_NAME.TO_INDEX);
                      }
                    }
                  });
                });
            })
            .catch(() => {
              this.$TAlert({
                tips: '很抱歉，受理失败了',
                confirmBtn: '确定'
              });
            });
        })
        .catch((err) => {
          this.$TAlert({
            tips: '很抱歉，受理失败了',
            confirmBtn: '确定'
          });
        });
    }
  }
};
</script>

<style lang="less" scoped></style>
