export const ruleList = [
  // 第2题选择A或B，第3题选择C或D,非强制互斥
  {
    questionId: '889',
    answerIdList: ['1', '2'],
    ruleType: '01',
    ruleNo: '01',
    questionList: [
      {
        questionId: '890',
        answerIds: ['5', '6']
      }
    ]
  },
  {
    questionId: '890',
    answerIdList: ['5', '6'],
    ruleType: '01',
    ruleNo: '01',
    questionList: [
      {
        questionId: '889',
        answerIds: ['1', '2']
      }
    ]
  },
  // 第12题选择包括BCD选项中的一项或两项，第13题选择A,非强制互斥
  {
    questionId: '1027',
    answerIdList: ['2', '3', '4'],
    ruleType: '01',
    ruleNo: '01',
    questionList: [
      {
        questionId: '1028',
        answerIds: ['1']
      }
    ]
  },
  {
    questionId: '1028',
    answerIdList: ['1'],
    ruleType: '01',
    ruleNo: '01',
    questionList: [
      {
        questionId: '1027',
        answerIds: ['2', '3', '4']
      }
    ]
  },
  // 第12题选择包括BCD选项中的一项或两项，第15题选择A,非强制互斥
  {
    questionId: '1027',
    answerIdList: ['2', '3', '4'],
    ruleType: '01',
    ruleNo: '01',
    questionList: [
      {
        questionId: '8020',
        answerIds: ['1']
      }
    ]
  },
  {
    questionId: '8020',
    answerIdList: ['1'],
    ruleType: '01',
    ruleNo: '01',
    questionList: [
      {
        questionId: '1027',
        answerIds: ['2', '3', '4']
      }
    ]
  },
  // 以下为强制互斥
  // 第15题选择A，第13题选择为BCD选项中的一项
  {
    questionId: '1028',
    answerIdList: ['2', '3', '4'],
    ruleType: '01',
    ruleNo: '02',
    questionList: [
      {
        questionId: '8020',
        answerIds: ['1']
      }
    ]
  },
  {
    questionId: '8020',
    answerIdList: ['1'],
    ruleType: '01',
    ruleNo: '02',
    questionList: [
      {
        questionId: '1028',
        answerIds: ['2', '3', '4']
      }
    ]
  },
  // 第5题选择A，第17题选择E
  {
    questionId: '892',
    answerIdList: ['1'],
    ruleType: '01',
    ruleNo: '02',
    questionList: [
      {
        questionId: '1029',
        answerIds: ['5']
      }
    ]
  },
  {
    questionId: '1029',
    answerIdList: ['5'],
    ruleType: '01',
    ruleNo: '02',
    questionList: [
      {
        questionId: '892',
        answerIds: ['1']
      }
    ]
  }
];
