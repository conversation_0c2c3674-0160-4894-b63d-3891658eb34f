<template>
  <section class="main fixed" data-page="home" style="position: fixed">
    <t-header></t-header>
    <article class="content">
      <h5 class="com_title">请选择见证营业部</h5>
      <div class="com_box">
        <div class="input_form">
          <div
            class="input_text text"
            @click="showOpenSelect = !showOpenSelect"
          >
            <span class="tit">营业部</span>
            <input
              class="t1 p_r70"
              type="text"
              readonly="readonly"
              placeholder="请选择营业部"
              :value="branchName"
            />
            <!-- <a class="icon_location" href="javascript:void(0);">附近</a> -->
            <!-- <div class="yyb_info_text">
              <p>
                <i class="addr"></i
                >江西省南昌市西湖区广场南路205号恒茂国际华城16栋A座501室
              </p>
              <p><i class="tel"></i>(0755）955337</p>
            </div> -->
          </div>
          <div class="imp_c_tips">
            <p>
              <span class="imp"
                >选择任何营业部开户都可在全国任意网点享受相同的服务</span
              >
            </p>
          </div>
        </div>
      </div>
      <div class="com_box mt10">
        <div class="input_form">
          <div class="input_text text">
            <span class="tit">预约日期</span>
            <div
              class="dropdown"
              style="
                padding-top: 0;
                padding-bottom: 0;
                display: flex;
                align-items: center;
              "
              placeholder="请选择"
            >
              <h-picker
                slot="right"
                v-model="advanceTime"
                :columns="columns"
                separator=" "
              />
            </div>
          </div>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" :class="{ disabled: isCheck }" @click="toNext"
          >下一步</a
        >
      </div>
    </footer>

    <openDressSelect
      v-model="showOpenSelect"
      :default="branchInfo"
      :is-open-dress="true"
      @selCallBack="openCallBack"
    ></openDressSelect>
  </section>
</template>

<script>
import { getSysBranchInfo, videoWatchList } from '@/service/service';
import openDressSelect from '@/components/openDressSelect';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'SelectBusinessDepartment',
  inject: ['eventMessage'],
  components: {
    openDressSelect
  },
  data() {
    return {
      branchName: '',
      branchNo: '',
      advanceTime: '',
      showOpenSelect: false,
      branchInfo: {},
      columns: [
        [
          { label: '', id: '' },
          { label: '', id: '' },
          { label: '', id: '' }
        ],
        [
          { label: '', id: '12:00' },
          { label: '', id: '17:00' }
        ]
      ]
    };
  },
  computed: {
    isCheck() {
      return !this.advanceTime || !this.branchName;
    }
  },
  mounted() {
    // TODO 根据ip获取附近营业部
    // getSysBranchInfo({ branchNo }).then((res) => {
    //   this.branchInfo = res.data[0];
    // });
    this.getDateWeek();
  },
  methods: {
    toNext() {
      if (this.isCheck) return;
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        preBranchNo: this.branchNo,
        preBranchName: this.branchName,
        preTime: this.advanceTime
      });
    },

    openCallBack(item) {
      this.branchName = item.branchName;
      this.branchNo = item.branchNo;
    },

    /* 获取日期和周 */
    getDateWeek() {
      videoWatchList({ flowToken: sessionStorage.getItem('TKFlowToken') }).then(
        (res) => {
          let lateTimeArr = res.data.recordList.map((item) => {
            return item.expireTime ? item.expireTime.split('天')[0] : '';
          });
          // todo 获取过期最小时间
          let overDay = Math.min.apply(Math, lateTimeArr);

          /* 得到当前日期的时间戳 */
          const timestamp = Date.now();
          let dateWeek = Array.from(new Array(overDay)).map((_, i) => {
            /* 得到当前周每一天的时间戳 */
            const weekTimestamp = new Date(timestamp + i * 24 * 60 * 60 * 1000);
            const date =
              String(weekTimestamp.getFullYear()) +
              '-' +
              String(weekTimestamp.getMonth() + 1).padStart(2, '0') +
              '-' +
              String(new Date(weekTimestamp).getDate()).padStart(2, '0');
            let week = weekTimestamp.getDay();
            return {
              label: date,
              week,
              id: date
            };
          });
          dateWeek = dateWeek.filter(
            (item) => item.week != 0 && item.week != 6
          );
          this.columns = [
            dateWeek,
            [
              { label: '09:00~12:00', id: '09:00~12:00' },
              { label: '13:00~17:00', id: '13:00~17:00' }
            ]
          ];
        }
      );
    }
  }
};
</script>
