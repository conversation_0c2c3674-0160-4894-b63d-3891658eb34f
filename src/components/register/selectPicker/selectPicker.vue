<template>
  <div class="picker-box">
    <van-field
      v-model="pickerValue"
      :label="label"
      is-link
      type="textarea"
      rows="1"
      readonly
      autosize
      :input-align="inputAlign"
      :placeholder="placeholder"
      :required="required"
      :error-message="errorMessages.join(',')"
      @click="show = true"
      @focus="iptFocus"
    />
    <van-popup v-model="show" round position="bottom">
      <div class="layer_tit">
        <h3>请选择</h3>
        <a class="close" @click="show = false"></a>
      </div>
      <div class="layer_cont">
        <ul class="select_list">
          <li
            v-for="item in options"
            :key="item.name"
            :class="{ active: value === item.name }"
            @click="onConfirm(item)"
          >
            <span>{{ item.label }}</span>
          </li>
        </ul>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'SelectPicker',
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    defaultValue: {
      type: [String, Number],
      default: ''
    },
    label: {
      type: String,
      default: ''
    },
    options: {
      type: Array,
      default: () => []
    },
    readonly: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    },
    inputAlign: {
      type: String,
      default: 'left'
    },
    errorMessages: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      show: false
    };
  },
  computed: {
    pickerValue() {
      let text = '';
      this.options.forEach((item) => {
        if (item.name == this.value) {
          text = item.label;
        }
      });
      return text;
    },
    defaultIndex() {
      let index = 0;
      this.options.forEach((item, id) => {
        if (item.name == this.value) {
          index = id;
        }
      });
      return index;
    }
  },
  watch: {
    defaultValue(val) {
      this.$emit('change', val);
    }
  },
  created() {
    if (this.defaultValue) {
      this.$emit('change', this.defaultValue);
    }
  },
  methods: {
    iptFocus() {
      // 禁止弹出手机键盘
      document.activeElement.blur();
    },

    onConfirm(value) {
      if (this.readonly) return;
      if (value.name) {
        this.$emit('change', value.name);
      } else {
        this.$emit('change', '');
      }
      this.show = false;
    }
  }
};
</script>

<style lang="scss" scoped>
// .picker-box {
//   position: relative;
//   width: 100%;
//   min-height: 24px;
//   .picker-cell {
//     width: 100%;
//     height: 100%;
//     min-height: 24px;
//     padding: 0;
//   }
// }
</style>
