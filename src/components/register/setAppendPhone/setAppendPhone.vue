<template>
  <article class="content">
    <div class="info_compage">
      <div class="com_title">
        <h5>{{ comTitle }}</h5>
      </div>
      <div class="input_form">
        <div class="input_text text">
          <span class="tit active">手机号</span>
          <van-field
            v-model.number="phoneNumInput"
            clearable
            class="t1"
            type="number"
            :maxlength="11"
            placeholder="请输入手机号"
          />
          <!-- <input
            v-model.number="phoneNumInput"
            ref="phoneNumInput"
            class="t1"
            type="number"
            placeholder="请输入手机号"
            :max="11"
            :maxlength="11"
            @input="MobileLength"
          /> -->
          <a
            v-if="$attrs.modify_mobile_num"
            class="code_btn2"
            @click="modifyMobile"
            >修改
          </a>
          <a
            v-else-if="$attrs.en_change_mobile_tel"
            class="code_btn2"
            @click="changeMobile"
            >变更手机号
          </a>
        </div>
        <div class="input_text text code">
          <span class="tit active">验证码</span>
          <input
            id="smsCode"
            v-model="smsCode"
            @input="smsCode = smsCode.replace(/[^\d]/g, '')"
            class="t1"
            type="tel"
            maxlength="6"
            placeholder="请输入短信验证码"
            autocomplete="off"
          />
          <sms-code-btn
            v-model="uuid"
            :need-img-code="false"
            :mobile-no="phone"
            biz-type="010200"
            @send-result="SMSCodeCallback"
          />
        </div>
      </div>
      <div v-if="$attrs.suport_voice" class="cond_tips">
        短信验证码收不到？试试<a @click="smsVoice" class="com_link"
          >语音验证码</a
        >吧！
      </div>
    </div>
  </article>
</template>

<script>
import SmsCodeBtn from '@/components/SmsCodeBtn.vue';
import { smsCodeVerification } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import { formatMobileNo } from '@/common/filter';
import HmosUtil from '@/common/HmosUtil';
const hmosUtil = new HmosUtil({});

export default {
  name: 'setAppendPhone',
  inject: ['eventMessage'],
  components: {
    SmsCodeBtn
  },
  data() {
    return {
      phone: '',
      smsCode: '',
      imgSrc: '',
      uuid: '',
      desensitize: true //是否手机号脱敏
    };
  },
  props: {
    com_title: {
      type: String,
      default: ''
    }
  },
  computed: {
    phoneNumInput: {
      get() {
        return this.phone;
      },
      set(val) {
        this.phone = val;
      }
    },
    comTitle() {
      return this.com_title === ''
        ? '融资融券交易过程中所有通知均会通过短信发送到您的手机，包括预警、追加保证金和平仓通知等。'
        : this.com_title;
    }
  },
  watch: {
    smsCode(v) {
      if (v.length > 0) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '下一步',
          display: true,
          btnStatus: 2,
          data: () => {
            this.toNext();
          }
        });
      } else {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
      }
    }
  },
  created() {
    this.$store.commit('flow/setWhiteBg', true);
    window.viewShowCallBack = this.viewShowCallBack;
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  mounted() {
    this.renderingView();
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
  },
  methods: {
    viewShowCallBack() {
      if (this.$attrs.en_change_mobile_tel) {
        this.renderingView();
      }
    },
    renderingView() {
      // clientInfoQryV2()
      //   .then((res) => {
      //     if (res.data.organFlag === '0') {
      //       //只有个人户获取手机号
      //       this.phone = res.data.mobileTel;
      //     }
      //   })
      //   .catch((err) => {
      //     _hvueToast({
      //       mes: err
      //     });
      //   });
    },
    back() {
      this.$router.back();
    },
    MobileLength() {
      if (`${this.phoneNumInput}`.length > 11) {
        this.phoneNumInput = Number(`${this.phoneNumInput}`.slice(0, 11));
      }
      console.info(this.phoneNumInput);
    },

    SMSCodeCallback(flag) {
      console.log(flag);
      if (!flag) {
        this.uuid = '';
      }
    },

    changeMobile() {
      // 跳转至个人资料
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010004', initJumpMode: '0' });
      });
      /* // 跳转至个人资料
      if ($hvue.platform === '0') {
        window.location.href = $hvue.customConfig.thirdPartyUrl.basicInfo;
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          params: {
            url: $hvue.customConfig.thirdPartyUrl.basicInfo,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      } */
    },

    modifyMobile() {
      this.desensitize = false;
      this.$nextTick(() => {
        this.$refs.phoneNumInput.focus();
      });
    },

    smsVoice() {
      if (!this.uuid) {
        _hvueAlert({
          title: '温馨提示',
          mes: '请先获取短信验证码'
        });
        return false;
      }
      _hvueConfirm({
        title: '温馨提示',
        mes: '亲，您可以使用输入的手机号，拨打95310-按3-按1，收听语音验证码。',
        opts: [
          {
            txt: '取消',
            color: '#333333'
          },
          {
            txt: '立即拨打',
            callback: () => {
              if (hmosUtil.checkHM) {
                hmosUtil.callPhone('95310');
              } else if ($hvue.platform === '0') {
                window.location.href = 'tel:95310';
              } else {
                let reqParams = {
                  funcNo: '50220',
                  telNo: '95310',
                  callType: '0'
                };
                console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
                const res = $h.callMessageNative(reqParams);
                console.log(`请求结果为: ~~${JSON.stringify(res)}`);
              }
            }
          }
        ]
      });
    },

    toNext() {
      if (!this.uuid) {
        _hvueToast({
          mes: '请先获取短信验证码'
        });
        return false;
      }
      if (this.smsCode.length !== 6) {
        _hvueToast({
          mes: '短信验证码不匹配'
        });
        return false;
      }
      smsCodeVerification({
        mobile: this.phone,
        captchaCode: this.smsCode,
        serialNumber: this.uuid
      })
        .then((res) => {
          if (res.data.verificationvFlag !== '1') {
            _hvueToast({ mes: '输入的验证码有误，请重新输入' });
            this.smsCode = '';
            return;
          }
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
            append_tel: this.phone
          });
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    }
  }
};
</script>
