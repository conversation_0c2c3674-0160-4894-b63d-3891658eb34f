<template>
  <div class="lr_spel_cont">
    <div class="com_box" v-for="(item, i) in value" :key="i">
      <div class="input_form">
        <div
          class="input_text text"
          :id="fromInputId('stockCode', i)"
          v-if="NormalUnit.includes('stockCode')"
        >
          <span class="tit">证券代码<em class="imp">*</em></span>
          <input
            class="t1"
            type="text"
            placeholder="请输入证券代码"
            v-model="item.stockCode"
            maxlength="10"
          />
        </div>
        <div
          class="input_text text"
          :id="fromInputId('stockName', i)"
          v-if="NormalUnit.includes('stockName')"
        >
          <span class="tit">证券名称<em class="imp">*</em></span>
          <input
            class="t1"
            type="text"
            placeholder="请输入证券名称"
            v-model="item.stockName"
            maxlength="50"
          />
        </div>
        <div
          class="input_text text"
          :id="fromInputId('number', i)"
          v-if="NormalUnit.includes('number')"
        >
          <span class="tit">持股数量<em class="imp">*</em></span>
          <input
            class="t1"
            type="text"
            placeholder="请输入持股数量"
            @input="handleInput($event, i)"
            v-model="item.number"
            maxlength="20"
          />
        </div>
        <div
          class="input_text text"
          :id="fromInputId('openDate', i)"
          v-if="NormalUnit.includes('openDate')"
        >
          <span class="tit">解禁日期</span>
          <h-datetime
            v-model="item.openDate"
            class="dropdown"
            title="请输入解禁日期"
            placeholder="请输入解禁日期"
            type="date"
            :start-year="curYear"
            :end-year="afterYear"
          ></h-datetime>
        </div>
        <div
          class="input_text text"
          :id="fromInputId('companyName', i)"
          v-if="NormalUnit.includes('companyName')"
        >
          <span class="tit">公司名称<em class="imp">*</em></span>
          <input
            class="t1"
            type="text"
            placeholder="请输入公司名称"
            v-model="item.companyName"
            maxlength="50"
          />
        </div>
        <div
          class="input_text text"
          :id="fromInputId('duty', i)"
          v-if="NormalUnit.includes('duty')"
        >
          <span class="tit">职务<em class="imp">*</em></span>
          <input
            class="t1"
            type="text"
            placeholder="请输入职务"
            v-model="item.duty"
            maxlength="50"
          />
        </div>
        <div
          class="input_text text"
          :id="fromInputId('employmentPeriod', i)"
          v-if="NormalUnit.includes('employmentPeriod')"
        >
          <span class="tit">任职日期</span>
          <h-datetime
            v-model="item.employmentPeriod"
            class="dropdown"
            title="请输入任职日期"
            placeholder="请输入任职日期"
            type="date"
            :start-year="curYear"
            :end-year="afterYear"
          ></h-datetime>
        </div>
        <div
          class="input_text text"
          :id="fromInputId('affiliatedName', i)"
          v-if="NormalUnit.includes('affiliatedName')"
        >
          <span class="tit">关联人姓名<em class="imp">*</em></span>
          <input
            class="t1"
            type="text"
            placeholder="请输入关联人姓名"
            v-model="item.affiliatedName"
            maxlength="50"
          />
        </div>
        <div
          class="input_text text"
          :id="fromInputId('idNo', i)"
          v-if="NormalUnit.includes('idNo')"
        >
          <span class="tit">证件号码<em class="imp">*</em></span>
          <input
            class="t1"
            type="text"
            placeholder="请输入证件号码"
            v-model="item.idNo"
            maxlength="20"
          />
        </div>
        <div
          class="input_text text"
          :id="fromInputId('affiliatedRelation', i)"
          v-if="NormalUnit.includes('affiliatedRelation')"
        >
          <span class="tit">关联人关系<em class="imp">*</em></span>
          <input
            class="t1"
            type="text"
            placeholder="请输入关联人关系"
            v-model="item.affiliatedRelation"
            maxlength="50"
          />
        </div>
        <div
          class="input_text text"
          :id="fromInputId('fundAccount', i)"
          v-if="NormalUnit.includes('fundAccount')"
        >
          <span class="tit">股东账号</span>
          <input
            class="t1"
            type="text"
            placeholder="请输入股东账号"
            v-model="item.fundAccount"
            maxlength="20"
          />
        </div>
      </div>
      <div class="opea_ctbox">
        <a class="add_btn_01" @click="add">新增</a>
        <a class="delete_btn_01" @click="remove(i)">删除</a>
      </div>
    </div>
  </div>
</template>

<script>
//  stockCode 证券代码、 stockName 证券名称、 number 数量、 openDate 解禁日期
//  stockCode 证券代码、 companyName 公司名称、 duty 职务、 employmentPeriod 任职日期
//  stockCode 证券代码、 number 持股数量  fundAccount 股东账号
//  affiliatedName 关联人姓名、 idNo 证件号码、 affiliatedRelation 关联人关系、 fundAccount 股东账号
// {stockCode stockName number openDate companyName duty employmentPeriod affiliatedName idNo affiliatedRelation fundAccount}
const Normal = {
  1: ['stockCode', 'stockName', 'number', 'openDate'],
  2: ['stockCode', 'companyName', 'duty', 'employmentPeriod'],
  3: ['stockCode', 'number', 'fundAccount'],
  4: ['affiliatedName', 'idNo', 'affiliatedRelation', 'fundAccount']
};

import { DomShowScroll, DomHideScroll } from './utils';
// import { getStockQuotation } from '../service';

export default {
  props: {
    name: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return { value: [], curYear: '', afterYear: '' };
  },
  computed: {
    NormalUnit() {
      return Normal[this.id];
    }
  },
  created() {
    this.getInitDate();
    this.value.length === 0 && this.add();
  },
  mounted() {},
  methods: {
    // 控制表单输入项id
    fromInputId(key, i) {
      return `${this.name}-${key}-${i}`;
    },
    // 初始化数据结构
    initStructure() {
      // {stockCode stockName number openDate companyName duty employmentPeriod affiliatedName idNo affiliatedRelation fundAccount}
      return {
        stockCode: '',
        stockName: '',
        number: '',
        openDate: '',
        companyName: '',
        duty: '',
        employmentPeriod: '',
        affiliatedName: '',
        idNo: '',
        affiliatedRelation: '',
        fundAccount: ''
      };
    },
    // 添加对应的信息内容
    add() {
      if (this.value.length >= 3) return;
      this.value.push(this.initStructure());
    },
    remove(i) {
      this.value.splice(i, 1);
    },
    // 获取结果内容
    async getData() {
      const contrast = {
        stockCode: '证券代码',
        stockName: '证券名称',
        number: '数量',
        companyName: '公司名称',
        duty: '职务',
        affiliatedName: '关联人姓名',
        idNo: '证件号码',
        affiliatedRelation: '关联人关系'
        // fundAccount: '股东账号'
      };
      for (let i = 0; i < this.value.length; i++) {
        const data = this.value[i];
        for (let item of this.NormalUnit) {
          // 股东账户为非必填
          if (data[item] === '' && contrast[item]) {
            _hvueToast({ mes: `${this.title}：${contrast[item]}不能为空！` });
            DomShowScroll(this.fromInputId(item, i));
            return false;
          } else {
            DomHideScroll(this.fromInputId(item, i));
          }
        }
      }
      // for (let i = 0; i < data.length; i++) {
      //   if (data[i].code != 0) {
      //     _hvueToast({ mes: `${this.title}：证券代码不能正确！` });
      //     DomShowScroll(this.fromInputId('stockCode', i));
      //     return false;
      //   }
      // }
      return this.value;
    },
    handleInput(event, i) {
      const regex = /^[0-9]+(\.[0-9]*)?$/;
      if (!regex.test(event.target.value)) {
        this.value[i].number = event.target.value.replace(/[^\d.]/g, '');
      }
    },
    getInitDate() {
      let date = new Date();
      this.curYear = date.getFullYear().toString();
      this.afterYear = Number(this.curYear) + 30;
    }
  }
};
</script>
<style lang="less" scoped>
.lr_spel_cont {
  .disabled {
    pointer-events: none;
  }
  .com_box:not(:last-child) {
    .add_btn_01 {
      display: none;
    }
  }
  .com_box:only-child {
    .delete_btn_01 {
      display: none;
    }
  }
  .com_box:nth-child(3) {
    .add_btn_01 {
      display: none;
    }
  }
}
</style>
