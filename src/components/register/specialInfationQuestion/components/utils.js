export function DomShowScroll(id) {
  const dom = document.getElementById(id);
  if (!dom) return;
  dom.classList.add('error-input');
  // dom.scrollIntoView();
  // dom.addEventListener('mouseover', DomError);
}
export function DomHideScroll(id) {
  const dom = document.getElementById(id);
  if (!dom) return;
  dom.classList.remove('error-input');
  // dom.scrollIntoView();
  // dom.addEventListener('mouseover', DomError);
}

function DomError({ target }) {
  target.classList.remove('error-input');
  target.removeEventListener('mouseover', DomError);
}
