<template>
  <section
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="com_title">
        <h5>请您根据自己的实际情况选择以下内客</h5>
      </div>
      <div class="lr_spel_list">
        <div
          class="lr_spel_item"
          v-for="item in topic"
          :key="item.questionNo"
          :class="item.questionNo"
        >
          <div class="base">
            <h5>{{ item.questionContent }}</h5>
            <div class="opea">
              <span
                class="icon_check"
                v-for="(answer, index) in item.answer"
                :key="index"
                :class="{ checked: answer.select }"
                @click="clickSelect(answer, item)"
              >
                {{ answer.answerContent }}
              </span>
            </div>
          </div>
          <AnswerDetail
            v-if="isAnswer(item)"
            :ref="`topic-${item.questionNo}`"
            :name="`topic-${item.questionNo}`"
            :title="item.questionContent"
            :id="item.questionNo"
          />
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a
          class="p_button"
          :class="{ disabled: submitDisabled }"
          @click="submit"
          >下一步</a
        >
      </div>
    </footer>
  </section>
</template>

<script>
import { AnswerDetail } from './components';
import { uniqBy, flatten } from 'lodash';
import { EVENT_NAME } from '@/common/formEnum';
export default {
  name: 'specialInfationQuestion',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: { AnswerDetail },
  props: { subjectNo: { type: String, default: '' } },
  data() {
    return {
      // 题目
      topic: []
    };
  },
  computed: {
    submitDisabled() {
      return (
        this.topic.filter(
          (item) => item.answer.filter(({ select }) => select).length > 0
        ).length != this.topic.length
      );
    }
  },
  created() {},
  mounted() {
    let data = [
      {
        answerContent: '否',
        answerNo: 'B',
        questionContent: '是否持有上市公司限售股份',
        questionNo: '1'
      },
      {
        answerContent: '是',
        answerNo: 'A',
        questionContent: '是否持有上市公司限售股份',
        questionNo: '1'
      },
      {
        answerContent: '否',
        answerNo: 'B',
        questionContent: '是否属于上市公司的董事、监事、高级管理人员',
        questionNo: '2'
      },
      {
        answerContent: '是',
        answerNo: 'A',
        questionContent: '是否属于上市公司的董事、监事、高级管理人员',
        questionNo: '2'
      },
      {
        answerContent: '否',
        answerNo: 'B',
        questionContent: '是否持有上市公司5%以上股份的股东',
        questionNo: '3'
      },
      {
        answerContent: '是',
        answerNo: 'A',
        questionContent: '是否持有上市公司5%以上股份的股东',
        questionNo: '3'
      },
      {
        answerContent: '否',
        answerNo: 'B',
        questionContent: '是否存在关联账户',
        questionNo: '4'
      },
      {
        answerContent: '是',
        answerNo: 'A',
        questionContent: '是否存在关联账户',
        questionNo: '4'
      }
    ];
    const topic = uniqBy(data, 'questionNo');
    this.topic = topic.map(({ questionContent, questionNo }) => {
      const answer = data.filter((item) => item.questionNo == questionNo);
      return {
        questionContent,
        questionNo,
        answer: answer.map((item) => ({ ...item, select: false }))
      };
    });
  },
  methods: {
    back() {
      this.eventMessage(this, EVENT_NAME.PREV_FLOW);
    },
    clickSelect(answer, item) {
      if (answer.select) {
        answer.select = !answer.select;
      } else {
        answer.select = !answer.select;
        item.answer = item.answer.map((item) =>
          item.answerNo !== answer.answerNo
            ? { ...item, select: !answer.select }
            : item
        );
      }
    },
    isAnswer(item) {
      const data = item.answer.filter(
        ({ select, answerNo }) => select && answerNo == 'A'
      );
      return data.length > 0;
    },
    isCheck(key, bool) {
      this.$set(this, key, bool);
    },
    async submit() {
      if (this.submitDisabled) return _hvueToast({ mes: `请选择所有的题目` });
      let answerArr = [];
      let answerStr = {};
      let answer = '';

      for (let i = 0; i < this.topic.length; i++) {
        const item = this.topic[i];
        const bool = this.isAnswer(item);
        !answer
          ? (answer += `${item.questionNo}|${bool ? '是' : '否'}`)
          : (answer += `^${item.questionNo}|${bool ? '是' : '否'}`);
        if (this.isAnswer(item)) {
          const data = await this.$refs[
            `topic-${item.questionNo}`
          ][0].getData();
          answerArr.push({
            question: item.questionContent,
            answer: bool,
            answerDetail: data
          });
        } else {
          answerArr.push({
            question: item.questionContent,
            answer: bool,
            answerDetail: []
          });
        }
      }
      console.log(JSON.stringify(answerArr));
      // const answerArray = Object.values(answerStr);
      // console.log(JSON.stringify(answerArray));
      try {
        const errorDom = document.getElementsByClassName('error-input');
        if (errorDom.length > 0) {
          return;
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
            company_limitsell_holder_json: JSON.stringify(answerArr[0]),
            market_company_execv_json: JSON.stringify(answerArr[1]),
            apply_major_stock_context: JSON.stringify(answerArr[2]),
            apply_company_context: JSON.stringify(answerArr[3])
          });
        }
      } catch (e) {
        _hvueToast({ mes: e });
      }
    }
  }
};
</script>

<style></style>
