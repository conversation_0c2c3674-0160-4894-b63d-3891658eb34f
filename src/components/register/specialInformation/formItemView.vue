<template>
  <div class="com_box">
    <div class="input_form">
      <template v-for="(item, index) in formItem">
        <div
          :key="index"
          class="input_text text"
          :class="{ error: errors.first(item.key) }"
        >
          <span :ref="item.key" :for="item.key + formIndex" class="tit">{{
            item.label
          }}</span>
          <multLineInput
            v-if="item.type === 'text'"
            :id="item.key + formIndex"
            v-model="form[item.key]"
            v-validate="item.key"
            class="t1"
            type="text"
            style="text-align: right"
            :maxlength="item.maxLength"
            :placeholder="item.placeholder"
            :name="item.key"
          />
          <div
            v-if="item.type === 'select'"
            :id="item.key + formIndex"
            v-model="form[item.key]"
            v-validate="item.key"
            class="t1"
            type="text"
            style="text-align: right"
            :maxlength="item.maxLength"
            :placeholder="item.placeholder"
            :name="item.key"
            @click="show = true"
          >
            <span v-if="form[item.key]">{{
              item.options.filter((it) => it.value === form[item.key])[0].label
            }}</span>
            <span v-if="!form[item.key]" style="color: #a9afb8">{{
              item.placeholder
            }}</span>
          </div>
          <van-popup
            v-if="item.type === 'select'"
            v-model="show"
            round
            position="bottom"
          >
            <div class="layer_tit">
              <h3>请选择</h3>
              <a class="close" @click="show = false"></a>
            </div>
            <div class="layer_cont">
              <ul class="select_list">
                <li
                  v-for="it in item.options"
                  :key="it.name"
                  :class="{ active: form[item.key] === it.value }"
                  @click="onConfirm(it, item.key)"
                >
                  <span style="text-align: left">{{ it.label }}</span>
                </li>
              </ul>
            </div>
          </van-popup>
          <span v-if="item.unit" class="unit_span">{{ item.unit }}</span>
          <p class="error_tips" style="text-align: right">
            {{ errors.first(item.key) }}
          </p>
        </div>
      </template>
    </div>
    <div class="opea_ctbox">
      <a class="add_btn_01" @click="addBtn">添加</a>
      <a v-if="showDelete" class="delete_btn_01" @click="deleteBtn">删除</a>
    </div>
  </div>
</template>

<script>
import multLineInput from '@/components/multLineInput';

export default {
  components: {
    multLineInput
  },
  model: {
    prop: 'form',
    event: 'change'
  },
  props: {
    form: {
      // 需要双向绑定的数据
      type: Object,
      default: () => {}
    },
    formIndex: {
      type: Number,
      default: 0
    },
    formItem: {
      type: Array,
      default: () => []
    },
    showDelete: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      show: false
    };
  },
  methods: {
    verify(cb) {
      this.$validate((res) => {
        cb(res);
      });
    },

    onConfirm(it, key) {
      this.form[key] = it.value;
      this.show = false;
    },

    addBtn() {
      this.$emit('add');
    },

    deleteBtn() {
      this.$emit('delete');
    }
  }
};
</script>

<style lang="scss" scope>
.errTips {
  // position: absolute;
  font-size: 0.1rem;
}
</style>
