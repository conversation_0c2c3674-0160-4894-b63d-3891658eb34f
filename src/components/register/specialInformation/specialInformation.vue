<template>
  <section class="main fixed" style="position: fixed; z-index: 99">
    <t-header @back="back"></t-header>
    <article class="content">
      <div>
        <!-- <div class="com_title">
					<h5>特殊信息申报</h5>
				</div> -->
        <div class="test_numbox">
          <span
            v-for="(item, index) in stepForm"
            :key="index"
            :class="{ off: item.hasCheck }"
            @click="jump(index)"
            >{{ index + 1 }}</span
          >
        </div>
        <div
          v-for="(item, index) in stepForm"
          v-show="step === index + 1"
          :key="index"
          class="spel_infobox"
        >
          <h5 class="title">{{ item.title }}</h5>
          <div class="radio_list">
            <span
              class="icon_radio"
              :class="{ checked: item.checked === 0 }"
              @click="changeStep(0, index)"
              >否</span
            >
            <span
              class="icon_radio"
              :class="{ checked: item.checked === 1 }"
              @click="changeStep(1, index)"
              >是</span
            >
          </div>
        </div>
      </div>
      <!-- 1上市公司董监高信息申报-->
      <div
        v-if="step === 1 && stepForm[0].checked === 1"
        class="spel_info_supp"
      >
        <h5 class="mid_title">请补充高管信息</h5>
        <template v-for="(item, index) in form1">
          <formItemView
            ref="form1"
            :key="index"
            v-model="item.detail"
            :form-index="index"
            :form-item="formItemConfig.step1View"
            :show-delete="form1.length > 1"
            @add="addInfo('form1')"
            @delete="deleteInfo('form1', index)"
          />
        </template>
      </div>
      <!-- 2持有上市公司限售股份信息申报-->
      <div
        v-if="step === 2 && stepForm[1].checked === 1"
        class="spel_info_supp"
      >
        <h5 class="mid_title">请补充持股信息</h5>
        <template v-for="(item, index) in form2">
          <formItemView
            ref="form2"
            :key="index"
            v-model="item.detail"
            :form-index="index"
            :form-item="formItemConfig.step2View"
            :show-delete="form2.length > 1"
            @add="addInfo('form2')"
            @delete="deleteInfo('form2', index)"
          />
        </template>
      </div>
      <!-- 3持有上市公司股份超过5%以上 -->
      <div
        v-if="step === 3 && stepForm[2].checked === 1"
        class="spel_info_supp"
      >
        <h5 class="mid_title">请补充持股信息</h5>
        <template v-for="(item, index) in form3">
          <formItemView
            ref="form3"
            :key="index"
            v-model="item.detail"
            :form-index="index"
            :form-item="formItemConfig.step3View"
            :show-delete="form3.length > 1"
            @add="addInfo('form3')"
            @delete="deleteInfo('form3', index)"
          />
        </template>
      </div>
      <!-- 4持有上市公司解除限售股 -->
      <div
        v-if="step === 4 && stepForm[3].checked === 1"
        class="spel_info_supp"
      >
        <h5 class="mid_title">请补充持股信息</h5>
        <template v-for="(item, index) in form4">
          <formItemView
            ref="form4"
            :key="index"
            v-model="item.detail"
            :form-index="index"
            :form-item="formItemConfig.step4View"
            :show-delete="form4.length > 1"
            @add="addInfo('form4')"
            @delete="deleteInfo('form4', index)"
          />
        </template>
      </div>
      <!-- 5持有上市公司大额小额限售非流通股 -->
      <div
        v-if="step === 5 && stepForm[4].checked === 1"
        class="spel_info_supp"
      >
        <h5 class="mid_title">请补充持股信息</h5>
        <template v-for="(item, index) in form5">
          <formItemView
            ref="form5"
            :key="index"
            v-model="item.detail"
            :form-index="index"
            :form-item="formItemConfig.step5View"
            :show-delete="form5.length > 1"
            @add="addInfo('form5')"
            @delete="deleteInfo('form5', index)"
          />
        </template>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn" @click="nextStep">
        <a class="p_button">下一步</a>
      </div>
    </footer>
  </section>
</template>

<script>
import formItemView from './formItemView';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'SpecialInformation',
  inject: ['eventMessage'],
  components: {
    formItemView
  },
  data() {
    return {
      step: 1,
      modalShow: false,
      tipInfo: {
        show: false,
        title: '',
        desc: ''
      },
      stepForm: [
        // { title: '1、投资者关联人信息申报', checked: -1, hasCheck: false },
        {
          title: '1、是否上市公司董事、监事、高级管理人员',
          checked: -1,
          hasCheck: false
        },
        {
          title: '2、是否持有上市公司限售股份信息申报',
          checked: -1,
          hasCheck: false
        },
        {
          title: '3、是否持有上市公司股票超过5%以上',
          checked: -1,
          hasCheck: false
        },
        {
          title: '4、是否持有上市公司解除受限股',
          checked: -1,
          hasCheck: false
        },
        {
          title: '5、是否持有上市公司大额小额限售非流通股',
          checked: -1,
          hasCheck: false
        }
      ],
      formItemConfig: {
        step1View: [
          {
            key: 'exchange_type',
            label: '交易类别',
            type: 'select',
            placeholder: '请选择交易类别',
            options: [
              { label: '上海', value: '0' },
              { label: '深圳', value: '1' }
            ]
            // maxLength: 10
          },
          {
            key: 'stock_account',
            label: '证券账户',
            type: 'text',
            placeholder: '请输入证券账户',
            maxLength: 10
          },
          {
            key: 'stock_code',
            label: '证券代码',
            type: 'text',
            placeholder: '请输入证券代码',
            maxLength: 10
          },
          {
            key: 'remark',
            label: '备注',
            type: 'text',
            placeholder: '请输入备注'
          }
        ],
        step2View: [
          {
            key: 'exchange_type',
            label: '交易类别',
            type: 'select',
            placeholder: '请选择交易类别',
            options: [
              { label: '上海', value: '0' },
              { label: '深圳', value: '1' }
            ]
            // maxLength: 10
          },
          {
            key: 'stock_account',
            label: '证券账户',
            type: 'text',
            placeholder: '请输入证券账户',
            maxLength: 10
          },
          {
            key: 'stock_code',
            label: '证券代码',
            type: 'text',
            placeholder: '请输入证券代码',
            maxLength: 10
          },
          {
            key: 'remark',
            label: '备注',
            type: 'text',
            placeholder: '请输入备注'
          }
        ],
        step3View: [
          {
            key: 'exchange_type',
            label: '交易类别',
            type: 'select',
            placeholder: '请选择交易类别',
            options: [
              { label: '上海', value: '0' },
              { label: '深圳', value: '1' }
            ]
            // maxLength: 10
          },
          {
            key: 'stock_account',
            label: '证券账户',
            type: 'text',
            placeholder: '请输入证券账户',
            maxLength: 10
          },
          {
            key: 'stock_code',
            label: '证券代码',
            type: 'text',
            placeholder: '请输入证券代码',
            maxLength: 10
          },
          {
            key: 'remark',
            label: '备注',
            type: 'text',
            placeholder: '请输入备注'
          }
        ],
        step4View: [
          {
            key: 'exchange_type',
            label: '交易类别',
            type: 'select',
            placeholder: '请选择交易类别',
            options: [
              { label: '上海', value: '0' },
              { label: '深圳', value: '1' }
            ]
            // maxLength: 10
          },
          {
            key: 'stock_account',
            label: '证券账户',
            type: 'text',
            placeholder: '请输入证券账户',
            maxLength: 10
          },
          {
            key: 'stock_code',
            label: '证券代码',
            type: 'text',
            placeholder: '请输入证券代码',
            maxLength: 10
          },
          {
            key: 'remark',
            label: '备注',
            type: 'text',
            placeholder: '请输入备注'
          }
        ],
        step5View: [
          {
            key: 'exchange_type',
            label: '交易类别',
            type: 'select',
            placeholder: '请选择交易类别',
            options: [
              { label: '上海', value: '0' },
              { label: '深圳', value: '1' }
            ]
            // maxLength: 10
          },
          {
            key: 'stock_account',
            label: '证券账户',
            type: 'text',
            placeholder: '请输入证券账户',
            maxLength: 10
          },
          {
            key: 'stock_code',
            label: '证券代码',
            type: 'text',
            placeholder: '请输入证券代码',
            maxLength: 10
          },
          {
            key: 'remark',
            label: '备注',
            type: 'text',
            placeholder: '请输入备注'
          }
        ]
      },
      apply_company_context: '',
      apply_stock_context: '',
      apply_major_stock_context: '',
      apply_restricted_context: '',
      apply_noncirculation_context: '',
      form1: [{ detail: {} }], // 1
      form2: [{ detail: {} }], //2
      form3: [{ detail: {} }], //3
      form4: [{ detail: {} }], //4
      form5: [{ detail: {} }] //5
    };
  },
  deactivated() {
    this.modalShow = false;
  },
  methods: {
    goBack() {
      this.modalShow = false;
    },

    jump(index) {
      if (this.stepForm[index].hasCheck) {
        this.step = index + 1;
      }
    },

    // 判断增删表单是否填写完整
    checkFormView(key, formKey, unRequiredKey) {
      let valArr = [];
      this[key].forEach((item) => {
        let ObjKey = this.formItemConfig[formKey].map((item) => item.key);
        ObjKey = ObjKey.filter((item) => {
          return !unRequiredKey.includes(item);
        });
        ObjKey.forEach((it) => {
          if (item.detail[it] === undefined) {
            valArr.push('');
          } else {
            valArr.push(item.detail[it]);
          }
        });
      });
      let flag = true;
      if (valArr.includes('')) {
        this.$TAlert({
          title: '特殊信息申报未完成',
          tips: '请继续完成填写',
          confirm: () => {}
        });
        flag = false;
      }
      return flag;
    },

    back() {
      if (this.step > 1) {
        this.step -= 1;
      } else if (this.step === 1) {
        this.$TAlert({
          title: '请确认',
          tips: '特殊信息申报尚未完成,是否确认返回上一步',
          confirm: () => {}
        });
      }
    },

    changeStep(val, index) {
      this.stepForm[index].checked = val;
      if (val === 0) {
        this.stepForm[index].hasCheck = true;
      }
      if (index < 4) {
        if (val === 0) {
          setTimeout(() => {
            this.nextStep();
          }, 200);
        }
      }
    },

    nextStep() {
      let canNext = true;
      if (this.step === 1) {
        if (this.stepForm[0].checked === 0) {
          this.stepForm[0].hasCheck = true;
        } else {
          if (this.stepForm[0].checked === -1) return;
          this.$refs.form1.forEach((item) => {
            item.verify(() => {});
          });
          let errors = this.$refs.form1[0].errors.items;
          if (
            !this.checkFormView('form1', 'step1View', ['remark']) ||
            errors.length > 0
          ) {
            canNext = false;
          }
        }
      }
      if (this.step === 2) {
        if (this.stepForm[1].checked === 0) {
          this.stepForm[1].hasCheck = true;
        } else {
          if (this.stepForm[1].checked === -1) return;
          this.$refs.form2.forEach((item) => {
            item.verify(() => {});
          });
          let errors = this.$refs.form2[0].errors.items;
          if (
            !this.checkFormView('form2', 'step2View', ['remark']) ||
            errors.length > 0
          ) {
            canNext = false;
          }
        }
      }
      if (this.step === 3) {
        if (this.stepForm[2].checked === 0) {
          this.stepForm[2].hasCheck = true;
        } else {
          if (this.stepForm[2].checked === -1) return;
          this.$refs.form3.forEach((item) => {
            item.verify(() => {});
          });
          let errors = this.$refs.form3[0].errors.items;
          if (
            !this.checkFormView('form3', 'step3View', ['remark']) ||
            errors.length > 0
          ) {
            canNext = false;
          }
        }
      }
      if (this.step === 4) {
        if (this.stepForm[3].checked === 0) {
          this.stepForm[3].hasCheck = true;
        } else {
          if (this.stepForm[3].checked === -1) return;
          this.$refs.form4.forEach((item) => {
            item.verify(() => {});
          });
          let errors = this.$refs.form4[0].errors.items;
          if (
            !this.checkFormView('form4', 'step4View', ['remark']) ||
            errors.length > 0
          ) {
            canNext = false;
          }
        }
      }
      if (this.step === 5) {
        if (this.stepForm[4].checked === 0) {
          this.stepForm[4].hasCheck = true;
        } else {
          if (this.stepForm[4].checked === -1) return;
          this.$refs.form5.forEach((item) => {
            item.verify(() => {});
          });
          let errors = this.$refs.form5[0].errors.items;
          if (
            !this.checkFormView('form5', 'step4View', ['remark']) ||
            errors.length > 0
          ) {
            canNext = false;
          }
        }
      }
      if (canNext && this.step <= 4) {
        this.stepForm[this.step - 1].hasCheck = true;
        this.step += 1;
      } else if (canNext && this.step == 5) {
        let is_company_manager = this.stepForm[0].checked;
        let is_company_limitsell_holder = this.stepForm[1].checked;
        let is_holder_major_stock = this.stepForm[2].checked;
        let is_holder_restricted_stock = this.stepForm[3].checked;
        let is_holder_noncirculation_stock = this.stepForm[4].checked;

        this.apply_company_context = this.stepForm[0].checked
          ? JSON.stringify(
              this.form1.map((item) => {
                let arr = [];
                let keys = Object.keys(item.detail);
                arr = keys.map((it) => {
                  let label = this.formItemConfig.step1View.find(
                    (t) => t.key === it
                  ).label;
                  return {
                    key: it,
                    label,
                    value: item.detail[it]
                  };
                });
                return arr;
              })
            )
          : '';
        this.apply_stock_context = this.stepForm[1].checked
          ? JSON.stringify(
              this.form2.map((item) => {
                let arr = [];
                let keys = Object.keys(item.detail);
                arr = keys.map((it) => {
                  let label = this.formItemConfig.step2View.find(
                    (t) => t.key === it
                  ).label;
                  return {
                    key: it,
                    label,
                    value: item.detail[it]
                  };
                });
                return arr;
              })
            )
          : '';
        this.apply_major_stock_context = this.stepForm[2].checked
          ? JSON.stringify(
              this.form3.map((item) => {
                let arr = [];
                let keys = Object.keys(item.detail);
                arr = keys.map((it) => {
                  let label = this.formItemConfig.step3View.find(
                    (t) => t.key === it
                  ).label;
                  return {
                    key: it,
                    label,
                    value: item.detail[it]
                  };
                });
                return arr;
              })
            )
          : '';
        this.apply_restricted_context = this.stepForm[3].checked
          ? JSON.stringify(
              this.form4.map((item) => {
                let arr = [];
                let keys = Object.keys(item.detail);
                arr = keys.map((it) => {
                  let label = this.formItemConfig.step4View.find(
                    (t) => t.key === it
                  ).label;
                  return {
                    key: it,
                    label,
                    value: item.detail[it]
                  };
                });
                return arr;
              })
            )
          : '';
        this.apply_noncirculation_context = this.stepForm[4].checked
          ? JSON.stringify(
              this.form5.map((item) => {
                let arr = [];
                let keys = Object.keys(item.detail);
                arr = keys.map((it) => {
                  let label = this.formItemConfig.step4View.find(
                    (t) => t.key === it
                  ).label;
                  return {
                    key: it,
                    label,
                    value: item.detail[it]
                  };
                });
                return arr;
              })
            )
          : '';
        this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
          isHolderRestrictedStock: is_holder_restricted_stock,
          isCompanyManager: is_company_manager,
          isCompanyLimitsellHolder: is_company_limitsell_holder,
          isHolderMajorStock: is_holder_major_stock,
          isHolderNoncirculationStock: is_holder_noncirculation_stock,
          applyRestrictedContext: this.apply_restricted_context,
          applyCompanyContext: this.apply_company_context,
          applyStockContext: this.apply_stock_context,
          applyNoncirculationContext: this.apply_noncirculation_context,
          applyMajorStockContext: this.apply_major_stock_context
        });
      }
    },

    addInfo(type) {
      this[type].push({ detail: {} });
    },

    deleteInfo(type, index) {
      if (this[type].length > 1) {
        this[type].splice(index, 1);
      }
    }
  }
};
</script>

<style></style>
