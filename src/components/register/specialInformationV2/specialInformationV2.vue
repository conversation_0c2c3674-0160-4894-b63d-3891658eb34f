<template>
  <section class="main fixed" style="position: fixed; z-index: 99">
    <t-header @back="back"></t-header>
    <article class="content">
      <div>
        <!-- <div class="com_title">
					<h5>特殊信息申报</h5>
				</div> -->
        <div class="test_numbox">
          <span
            v-for="(item, index) in stepForm"
            :key="index"
            :class="{ off: item.hasCheck }"
            @click="jump(index)"
            >{{ index + 1 }}</span
          >
        </div>

        <div
          v-for="(item, index) in stepForm"
          v-show="step === index + 1"
          :key="index"
        >
          <div class="spel_infobox">
            <h5 class="title">{{ item.title }}</h5>
            <div class="radio_list">
              <span
                class="icon_radio"
                :class="{ checked: item.checked === 0 }"
                @click="changeStep(0, index, item)"
                >否</span
              >
              <span
                class="icon_radio"
                :class="{ checked: item.checked === 1 }"
                @click="changeStep(1, index, item)"
                >是</span
              >
            </div>
          </div>
          <div
            v-if="item.checked === 1 && item.formItemConfig.length > 0"
            class="spel_info_supp"
          >
            <h5 class="mid_title">请补充信息</h5>
            <template v-for="(it, idx) in item.formDetail">
              <formItemView
                :key="idx"
                v-model="it.detail"
                :form-index="idx"
                :form-item="item.formItemConfig"
                @add="addInfo(index)"
                @delete="deleteInfo(index, idx)"
              />
            </template>
            <!-- <template>
              <formItemView
                v-model="it.detail"
                :form-index="index"
                :form-item="item.formItemConfig"
              />
            </template> -->
          </div>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn" @click="nextStep">
        <a class="p_button">下一步</a>
      </div>
    </footer>
  </section>
</template>

<script>
import formItemView from './formItemView';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'SpecialInformationV2',
  inject: ['eventMessage'],
  components: {
    formItemView
  },
  props: {
    qusetionJSON: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      step: 1,
      modalShow: false,
      tipInfo: {
        show: false,
        title: '',
        desc: ''
      },
      stepForm: []
    };
  },
  computed: {
    answerList() {
      let arr = [];
      this.stepForm.forEach((item) => {
        let obj = {
          flag: item.checked,
          key: item.key
        };
        let otherArr = [];
        item.formDetail.forEach((it) => {
          if (JSON.stringify(it.detail) !== '{}') {
            otherArr.push(it.detail);
          }
        });
        obj = { ...obj, other: otherArr };
        arr.push(obj);
      });
      return arr;
    }
  },
  mounted() {
    this.renderingView();
  },
  methods: {
    goBack() {
      this.modalShow = false;
    },

    renderingView() {
      this.stepForm = [];
      JSON.parse(this.qusetionJSON).forEach((item) => {
        this.stepForm.push({
          title: item.question,
          key: item.key,
          checked: -1,
          hasCheck: false,
          formDetail: [{ detail: {} }],
          formItemConfig: item.additionalData
        });
      });
    },

    jump(index) {
      if (this.stepForm[index].hasCheck) {
        this.step = index + 1;
      }
    },

    // 判断增删表单是否填写完整
    checkFormView(key, formKey, unRequiredKey) {
      let valArr = [];
      this[key].forEach((item) => {
        let ObjKey = this.formItemConfig[formKey].map((item) => item.key);
        ObjKey = ObjKey.filter((item) => {
          return !unRequiredKey.includes(item);
        });
        ObjKey.forEach((it) => {
          if (item.detail[it] === undefined) {
            valArr.push('');
          } else {
            valArr.push(item.detail[it]);
          }
        });
      });
      let flag = true;
      if (valArr.includes('')) {
        this.$TAlert({
          title: '特殊信息申报未完成',
          tips: '请继续完成填写',
          confirm: () => {}
        });
        flag = false;
      }
      return flag;
    },

    back() {
      if (this.step > 1) {
        this.step -= 1;
      } else if (this.step === 1) {
        this.$TAlert({
          title: '请确认',
          tips: '特殊信息申报尚未完成,是否确认返回上一步',
          confirm: () => {}
        });
      }
    },

    changeStep(val, index, item) {
      this.stepForm[index].checked = val;
      if (val === 0) {
        this.stepForm[index].hasCheck = true;
      }
      if (index < this.stepForm.length - 1) {
        if (val === 0) {
          setTimeout(() => {
            this.nextStep();
          }, 200);
          return;
        }
        if (item.formItemConfig.length === 0) {
          setTimeout(() => {
            this.nextStep();
          }, 200);
          return;
        }
      }
    },

    nextStep() {
      let canNext = true;
      if (canNext && this.step <= this.stepForm.length - 1) {
        this.stepForm[this.step - 1].hasCheck = true;
        this.step += 1;
      } else if (canNext && this.step == this.stepForm.length) {
        // 下一步
        this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
          specialInfoJson: JSON.stringify(this.answerList)
        });
      }
      // if (canNext && this.step <= 4) {
      //   this.stepForm[this.step - 1].hasCheck = true;
      //   this.step += 1;
      // } else if (canNext && this.step == 5) {
      //   this.eventMessage(this, EVENT_NAME.NEXT_STEP, {});
      // }
    },

    addInfo(index) {
      this.stepForm[index].formDetail.push({ detail: {} });
    },

    deleteInfo(index, idx) {
      if (this.stepForm[index].formDetail.length > 1) {
        this.stepForm[index].formDetail.splice(idx, 1);
      }
    }
  }
};
</script>

<style></style>
