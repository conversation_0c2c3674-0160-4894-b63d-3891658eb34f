<template>
  <section class="main fixed" style="position: fixed">
    <t-header></t-header>
    <article class="content">
      <div v-if="!isJH" class="com_title">
        <h5>如果您忘记服务密码，可以挂失后重新申请</h5>
      </div>
      <div class="input_form form_tit_right mt10">
        <div class="input_text text">
          <span class="tit active">证券账户</span>
          <div @click="showSelect = true" class="dropdown" placeholder="请选择">
            <span v-if="selectedAccount.stockAccount"
              >{{
                selectedAccount.exchangeType === 'H' ? '深圳B股' : '深圳A股'
              }}
              {{ selectedAccount.stockAccount }}</span
            >
          </div>
        </div>
        <div v-if="isJH" class="input_text text">
          <span class="tit">检验码</span>
          <input
            v-model="JYM"
            class="t1 pl60"
            maxlength="9"
            placeholder="请输入申请密码时系统自动分配的号码"
          />
        </div>
      </div>
      <div style="background-color: #ffffff; margin: 0.16rem 0 0.16rem 0">
        <div
          v-if="isJH"
          class="form_tips"
          style="padding-top: 0.16rem; padding-bottom: 0.16rem"
        >
          温馨提醒：激活密码后，可通过指定网站(http://wltp.cninfo.com.cn )
          查询是否激活成功。
        </div>
        <div
          v-if="!isJH"
          class="form_tips"
          style="padding-top: 0.16rem; padding-bottom: 0.16rem"
        >
          <p>温馨提醒：服务密码挂失后，系统将注销原有用户信息</p>
          <p>
            投资者若想使用密码服务，须重新申请服务密码并激活密码。服务密码挂失半日后正式注销，注销后投资者方可重新申领。
          </p>
        </div>
      </div>

      <!-- <div class="ce_btn mt40">
        <a class="p_button" href="#">提交</a>
      </div> -->
    </article>

    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" :class="{ disabled: !canNext }" @click="toNext"
          >提交</a
        >
      </div>
    </footer>

    <div v-show="showSelect">
      <div class="dialog_overlay"></div>
      <div class="layer_box">
        <div class="layer_tit">
          <h3>选择证券账户</h3>
          <a class="close" @click="showSelect = !showSelect"></a>
        </div>
        <div class="layer_cont">
          <ul class="select_list">
            <li
              v-for="(item, index) in accountList"
              :key="index"
              :class="{
                active: selectedAccount.stockAccount === item.stockAccount
              }"
              @click="selectAccount(item)"
            >
              <span
                >{{ item.exchangeType === 'H' ? '深圳B股' : '深圳A股' }}
                {{ item.stockAccount }}</span
              >
            </li>
          </ul>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import { bkSzStockServicePasswordQry } from '@/service/shareholdAccountService';
import { updateFlowForm, flowSubmit } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'accountPwdSelect',
  inject: ['eventMessage', 'tkFlowInfo'],
  data() {
    return {
      showSelect: false,
      accountList: [],
      selectedAccount: {},
      isJH: null,
      JYM: ''
    };
  },
  computed: {
    canNext() {
      if (this.isJH) {
        if (this.selectedAccount.stockAccount && this.JYM) {
          return true;
        } else {
          return false;
        }
      } else {
        if (this.selectedAccount.stockAccount) {
          return true;
        } else {
          return false;
        }
      }
    }
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
  },
  mounted() {
    const { inProperty } = this.tkFlowInfo();
    if (inProperty.pwdBiztype === '1') {
      this.isJH = true;
    } else {
      this.isJH = false;
    }
    bkSzStockServicePasswordQry().then((res) => {
      this.accountList = res.data;
      this.selectedAccount = res.data[0];
    });
  },
  methods: {
    selectAccount(item) {
      this.selectedAccount = item;
      this.showSelect = false;
    },

    toNext() {
      if (!this.canNext) {
        return;
      }
      if (this.isJH) {
        if (!/^[0-9]+$/.test(this.JYM)) {
          _hvueToast({
            mes: '检验码必须为数字'
          });
          return;
        }
      }
      const { inProperty, flowName } = this.tkFlowInfo();
      const bizType = inProperty.bizType;
      updateFlowForm({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        bizType: bizType,
        source: `保存选择账户信息`,
        formParam: {
          selected_accounts_data: JSON.stringify([this.selectedAccount]),
          sjs_pwd_code: this.JYM
        }
      }).then(() => {
        flowSubmit({ flowToken: sessionStorage.getItem('TKFlowToken') })
          .then((data) => {
            if (data.code === 0) {
              let tips = '';
              if (this.isJH) {
                tips =
                  '激活申请已提交，如系统确认成功，半日后密码正式生效。您也可以通过指定网站（http://wltp.cninfo.com.cn）查询是否激活成功';
              } else {
                tips =
                  '挂失申请已提交，如系统确认成功，半日后密码正式生效。您也可以通过指定网站（http://wltp.cninfo.com.cn）查询是否挂失成功';
              }
              this.$TAlert({
                title: '温馨提示',
                tips,
                confirm: () => {
                  this.eventMessage(this, EVENT_NAME.TO_INDEX);
                }
              });
            } else {
              return Promise.reject(data.msg);
            }
          })
          .catch((err) => {});
      });
      // this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
      //   selectedAccountsData: JSON.stringify([this.selectedAccount])
      // });
    }
  }
};
</script>

<style lang="less" scoped>
.form_tit_right .input_text.text .t1:focus,
.form_tit_right .input_text.text .tarea1:focus,
.form_tit_right .input_text.text .tarea1.focus {
  text-align: right;
  padding-right: 0;
}
</style>
