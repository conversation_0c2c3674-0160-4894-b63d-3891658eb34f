<template>
  <article class="content">
    <ul class="p_cm_list">
      <li @click="toslmm">
        <div class="ct">
          <p>申领密码</p>
        </div>
        <i class="arrow"></i>
      </li>
      <li @click="toNext('1')">
        <div class="ct">
          <p>激活密码</p>
        </div>
        <i class="arrow"></i>
      </li>
      <li @click="toNext('2')">
        <div class="ct">
          <p>挂失密码</p>
        </div>
        <i class="arrow"></i>
      </li>
    </ul>
    <sjspwd ref="sjspwd" />
  </article>
</template>

<script>
import sjspwd from './sjspwd.vue';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'selectAccountPwdBizType',
  inject: ['eventMessage'],
  components: {
    sjspwd
  },
  created() {
    $h.setSession('addShareHoldAccount', false);
  },
  mounted() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
  },
  methods: {
    toslmm() {
      // this.$router.push({ name: 'sjspwd' });
      this.$refs.sjspwd.show();
    },

    toNext(val) {
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        pwdBizType: val
      });
    }
  }
};
</script>

<style></style>
