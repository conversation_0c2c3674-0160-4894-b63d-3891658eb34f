<template>
  <fragment>
    <div class="com_title">
      <h5>请选择您的纳税身份</h5>
    </div>
    <div class="com_box">
      <ul class="check_cmlist">
        <li v-for="({ dictLabel }, index) in selectBox" :key="index">
          <span
            class="icon_radio"
            :class="{ checked: index === selectIndex }"
            @click="selectIndex = index"
            >{{ dictLabel }}</span
          >
        </li>
      </ul>
    </div>
    <div v-if="showInput">
      <div class="com_title">
        <h5>请完善下列涉税信息</h5>
      </div>
      <div class="com_box">
        <div class="input_form form_tit_right">
          <div class="input_text text">
            <span class="tit">姓(英文/拼音)</span>
            <input
              v-model="clientSurname"
              class="t1"
              type="text"
              placeholder="请输入"
            />
          </div>
          <div class="input_text text">
            <span class="tit">名(英文/拼音)</span>
            <input
              v-model="personalName"
              class="t1"
              type="text"
              placeholder="请输入"
            />
          </div>
          <!--          <div class="input_text text">
            <span class="tit">出生日期</span>
            <h-datetime
              v-model="birthday"
              class="dropdown"
              title="请选择日期"
              placeholder="请选择日期"
              type="date"
              start-year="1900"
              end-year="2100"
            ></h-datetime>
          </div>-->
        </div>
      </div>
      <div class="com_box mt10">
        <div class="input_form form_tit_right">
          <div class="input_text text">
            <span class="tit">出生地</span>
            <div
              class="dropdown"
              placeholder="请选择国家"
              @click="showPicker('engCountry')"
            >
              {{ engCountryStr }}
            </div>
          </div>
          <div v-if="engCountryStr === '中国'" class="input_text text">
            <span class="tit">选择地区</span>
            <div
              class="dropdown"
              placeholder="选择地区（省/市/县）"
              @click="openAreaSelect(areaSelKeyMap.birthplace)"
            >
              {{ engProvince + '' + engCity }}
            </div>
          </div>
          <div class="input_text">
            <multLineInput
              v-model="engAddress"
              class="tarea1 needsclick"
              :maxlength="64"
              placeholder="请输入您的详细地址（英文/拼音）"
              autocomplete="off"
            />
          </div>
          <div class="imp_c_tips">
            <p>
              <span class="imp"
                >常住地址需详细到 单元号、门牌号、楼层和房间号部门科室</span
              >
            </p>
          </div>
        </div>
      </div>
      <div class="com_box mt10">
        <div class="input_form form_tit_right">
          <div class="input_text text">
            <span class="tit">现居地</span>
            <div
              class="dropdown"
              placeholder="请选择国家"
              @click="showPicker('livingCountry')"
            >
              {{ livingCountryStr }}
            </div>
          </div>
          <div v-if="livingCountryStr === '中国'" class="input_text text">
            <span class="tit">选择地区</span>
            <div
              class="dropdown"
              placeholder="选择地区（省/市/县）"
              @click="openAreaSelect(areaSelKeyMap.livePlace)"
            >
              {{ livingProvince + '' + livingCity }}
            </div>
          </div>
          <div class="input_text">
            <multLineInput
              v-model="livingAddressEn"
              class="tarea1 needsclick"
              :maxlength="64"
              placeholder="请输入您的详细地址（英文/拼音）"
              autocomplete="off"
            />
          </div>
          <div class="imp_c_tips">
            <p>
              <span class="imp"
                >常住地址需详细到 单元号、门牌号、楼层和房间号部门科室</span
              >
            </p>
          </div>
        </div>
      </div>
      <div>
        <div
          v-for="(taxResidentItem, index) in taxResViewBox"
          :key="index"
          class="com_box mt10"
        >
          <div class="input_form form_tit_right">
            <div class="input_text text">
              <span class="tit">税收居民国(地区）</span>
              <div
                class="dropdown"
                placeholder="请选择"
                @click="openTaxResCountry(index)"
              >
                {{ taxResidentItem.taxResidentCountryStr }}
              </div>
            </div>
            <div class="input_text text">
              <span class="tit">是否有纳税人识别号</span>
              <div
                class="dropdown"
                placeholder="请选择"
                @click="
                  taxResidentItem.showTaxResNumPicker = true;
                  taxResViewIndex = index;
                "
              >
                {{ taxResidentItem.taxResNumFlag }}
              </div>
            </div>
            <div
              v-if="taxResidentItem.taxResNumFlag === '是'"
              class="input_text text"
            >
              <span class="tit active">纳税人识别号</span>
              <input
                v-model="taxResidentItem.enTaxIdentitynumStr"
                class="t1"
                type="text"
                placeholder="请输入纳税人识别号"
              />
            </div>
            <div
              v-else-if="taxResidentItem.taxResNumFlag === '否'"
              class="input_text text"
            >
              <span class="tit active">无纳税人识别号原因</span>
              <div
                class="dropdown"
                placeholder="请选择"
                @click="
                  taxResidentItem.showNoIdReasonPicker = true;
                  taxResViewIndex = index;
                "
              >
                {{ taxResidentItem.noIdentitynoReasonLabel }}
              </div>
            </div>
            <div
              v-if="taxResidentItem.noIdentitynoReason === '2'"
              class="input_text"
            >
              <input
                v-model="taxResidentItem.nrfaNoIdentitynoRemark"
                class="t1"
                type="text"
                placeholder="请输入未取得纳税人识别号原因"
              />
            </div>
          </div>
          <div class="opea_ctbox">
            <a
              v-if="taxResViewBox.length > 1"
              class="delete_btn_01"
              @click="removeTaxResView(index)"
              >删除</a
            >
            <a
              v-if="taxResViewBox.length < 3"
              class="add_btn_01"
              @click="addTaxResView"
              >添加另一项纳税信息</a
            >
          </div>
          <van-popup
            v-model="taxResidentItem.showTaxResNumPicker"
            round
            position="bottom"
          >
            <v-picker
              v-model="taxResidentItem.taxResNumFlag"
              :columns="[
                { label: '是', value: '是' },
                { label: '否', value: '否' }
              ]"
              @onConfirm="taxResNumPickerCallback"
              @onCancel="taxResidentItem.showTaxResNumPicker = false"
            />
          </van-popup>
          <van-popup
            v-model="taxResidentItem.showNoIdReasonPicker"
            round
            position="bottom"
          >
            <v-picker
              v-model="taxResidentItem.noIdentitynoReason"
              :columns="noIdentitynoReasonData"
              @onConfirm="noIdReasonPickerCallback"
              @onCancel="taxResidentItem.showNoIdReasonPicker = false"
            />
          </van-popup>
          <van-popup
            v-model="taxResidentItem.showTaxResViewSelect"
            round
            position="bottom"
          >
            <v-picker
              v-model="taxResidentItem.taxResidentCountry"
              :columns="nationalityData"
              @onConfirm="taxResViewSelectCallback"
              @onCancel="taxResidentItem.showTaxResViewSelect = false"
            />
          </van-popup>
        </div>
      </div>
      <div class="rule_check">
        <span
          class="icon_check"
          :class="{ checked: isChecked }"
          @click="isChecked = !isChecked"
        ></span>
        <label
          >本人确认上述信息的真实、准确和完整，且当这些信息发生变更时，将在30日内通知贵机构，否则本人承担由此造成的不利后果，请参与
          <a
            v-for="({ agreementName }, i) in agreeList"
            :key="i"
            @click="showAgreeDetail = true"
            >{{ `《${agreementName}》` }}</a
          >
        </label>
      </div>
      <protocol-detail
        v-if="showAgreeDetail"
        v-model="agreeIndex"
        :agree-list="agreeList"
        :is-count="!isCount"
        @callback="agreeCallBack"
      />
      <van-popup v-model="showEngCountrySelect" round position="bottom">
        <v-picker
          ref="engCountryPicker"
          :columns="nationalityData"
          @onConfirm="engCountryCallback"
          @onCancel="showEngCountrySelect = false"
        />
      </van-popup>
      <van-popup v-model="showLivingCountrySelect" round position="bottom">
        <v-picker
          :columns="nationalityData"
          @onConfirm="livingCountryCallback"
          @onCancel="showLivingCountrySelect = false"
        />
      </van-popup>
    </div>
    <openAreaSelect
      v-if="showCitySelect"
      v-model="areaSelVal"
      @finish="areaCallback"
    ></openAreaSelect>
  </fragment>
</template>

<script>
import openAreaSelect from '@/components/openAreaSelect';
import multLineInput from '@/components/multLineInput';
import { DICT_TYPE } from '@/common/enumeration';
import VPicker from '@/components/VPicker';
import { getDictData } from '@/service/commonService';
import {
  getJwtToken,
  getRevenueResidentType,
  queryAgreement
} from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import ProtocolDetail from '@/components/ProtocolDetail';
import { signAgree } from '@/common/util';

export default {
  name: 'TaxResidentInfo',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {
    openAreaSelect,
    multLineInput,
    VPicker,
    ProtocolDetail
  },
  data() {
    return {
      DICT_TYPE,
      selectBox: null,
      taxResidentMap: {
        isShow: false,
        showTaxResNumPicker: false,
        showNoIdReasonPicker: false,
        showTaxResViewSelect: false,
        taxResNumFlag: '', // 是否有纳税人识别号
        taxResidentCountry: '', // 税收居民国(字典)
        taxResidentCountryStr: '', // 税收居民国
        enTaxIdentitynumStr: '', // 纳税人识别号串
        noIdentitynoReason: '', // 无法提供纳税人识别号原因
        noIdentitynoReasonLabel: '',
        nrfaNoIdentitynoRemark: '' // 非居民未能取得税号原因
      },
      showAgreeDetail: false,
      agreeList: [],
      readList: [],
      agreeIndex: 0,
      isChecked: false,
      taxResViewBox: [],
      taxResViewIndex: 0,
      nationalityData: null, // 国家数据字典信息
      noIdentitynoReasonData: null, // 无纳税人识别号原因数据字典信息
      selectIndex: 0,
      showEngCountrySelect: false,
      showLivingCountrySelect: false,
      showCitySelect: false,
      areaSelKeyMap: {
        birthplace: 'birthplace',
        livePlace: 'livePlace',
        taxResidentPlace: 'taxResidentPlace'
      },
      areaSelVal: '',
      areaSelKey: '',
      clientSurname: '', // 姓氏
      personalName: '', // 名字
      // birthday: '', //	出生日期
      enTaxIdentitynumStr: '', //	纳税人识别号串
      engCountry: '', // 出生国(字典)
      engCountryStr: '', // 出生国
      engProvince: '', // 出生省
      engCity: '', // 出生市
      engAddress: '', // 出生地址
      livingCountry: '', // 现居国(字典)
      livingCountryStr: '', // 现居国
      livingProvince: '', // 现居省
      livingCity: '', //	现居市
      livingAddressEn: '' // 现居地址（英）
    };
  },
  computed: {
    showInput() {
      return this.selectIndex !== 0;
    },
    isCount() {
      return this.readList.includes(this.agreeId);
    },
    agreeId() {
      return this.agreeList[this.agreeIndex]?.agreementId;
    },
    allReadFlag() {
      return this.agreeList.every((a) => {
        return this.readList.includes(a.agreementId);
      });
    },
    isAdvAge() {
      return this.$attrs.clientAge >= '70';
    }
  },
  created() {
    this._getDictData();
    this.addTaxResView();
    this._queryAgreement();

    this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
      btnStatus: 2,
      data: this.checkInput
    });
  },
  methods: {
    getTaxResInfo() {
      getRevenueResidentType({})
        .then(({ data }) => {
          const { revenueResidentType, taxResidentData } = data;
          this.selectIndex = this.selectBox.findIndex(
            ({ dictValue }) => dictValue === revenueResidentType
          );
          if (this.showInput) {
            const taxResidentList = JSON.parse(taxResidentData);
            let { engCountry, livingCountry, ...res } = data;
            if (engCountry) {
              const i = this.nationalityData.findIndex(
                ({ value }) => value === engCountry
              );
              this.engCountryStr = this.nationalityData[i].label;
              this.engCountry = this.nationalityData[i].value;
            }
            if (livingCountry) {
              const i = this.nationalityData.findIndex(
                ({ value }) => value === livingCountry
              );
              this.livingCountryStr = this.nationalityData[i].label;
              this.livingCountry = this.nationalityData[i].value;
            }
            for (const {
              taxResidentCountry,
              enTaxIdentitynumStr,
              noIdentitynoReason,
              nrfaNoIdentitynoRemark
            } of taxResidentList) {
              let taxResNumFlag = '否';
              let taxResidentCountryStr = '';
              let noIdentitynoReasonLabel = '';
              if (enTaxIdentitynumStr) {
                taxResNumFlag = '是';
              }
              if (taxResidentCountry) {
                const natIndex = this.nationalityData.findIndex(
                  ({ value }) => value === taxResidentCountry
                );
                taxResidentCountryStr = this.nationalityData[natIndex].label;
              }
              if (noIdentitynoReason) {
                const reasonIndex = this.noIdentitynoReasonData.findIndex(
                  ({ value }) => value === noIdentitynoReason
                );
                noIdentitynoReasonLabel =
                  this.noIdentitynoReasonData[reasonIndex].label;
              }
              this.taxResViewBox.push({
                ...this.taxResidentMap,
                taxResidentCountry,
                taxResidentCountryStr,
                taxResNumFlag,
                enTaxIdentitynumStr,
                noIdentitynoReason,
                noIdentitynoReasonLabel,
                nrfaNoIdentitynoRemark
              });
            }
            Object.assign(this, res);
          }
        })
        .catch((e) => {
          _hvueToast({
            mes: e
          });
        });
    },
    async _queryAgreement() {
      const { flowNodeNo, inProperty } = this.tkFlowInfo();
      const { agreement_ids } = this.$attrs;
      let tokenRes;
      try {
        tokenRes = await getJwtToken({
          flowNo: flowNodeNo,
          businessType: inProperty.bizType
        });
      } catch (e) {
        _hvueToast({
          mes: e
        });
        return;
      }
      $h.setSession('jwtToken', tokenRes.data);
      queryAgreement({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        bizType: inProperty.bizType,
        agreementId: agreement_ids
      })
        .then(({ data, code, msg }) => {
          if (code === 0) {
            this.agreeList = data;
          } else {
            return Promise.reject(msg);
          }
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    },
    _getDictData() {
      getDictData({
        dictType: DICT_TYPE.TAX_RESIDENT_PERSON
      })
        .then(({ data }) => {
          this.selectBox = data[DICT_TYPE.TAX_RESIDENT_PERSON];
          return getDictData({
            dictType: DICT_TYPE.NO_IDENTITYNO_REASON
          });
        })
        .then(({ data }) => {
          const list = data[DICT_TYPE.NO_IDENTITYNO_REASON];
          this.noIdentitynoReasonData = list.map((d) => {
            d.label = d.dictLabel;
            d.value = d.dictValue;
            return d;
          });
          return getDictData({
            dictType: DICT_TYPE.NATIONALITY
          });
        })
        .then(({ data }) => {
          const list = data[DICT_TYPE.NATIONALITY];
          list.map((d, i) => {
            if (d.dictLabel.includes('中国')) {
              list.unshift(list.splice(i, 1)[0]);
            }
            d.label = d.dictLabel;
            d.value = d.dictValue;
            return d;
          });
          this.nationalityData = [...list];
          return this.getTaxResInfo();
        })
        .catch((e) => {
          _hvueToast({ mes: e });
        });
    },
    addTaxResView() {
      this.taxResViewBox.push({ ...this.taxResidentMap });
    },
    removeTaxResView(i) {
      this.taxResViewBox.splice(i, 1);
    },
    showPicker(type) {
      if (type === 'engCountry') {
        this.showEngCountrySelect = true;
      } else {
        this.showLivingCountrySelect = true;
      }
    },
    openAreaSelect(key) {
      if (this.areaSelKey !== key) {
        this.areaSelVal = '';
      }
      this.areaSelKey = `${key}`;
      this.showCitySelect = true;
    },
    areaCallback(data) {
      this.showCitySelect = false;
      if (!data) return;
      const [{ text: prov }, { text: city }, { text: area }] = data;
      switch (this.areaSelKey) {
        case this.areaSelKeyMap.birthplace:
          this.engProvince = prov;
          this.engCity = city + area;
          break;
        case this.areaSelKeyMap.livePlace:
          this.livingProvince = prov;
          this.livingCity = city + area;
          break;
      }
    },
    engCountryCallback({ label, value }) {
      this.showEngCountrySelect = false;
      if (this.engCountryStr !== label) {
        this.engProvince = '';
        this.engCity = '';
      }
      this.engCountry = value;
      this.engCountryStr = label;
    },
    livingCountryCallback({ label, value }) {
      this.showLivingCountrySelect = false;
      if (this.livingCountryStr !== label) {
        this.livingProvince = '';
        this.livingCity = '';
      }
      this.livingCountry = value;
      this.livingCountryStr = label;
    },
    openTaxResCountry(i) {
      this.taxResViewIndex = i;
      this.taxResViewBox[this.taxResViewIndex].showTaxResViewSelect = true;
    },
    taxResViewSelectCallback({ label }) {
      this.taxResViewBox[this.taxResViewIndex].showTaxResViewSelect = false;
      this.taxResViewBox[this.taxResViewIndex].taxResidentCountryStr = label;
    },
    taxResNumPickerCallback() {
      const flag = this.taxResViewBox[this.taxResViewIndex].taxResNumFlag;
      if (flag === '是') {
        this.taxResViewBox[this.taxResViewIndex].noIdentitynoReasonLabel = '';
        this.taxResViewBox[this.taxResViewIndex].noIdentitynoReason = '';
        this.taxResViewBox[this.taxResViewIndex].nrfaNoIdentitynoRemark = '';
      } else {
        this.taxResViewBox[this.taxResViewIndex].enTaxIdentitynumStr = '';
      }
      this.taxResViewBox[this.taxResViewIndex].showTaxResNumPicker = false;
    },
    noIdReasonPickerCallback({ label, value }) {
      if (value !== '2') {
        this.taxResViewBox[this.taxResViewIndex].nrfaNoIdentitynoRemark = '';
      }
      this.taxResViewBox[this.taxResViewIndex].showNoIdReasonPicker = false;
      this.taxResViewBox[this.taxResViewIndex].noIdentitynoReasonLabel = label;
    },
    agreeCallBack(flag) {
      if (flag) {
        if (!this.isCount && !this.readList.includes(this.agreeId)) {
          this.readList.push(this.agreeId);
        }
        this.isChecked = this.allReadFlag;
        if (this.allReadFlag || this.agreeIndex + 1 === this.agreeList.length)
          this.showAgreeDetail = false;
      } else {
        this.showAgreeDetail = false;
      }
    },
    checkInput() {
      let errorMessage;
      const checkRule = [
        {
          name: '姓(英文/拼音)',
          key: 'clientSurname',
          regExp: /^[a-zA-Z]{0,18}$/i
        },
        {
          name: '名(英文/拼音)',
          key: 'personalName',
          regExp: /^[a-zA-Z]{0,18}$/i
        },
        /*{
          name: '出生日期',
          key: 'birthday',
          regExp: /\d{4}-\d{2}-\d{2}/
        },*/
        {
          name: '出生地国家',
          key: 'engCountry',
          method: () => {
            if (this.engCountryStr === '中国') {
              if (this.engProvince === '' || this.engCity === '') {
                return '请选择出生地区';
              }
            }
            return '';
          }
        },
        {
          name: '出生地详细地址',
          key: 'engAddress',
          regExp: /^[a-zA-Z0-9]{0,64}$/i
        },
        {
          name: '现居地国家',
          key: 'livingCountry',
          method: () => {
            if (this.livingCountryStr === '中国') {
              if (this.livingProvince === '' || this.livingCity === '') {
                return '请选择现居地区';
              }
            }
            return '';
          }
        },
        {
          name: '现居地详细地址',
          key: 'livingAddressEn',
          regExp: /^[a-zA-Z0-9]{0,64}$/i
        },
        {
          name: '纳税信息',
          method: this.checkTaxResInfo
        }
      ];
      if (this.selectIndex === 0) {
        this.submitForm();
      } else {
        if (!this.isChecked) {
          _hvueToast({
            mes: '请先阅读协议'
          });
          return;
        }
        for (const { key, name, regExp, method } of checkRule) {
          const value = this[key] && this[key].replace(/\s+/g, '');
          if (value === '') {
            errorMessage = `${name}不能为空`;
            break;
          } else if (regExp && !regExp.test(value)) {
            errorMessage = `${name}格式不正确`;
            break;
          } else if (method) {
            const res = method();
            if (res !== '') {
              errorMessage = res;
              break;
            }
          }
        }
        if (errorMessage) {
          _hvueToast({ mes: errorMessage });
        } else {
          this.submitForm();
        }
      }
    },
    checkTaxResInfo() {
      let errorMessage;
      for (const {
        taxResidentCountry,
        taxResNumFlag,
        enTaxIdentitynumStr,
        noIdentitynoReason,
        nrfaNoIdentitynoRemark
      } of this.taxResViewBox) {
        if (taxResidentCountry === '') {
          errorMessage = '税收居民国(地区）不能为空';
          break;
        } else if (taxResNumFlag === '') {
          errorMessage = '请选择是否有纳税人识别号';
          break;
        } else if (taxResNumFlag === '是' && enTaxIdentitynumStr === '') {
          errorMessage = '纳税人识别号不能为空';
          break;
        } else if (taxResNumFlag === '否') {
          if (noIdentitynoReason === '') {
            errorMessage = '请选择无纳税人识别号原因';
            break;
          } else if (
            noIdentitynoReason === '2' &&
            nrfaNoIdentitynoRemark === ''
          ) {
            errorMessage = '未取得纳税人识别号原因不能为空';
            break;
          }
        }
      }
      return errorMessage;
    },
    submitForm() {
      const tkFlowInfo = this.tkFlowInfo();
      signAgree(tkFlowInfo, this.agreeList)
        .then((epaperSignJson) => {
          let taxResidentData = [];
          const {
            selectBox,
            selectIndex,
            clientSurname,
            personalName,
            // birthday,
            engCountry,
            engProvince,
            engCity,
            engAddress,
            livingCountry,
            livingProvince,
            livingCity,
            livingAddressEn
          } = this;
          let nextParam = {
            epaperSignJson,
            taxResidentPerson: selectBox[selectIndex].dictValue
          };
          if (this.selectIndex !== 0) {
            taxResidentData = this.taxResViewBox.map(
              ({
                taxResidentCountry,
                enTaxIdentitynumStr,
                noIdentitynoReason,
                nrfaNoIdentitynoRemark
              }) => {
                return {
                  taxResidentCountry,
                  enTaxIdentitynumStr,
                  noIdentitynoReason,
                  nrfaNoIdentitynoRemark
                };
              }
            );
            try {
              taxResidentData = JSON.stringify(taxResidentData);
            } catch (mes) {
              _hvueToast({ mes });
              return;
            }
            nextParam = {
              ...nextParam,
              clientSurname,
              personalName,
              // birthday,
              engCountry,
              engProvince,
              engCity,
              engAddress,
              livingCountry,
              livingProvince,
              livingCity,
              livingAddressEn,
              taxResidentData
            };
          }
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, nextParam);
        })
        .catch((error) => {
          _hvueToast({
            mes: error
          });
        });
    }
  }
};
</script>

<style scoped></style>
