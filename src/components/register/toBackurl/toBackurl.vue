<template>
  <div></div>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
export default {
  name: 'toBackurl',
  inject: ['tkFlowInfo', 'eventMessage'],
  mounted() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    this.eventMessage(this, EVENT_NAME.NEXT_STEP);
    if ($h.getSession('backurl')) {
      window.location = $h.getSession('backurl');
    }
  }
};
</script>
