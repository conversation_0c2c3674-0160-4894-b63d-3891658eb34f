<template>
  <div></div>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'ToNext',
  inject: ['tkFlowInfo', 'eventMessage'],
  mounted() {
    console.log('this.$attrs', this.$attrs);
    this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
      selectedAccountsData: JSON.stringify([
        { fundAccount: this.tkFlowInfo().inProperty.creditFundAccount, assetProp: this.$attrs.assetProp }
      ])
    });
  }
};
</script>

<style></style>
