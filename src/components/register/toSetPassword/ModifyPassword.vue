<template>
  <fragment>
    <div class="set_pword_item" v-show="displayTradePwd">
      <div class="input_form mobile_module mt10">
        <div class="input_text text">
          <span class="tit active">账户类型</span>
          <div class="dropdown" @click="showAccountMap = true">
            {{ selectAccount.label }}
          </div>
        </div>
        <div class="input_text pword">
          <span class="tit">原交易密码</span>
          <h-keypanel
            :key="viewShowCount"
            v-model="trade_password"
            type="tel2"
            class="pwd_input"
            :mask="true"
            :is-head-icon="true"
            extra-parent-el=".hui-flexview"
            placeholder="请输入原交易密码"
          >
            <div slot="head" class="safe-head">
              <img src="../../../assets/images/logo.png" alt="" />
              <span>佣金宝安全输入</span>
            </div>
          </h-keypanel>
        </div>
        <div class="input_text">
          <span class="tit">新交易密码</span>
          <h-keypanel
            :key="viewShowCount"
            v-model="new_trade_password"
            :length="6"
            type="tel2"
            :mask="true"
            :is-head-icon="true"
            extra-parent-el=".hui-flexview"
            placeholder="请输入6位数字新交易密码"
          >
            <div slot="head" class="safe-head">
              <img src="../../../assets/images/logo.png" alt="" />
              <span>佣金宝安全输入</span>
            </div>
          </h-keypanel>
        </div>
        <div class="input_text">
          <span class="tit">新交易密码</span>
          <h-keypanel
            :key="viewShowCount"
            v-model="confirm_trade_password"
            :length="6"
            type="tel2"
            :mask="true"
            :is-head-icon="true"
            extra-parent-el=".hui-flexview"
            placeholder="请输入6位数字新交易密码"
          >
            <div slot="head" class="safe-head">
              <img src="../../../assets/images/logo.png" alt="" />
              <span>佣金宝安全输入</span>
            </div>
          </h-keypanel>
        </div>
      </div>
    </div>
    <div v-show="displayFundPwd" class="set_pword_item">
      <!-- <div class="com_title">
        <h5>请设置新资金密码<em class="small">资金密码用于资金划转</em></h5>
      </div> -->
      <div class="input_form mobile_module mt10">
        <div class="input_text text">
          <span class="tit active">选择账号</span>
          <div class="dropdown" @click="showAccountMap = true">
            {{ selectAccount.label }}
          </div>
        </div>
        <div class="input_text pword">
          <span class="tit">原资金密码</span>
          <h-keypanel
            :key="viewShowCount"
            v-model="fund_password"
            type="tel2"
            :mask="true"
            :is-head-icon="true"
            extra-parent-el=".hui-flexview"
            placeholder="请输入原资金密码"
          >
            <div slot="head" class="safe-head">
              <img src="../../../assets/images/logo.png" alt="" />
              <span>佣金宝安全输入</span>
            </div>
          </h-keypanel>
          <!-- <a
            class="icon_eye"
            :class="show_fund_password ? 'show' : ''"
            @click="show_fund_password = !show_fund_password"
          ></a> -->
        </div>
        <div class="input_text">
          <span class="tit">新资金密码</span>
          <h-keypanel
            :key="viewShowCount"
            v-model="new_fund_password"
            :length="6"
            type="tel2"
            :mask="true"
            :is-head-icon="true"
            extra-parent-el=".hui-flexview"
            placeholder="请输入6位数字新资金密码"
          >
            <div slot="head" class="safe-head">
              <img src="../../../assets/images/logo.png" alt="" />
              <span>佣金宝安全输入</span>
            </div>
          </h-keypanel>
        </div>
        <div class="input_text">
          <span class="tit">新资金密码</span>
          <h-keypanel
            :key="viewShowCount"
            v-model="confirm_fund_password"
            :length="6"
            type="tel2"
            :mask="true"
            :is-head-icon="true"
            extra-parent-el=".hui-flexview"
            placeholder="请输入6位数字新资金密码"
          >
            <div slot="head" class="safe-head">
              <img src="../../../assets/images/logo.png" alt="" />
              <span>佣金宝安全输入</span>
            </div>
          </h-keypanel>
        </div>
      </div>
    </div>
    <div class="form_tips" v-show="displaySpecialPwd">
      <p @click="toBizType">
        若您需要启用或取消独立密码，请点击
        <a class="com_link">启用/取消独立密码</a>
      </p>
    </div>
    <div class="form_tips">
      <p>温馨提示：</p>
      <p>为了您的资金安全，以下密码不能设置使用。</p>
      <p>1、三位数重复2次，如-123123</p>
      <p>2、后三位末前三位倒序数，如-123321</p>
      <p>3、两位数重复3次，如-121212</p>
      <p>4、一和二，三和四，五和六位相同，如-112233</p>
      <p>5、一二三位，四五六位相同，如-111222</p>
      <p>6、六位连续升序或降序的数字，如-123456，654321</p>
      <p class="mt10" v-show="displayTradePwd === true">
        如您的信用资金账户和衍生品资金账户已“启用”独立密码，可选择对应账户类型进行密码修改。如未“启用”独立密码，选择任一账户进行密码修改会对所有未设置独立密码的资金账户生效。
      </p>
    </div>
    <div class="main fixed" v-show="showAccountMap">
      <van-popup v-model="showAccountMap" round position="bottom">
        <v-picker
          v-model="selectAccount.value"
          :columns="fundAccListMap"
          @onConfirm="pickerCallback"
          @onCancel="showAccountMap = false"
        />
      </van-popup>
    </div>
  </fragment>
</template>

<script>
import VPicker from '@/components/VPicker';
import { uniqBy, groupBy } from 'lodash';
import { getPwdEncryption, wakeLoginAppGJ } from '@/common/util.js';
import TxzxgUtil from '@/common/TxzxgUtil.js';

import {
  fundAccountListQryV1,
  creditPwdCheck,
  updateFlowForm,
  flowSubmit
} from '@/service/service.js';
import { EVENT_NAME } from '@/common/formEnum';
import { ASSET_PROP } from '@/common/enumeration';
import { WEAKPWD } from '@/plugins/weak-pwd/WEAKPWD-min';
import '@/nativeShell/nativeCallH5';
import { nativeFunc60094 } from '@/nativeShell/h5CallNative';

export default {
  components: {
    VPicker
  },
  name: 'ModifyPassword',
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {
    displayFundPwd: {
      type: Boolean,
      default: false
    },
    displayTradePwd: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fundAccountList: [],
      rawFundAccList: [],
      showAccountMap: false,
      selectAccount: {},
      trade_password: '', //交易账号密码
      new_trade_password: '',
      confirm_trade_password: '',
      fund_password: '', // 资金账号密码
      new_fund_password: '',
      confirm_fund_password: '',
      viewShowCount: 0
    };
  },
  computed: {
    fundAccListMap: {
      set(list) {
        console.log(list);
        this.fundAccountList = list;
      },
      get() {
        return uniqBy(this.fundAccountList, 'label');
      }
    },
    displaySpecialPwd() {
      return this.displayTradePwd && this.rawFundAccList.length > 1;
    },
    disabled() {
      let filterList = [];
      if (this.displayFundPwd) {
        filterList.push(
          this.fund_password,
          this.new_fund_password,
          this.confirm_fund_password
        );
      }
      if (this.displayTradePwd) {
        filterList.push(
          this.trade_password,
          this.new_trade_password,
          this.confirm_trade_password
        );
      }
      return !filterList.every((v) => v !== '');
    },
    tradePwdFundAccList() {
      const {
        clientId,
        assetProp,
        mainFlag,
        specialFlag,
        clientRights,
        fundAccount
      } = this.selectAccount;
      if (specialFlag === '1') {
        return [
          {
            clientId,
            assetProp,
            mainFlag,
            specialFlag,
            clientRights,
            fundAccount
          }
        ];
      }
      return this.fundAccountList
        .filter(({ specialFlag }) => specialFlag !== '1')
        .map(
          ({
            clientId,
            assetProp,
            mainFlag,
            specialFlag,
            clientRights,
            fundAccount
          }) => {
            return {
              clientId,
              assetProp,
              mainFlag,
              specialFlag,
              clientRights,
              fundAccount
            };
          }
        );
    }
  },
  watch: {
    disabled: {
      handler(f) {
        if (!f) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            text: '确认修改',
            btnStatus: 2,
            display: true,
            data: () => {
              this.toNext();
            }
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            text: '确认修改',
            btnStatus: 0
          });
        }
      },
      immediate: true
    }
  },
  created() {
    nativeFunc60094({
      isInterceptScreenshot: '1',
      screenCaptureTip:
        '请妥善保存您的账号及密码，您保存的图片内容可能涉及到敏感信息，请请勿发送给他人'
    });
    document.addEventListener('visibilitychange', this.viewHideActivate);
  },
  mounted() {
    fundAccountListQryV1({})
      .then(({ data, code, msg }) => {
        if (code === 0) {
          this.rawFundAccList = data.fundAccountList;
          this.fundAccountList = data.fundAccountList
            .map(({ fundAccount, ...it }) => {
              const fundAcc = this.displayTradePwd ? '' : ` ${fundAccount}`;
              return {
                ...it,
                fundAccount,
                label: `${this.getAssetPropMap(
                  it.assetProp,
                  it.mainFlag
                )}${fundAcc}`,
                value: fundAccount
              };
            })
            .filter(({ fundAccountStatus }) => {
              return fundAccountStatus === '0';
            });
          this.selectAccount = this.fundAccountList[0];
        } else {
          return Promise.reject(msg);
        }
      })
      .catch((err) => {
        this.$TAlert({
          title: '温馨提示',
          tips: err
        });
      });
  },
  destroyed() {
    nativeFunc60094({
      isInterceptScreenshot: '0'
    });
    document.removeEventListener('visibilitychange', this.viewHideActivate);
  },
  methods: {
    viewHideActivate() {
      if (document.visibilityState === 'hidden') {
        // 页面进入后台时执行的代码
        this.trade_password = ''; //交易账号密码
        this.new_trade_password = '';
        this.confirm_trade_password = '';
        this.fund_password = ''; // 资金账号密码
        this.new_fund_password = '';
        this.confirm_fund_password = '';
        this.viewShowCount++;
        console.log('页面进入后台');
      }
    },
    getAssetPropMap(a, m) {
      let getMap = new Map();
      let mainFlagTxt = '（主）'; // 主辅标识翻译
      if (this.displayFundPwd) {
        if (m !== '1') mainFlagTxt = '（辅）';
      }
      getMap.set(ASSET_PROP.DERIVATIVES_ACCOUNT, '衍生品资金账户');
      getMap.set(ASSET_PROP.ORDINARY_ACCOUNT, '普通资金账户' + mainFlagTxt);
      getMap.set(ASSET_PROP.CREDIT_ACCOUNT, '信用资金账户');
      getMap.set(ASSET_PROP.OPTIONS_ACCOUNT, '期权资金账户');
      getMap.set(ASSET_PROP.FUND_ACCOUNT, '基金资金账户');
      return getMap.get(a) || '';
    },
    getLabelNameMap() {
      let labelList = [];
      let groupData = groupBy(this.tradePwdFundAccList, 'assetProp');
      for (const k in groupData) {
        let fundList = groupData[k].map(({ fundAccount }) => {
          return fundAccount;
        });
        const label = this.getAssetPropMap(k);
        labelList.push(
          `${label.replace('资金', '').replace('（主）', '')}（${fundList.join(
            '、'
          )}）`
        );
      }
      return labelList;
    },
    pickerCallback(item) {
      const fundAcc = this.displayTradePwd ? '' : ` ${item.value}`;
      this.selectAccount = {
        ...item,
        label: `${this.getAssetPropMap(
          item.assetProp,
          item.mainFlag
        )}${fundAcc}`,
        value: item.value
      };
    },
    verifyYes(pwd, type = '') {
      if (pwd.length < 6) {
        this.$TAlert({
          title: '温馨提示',
          tips: '请输入6位数字密码'
        });
        return false;
      }
      if (WEAKPWD(pwd).status) {
        this.errTxt = `您输入的${type}新密码格式有误`;
        this.$TAlert({
          title: '温馨提示',
          tips: this.errTxt
        });
        return false;
      }
      return true;
    },
    //判断两次密码设置是否一致
    verifySame(type) {
      let pwd1;
      let pwd2;
      if (type === 'trade') {
        pwd1 = this.new_trade_password;
        pwd2 = this.confirm_trade_password;
      } else if (type === 'fund') {
        pwd1 = this.new_fund_password;
        pwd2 = this.confirm_fund_password;
      }
      if (pwd1 !== pwd2) {
        this.$TAlert({
          title: '温馨提示',
          tips: '您输入的密码不一致'
        });
        return false;
      }
      return true;
    },

    toNext() {
      if (this.displayFundPwd) {
        if (
          !this.verifyYes(this.new_fund_password) ||
          !this.verifyYes(this.confirm_fund_password, '确认') ||
          !this.verifySame('fund')
        )
          return;
      }
      if (this.displayTradePwd) {
        if (
          !this.verifyYes(this.new_trade_password) ||
          !this.verifyYes(this.confirm_trade_password, '确认') ||
          !this.verifySame('trade')
        )
          return;
      }
      this.verifyPwd();
    },
    async verifyPwd() {
      try {
        if (this.displayFundPwd) {
          const { codeFund, msgFund } = await creditPwdCheck({
            flowToken: sessionStorage.getItem('TKFlowToken'),
            account: this.selectAccount.value,
            password: 'encrypt:' + getPwdEncryption(this.fund_password),
            assetProp: this.selectAccount.assetProp,
            mainFlag: this.selectAccount.mainFlag,
            clientPwdType: '1' //密码类型 1.资金密码  2交易密码
          });
          this.submitFlowInfo();
        }
        if (this.displayTradePwd) {
          const { value, assetProp, mainFlag, specialFlag } =
            this.selectAccount;
          const { code, msg } = await creditPwdCheck({
            flowToken: sessionStorage.getItem('TKFlowToken'),
            account: value,
            assetProp,
            mainFlag,
            password: 'encrypt:' + getPwdEncryption(this.trade_password),
            clientPwdType: '2' //密码类型 1.资金密码  2交易密码
          });
          // 根据账户类型判断是否存在独立密码，如果是独立密码不需要弹窗
          if (specialFlag === '1') {
            this.submitFlowInfo();
          } else {
            this.$TAlert({
              title: '温馨提示',
              tips: `亲，您在本页面设置的新交易密码将对您的${this.getLabelNameMap().join(
                '，'
              )} 生效。`,
              hasCancel: true,
              confirmBtn: '确认',
              confirm: this.submitFlowInfo
            });
          }
        }
      } catch (err) {
        this.$TAlert({
          title: '温馨提示',
          tips: err
        });
      }
    },
    submitFlowInfo() {
      let selected_accounts_data = {};
      let pwdTypeName = [];
      const { flowName } = this.tkFlowInfo();
      const flowToken = sessionStorage.getItem('TKFlowToken');
      if (this.new_fund_password) {
        const {
          clientId,
          assetProp,
          mainFlag,
          specialFlag,
          clientRights,
          fundAccount
        } = this.selectAccount;
        selected_accounts_data = {
          assetProp,
          clientId,
          mainFlag,
          specialFlag,
          clientRights,
          fundAccount
        };
        selected_accounts_data.fundPassword =
          'encrypt:' + getPwdEncryption(this.fund_password);
        selected_accounts_data.newFundPassword =
          'encrypt:' + getPwdEncryption(this.new_fund_password);
        pwdTypeName.push('资金');
        selected_accounts_data = JSON.stringify([
          { ...selected_accounts_data }
        ]);
      }
      if (this.new_trade_password) {
        selected_accounts_data = this.tradePwdFundAccList.map((it) => {
          return {
            ...it,
            tradePassword: 'encrypt:' + getPwdEncryption(this.trade_password),
            newTradePassword:
              'encrypt:' + getPwdEncryption(this.new_trade_password)
          };
        });
        pwdTypeName.push('交易');
        selected_accounts_data = JSON.stringify(selected_accounts_data);
      }

      updateFlowForm({
        flowToken,
        source: `${flowName}-初始化`,
        formParam: { selected_accounts_data }
      })
        .then(() => {
          return flowSubmit({ flowToken });
        })
        .then(() => {
          if (this.displayFundPwd) {
            this.$TAlert({
              title: '温馨提示',
              tips: `您的${pwdTypeName.join(
                '/'
              )}密码修改成功！为了您的账户安全，请务必保管好您的${pwdTypeName.join(
                '/'
              )}账号、密码等重要信息，不要透露给他人。`,
              confirm: () => {
                this.eventMessage(this, EVENT_NAME.TO_INDEX);
              }
            });
          } else if (this.displayTradePwd) {
            const logoutType = this.getLogoutType();
            console.log(`***** logoutType = ${logoutType} ****`);

            const TxzxgUtilObj = new TxzxgUtil();
            console.log(`***** TxzxgUtilObj.checkTxzxg = ${TxzxgUtilObj.checkTxzxg} ****`);
            this.$TAlert({
              title: '温馨提示',
              tips: `您的交易密码修改成功，对应账户需重新进行交易登录！为了您的账户安全，请务必保管好您的资金账号、密码等重要信息，不要透露给他人。`,
              confirm: () => {
                this.$store.commit('user/setUserInfo', null);
                if (TxzxgUtilObj.checkTxzxg) {
                  const { fundAccount = '' } = this.tradePwdFundAccList[0];
                  console.info('fundAccount ==== ', fundAccount);
                  TxzxgUtilObj.logout({ fundAccount }, () => {
                    this.eventMessage(this, EVENT_NAME.TO_INDEX);
                  });
                  return;
                }
                if ($hvue.platform !== '0' && logoutType !== false) {
                  wakeLoginAppGJ(logoutType);
                } else {
                  this.eventMessage(this, EVENT_NAME.TO_INDEX);
                }
              }
            });
          }
        })
        .catch((err) => {
          this.$TAlert({
            title: '温馨提示',
            tips: err
          });
        });
    },
    /*
     * 获取退出登录状态类型
     * @return {Number} 1普通登录 2信用登录 3担保品划转登录（普通+信用） 16期权
     */
    getLogoutType() {
      //信用资金账户
      let logoutMap = new Map();
      logoutMap.set(ASSET_PROP.ORDINARY_ACCOUNT, 1);
      logoutMap.set(ASSET_PROP.CREDIT_ACCOUNT, 2);
      logoutMap.set(ASSET_PROP.DERIVATIVES_ACCOUNT, 16);
      let list = [];
      for (const { assetProp } of this.tradePwdFundAccList) {
        list.push(logoutMap.get(assetProp));
      }
      return list;
    },
    toBizType() {
      this.$router.push({
        name: 'pickSeparatePwd'
      });
    }
  }
};
</script>
<style scoped>
div.input_text > .hui-keypanel {
  padding-left: 1rem;
}
.van-popup >>> .layer_cont {
  height: auto;
}
.pword_tips > .bold_text {
  font-size: 0.15rem;
  font-weight: 600;
  color: #333333;
}
</style>
