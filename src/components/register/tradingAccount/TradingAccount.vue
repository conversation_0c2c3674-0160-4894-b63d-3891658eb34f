<template>
  <section class="main fixed" data-page="home" style="position: fixed">
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" href="javascript:void(0);"></a>
        <h1 class="title">A股账户</h1>
      </div>
    </header>
    <article class="content">
      <ul class="com_infolist spel">
        <li>
          <span class="tit">普通资金账户</span>
          <p>{{ fundAccount }}</p>
        </li>
      </ul>
      <ul class="market_ctlist">
        <template v-if="accountLoading && stockAccountList.length <= 0">
          <li>
            <div class="base">
              <div class="bg"><img :src="BgShangHai" /></div>
              <h5>上海A股</h5>
              <p>
                <span class="num"> 尚未开通或状态异常 </span>
              </p>
            </div>
            <div class="opea">
              <a class="com_btn" href="javascript:void(0)" @click="homeClick">
                管理A股账户
              </a>
            </div>
          </li>
        </template>
        <template v-for="item in stockAccountList">
          <li>
            <div class="base">
              <div class="bg"><img :src="BgShangHai" /></div>
              <h5>上海A股</h5>
              <p>
                <span class="num">
                  {{ item.stockAccount }}
                  <em class="acct_s_tag">{{ item.holderStatusDesc }}</em>
                </span>
              </p>
            </div>
            <div
              class="opea"
              v-if="item.appointTradeShow == 1 || item.openAccountShow == 1"
            >
              <a
                v-if="item.appointTradeShow == 1"
                class="com_btn"
                @click="appointClick(item)"
              >
                指定交易
              </a>
              <a
                v-if="item.openAccountShow == 1"
                class="com_btn"
                href="javascript:void(0)"
                @click="homeClick"
              >
                管理A股账户
              </a>
            </div>
          </li>
        </template>
        <template v-for="item in closedAccountList">
          <li>
            <div class="base">
              <div class="bg"><img :src="BgShangHai" /></div>
              <h5>上海封闭式基金</h5>
              <p>
                <span class="num">
                  {{ item.stockAccount }}
                  <em class="acct_s_tag">{{ item.holderStatusDesc }}</em>
                </span>
              </p>
            </div>
            <div
              class="opea"
              v-if="item.appointTradeShow == 1 || item.openAccountShow == 1"
            >
              <a
                v-if="item.appointTradeShow == 1"
                class="com_btn"
                @click="appointClick(item)"
              >
                指定交易
              </a>
              <a
                v-if="item.openAccountShow == 1"
                class="com_btn"
                href="javascript:void(0)"
                @click="homeClick"
              >
                管理A股账户
              </a>
            </div>
          </li>
        </template>
      </ul>
    </article>
  </section>
</template>

<script>
import { bkAppointTradeQry } from '@/service/service.js';
// import { jumpThirdPartyUrl } from '@/util';
// 引入上海背景图
import BgShangHai from '@/assets/images/bg_shanghai.png';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'TradingAccount',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      // 背景图
      BgShangHai,
      // 账户列表是否加载完成
      accountLoading: false,
      // A股账户
      stockAccountList: [],
      // 封闭式基金账户列表
      closedAccountList: [],
      // 当前登录用户普通资金账户
      fundAccount: ''
    };
  },
  mounted() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    const { inProperty } = this.tkFlowInfo();
    this.fundAccount = inProperty.fundAccount;
    this.getAccountList();
  },
  methods: {
    // 查询对应的账号列表
    async getAccountList() {
      this.accountLoading = false;
      const { code, data, msg } = await bkAppointTradeQry();
      if (code == 0) {
        this.stockAccountList = data.stockAccountShList;
        this.closedAccountList = data.stockAccountShFundList;
        this.accountLoading = true;
        console.info(this.stockAccountList, this.closedAccountList);
      } else {
        _hvueToast({ mes: msg });
      }
    },
    // 用户点击管理A股账户按钮
    homeClick() {
       // 跳转至补开股东户
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010044', initJumpMode: '0' });
      });
      /* const bizType = '010044';
      const url = $hvue.customConfig.thirdPartyUrl.reissueStockAcc;
      const pageData =
        $hvue.customConfig?.tkBizTypeList.filter(
          (it) => it.bizType === bizType
        )[0] || {};
      if (pageData.bizType) {
        if (pageData.pageName) {
          window.$router.push({
            name: pageData.pageName
          });
          $h.setSession('routerBack', 'true');
        } else {
          import('@/common/flowMixin.js').then((a) => {
            a.initFlow.call(this, pageData.bizType);
          });
        }
        return;
      }
      if ($hvue.platform === '0') {
        window.location.href = url;
      } else {
        console.log(setSsoLoginCache);
        setSsoLoginCache({
          authorization: $h.getSession('authorization'),
          userInfo: window?.$router.app.$store.state.user.userInfo
        });
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          params: {
            url,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      } */
    },
    // 用户点击指定交易按钮
    appointClick(item) {
      if (!!item.tips) {
        this.$TAlert({
          title: '温馨提示',
          tips: item.tips,
          confirm: () => {}
        });
        return;
      }
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        selected_accounts_data: JSON.stringify([item])
      });
    }
  }
};
</script>
<style scoped></style>
