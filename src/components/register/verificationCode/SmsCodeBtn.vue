<template>
  <a
    class="code_btn2"
    :class="{ disabled: countdown }"
    :style="cssCode"
    @click="sendSMS"
    >{{ countdown ? `重新发送${countdown}` : '获取验证码' }}
  </a>
</template>

<script>
import { smsCheckCodeSend, checkMobileTelNum } from '@/service/service';

export default {
  name: 'SmsCodeBtn',
  model: {
    prop: 'uuid',
    event: 'change'
  },
  props: {
    needImgCode: {
      type: Boolean,
      default: false
    },
    uuid: {
      type: String,
      default: ''
    },
    cssCode: {
      type: Object,
      default: () => {}
    },
    mobileNo: {
      type: String,
      default: ''
    },
    bizType: {
      type: String,
      default: ''
    },
    captcha: {
      type: String,
      default: ''
    },
    captchaToken: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      count: 0 // 短信验证码倒计时
    };
  },
  computed: {
    countdown() {
      if (this.count <= 0) {
        return false;
      } else {
        return `（${this.count}）`;
      }
    }
  },
  methods: {
    sendSMS() {
      if (this.countdown) return;
      if (!/1[3-9][\d]{9}/.test(this.mobileNo)) {
        _hvueToast({
          mes: '手机号格式不正确'
        });
        return false;
      }
      checkMobileTelNum({
        mobileTel: this.mobileNo
      })
        .then(({data}) => {
          const checkFlag = data.checkFlag; // 1输入的手机号已有5个资金账户在使用
          if(checkFlag === '1') {
            this.$TAlert({
              tips: '亲，您输入的手机号码有其他5个资金账户已使用，请重新输入。'
            })
            this.$emit('send-result', false);
            return
          }
          if (this.needImgCode) {
            if (!this.captcha) {
              _hvueToast({
                mes: '请输入图形验证码'
              });
              this.$emit('send-result', false);
              return false;
            }
            smsCheckCodeSend({
              mobile: this.mobileNo,
              captcha: this.captcha,
              captchaCode: this.captcha,
              captchaToken: this.captchaToken
            })
              .then((data) => {
                const uuid = data.data.serialNumber;
                this.count = $hvue.customConfig.sendSMSCount || 0;
                const timer = setInterval(() => {
                  if (this.count <= 0) {
                    clearInterval(timer);
                  } else {
                    this.count--;
                  }
                }, 1000);
                this.$once('hook:deactivated', () => {
                  clearInterval(timer);
                });
                this.$emit('change', uuid);
                this.$emit('send-result', true);
              })
              .catch((error) => {
                _hvueToast({
                  mes: error
                });
                this.$emit('send-result', false);
              });
            return;
          }
          smsCheckCodeSend({
            mobile: this.mobileNo
          })
            .then((data) => {
              const uuid = data.data.uuid || data.data.serialNumber;
              this.count = $hvue.customConfig.sendSMSCount || 0;
              const timer = setInterval(() => {
                if (this.count <= 0) {
                  clearInterval(timer);
                } else {
                  this.count--;
                }
              }, 1000);
              this.$once('hook:deactivated', () => {
                clearInterval(timer);
              });
              this.$emit('change', uuid);
              this.$emit('send-result', true);
            })
            .catch((error) => {
              _hvueToast({
                mes: error
              });
              this.$emit('send-result', false);
            });
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    }
  }
};
</script>
