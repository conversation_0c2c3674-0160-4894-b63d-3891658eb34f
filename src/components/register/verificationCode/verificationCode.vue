<template>
  <fragment>
    <van-field
      v-if="needImgCode"
      v-model="captcha"
      type="tel"
      input-align="left"
      maxlength="4"
      label="图形验证码"
      placeholder="请输入"
      :error-message="errorMessages[0] && errorMessages[0].imageCode"
    >
      <template #button
        ><a class="code_img" @click="imgClick"><img :src="imgSrc" /></a
      ></template>
    </van-field>
    <van-field
      v-model="fieldValue"
      type="tel"
      maxlength="6"
      :label="$attrs.labelSMS"
      :error-message="errorMessages[0] && errorMessages[0].messageCode"
      placeholder="请输入"
    >
      <template #button>
        <sms-code-btn
          v-model="uuid"
          :need-img-code="needImgCode"
          :mobile-no="mobileTel"
          :captcha="captcha"
          :captcha-token="captchaToken"
          :biz-type="'010009'"
          @send-result="SMSCodeCallback"
        />
      </template>
    </van-field>
  </fragment>
</template>

<script>
import { getImgCode } from '@/service/service';
import SmsCodeBtn from '@/components/register/verificationCode/SmsCodeBtn';
export default {
  name: 'VerificationCode',
  inject: ['setPropsByForm'],
  components: { SmsCodeBtn },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    needImgCode: {
      type: Boolean,
      default: true
    },
    mobileTel: {
      type: String,
      default: ''
    },
    errorMessages: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      fieldValue: '',
      uuid: '',
      imgSrc: '',
      captcha: '',
      captchaToken: ''
    };
  },
  watch: {
    uuid: {
      handler(val) {
        if (val) {
          $h.setSession('verificationCode_messageCode', val);
        }
      },
      deep: false,
      immediate: false
    },
    captcha: {
      handler(val) {
        this.setPropsByForm(this.$attrs.propKey, 'captcha', val);
      },
      deep: false,
      immediate: false
    },
    fieldValue: {
      handler(val) {
        this.setPropsByForm(this.$attrs.propKey, 'fieldValue', val);
        this.$emit('change', val);
      },
      deep: false,
      immediate: false
    }
  },
  mounted() {
    this.imgClick();
  },
  methods: {
    imgClick() {
      getImgCode({}, { loading: false }).then((data) => {
        if (data.code === 0) {
          let results = data.data;
          this.imgSrc = results.image;
          this.captchaToken = results.token;
        }
      });
    },

    SMSCodeCallback(flag) {
      if (!flag) {
        // 重新发送图形验证码
        this.imgClick();
      }
    }
  }
};
</script>

<style lang="less" scope>
// .component {
//   width: 100%;
//   // .van-cell {
//   //   min-height: 0.24rem;
//   //   line-height: 0.24rem;
//   //   padding: 0;
//   // }
//   // .van-cell::after {
//   //   display: none;
//   // }
// }
</style>
