<template>
  <section class="main fixed white_bg" data-page="home" style="position: fixed">
    <t-header></t-header>
    <article class="content">
      <div class="upload_progress" v-show="pageState === 2">
        <div class="progress_chart">
          <i class="bg">
            <svg
              width="193"
              height="193"
              viewBox="0 0 193 193"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M0 96.5C0 43.2045 43.2045 0 96.5 0C149.795 0 193 43.2045 193 96.5C193 97.3284 192.328 98 191.5 98C190.672 98 190 97.3284 190 96.5C190 44.8614 148.139 3 96.5 3C44.8614 3 3 44.8614 3 96.5C3 148.139 44.8614 190 96.5 190V193C43.2045 193 0 149.795 0 96.5Z"
              />
            </svg>
          </i>
          <div class="img"></div>
        </div>
        <h5>
          视频上传中(<span>{{ progressCount }}</span
          >%)
        </h5>
      </div>
      <div class="video_compage" v-show="pageState === 3">
        <div class="review_video" @click="videoStart">
          <div class="window">
            <video
              x5-video-player-type="h5"
              webkit-playsinline
              playsinline
              :src="regResult.videoBase64"
              ref="video"
            ></video>
          </div>
          <a v-show="videoPlayed" class="btn">点击预览</a>
          <a v-show="videoStoped" class="btn on">点击播放</a>
        </div>
        <div class="video_info">
          <h5>请确认影像和声音正确后提交</h5>
          <ul>
            <li><i></i>头像是否完整</li>
            <li><i></i>语音是否清晰</li>
            <li><i></i>视频里不能出现他人</li>
          </ul>
        </div>
      </div>
      <div class="result_page" v-show="pageState === 4">
        <div class="result_tips">
          <div class="icon vedio_ok"></div>
          <h5>恭喜! 视频已录制完成</h5>
          <p>您可以继续下一步</p>
        </div>
      </div>
      <div class="notice_box spel" v-show="pageState === 5">
        <div class="pic"><img src="@/assets/images/notice_error.png" /></div>
        <h5 class="error">视频上传失败</h5>
        <p>请重新上传视频文件</p>
      </div>
      <div v-show="pageState === 1">
        <div class="lz_basebox">
          <p>
            请录制一段{{ enVideoLengthMin }}~{{ enVideoLengthMax }}秒钟的视频。
          </p>
          <p>录制视频时，请使用普通话朗读:</p>
          <h5>{{ bizStandScript }}</h5>
          <div class="pic"><img src="@/assets/images/video_face2.png" /></div>
        </div>
        <div class="lz_tipbox">
          <h5 class="title">视频环节请注意以下事项：</h5>
          <ul>
            <li>
              <i><img src="@/assets/images/p_fc_tp01.png" /></i
              ><span>确保光线清晰</span>
            </li>
            <li>
              <i><img src="@/assets/images/p_fc_tp04.png" /></i
              ><span>远离嘈杂环境</span>
            </li>
            <li>
              <i><img src="@/assets/images/p_fc_tp03.png" /></i
              ><span>不能戴帽子</span>
            </li>
          </ul>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn" v-if="pageState === 1">
        <a class="p_button" @click="start">开始录制</a>
      </div>
      <div class="ce_btn" v-else-if="pageState === 3">
        <a class="p_button border" @click="start">重新录制</a>
        <a class="p_button" @click="_uploadVideo">确认提交</a>
      </div>
      <div class="ce_btn" v-else-if="pageState === 4">
        <a class="p_button" @click="toNext">下一步</a>
      </div>
      <div class="ce_btn block" v-else-if="pageState === 5">
        <a class="p_button" @click="start">重新上传</a>
        <a class="p_button link" @click="toIndex">放弃办理</a>
      </div>
    </footer>
    <videorecord
      ref="getVideoBoxBrowser"
      @getVideoCallBack="videoCallBack"
      @onerror="onerror"
    ></videorecord>
    <video-recording
      v-if="visibleVideoRecord"
      :context="bizScript"
      :enVideoLengthMin="enVideoLengthMin"
      :enVideoLengthMax="enVideoLengthMax"
      @callback="videoCallBack"
    />
  </section>
</template>

<script>
import { videoUpload } from '@/service/service';
import videorecord from '@/components/uploadImage/videorecord';
import VideoRecording from '@/components/media/VideoRecording';
import { EVENT_NAME } from '@/common/formEnum';
import { fileToBase64, uploadVideo, filterBase64Pre } from '@/common/util';
import AlipayUtil from '@/common/AlipayUtil';
import WeixinUtil from '@/common/WeixinUtil';

export default {
  name: 'witnessOne',
  inject: ['eventMessage'],
  components: {
    videorecord,
    VideoRecording
  },
  props: {
    enVideoLengthMin: {
      type: String,
      default: ''
    },
    enVideoLengthMax: {
      type: String,
      default: ''
    },
    clientName: {
      type: String,
      default: ''
    },
    bizScript: {
      type: String,
      default:
        '我已知晓证券市场风险，已阅读且充分理解开户协议条款并自愿在国金证券办理。'
    }
  },
  data() {
    return {
      pageState: 1, // 1准备页面  2上传中 3预览视频 4成功页面 5失败页面
      regResult: {},
      rejectReason: [],
      progressCount: '',
      filePath: '',
      fileData: {},
      videoPlayed: true,
      videoStoped: false,
      visibleVideoRecord: false
    };
  },
  watch: {
    pageState(state) {
      if (state === 4) {
        this.eventMessage(this, EVENT_NAME.BACK_BTN, { display: false });
      } else {
        this.eventMessage(this, EVENT_NAME.BACK_BTN, { display: true });
      }
    }
  },
  computed: {
    bizStandScript() {
      return this.bizScript.replace('${clientName}', this.clientName);
    }
  },
  created() {},
  deactivated() {
    this.pageState = 1;
  },
  methods: {
    back() {},
    toIndex() {
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    },
    start() {
      if ($hvue.platform == 0) {
        const alipayUtil = new AlipayUtil({
          recordTime: this.enVideoLengthMax,
          mainContent: this.bizStandScript,
          successFunc: this.videoCallBack
        });
        const wxUtil = new WeixinUtil();
        if (alipayUtil.checkAlipay) {
          alipayUtil.videoRecord();
        } else if (wxUtil.checkWx) {
          this.$refs.getVideoBoxBrowser.getVideo();
        } else {
          this.visibleVideoRecord = true;
        }
        return;
      }
      window.oneVideoCallBack = this.videoCallBack;
      var param = {
        moduleName: $hvue.customConfig.moduleName, // 必须为open
        funcNo: '60030', // 单向视频见证
        shortestTime: this.enVideoLengthMin,
        longestTime: this.enVideoLengthMax,
        readString: this.bizStandScript,
        mainColor: '#fa443a'
      };
      let result = $h.callMessageNative(param);
      if (result.error_no !== '0') {
        console.log(result.error_info);
      }
    },
    videoCallBack(data) {
      console.log(Object.keys(data));
      if (data.error_no == '-1') {
        console.log('取消');
        return;
      } else if (data.error_no != '0') {
        _hvueAlert({ mes: data.error_info });
        return;
      }
      try {
        this.pageState = 3;
        if (data.videoBase64) {
          // visibleVideoRecord === true 表示使用前端组件录制
          if (this.visibleVideoRecord) {
            this.visibleVideoRecord = false;
            this.regResult = { videoBase64: data.videoBase64 };
          } else {
            this.regResult = { videoBase64: data.videoBase64 };
            this._uploadVideo();
          }
        } else {
          this.fileData = data.file;
          fileToBase64(data.file)
            .then((base64) => {
              console.log('fileToBase64 callback success');
              this.regResult = { videoBase64: base64 };
            })
            .catch((e) => console.info('fileToBase64 callback error', e));
        }
      } catch (e) {
        console.info('pageState3 处理影像资料异常：', e);
      }
    },
    _uploadVideo() {
      console.log('确认提交 start');
      let _this = this;
      let param = {
        videoBase: filterBase64Pre(this.regResult.videoBase64),
        videoExt: 'mp4'
      };
      const listner = {
        success: (data) => {
          if (data.code === 0) {
            // _this.pageState = 4;
            _this.filePath = data.data.fileKey;
            _this.toNext();
          } else {
            _this.pageState = 5;
            console.error(data.msg);
          }
        },
        error: (err) => {
          _this.pageState = 5;
          console.error(err);
        },
        progress: (p) => {
          _this.pageState = 2;
          _this.progressCount = p;
        }
      };
      uploadVideo({
        url: $hvue.customConfig.serverUrl + '/video/upload',
        param,
        listner
      });
    },
    onerror(o) {
      _hvueToast({ mes: o.msg });
      this.pageState = 5;
    },
    videoStart() {
      const $video = this.$refs.video;
      if (this.videoPlayed || $video.paused) {
        this.$refs.video.play();
        this.videoPlayed = false;
        this.videoStoped = false;
      } else if ($video.ended) {
        this.videoStoped = false;
        this.videoPlayed = true;
        this.$refs.video.load();
      } else {
        this.videoStoped = true;
        this.videoPlayed = false;
        this.$refs.video.pause();
      }
      const _this = this;
      $video.addEventListener(
        'ended',
        function () {
          _this.videoPlayed = true;
          _this.videoStoped = false;
        },
        false
      );
    },
    toNext() {
      const $video = this.$refs.video;
      let formData = {
        witness_video: this.filePath,
        video_size: this.fileData.size,
        video_length: $video.duration.toFixed(2),
        biz_stand_script: this.bizStandScript,
        is_witness_pass: '0',
        witness_way: '1' //0双向 1单向
      };
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, formData);
    }
  }
};
</script>
