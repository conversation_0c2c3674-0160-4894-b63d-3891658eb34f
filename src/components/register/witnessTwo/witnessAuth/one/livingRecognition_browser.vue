<template>
  <article class="content">
    <div v-show="!errorPageHint" class="video_main">
      <div class="common_video">
        <div
          id="localDiv"
          class="one_video_local_div"
          style="width: 100%; height: 100%"
        ></div>
        <div id="remoteDiv" style="display: none"></div>
      </div>
      <div v-if="!isReciveLocalStream" class="video_loading">
        <div class="pic"><img src="./images/v_loading.png" /></div>
        <span>正在连接服务…</span>
      </div>
      <div v-show="isReciveLocalStream" class="video_flex_wrap spel">
        <div class="video_flex_head">
          <a class="back_btn" @click="cancelClick"></a>
        </div>
        <div class="video_flex_top">
          <div class="table_wrap">
            <div class="table_td">
              <div class="v_opea_tips">{{ actionArr[actionIndex].desc }}</div>
            </div>
          </div>
        </div>
        <div class="video_flex_middle">
          <div class="portrait_line"></div>
          <div v-show="errorHint" class="video_errortips">
            <span>{{ errorHint }}</span>
          </div>
        </div>
        <div class="video_flex_bottom">
          <div class="table_wrap">
            <div class="table_td">
              <div class="face_icon">
                <img :src="actionArr[actionIndex].img" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-show="networkStatus != 0" class="network_state">
        <span v-if="networkStatus == 1 || networkStatus == 2" class="ok"
          >网络正常</span
        >
        <span v-else-if="networkStatus == 3" class="warn">网络卡顿</span>
        <span v-else-if="networkStatus == 4" class="error">网络异常</span>
      </div>
    </div>
    <div v-if="errorPageHint">
      <!-- <header class="header">
				<div class="header_inner">
					<a class="icon_back" @click="cancelClick"></a>
				</div>
			</header> -->
      <div class="notice_box spel">
        <div class="pic"><img src="./images/notice_error.png" /></div>
        <h5 class="error">人脸识别不通过</h5>
        <p>{{ errorPageHint }}</p>
      </div>
      <div class="ce_btn mt30">
        <a class="p_button" @click="start">重新识别</a>
      </div>
    </div>
  </article>
</template>

<script>
import { cancelRequest } from 'thinkive-hvue';
import { faceCheck, getRoomNo, livingCheck } from './service/videoOneService';
import {
  playerAudio,
  getFaceRect,
  filterBase64Pre,
  takePic
} from './scripts/utils';
import './tchat/protobuf.min.js';
import './tchat/md5.js';
import './tchat/sdp-transform.js';
import './tchat/TChatRTC.js';

export default {
  name: 'LivingRecognitionBrowser',
  props: {
    userInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isReciveRmoteStream: false,
      isReciveLocalStream: false,
      faceBase64: '',
      room_no: -1,
      actionIndex: 0,
      livingRecognitionIntv: null,
      actionArr: [
        {
          audio: './audio/fxc_faceliveness_prepare.mp3',
          action: 'prepare',
          desc: '请您正对手机屏幕,开始刷脸',
          img: require('./images/fc_default.png')
        },
        {
          audio: './audio/fxc_common_notice_blink.mp3',
          action: 'blink',
          desc: '请眨眼',
          img: require('./images/fc_zhayan.gif')
        },
        {
          audio: './audio/fxc_common_notice_nod.mp3',
          action: 'nod',
          desc: '请上下点头',
          img: require('./images/fc_diantou.gif')
        },
        {
          audio: './audio/fxc_common_notice_mouth.mp3',
          action: 'mouth',
          desc: '请张嘴，随后合拢。',
          img: require('./images/fc_zhangzui.gif')
        },
        {
          audio: './audio/fxc_common_notice_yaw.mp3',
          action: 'yaw',
          desc: '请缓慢摇头',
          img: require('./images/fc_yaotou.gif')
        }
      ],
      errorHint: '',
      noticeErrorAudio: './audio/fxc_common_notice_error.mp3',
      isStartLivingRecognition: false,
      errorPageHint: '',
      liveFaceFailCount: 0, // 活体在框检测错误次数 计数
      liveFaceTotalFileCount: 0, // 在框检测错误页面跳转次数 计数
      liveActionFailCount: 0, // 活体动作错误次数 计数
      liveActionTotalFailCount: 0, // 活体动作错误页面跳转 计数
      videoPath: '',
      signalServer: '',
      signalSecretKey: '',
      loginId: '',
      authKey: '',
      audioObj: {},
      networkStatus: 0 // 网络状态   网络卡顿 网络不稳定  0未知 1很 2较好 3较差 4很差
    };
  },
  deactivated() {
    document.removeEventListener('visibilitychange', this.visibilitychange);
  },
  activated() {
    document.addEventListener('visibilitychange', this.visibilitychange);
  },
  created() {},
  beforeDestroy() {
    this.clearPage();
  },
  mounted() {
    this.clearPage();
    this.start();
  },
  methods: {
    cancelClick() {
      this.clearPage();
      this.$emit('cancel');
    },

    faceCheck() {
      let faceBase64 = filterBase64Pre(takePic());
      faceCheck(
        {
          flowNo: this.userInfo.flow_no,
          faceImageData: faceBase64,
          rect: getFaceRect()
        },
        { loading: false }
      ).then((data) => {
        let error_info = data.msg;
        if (data.code == '0') {
          let result = data.data;
          if (result.facePass == '1') {
            if (!this.isStartLivingRecognition) {
              this.faceBase64 = faceBase64;
              this.isStartLivingRecognition = true;
              this.actionIndex = parseInt(this.getRandAction(1).num) + 1;
              this.startRecord();
            }
            return;
          } else {
            error_info = result.checkDesc;
          }
        }
        // if (
        //   this.liveFaceTotalFileCount >= $hvue.customConfig.video.liveFaceTotalFileNum - 1 &&
        //   this.liveFaceFailCount >= $hvue.customConfig.video.liveFaceFailNum - 1
        // ) {
        //   this.cancelClick();
        //   _hvueAlert({
        //     title: '温馨提示',
        //     mes: '由于长时间未完成人脸认证，我们为您切换到一对一人工见证服务。',
        //     callback: () => {
        //       this.$router.replace({ path: 'witnessTwo' });
        //     }
        //   });
        // } else {
        if (
          this.liveFaceFailCount >=
          $hvue.customConfig.video.liveFaceFailNum - 1
        ) {
          this.liveFaceTotalFileCount++;
          this.errorPageHint =
            '由于长时间未检测到面部在框，本次人脸认证失败，请重新检测。';
          this.clearVideoPage();
          cancelRequest();
        } else {
          this.showErrorHint(error_info);
          this.liveFaceFailCount++;
        }
        // }
      });
    },
    showErrorHint(msg, time) {
      time = time || 1;
      if (msg) {
        this.errorHint = msg;
        window.clearTimeout(window.showErrorHintTimeout);
        window.showErrorHintTimeout = window.setTimeout(() => {
          this.errorHint = '';
        }, time * 1000);
      }
    },
    getRoomNo() {
      this.isReciveRmoteStream = false;
      this.isReciveLocalStream = false;
      getRoomNo({ flowNo: this.userInfo.flow_no }, { loading: false }).then(
        (data) => {
          if (data.code == 0) {
            let result = data.data;
            if (result.sercurityNo == '-1') {
              _hvueAlert({ mes: '视频人数太多，请稍后再试。' });
              this.cancelClick();
            } else {
              this.room_no = result.sercurityNo;
              this.videoPath = result.videoPath;
              this.signalServer = result.signalServer;
              this.signalSecretKey = result.signalSecretKey;
              this.loginId = result.loginId;
              this.authKey = result.authKey;
              TChatRTC.openLocalCamera(
                () => {
                  this.isReciveLocalStream = true;
                  this.startWebRtc();
                },
                (e) => {
                  if (e.name == 'NotAllowedError') {
                    this.cancelClick();
                    _hvueAlert({ mes: '无摄像头权限' });
                  } else {
                    _hvueAlert({ mes: `获取摄像头失败，${e.name || e}` });
                    this.cancelClick();
                  }
                }
              );
            }
          } else {
            _hvueAlert({
              mes: data.msg
            });
            this.cancelClick();
          }
        }
      );
    },
    startWebRtc() {
      let tchatParamConfig = {
        signalServer: this.signalServer, //信令服务器地址
        protobufFileLoaction:
          $hvue.customConfig.video.tchat.protobufFileLoaction, //pb文件地址
        secretkey: this.signalSecretKey, //信令服务器认证秘钥
        roomid: this.room_no //排队获取的房间号
      };
      TChatRTC.init(
        {
          addNewUser: function () {}, //新用户加入
          exitRoomNotify: () => {
            _hvueToast({ mes: '录制异常' });
            this.cancelClick();
          }, //坐席离开房间
          onMsgNotify: () => {
            _hvueToast({ mes: '文本消息' });
          }, //坐席发送文本消息回调
          onTransBufferNotify: this.onTransBufferNotify, //坐席发送指派指令
          onRemoteStreamAdd: () => {
            //远程坐席视频流加入回调
            console.log('远程流加入');
            this.isReciveRmoteStream = true;
          },
          timeout: () => {
            //信令服务器超时
            // _hvueToast({ mes: '连接信令服务器超时' });
            _hvueToast({ mes: '录制异常' });
            this.cancelClick();
          },
          netError: () => {
            //网络错误
            _hvueToast({ mes: '您的网络异常' });
            this.cancelClick();
          },
          socketError: () => {
            //坐席网络异常
            _hvueToast({ mes: '网络连接异常，请退出重试' });
            this.cancelClick();
          },
          destory: function () {}
        },
        tchatParamConfig,
        this.videoInitSuccessBack
      );
    },
    videoInitSuccessBack(data) {
      if (data.errcode == 0) {
        TChatRTC.login(
          {
            //登录信令服务器
            username: this.loginId
          },
          (data) => {
            if (data.errcode == 0) {
              TChatRTC.enterRoom(
                { roomid: this.room_no, password: this.authKey },
                (data) => {
                  //进入房间
                  if (data.errcode == 0) {
                    TChatRTC.startWebRtc(data.result);
                  } else {
                    _hvueToast({ mes: data.errmsg });
                    this.cancelClick();
                  }
                }
              );
            } else {
              _hvueToast({ mes: data.errmsg });
              this.cancelClick();
            }
          }
        );
      } else {
        if (data.errcode == -1003) {
          data.errmsg = '网络连接异常，请退出重试!';
        }
        _hvueToast({ mes: data.errmsg });
        this.cancelClick();
      }
    },
    startRecord() {
      playerAudio(this.actionArr[this.actionIndex].audio, this.audioObj);
      console.log('开始录制');
      TChatRTC.sendMsgByTransBuffer({
        msg: `h5cmd@{"type":"start_record","path":"${this.videoPath}"}`
      });
    },
    stopRecord() {
      cancelRequest();
      window.clearInterval(this.livingRecognitionIntv);
      console.log('停止录制');
      TChatRTC.sendMsgByTransBuffer({ msg: 'h5cmd@{"type":"stop_record"}' });
    },
    onTransBufferNotify(data) {
      if (data.errcode == '0') {
        var msg = decodeURI(
          String.fromCharCode.apply(null, data.result.cmdmsg)
        );
        msg = msg.replace(/\\&quot;/g, '"'); //将返回信息中的引用还原
        msg = msg.replace(/&quot;/g, '"'); //将返回json中多余的引号去掉
        console.log(msg);
        if (msg.includes('h5ret@')) {
          msg = JSON.parse(msg.replace('h5ret@', ''));
          if (msg.type == 'record_start') {
            if (msg.error_code == '0') {
              console.log('开始录制成功');
              window.setTimeout(this.stopRecord, 4 * 1000);
            } else {
              _hvueAlert({ mes: msg.error_code });
            }
          } else if (msg.type == 'record_stop') {
            if (msg.error_code == '0') {
              console.log('停止录制成功');
              livingCheck(
                {
                  flowNo: this.userInfo.flow_no,
                  videoPath: msg.path,
                  actionType: this.actionArr[this.actionIndex].action
                },
                { loading: false }
              ).then((data) => {
                if (data.code == '0') {
                  let result = data.data;
                  if (result.livingPass) {
                    this.$emit('getImgCallBack', {
                      error_no: '0',
                      faceImage: result.livingImagePath,
                      base64: this.faceBase64
                    });
                    return;
                  } else {
                    data.msg = '活体识别不通过,请重试';
                  }
                }
                if (
                  this.liveActionTotalFailCount >=
                    $hvue.customConfig.video.liveActionTotalFailNum - 1 &&
                  this.liveActionFailCount >=
                    $hvue.customConfig.video.liveActionFailNum - 1
                ) {
                  this.cancelClick();
                  _hvueAlert({
                    title: '温馨提示',
                    mes:
                      '由于长时间活体识别未通过，本次人脸认证失败，请重新检测。'
                  });
                } else {
                  if (
                    this.liveActionFailCount >=
                    $hvue.customConfig.video.liveActionFailNum - 1
                  ) {
                    this.liveActionTotalFailCount++;
                    this.errorPageHint =
                      '由于长时间活体识别未通过，本次人脸认证失败，请重新检测。';
                    this.clearVideoPage();
                    cancelRequest();
                  } else {
                    this.showErrorHint(data.msg);
                    this.liveActionFailCount++;
                    this.startRecord();
                    this.startFaceCheck();
                  }
                }
              });
            } else {
              _hvueAlert({ mes: msg.error_code });
              this.cancelClick();
            }
          } else if (msg.type == 'record_error') {
            console.log(`录制异常,${msg.error_code}`);
            _hvueAlert({ mes: `录制异常,错误码：${msg.error_code}` });
            this.cancelClick();
          } else if (msg.type == 'tts_start') {
            console.log('开始播报回调');
          } else if (msg.type == 'tts_stop') {
            console.log('停止播报回调');
            if (msg.name) {
              // 播放mp3提示回调
              window.clearTimeout(window.playerAudioTimeout);
              window.playerAudioCallBack &&
                window.playerAudioCallBack(msg.name);
              window.playerAudioCallBack = null;
            }
          } else if (msg.type == 'channel_ready') {
            playerAudio(this.actionArr[0].audio, this.audioObj, () => {
              this.startFaceCheck();
            });
          } else if (msg.type == 'net_qos') {
            this.networkStatus = msg.quality;
          }
        }
      }
      console.log(data);
    },
    startFaceCheck() {
      this.livingRecognitionIntv = window.setInterval(
        this.faceCheck,
        $hvue.customConfig.video.checkIntvTime
      );
    },
    /**
     * 产生随机动作
     * num 动作个数
     */
    getRandAction(num) {
      var actionArr = ['0', '1', '2', '3']; // 0:眨眼 1:上下点头 2:张嘴 3:左右转头
      var resultArr = [];
      if (!num) num = 1;
      if (num > actionArr.length) num = actionArr.length;
      for (var i = 0; i < num; i++) {
        var rand = Math.floor(Math.random() * actionArr.length);
        resultArr.push(actionArr.splice(rand, 1));
      }
      let nameArr = [];
      let nameMap = {
        0: 'blink',
        1: 'nod',
        2: 'mouth',
        3: 'yaw'
      };
      resultArr.forEach((a) => {
        nameArr.push(nameMap[a]);
      });
      return {
        num: resultArr.join(','),
        name: nameArr.join('|')
      };
    },
    clearVideoPage() {
      this.networkStatus = 0;
      this.isStartLivingRecognition = false;
      this.liveFaceFailCount = 0;
      this.liveActionFailCount = 0;
      this.actionIndex = 0;
      window.clearInterval(this.livingRecognitionIntv);
      window.clearTimeout(window.playerAudioTimeout);
      try {
        TChatRTC.closeAll();
        if (this.audioObj.buffSource) {
          this.audioObj.buffSource.stop();
        }
      } catch (e) {
        console.log(e);
      }
    },
    clearPage() {
      this.isReciveLocalStream = false;
      this.liveFaceTotalFileCount = 0;
      this.liveActionTotalFailCount = 0;
      this.clearVideoPage();
    },
    start() {
      this.errorPageHint = '';
      this.getRoomNo();
    },
    visibilitychange() {
      var isHidden = document.hidden;
      console.log(document.visibilityState);
      if (isHidden) {
        this.cancelClick();
        _hvueAlert({ mes: '视频认证已取消' });
      }
    }
  }
};
</script>
<style scoped>
.content {
  margin: 0;
  padding: 0;
  font-size: 0.14rem;
  height: 100%;
  width: 100%;
  background: #fff;
}
.content h5 {
  margin: 0;
}
.common_video {
  width: 100%;
  height: 100%;
  position: relative;
}
.video_main {
  width: 100%;
  background: #000000;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
}
.common_video >>> video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.video_flex_wrap.no_bg .video_flex_head,
.video_flex_wrap.no_bg .video_flex_top,
.video_flex_wrap.no_bg .video_flex_bottom,
.video_flex_wrap.no_bg .video_flex_middle:before,
.video_flex_wrap.no_bg .video_flex_middle:after {
  background: none;
}

.video_flex_wrap {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  -webkit-flex-direction: column;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  z-index: 200;
}

.video_flex_head,
.video_flex_top,
.video_flex_bottom,
.video_flex_middle:before,
.video_flex_middle:after {
  background: rgba(0, 0, 0, 0.7);
}

.video_flex_head {
  display: block;
  height: 0.44rem;
  width: 100%;
  position: relative;
}

.video_flex_top {
  height: 1.5rem;
}

.video_flex_top .table_wrap {
  width: 100%;
  display: table;
  height: 100%;
}

.video_flex_top .table_td {
  height: 100%;
  display: table-cell;
  vertical-align: middle;
  text-align: center;
  padding: 0 0.2rem;
}

.video_flex_middle {
  width: 100%;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  position: relative;
}

.video_flex_middle:before,
.video_flex_middle:after {
  content: '';
  display: block;
  height: 3.44rem;
  -moz-box-flex: 1;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  width: 100%;
}

.portrait_line {
  display: block;
  width: 3.04rem;
  height: 3.44rem;
  background: url(./images/portrait_icon3.png) no-repeat center;
  background-size: 100%;
  position: relative;
}

.portrait_line:before {
  content: '';
  width: 100%;
  height: 100%;
  background: url(./images/portrait_icon4.png) no-repeat center;
  background-size: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.video_flex_bottom {
  -moz-box-flex: 1;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  width: 100%;
}

.video_flex_bottom .table_wrap {
  height: 100%;
  width: 100%;
  display: table;
}

.video_flex_bottom .table_wrap .table_td {
  height: 100%;
  display: table-cell;
  vertical-align: middle;
  text-align: center;
  padding: 0 0.2rem;
}
.video_errortips {
  width: 100%;
  text-align: center;
  position: fixed;
  top: 38%;
  left: 0;
  z-index: 200;
}

.video_errortips span {
  display: inline-block;
  padding: 0.05rem 0.17rem;
  text-align: center;
  background: #fe3b30;
  border-radius: 0.03rem;
  font-size: 0.22rem;
  line-height: 0.3rem;
  color: #fff;
}

.video_flex_middle .video_errortips {
  padding: 0 0.25rem;
  position: absolute;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.video_flex_middle .video_errortips span {
  background: #fd4d43;
  padding: 0.08rem 0.15rem;
  font-size: 0.18rem;
  line-height: 0.24rem;
  color: #eaeaea;
  border-radius: 0.1rem;
}

.back_btn {
  display: block;
  width: 0.32rem;
  height: 0.32rem;
  background: rgba(255, 255, 255, 0.2) url(./images/icon_back4.png) no-repeat
    center;
  background-size: 0.16rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 50%;
  position: absolute;
  top: 0.06rem;
  left: 0.2rem;
  z-index: 50;
}

.video_flex_wrap.spel .video_flex_head {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 50;
  background: none;
}

.video_flex_wrap.spel .video_flex_top {
  flex: 1;
  padding-top: 0.44rem;
  height: auto;
}
.v_opea_tips {
  display: block;
  padding: 0.08rem 0.1rem;
  text-align: center;
  color: #ffffff;
  font-size: 0.18rem;
  line-height: 0.24rem;
  background: rgba(12, 52, 88, 0.5);
  border: 1px solid rgba(73, 143, 213, 0.4);
  border-radius: 0.1rem;
}

.face_icon {
  height: 0.9rem;
  position: relative;
  margin: 0 auto;
}

.face_icon img {
  display: block;
  height: 100%;
  margin: 0 auto;
}

.network_state {
  line-height: 0.2rem;
  font-size: 0.12rem;
  color: #ffffff;
  position: absolute;
  top: 0.12rem;
  right: 0.2rem;
  z-index: 200;
}

.network_state .error {
  color: #ff4951;
}

.network_state .warn {
  color: #ffbe00;
}

.network_state .ok {
  color: #17d744;
}

.video_loading {
  width: 1.6rem;
  padding: 0.15rem;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 0.05rem;
  text-align: center;
  font-size: 0.16rem;
  line-height: 0.2rem;
  color: #51b4fe;
  position: fixed;
  top: 50%;
  left: 50%;
  margin-left: -0.8rem;
  -webkit-transform: translateY(-100%);
  transform: translateY(-100%);
  z-index: 200;
}

.video_loading .pic {
  height: 0.72rem;
  margin-bottom: 0.25rem;
}

.video_loading .pic img {
  display: block;
  height: 100%;
  margin: 0 auto;
}
.header_inner {
  position: relative;
  height: 0.44rem;
  line-height: 0.44rem;
}
.icon_back {
  width: 0.48rem;
  height: 0.44rem;
  background: url(./images/icon_back.png) no-repeat center;
  background-size: 0.24rem;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 50;
}
.notice_box {
  background: #ffffff;
  padding: 0.34rem 0.16rem 0.2rem;
  min-height: 2.8rem;
  text-align: center;
  color: #999999;
  line-height: 0.2rem;
}

.notice_box .pic {
  width: 1.2rem;
  height: 1.2rem;
  margin: 0 auto 0.04rem;
}

.notice_box .pic img {
  display: block;
  width: 100%;
  height: 100%;
}

.notice_box h5 {
  font-size: 0.24rem;
  font-weight: normal;
  line-height: 0.32rem;
  margin-bottom: 0.12rem;
  color: #333333;
}

.notice_box.spel {
  padding-top: 0.5rem;
}

.notice_box h5.error {
  padding-top: 0.3rem;
  color: #ff4848;
}

.notice_box p a {
  color: #1061ff;
}
.ce_btn {
  display: flex;
  padding: 0.15rem 0.16rem;
}
.mt30 {
  margin-top: 0.3rem !important;
}
.ce_btn a {
  flex: 1;
  margin-left: 0.12rem;
}
.ce_btn a:first-child {
  margin-left: 0;
}
.p_button {
  display: block;
  height: 0.44rem;
  line-height: 0.44rem;
  text-align: center;
  font-size: 0.16rem;
  background: linear-gradient(90deg, #1061ff 0%, #5a92ff 100%);
  color: #ffffff;
  border-radius: 0.5rem;
  font-weight: 500;
}
</style>
