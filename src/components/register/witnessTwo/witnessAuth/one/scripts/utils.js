export function playerAudio(audioUrl, audioObj, endCallback, timeout = 10000) {
  console.log('playerAudio---------------------' + audioUrl);
  // if ($hvue.customConfig.video.playerAudioByHttp) {
  //   playerAudioByHttp(audioUrl, audioObj, endCallback, timeout);
  // } else {
  audioUrl = audioUrl.replace('./audio/', '');
  playerAudioByTchat(audioUrl, endCallback, timeout);
  // }
}

export function playerAudioByTchat(audioUrl, endCallback, timeout) {
  window.playerAudioTimeout = window.setTimeout(() => {
    console.log('触发播报回调超时逻辑');
    window.playerAudioCallBack && window.playerAudioCallBack(audioUrl);
    window.playerAudioCallBack = null;
  }, timeout);
  window.playerAudioCallBack = endCallback || function () {};
  TChatRTC.sendMsgByTransBuffer({
    msg: 'h5cmd@{"type":"start_playex","name":"' + audioUrl + '"}'
  });
}

/*从tchat获取本地视频获取一张照片 */
export function takePic() {
  let localVideo = document.querySelector('#localDiv').querySelector('video');
  let canvas = document.createElement('canvas');
  canvas.width = localVideo.videoWidth;
  canvas.height = localVideo.videoHeight;
  canvas
    .getContext('2d')
    .drawImage(localVideo, 0, 0, localVideo.videoWidth, localVideo.videoHeight);
  let base64 = canvas.toDataURL('image/jpeg', 0.8);
  canvas = null;
  return base64;
}

/**
 * 获取人脸框坐标
 * @returns
 */
export function getFaceRect() {
  let localVideo = document.querySelector('#localDiv').querySelector('video');
  let faceEl = document.querySelector('.portrait_line'); // 人脸校准框
  let videoWidth = localVideo.videoWidth; // 视频真实宽
  let videoHeight = localVideo.videoHeight; // 视频真实高
  let clientWidth = document.body.clientWidth; // 屏幕宽
  let clientHeight = document.body.clientHeight; // 屏幕高
  let clientFaceWidth = faceEl.offsetWidth; // 人脸校准框图相对屏幕的宽度
  let clientFaceHeight = faceEl.offsetHeight; // 人脸校准框图相对屏幕的高度
  let leftTopX = (clientWidth - faceEl.offsetWidth) / 2; // 人脸框相对屏幕左上角X
  let leftTopY =
    document.querySelector('.video_flex_head').offsetHeight +
    document.querySelector('.video_flex_top').offsetHeight; // 人脸框相对屏幕左上角Y
  let rightBottomX = leftTopX + clientFaceWidth; // 人脸框相对屏幕右下角X
  let rightBottomY = leftTopY + clientFaceHeight; // 人脸框相对屏幕右下角Y

  // 因为视频上下有黑边，真实的Y坐标需要减掉上黑边高度
  let blackBarHeight =
    (clientHeight - (clientWidth / videoWidth) * videoHeight) / 2; // 上黑边的高度
  leftTopY = leftTopY - blackBarHeight;
  rightBottomY = rightBottomY - blackBarHeight;

  // 计算人脸框相对真实视频的坐标
  let ratio = videoWidth / clientWidth;
  leftTopX = ratio * leftTopX;
  leftTopY = ratio * leftTopY;
  rightBottomX = ratio * rightBottomX;
  rightBottomY = ratio * rightBottomY;

  let errorRate = 0; // 容错率
  leftTopX = (leftTopX * (1 - errorRate)).toFixed(0);
  leftTopY = (
    leftTopY * (leftTopY > 0 ? 1 - errorRate : 1 + errorRate)
  ).toFixed(0);
  rightBottomX = (rightBottomX * (1 + errorRate)).toFixed(0);
  rightBottomY = (rightBottomY * (1 + errorRate)).toFixed(0);
  leftTopX = Math.max(leftTopX, 0);
  leftTopY = Math.max(leftTopY, 0);
  console.log(
    `坐标：${leftTopX},${leftTopY},${rightBottomX},${rightBottomY},视频宽高：${videoWidth},${videoHeight},屏幕宽高：${clientWidth},${clientHeight}`
  );
  return `${leftTopX},${leftTopY},${rightBottomX},${rightBottomY}`;
}

/**
 * 去掉图片base64前缀
 * @param {*} ndata
 * @returns
 */
export function filterBase64Pre(ndata) {
  let arr = ndata.split('base64,');
  return arr[arr.length - 1];
}
