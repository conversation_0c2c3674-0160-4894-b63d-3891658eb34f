/*创建时间2022-05-06 00:31:44 AM 作者：xhq*/
var TChatRTC={init:function(){},login:function(){},enterRoom:function(){},getUserList:function(){},getRoomList:function(){},videoCall:function(){},replayVideoCall:function(){},stopVideo:function(){},exitRoom:function(){},heartbeat:function(){},sendMsg:function(){},sendMsgByTransBuffer:function(){},startWebRtc:function(){},reConnect:function(){},wsClose:function(){},closeAll:function(){},openLocalCamera:function(){},closeMedia:function(){},listenerConfig:{addNewUser:null,videoCalled:null,exitRoomNotify:null,onMsgNotify:null,onTransBufferNotify:null,onRemoteStreamAdd:null,timeout:null,netError:null,socketError:null,destory:null},version:"2.4.1"};!function(a){function b(){try{M=new WebSocket(a.config.signalServer),M.binaryType="arraybuffer",M.onmessage=qb,M.onopen=rb,M.onerror=sb,M.onclose=tb}catch(b){alert("wsConnect err:"+JSON.stringify(b))}}function c(b,c){var d={msgtype:b,request:c};1==a.config.isDebug&&console.log("send:"+JSON.stringify(d));var e=$&&$.encode($.fromObject(d)).finish();M&&1==M.readyState&&M.send(e)}function d(b,c){var d={msgtype:b,response:c};1==a.config.isDebug&&console.log("send:"+JSON.stringify(d));var e=$&&$.encode($.fromObject(d)).finish();M&&1==M.readyState&&M.send(e)}function e(){if(!O){if(R=document.querySelector("#"+a.config.localId),!R)return alert(a.config.localId+"不存在"),void 0;O=document.createElement("video"),O.setAttribute("playsinline",""),O.setAttribute("webkit-playsinline",""),O.setAttribute("id","tchatLocalVideo"),O.setAttribute("muted",""),O.setAttribute("poster","data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAIAAAACCAYAAABytg0kAAAAE0lEQVQImWNkYGD4D8QMTAxQAAAOKAED2ysUfQAAAABJRU5ErkJggg=="),O.muted=!0,O.style.webkitTransform="scale(-1,1)",O.style.transform="scale(-1,1)",R.appendChild(O)}}function f(a,b){a?protobuf.load(a,function(c,d){c?(console.log(a),console.log(c),g(b,c)):b(d)}):g(b)}function g(a,b){var c=new Error("err"),d=c.stack||c.sourceURL||c.stacktrace||"",e=/(?:http|https|file):\/\/.*?\/.+?.js/,f=(e.exec(d)||[])[0]||"";f=f.substring(0,f.lastIndexOf("/")+1).replace(location.protocol+"//"+location.host,""),protobuf.load(f+"sd.data_structure.proto",function(c,d){c?alert("加载protobuf文件报错:"+b+";"+c):a(d)})}function h(a){var b,c,d=new Array;b=a.length;for(var e=0;b>e;e++)c=a.charCodeAt(e),c>=65536&&1114111>=c?(d.push(c>>18&7|240),d.push(c>>12&63|128),d.push(c>>6&63|128),d.push(63&c|128)):c>=2048&&65535>=c?(d.push(c>>12&15|224),d.push(c>>6&63|128),d.push(63&c|128)):c>=128&&2047>=c?(d.push(c>>6&31|192),d.push(63&c|128)):d.push(255&c);return d}function i(b,c){N=b;var d=new MediaStream;b.getVideoTracks().forEach(function(a){d.addTrack(a)}),O.srcObject=d,O.onloadedmetadata=function(){if(b.addTrack){var d=b.getVideoTracks();z("video tracks length："+d.length);var e=b.getAudioTracks();z("audio tracks length："+e.length),b.getTracks().forEach(function(a){z("tracks prop:"+JSON.stringify({id:a.id,contentHint:a.contentHint,enabled:a.enabled,kind:a.kind,label:a.label,muted:a.muted,readonly:a.readonly,readyState:a.readyState,remote:a.remote}))})}else z("stream get success");O.play(),a.config.useCanvasRender&&j(O),c()}}function j(a){var b=document.querySelector("#"+a.id+"Canvas");b||(b=document.createElement("canvas"),b.width=a.videoWidth,b.height=a.videoHeight,b.id=a.id+"Canvas",a.parentNode.appendChild(b));var c=b.getContext("2d");k(a,b,c)}function k(a,b,c){a&&!a.ended&&(c.drawImage(a,0,0,b.width,b.height),window.requestAnimationFrame(function(){k(a,b,c)}))}function l(a,b,c,d){navigator.mediaDevices.getUserMedia(a).then(function(e){z("get media success. constraints："+JSON.stringify(a));var f=e.getVideoTracks();d&&f&&f[0]&&f[0].getSettings().facingMode&&"user"!=f[0].getSettings().facingMode?(z("camera is not front, retry front","warn"),e.getTracks().forEach(function(a){a.stop()}),l({audio:!0,video:{facingMode:"user"}},b,c)):b(e)}).catch(function(b){var d="";Object.getOwnPropertyNames(b).forEach(function(a){d+=a+","+b[a]+";"}),z("get media error. constraints："+JSON.stringify(a)+","+d,"error"),z(JSON.stringify(b),"error"),c(b)})}function m(){n(),hb=setInterval(function(){navigator.onLine?n():(window.clearInterval(hb),window.clearInterval(cb),window.clearInterval(kb),a.closeAll(),a.listenerConfig.netError&&a.listenerConfig.netError())},300*fb)}function n(){a.heartbeat();var b=new Date,c=(b.getTime()-gb.getTime())/1e3;0==db&&c>fb&&(window.clearInterval(hb),window.clearInterval(cb),window.clearInterval(kb),a.closeAll(),setTimeout(function(){navigator.onLine?(db=!0,a.listenerConfig.timeout&&a.listenerConfig.timeout()):a.listenerConfig.netError&&a.listenerConfig.netError()},200))}function o(a){z("send ice"),c(8265,{eireq:{iceinfo:a,ownuserid:L.userid,peeruserid:L.peeruserid,roomid:L.roomid}})}function p(){var b=0,c=0;cb=setInterval(function(){a.getStatsData(function(d){if(a.config.netInfoIsShow)q(d,b,c);else{var e=document.querySelector(".tchatNet");e&&e.parentNode.removeChild(e)}if(a.config.netInfoIsSend){var f={duration:a.config.netInfoSendTime,senddata:d.curTotalSend-b,recvdata:d.curTotalReceived-c};a.sendStats(f)}b=d.curTotalSend,c=d.curTotalReceived})},1e3*a.config.netInfoSendTime)}function q(b,c,d){function e(a){for(var b in a)i+=b+":&nbsp;"+a[b]+"<br>"}var f=(b.curTotalSend-c)/1024/a.config.netInfoSendTime,g=(b.curTotalReceived-d)/1024/a.config.netInfoSendTime,h={U:f.toFixed(2)+"kb/s &nbsp;&nbsp;D:"+g.toFixed(2)+"kb/s",roomid:L.roomid,audioSend:(b.audioBytesSent/1024).toFixed(2)+"kb",videoBytesSent:(b.videoBytesSent/1024).toFixed(2)+"kb",audioReceived:(b.audioBytesReceived/1024).toFixed(2)+"kb",videoReceived:(b.videoBytesReceived/1024).toFixed(2)+"kb",ua:navigator.userAgent},i="";e(h),N.getTracks().forEach(function(a){e(a.getSettings())});var j=document.querySelector(".tchatNet");j||(j=document.createElement("div"),j.style.position="absolute",j.style.top=a.config.netInfoTop||"0",j.style.left=a.config.netInfoLeft||"10px",j.style.bottom=a.config.netInfoBottom||"none",j.style.right=a.config.netInfoRight||"none",j.style.color="white",j.style.fontSize="13px",j.style.overflow="auto",j.style.opacity="0.5",j.style.background="black",j.style.pointerEvents="none",j.style.left="0",j.style.zIndex="9999",j.className="tchatNet",document.body.appendChild(j)),j.innerHTML=i}function r(b){if("undefined"==typeof sdpTransform)return b;var c=sdpTransform.parse(b),d=0;for(d=0;d<c.media.length;d++)if("video"==c.media[d].type){var e=c.media[d],f={};for(d=0;d<e.rtp.length;d++)if("VP8"==e.rtp[d].codec){f=e.rtp[d];break}var g="";for(d=0;d<e.fmtp.length;d++)if(e.fmtp[d].payload==f.payload){g=e.fmtp[d];break}var h="x-google-start-bitrate="+a.config["x-google-start-bitrate"]+";x-google-max-bitrate="+a.config["x-google-max-bitrate"]+";x-google-min-bitrate="+a.config["x-google-min-bitrate"];g?g.config=g.config+";"+h:e.fmtp.push({payload:f.payload,config:h}),b=sdpTransform.write(c)}return b}function s(b,c,d,e){a.config.isDataChannelSend?(a.config.isDataChannelSend&&1==e&&(b=b.replace(/sendrecv/g,"recvonly")),a.config.isDataChannelSend&&2==e&&(b=b.replace(/sendrecv/g,"sendonly"))):(b=b.replace(/sendonly/g,"sendrecv"),b=b.replace(/recvonly/g,"sendrecv"));for(var f=b.split("\n"),g=-1,h=0;h<f.length;h++)if(0===f[h].indexOf("m="+c)){g=h;break}if(-1===g)return z("sdp中不包含"+c+"信息","error"),b;for(g++;0===f[g].indexOf("i=")||0===f[g].indexOf("c=");)g++;for(;0===f[g].indexOf("b");)f.splice(g,1);return d="b=AS:"+(d||100),f.splice(g,0,d),f.join("\n")}function t(b,c){a.listMedia("",function(a){z("media list:"+JSON.stringify(a))});try{lb&&mb||(z("you browser is not support webrtc"),c?c({errcode:"-2000",errmsg:"您的浏览器不支持webrtc"}):alert("您的浏览器不支持webrtc")),pb=[],J=!1;var d=[];b.vserverip.split("|").forEach(function(a){var c=a.split(";");c.length>1?d.push({urls:c[0],username:c[1],credential:c[2]}):d.push({urls:"turn:"+b.vserverip+":"+b.vserverport,username:"test1",credential:"12345678"})}),z("RTCPeerConnection iceServers:"+JSON.stringify(d));var e={iceServers:d};a.config.isFoceSturn&&(e.iceTransportPolicy="relay");try{_=new lb(e)}catch(f){z("peerconnection create error:"+JSON.stringify(f)),c?c({errcode:"-2001",errmsg:"peerconnection create error:"+JSON.stringify(f)}):alert("peerconnection create error:"+JSON.stringify(f))}_.onicecandidate=function(a){a.candidate&&a.candidate&&(J?o(JSON.stringify(a.candidate)):pb.push(JSON.stringify(a.candidate)))},a.config.isDataChannelSend&&x(c);try{p()}catch(f){z("get net statu error:"+f.name)}_.addTrack?(_.ontrack=function(b){z("recive track:"+b.track.kind);var c=b.streams[0];c||(z("ontrack new MediaStream"),c=new MediaStream,c.addTrack(b.track)),a.config.isDeparted?"video"===b.track.kind?(z("play remote video"),P.srcObject=c,P.muted=!0,P.onloadedmetadata=function(){P.play(),a.config.useCanvasRender&&j(P)}):E?(z("connectToSpeaker"),B(c,10)):(z("play remote audio"),Q.srcObject=c,Q.onloadedmetadata=function(a){try{Q.play().then(function(){z("remoteAudio play success")}).catch(function(a){console.log(a),z("remoteAudio play error:"+a.message,"error")})}catch(a){console.log(a),z("remoteAudio play error:"+a.message,"error")}}):(z("play remote video and audio"),P.srcObject=c,P.muted=!1,P.onloadedmetadata=function(b){try{P.play().then(function(){z("remoteVideo play success")}).catch(function(a){console.log(a),z("remoteVideo play error:"+a.message,"error")})}catch(b){console.log(b),z("remoteVideo play error:"+b.message,"error")}a.config.useCanvasRender&&j(P)}),a.listenerConfig.onRemoteStreamAdd(c)},N.getTracks().forEach(function(a){z("send track:"+a.kind),_.addTrack(a),z("local tracks prop:"+JSON.stringify({id:a.id,contentHint:a.contentHint,enabled:a.enabled,kind:a.kind,label:a.label,muted:a.muted,readonly:a.readonly,readyState:a.readyState,remote:a.remote})),z("local tracks setting:"+JSON.stringify(a.getSettings()))})):(_.onaddstream=function(b){z("recive stream");var c=b.stream;P.srcObject=c,P.onloadedmetadata=function(b){try{P.play().then(function(){z("remoteVideo play success")}).catch(function(a){console.log(a),z("remoteVideo play error:"+a.message,"error")})}catch(b){console.log(b),z("remoteVideo play error:"+b.message,"error")}a.config.useCanvasRender&&j(P)},a.listenerConfig.onRemoteStreamAdd(c)},z("send localStream"),_.addStream(N)),setTimeout(function(){a.getStatsData(function(a){M&&1==M.readyState&&z("information after 5 seconds:"+JSON.stringify(a))})},5e3),1==K&&(I?_.createOffer().then(function(a){u(a)}).catch(function(a){z("createOffer error:"+a,"error"),alert("Failure callback: "+a)}):_.createOffer(function(a){u(a)},function(a){z("createOffer error:"+a,"error")}))}catch(f){z("error:"+f.message+f.stack,"error")}}function u(b){a.config.isLimitNet&&(b.sdp=s(s(b.sdp,"video",a.config.reviceVideoBitrate,1),"audio",a.config.reviceAudioBitrate,1)),z("set local sdp"),b.sdp=b.sdp.replace("a=extmap-allow-mixed\r\n",""),_.setLocalDescription(new ob(b),function(){z("setLocalDescription success")},function(a){z("setLocalDescription error:"+a,"error")}),z("send sdp"),c(8263,{esreq:{sdpinfo:JSON.stringify(b),ownuserid:L.userid,peeruserid:L.peeruserid,roomid:L.roomid}})}function v(a){ib=new MediaRecorder(a,{bitsPerSecond:24e4,mimeType:"video/webm"}),ib.ondataavailable=function(a){jb.push(a.data)},ib.onerror=function(a){console.log("onerror: ",a)},ib.onstart=function(){z("start media prepare"),jb=[]},ib.onwarning=function(a){console.log("onwarning: "+a)},ib.onstop=function(a){console.log("onstop: "+a)},ib.onresume=function(a){console.log("onstop: "+a)},ib.start(20)}function w(){if(jb&&0!=jb.length&&"open"==ab.readyState){var a=jb.shift(),b=new FileReader;b.onload=function(a){"open"==ab.readyState&&ab.send(a.target.result)},b.readAsArrayBuffer(a)}}function x(a){try{z("start init media channel"),ab=_.createDataChannel("ThinkiveMediaData",{ordered:!0,reliable:!0}),ab.binaryType="arraybuffer",ab.onopen=function(){z("media channel opened")},ab.onerror=function(a){z("SendDatachannel onerror: "+a.error,"warn")},ab.onmessage=function(a){z("media channel onmessage"+a),"i_am_ready"==a.data&&(z("start Send media channel data"),v(N),kb=setInterval(w,20))},ab.onclose=function(a){console.log("SendDatachannel onclose: "+a.toString())}}catch(b){z("init media channel error:"+b.message,"error"),a?a({errcode:"-2001",errmsg:"datachannel create error:"+b.message}):alert("datachannel create error:"+b.message)}}function y(a){if(a){var b=a.srcObject;if(b){var c=b.getTracks();c.forEach(function(a){a.stop()})}a.srcObject=null}}function z(b,c){c=c||"info",console[c](b),b=b+"("+L.roomid+","+L.userid+","+L.peeruserid+","+L.peerusername+")",a.sendMsgByTransBuffer({msg:"h5report@"+c+"@"+b})}function A(){var a=navigator.userAgent.match(/os [\d._]*/gi),b=(a+"").replace(/[^0-9|_.]/gi,"").replace(/_/gi,".");return b}function B(a,b){try{var c=new(window.AudioContext||window.webkitAudioContext),d=c.createMediaStreamSource(a),e=c.createGain();e.gain.value=b,d.connect(e),e.connect(c.destination),e.start(0)}catch(f){z("connectToSpeaker error:"+f.message,"error"),console.log(f)}}var C={ios:/iphone/.test(navigator.userAgent.toLocaleLowerCase()),android:/Android/.test(navigator.userAgent)||/Linux/.test(navigator.userAgent),safari:/^((?!chrome|android).)*safari/i.test(navigator.userAgent)},D=navigator.userAgent.toLowerCase(),E=(/micromessenger/.test(D),C.ios&&A()>="15.0");a.config={isDebug:!0,isFoceSturn:!1,isCallMode:!1,netInfoIsSend:!1,netInfoSendTime:2,netInfoIsShow:!1,isDataChannelSend:!1,isLimitNet:!0,sendVideoBitrate:350,sendAudioBitrate:30,reviceVideoBitrate:160,reviceAudioBitrate:30,isLimitAndroidVideoBitrate:!0,"x-google-start-bitrate":240,"x-google-min-bitrate":200,"x-google-max-bitrate":300,signalServer:"",protobufFileLoaction:"",secretkey:"",localId:"localDiv",remoteId:"remoteDiv",isDeparted:!0,deviceWaitTime:4e3,useCanvasRender:navigator.userAgent.indexOf("HeyTap")>-1};var F=null,G=0,H=C.ios?"3":"2",I=C.ios||/mac\sos/.test(D),J=!1,K=1,L={userid:null,peeruserid:null,peerusername:null,roomid:null};a.user=L;var M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,ab,bb,cb,db,eb,fb,gb,hb,ib,jb,kb,lb=window.RTCPeerConnection||window.mozRTCPeerConnection||window.webkitRTCPeerConnection||"",mb=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia||navigator.mediaDevices||"",nb=window.mozRTCIceCandidate||window.RTCIceCandidate||"",ob=window.mozRTCSessionDescription||window.RTCSessionDescription||"",pb=[],qb=function(b){if(b.data){var d=$.decode(new Uint8Array(b.data)),e={errcode:d.errcode,errmsg:d.errmsg,msgtype:d.msgtype,result:null};switch(1==a.config.isDebug&&console.log("response:"+JSON.stringify(d)),d.msgtype){case Z.MsgType_Client.CLIENT_LOGIN_RESP:1==a.config.isDebug&&console.log("CLIENT_LOGIN_RESP"),d.response&&(e.result=d.response.lresp),0==e.errcode&&(L.userid=e.result.userid,fb=e.result.timeout,gb=new Date,m()),U(e);break;case Z.MsgType_Client.CLIENT_ENTER_ROOM_RESP:if(1==a.config.isDebug&&console.log("CLIENT_ENTER_ROOM_RESP"),d.response&&(e.result=d.response.erresp),z(navigator.userAgent),z("TChatRTC version:"+a.version),0!=e.errcode)return V(e),void 0;a.config.isCallMode?V(e):a.getUserList({roomid:L.roomid},function(a){for(var b=!1,c=0;c<a.result.list.length;c++)a.result.list[c].userid!=L.userid&&(b=!0,L.peeruserid=a.result.list[c].userid,L.peerusername=a.result.list[c].username);z("exist other:"+b),!b&&(e.errcode=-1,e.errmsg="匹配坐席失败，请重新排队"),V(e)});break;case Z.MsgType_Client.CLIENT_GET_USERLIST_RESP:1==a.config.isDebug&&console.log("CLIENT_GET_USERLIST_RESP"),d.response&&(e.result=d.response.guresp),W(e);break;case Z.MsgType_Server.SERVER_INROOM_PUSH_REQ:1==a.config.isDebug&&console.log("SERVER_INROOM_PUSH_REQ"),d.request&&(e.result=d.request.irpreq),0==e.errcode&&8==e.result.roomid?a.listenerConfig.addNewUser(e):1==a.config.isDebug&&console.log(e.errmsg);break;case Z.MsgType_Server.SERVER_VIDEO_CALL_PUSH_REQ:1==a.config.isDebug&&console.log("SERVER_VIDEO_CALL_PUSH_REQ"),d.request&&(e.result=d.request.vcpreq),a.listenerConfig.videoCalled(e);break;case Z.MsgType_Server.SERVER_SEND_ENTERN_ROOM_REQ:if(1==a.config.isDebug&&console.log("SERVER_SEND_ENTERN_ROOM_REQ"),d.request&&(e.result=d.request.serreq),X(e),0==e.errcode){var f={roomid:e.result.roomid,password:e.result.password};L.peeruserid=e.result.userid,L.roomid=e.result.roomid,a.enterRoom(f,function(a){0==a.errcode?t(a.result):T({errcode:-1005,errmsg:a.errmsg})})}else 1==a.config.isDebug&&console.log(e.errmsg);break;case Z.MsgType_Server.SERVER_OUTROOM_PUSH_REQ:1==a.config.isDebug&&console.log("SERVER_OUTROOM_PUSH_REQ"),d.request&&(e.result=d.request.orpreq),a.listenerConfig.exitRoomNotify(e);break;case Z.MsgType_Client.CLIENT_EXCHANGE_SDP_REQ:z("receive sdp"),1==a.config.isDebug&&console.log("CLIENT_EXCHANGE_SDP_REQ"),e.result=d.request.esreq,e.result.sdpinfo=JSON.parse(e.result.sdpinfo),a.config.isLimitNet&&(e.result.sdpinfo.sdp=s(s(e.result.sdpinfo.sdp,"video",a.config.sendVideoBitrate,2),"audio",a.config.sendAudioBitrate,2)),!C.ios&&a.config.isLimitAndroidVideoBitrate&&(e.result.sdpinfo.sdp=r(e.result.sdpinfo.sdp)),_.setRemoteDescription(new ob(e.result.sdpinfo),function(){console.log("setRemoteDescription success")},function(a){console.error("setRemoteDescription error:"+a)}),J=!0,pb.forEach(function(a){o(a)}),1!=K&&_.createAnswer(function(b){a.config.isLimitNet&&(b.sdp=s(s(b.sdp,"video",a.config.reviceVideoBitrate,1),"audio",a.config.reviceAudioBitrate,1)),_.setLocalDescription(new ob(b),function(){console.log("setLocalDescription success")},function(a){console.error("setLocalDescription error:"+a)}),z("send sdp"),c(8263,{esreq:{sdpinfo:JSON.stringify(b),ownuserid:L.userid,peeruserid:L.peeruserid,roomid:L.roomid}})},function(b){1==a.config.isDebug&&console.log("Failure callback: "+b)});break;case Z.MsgType_Client.CLIENT_EXCHANGE_ICE_REQ:z("receive ice"),1==a.config.isDebug&&console.log("CLIENT_EXCHANGE_ICE_REQ"),e.result=d.request.eireq,0==e.errcode?_.addIceCandidate(new nb(JSON.parse(e.result.iceinfo))):1==a.config.isDebug&&console.log(e.errmsg);break;case Z.MsgType_Server.SERVER_MESSAGE_PUSH_REQ:1==a.config.isDebug&&console.log("SERVER_MESSAGE_PUSH_REQ"),d.request&&(e.result=d.request.mpreq),a.listenerConfig.onMsgNotify(e);break;case Z.MsgType_Server.SERVER_TRANS_BUFFER_PUSH_REQ:1==a.config.isDebug&&console.log("SERVER_TRANS_BUFFER_PUSH_REQ"),d.request&&(e.result=d.request.tbpreq),a.listenerConfig.onTransBufferNotify(e);break;case Z.MsgType_Client.CLIENT_USER_CAMERA_REQ:1==a.config.isDebug&&console.log("CLIENT_USER_CAMERA_REQ"),e.result=d.request.ucreq,console.log("对方摄像头是否打开："+e.result.isopen);break;case Z.MsgType_Server.SERVER_PUSH_H5USER_CONNECTION_RESP:1==a.config.isDebug&&console.log("SERVER_PUSH_H5USER_CONNECTION_RESP"),e.result=d.response.hucresp,0==e.errcode&&e.result.success?T({errcode:"0"}):T({errcode:-1003,errmsg:e.errmsg});break;case Z.MsgType_Client.CLIENT_SECRET_KEY_RESP:if(1==a.config.isDebug&&console.log("CLIENT_SECRET_KEY_RESP"),e.result=d.response.skresp,0==e.errcode){var g=e.result.secretkey+a.config.secretkey;g=MD5(g),g=g.substr(-16)+g.substr(0,16),g=MD5(g),g=g.substr(0,8),c(8239,{dvreq:{version:1,secretkey:g,roomid:L.roomid,userid:L.peeruserid}})}else T({errcode:-1002,errmsg:e.errmsg});break;case Z.MsgType_Client.CLIENT_DATA_VERIFIER_RESP:1==a.config.isDebug&&console.log("DATA_VERIFIER_RESP"),0==e.errcode||T({errcode:-1004,errmsg:e.errmsg});break;case Z.MsgType_Client.CLIENT_USER_HEARTBEAT_RESP:1==a.config.isDebug&&console.log("CLIENT_USER_HEARTBEAT_RESP"),gb=new Date;break;case Z.MsgType_Server.CLIENT_LEAVE_ROOM_RESP:Y&&Y(e);break;default:1==a.config.isDebug&&console.log("0x"+d.msgtype.toString(16)+"消息未处理")}}else 1==a.config.isDebug&&console.log("ws message no data"),alert("ws message no data")},rb=function(){db=!1,eb=!1,console.log("web socket init success"),c(8257,{skreq:{}})},sb=function(b){var c=document.querySelector(".tchatNet");c&&c.parentNode.removeChild(c),window.clearInterval(hb),window.clearInterval(cb),window.clearInterval(kb),a.closeAll();var d=b.code?",code:"+d:"";T({errcode:-10001,errmsg:"连接信令服务器异常"+d})},tb=function(b){var c=document.querySelector(".tchatNet");c&&c.parentNode.removeChild(c),window.clearInterval(hb),window.clearInterval(cb),window.clearInterval(kb),eb||(a.closeAll(),db?a.listenerConfig.timeout&&a.listenerConfig.timeout():a.listenerConfig.socketError&&a.listenerConfig.socketError()),console.log("web socket close:"+b.code)};a.reConnect=function(){a.closeAll(),b()},a.init=function(c,d,g){window.clearTimeout(F),G=0,T=g,L.roomid=d.roomid,L.peeruserid=d.peer_userid,c=c||{},d=d||{};for(var h in c)this.listenerConfig[h]=c[h];for(var i in d)this.config[i]=d[i];e(),S=document.querySelector("#"+this.config.remoteId),R&&S||alert("not exist local/remote video parent element"),P=document.createElement("video"),P.setAttribute("playsinline",""),P.removeAttribute("controls"),P.setAttribute("webkit-playsinline",""),P.setAttribute("id","tchatRemoteVideo"),this.config.isDeparted&&!E&&(Q=document.createElement("audio"),Q.setAttribute("playsinline",""),Q.removeAttribute("controls"),Q.setAttribute("webkit-playsinline",""),Q.setAttribute("id","tchatRemoteAudio"),Q.style.display="none"),S.appendChild(P),this.config.isDeparted&&!E&&S.appendChild(Q),f(a.config.protobufFileLoaction,function(c){Z=c,$=Z.lookupType("Message");var d=window.TChatRTCCameraCloseTime||0,e=(new Date).getTime()-d;e>a.config.deviceWaitTime?b():(z("delay wsConnect:"+(a.config.deviceWaitTime-e),"warn"),window.setTimeout(function(){O?b():z("close tchat and connect not complate","warn")},a.config.deviceWaitTime-e))})},a.login=function(a,b){U=b,c(8209,{lreq:{username:a.username,passwd:a.passwd,devicetype:H}})},a.enterRoom=function(a,b){V=b,L.roomid=a.roomid,a.content=navigator.userAgent,c(8215,{erreq:{roomid:a.roomid,password:a.password,roomname:a.roomname,content:a.content}})},a.getUserList=function(a,b){W=b,c(8227,{gureq:{roomid:a.roomid,content:a.content}})},a.videoCall=function(a,b){K=1,X=b,c(8231,{vcreq:{userid:a.userid,calltype:a.calltype||1}})},a.replayVideoCall=function(a,b){K=0,X=b,L.peeruserid=Number(a.userid),d(4146,{vcpresp:{userid:a.userid,isagree:a.isagree,calltype:a.calltype||1}})},a.stopVideo=function(){c(8241,{stvreq:{userid:L.peeruserid}})},a.exitRoom=function(){c(8217,{lrreq:{}})},a.sendMsg=function(a){c(8219,{smreq:{userid:L.peeruserid,msg:a.msg}})},a.sendMsgByTransBuffer=function(a){var b={msgtype:8225,request:{stbreq:{userid:L.peeruserid,cmdmsg:h(a.msg)}}},c=$&&$.encode($.fromObject(b)).finish();M&&1==M.readyState&&M.send(c)},a.heartbeat=function(){c(8213,{uhreq:{userid:L.userid}})},a.sendStats=function(a){a.devicetype=H,a.username=L.userid,c(8271,{buireq:a})},a.wsClose=function(){eb=!0,window.clearInterval(hb),window.clearInterval(cb),window.clearInterval(kb),a.hangup(),M&&M.close(),a.closeMedia()},a.closeMedia=function(){if(!N)return console.log("local media is null"),!1;for(var a=N.getTracks(),b=0;b<a.length;b++)a[b].stop();N=null},a.hangup=function(){try{ab&&ab.close()&&(ab=null),_&&_.close()&&(_=null)}catch(a){console.error(a)}},a.closeAll=function(){if(a.stopVideo(),a.exitRoom({},function(){}),a.wsClose(),O){var b=document.querySelector("#"+O.id+"Canvas");b&&b.remove(),O=document.getElementById("tchatLocalVideo"),y(O),O&&O.remove(),O=null,window.TChatRTCCameraCloseTime=(new Date).getTime()}if(P){var c=document.querySelector("#"+P.id+"Canvas");c&&c.remove(),P=document.getElementById("tchatRemoteVideo"),y(P),P&&P.remove(),P=null}Q&&(Q=document.getElementById("tchatRemoteAudio"),y(Q),Q&&Q.remove(),Q=null),a.listenerConfig.destory&&a.listenerConfig.destory()},a.startWebRtc=function(a,b){t(a,b)},a.openLocalCamera=function(b,c,d){d=d||{},e();var f=320,g=240,h={audio:{echoCancellation:!0},video:!0},j={audio:{echoCancellation:!0},video:{width:{exact:f},height:{exact:g}}};navigator.mediaDevices.getSupportedConstraints().facingMode&&(h.video={facingMode:"user"},j.video.facingMode="user"),window.clearTimeout(F),G=0,F=window.setTimeout(function(){z("openCameraTimeout","error"),c({name:"打开摄像头超时",code:1e3}),G=1},1e3*(d.openCameraTime||10)),l(j,function(d){return 1==G?(a.closeMedia(),void 0):(window.clearTimeout(F),i(d,b,function(){c({name:"获取摄像头失败"})}),void 0)},function(){l(h,function(d){return 1==G?(a.closeMedia(),void 0):(window.clearTimeout(F),i(d,b,function(){c({name:"获取摄像头失败"})}),void 0)},function(a){window.clearTimeout(F),c(a)})},!0)},a.getStatsData=function(a){_.getStats().then(function(b){var c=[];b.result?b.result().forEach(function(a){var b={};a.names().forEach(function(c){b[c]=a.stat(c)}),b.id=a.id,b.type=a.type,b.timestamp=a.timestamp,c.push(b)}):b.forEach(function(a){c.push(a)});var d,e,f,g,h,i;c.forEach(function(a){"outbound-rtp"==a.type?a.id.toLowerCase().indexOf("video")>-1&&a.bytesSent?e=parseInt(a.bytesSent):a.id.toLowerCase().indexOf("audio")>-1&&a.bytesSent&&(d=parseInt(a.bytesSent)):"inbound-rtp"==a.type?a.id.toLowerCase().indexOf("video")>-1&&a.bytesReceived?g=parseInt(a.bytesReceived):a.id.toLowerCase().indexOf("audio")>-1&&a.bytesReceived&&(f=parseInt(a.bytesReceived)):"ssrc"==a.type?"video"==a.mediaType?(a.bytesSent&&(e=parseInt(a.bytesSent)),a.bytesReceived&&(g=parseInt(a.bytesReceived))):"audio"==a.mediaType&&(a.bytesSent&&(d=parseInt(a.bytesSent)),a.bytesReceived&&(f=parseInt(a.bytesReceived))):"transport"==a.type&&(a.bytesSent&&(h=parseInt(a.bytesSent)),a.bytesReceived&&(i=parseInt(a.bytesReceived)))}),a({audioBytesSent:d,videoBytesSent:e,audioBytesReceived:f,videoBytesReceived:g,curTotalSend:h,curTotalReceived:i})}).catch(function(a){z("getStats error:"+JSON.stringify(a),"error")})},a.listMedia=function(a,b){return bb?(a?b(bb[a]):b(bb.videoinput.concat(bb.audioinput)),void 0):navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices?(navigator.mediaDevices.enumerateDevices().then(function(c){var d=[],e=[];c.forEach(function(a){"videoinput"==a.kind?d.push(a):"audioinput"==a.kind&&e.push(a)}),bb={videoinput:d,audioinput:e},a?b(bb[a]):b(bb.videoinput.concat(bb.audioinput))}).catch(function(a){alert(a.name+": "+a.message)}),void 0):(z("not support enumerateDevices"),[])},a.changeVideo=function(b){var c={audio:!0,video:{deviceId:{exact:b}}};l(c,function(b){z("change video get Stream success:"+JSON.stringify(c)),a.config.isDataChannelSend?z("DataChannelSend not support changeVideo"):(O.srcObject=b,b.getTracks().forEach(function(a){var b=_.getSenders().find(function(b){return b.track.kind==a.kind});b.replaceTrack(a)}),a.closeMedia(),N=b,O.onloadedmetadata=function(){O.play()})},function(a){z("change video fail,constraints:"+JSON.stringify(c)),z(JSON.stringify(a))})},a.reGetMedia=function(b,c){a.closeMedia(),a.openLocalCamera(function(){N.getTracks().forEach(function(a){var c=_.getSenders().find(function(b){return b.track.kind==a.kind});c.replaceTrack(a),b&&b()})},function(a){c&&c(a)})}}(TChatRTC),window.TChatRTC=TChatRTC;
//# sourceMappingURL=./dest/js.map