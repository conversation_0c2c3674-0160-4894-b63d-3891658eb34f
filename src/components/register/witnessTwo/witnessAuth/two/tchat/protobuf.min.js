/*!
 * protobuf.js v6.8.1 (c) 2016, daniel wirtz
 * compiled tue, 11 jul 2017 15:52:12 utc
 * licensed under the bsd-3-clause license
 * see: https://github.com/dcodeio/protobuf.js for details
 */
!function(t,e){"use strict";!function(e,r,n){function i(t){var n=r[t];return n||e[t][0].call(n=r[t]={exports:{}},i,n,n.exports),n.exports}var o=t.protobuf=i(n[0]);"function"==typeof define&&define.amd&&define(["long"],function(t){return t&&t.isLong&&(o.util.Long=t,o.configure()),o}),"object"==typeof module&&module&&module.exports&&(module.exports=o)}({1:[function(t,e){function r(t,e){for(var r=Array(arguments.length-1),n=0,i=2,o=!0;i<arguments.length;)r[n++]=arguments[i++];return new Promise(function(i,s){r[n]=function(t){if(o)if(o=!1,t)s(t);else{for(var e=Array(arguments.length-1),r=0;r<e.length;)e[r++]=arguments[r];i.apply(null,e)}};try{t.apply(e||null,r)}catch(t){o&&(o=!1,s(t))}})}e.exports=r},{}],2:[function(t,r,n){var i=n;i.length=function(t){var e=t.length;if(!e)return 0;for(var r=0;--e%4>1&&"="===t.charAt(e);)++r;return Math.ceil(3*t.length)/4-r};for(var o=Array(64),s=Array(123),a=0;a<64;)s[o[a]=a<26?a+65:a<52?a+71:a<62?a-4:a-59|43]=a++;i.encode=function(t,e,r){for(var n,i=null,s=[],a=0,u=0;e<r;){var f=t[e++];switch(u){case 0:s[a++]=o[f>>2],n=(3&f)<<4,u=1;break;case 1:s[a++]=o[n|f>>4],n=(15&f)<<2,u=2;break;case 2:s[a++]=o[n|f>>6],s[a++]=o[63&f],u=0}a>8191&&((i||(i=[])).push(String.fromCharCode.apply(String,s)),a=0)}return u&&(s[a++]=o[n],s[a++]=61,1===u&&(s[a++]=61)),i?(a&&i.push(String.fromCharCode.apply(String,s.slice(0,a))),i.join("")):String.fromCharCode.apply(String,s.slice(0,a))};i.decode=function(t,r,n){for(var i,o=n,a=0,u=0;u<t.length;){var f=t.charCodeAt(u++);if(61===f&&a>1)break;if((f=s[f])===e)throw Error("invalid encoding");switch(a){case 0:i=f,a=1;break;case 1:r[n++]=i<<2|(48&f)>>4,i=f,a=2;break;case 2:r[n++]=(15&i)<<4|(60&f)>>2,i=f,a=3;break;case 3:r[n++]=(3&i)<<6|f,a=0}}if(1===a)throw Error("invalid encoding");return n-o},i.test=function(t){return/^(?:[A-Za-z0-9+\/]{4})*(?:[A-Za-z0-9+\/]{2}==|[A-Za-z0-9+\/]{3}=)?$/.test(t)}},{}],3:[function(t,r){function n(t,r){function i(t){if("string"!=typeof t){var e=o();if(n.verbose&&console.log("codegen: "+e),e="return "+e,t){for(var r=Object.keys(t),a=Array(r.length+1),u=Array(r.length),f=0;f<r.length;)a[f]=r[f],u[f]=t[r[f++]];return a[f]=e,Function.apply(null,a).apply(null,u)}return Function(e)()}for(var l=Array(arguments.length-1),p=0;p<l.length;)l[p]=arguments[++p];if(p=0,t=t.replace(/%([%dfijs])/g,function(t,e){var r=l[p++];switch(e){case"d":case"f":return+r+"";case"i":return Math.floor(r)+"";case"j":return JSON.stringify(r);case"s":return r+""}return"%"}),p!==l.length)throw Error("parameter count mismatch");return s.push(t),i}function o(e){return"function "+(e||r||"")+"("+(t&&t.join(",")||"")+"){\n  "+s.join("\n  ")+"\n}"}"string"==typeof t&&(r=t,t=e);var s=[];return i.toString=o,i}r.exports=n,n.verbose=!1},{}],4:[function(t,r){function n(){this.a={}}r.exports=n,n.prototype.on=function(t,e,r){return(this.a[t]||(this.a[t]=[])).push({fn:e,ctx:r||this}),this},n.prototype.off=function(t,r){if(t===e)this.a={};else if(r===e)this.a[t]=[];else for(var n=this.a[t],i=0;i<n.length;)n[i].fn===r?n.splice(i,1):++i;return this},n.prototype.emit=function(t){var e=this.a[t];if(e){for(var r=[],n=1;n<arguments.length;)r.push(arguments[n++]);for(n=0;n<e.length;)e[n].fn.apply(e[n++].ctx,r)}return this}},{}],5:[function(t,r){function n(t,e,r){return"function"==typeof e?(r=e,e={}):e||(e={}),r?!e.xhr&&s&&s.readFile?s.readFile(t,function(i,o){return i&&"undefined"!=typeof XMLHttpRequest?n.xhr(t,e,r):i?r(i):r(null,e.binary?o:o.toString("utf8"))}):n.xhr(t,e,r):i(n,this,t,e)}r.exports=n;var i=t(1),o=t(7),s=o("fs");n.xhr=function(t,r,n){var i=new XMLHttpRequest;i.onreadystatechange=function(){if(4!==i.readyState)return e;if(0!==i.status&&200!==i.status)return n(Error("status "+i.status));if(r.binary){var t=i.response;if(!t){t=[];for(var o=0;o<i.responseText.length;++o)t.push(255&i.responseText.charCodeAt(o))}return n(null,"undefined"!=typeof Uint8Array?new Uint8Array(t):t)}return n(null,i.responseText)},r.binary&&("overrideMimeType"in i&&i.overrideMimeType("text/plain; charset=x-user-defined"),i.responseType="arraybuffer"),i.open("GET",t),i.send()}},{1:1,7:7}],6:[function(t,e){function r(t){return"undefined"!=typeof Float32Array?function(){function e(t,e,r){o[0]=t,e[r]=s[0],e[r+1]=s[1],e[r+2]=s[2],e[r+3]=s[3]}function r(t,e,r){o[0]=t,e[r]=s[3],e[r+1]=s[2],e[r+2]=s[1],e[r+3]=s[0]}function n(t,e){return s[0]=t[e],s[1]=t[e+1],s[2]=t[e+2],s[3]=t[e+3],o[0]}function i(t,e){return s[3]=t[e],s[2]=t[e+1],s[1]=t[e+2],s[0]=t[e+3],o[0]}var o=new Float32Array([-0]),s=new Uint8Array(o.buffer),a=128===s[3];t.writeFloatLE=a?e:r,t.writeFloatBE=a?r:e,t.readFloatLE=a?n:i,t.readFloatBE=a?i:n}():function(){function e(t,e,r,n){var i=e<0?1:0;if(i&&(e=-e),0===e)t(1/e>0?0:2147483648,r,n);else if(isNaN(e))t(2143289344,r,n);else if(e>3.4028234663852886e38)t((i<<31|2139095040)>>>0,r,n);else if(e<1.1754943508222875e-38)t((i<<31|Math.round(e/1.401298464324817e-45))>>>0,r,n);else{var o=Math.floor(Math.log(e)/Math.LN2),s=8388607&Math.round(e*Math.pow(2,-o)*8388608);t((i<<31|o+127<<23|s)>>>0,r,n)}}function r(t,e,r){var n=t(e,r),i=2*(n>>31)+1,o=n>>>23&255,s=8388607&n;return 255===o?s?NaN:i*(1/0):0===o?1.401298464324817e-45*i*s:i*Math.pow(2,o-150)*(s+8388608)}t.writeFloatLE=e.bind(null,n),t.writeFloatBE=e.bind(null,i),t.readFloatLE=r.bind(null,o),t.readFloatBE=r.bind(null,s)}(),"undefined"!=typeof Float64Array?function(){function e(t,e,r){o[0]=t,e[r]=s[0],e[r+1]=s[1],e[r+2]=s[2],e[r+3]=s[3],e[r+4]=s[4],e[r+5]=s[5],e[r+6]=s[6],e[r+7]=s[7]}function r(t,e,r){o[0]=t,e[r]=s[7],e[r+1]=s[6],e[r+2]=s[5],e[r+3]=s[4],e[r+4]=s[3],e[r+5]=s[2],e[r+6]=s[1],e[r+7]=s[0]}function n(t,e){return s[0]=t[e],s[1]=t[e+1],s[2]=t[e+2],s[3]=t[e+3],s[4]=t[e+4],s[5]=t[e+5],s[6]=t[e+6],s[7]=t[e+7],o[0]}function i(t,e){return s[7]=t[e],s[6]=t[e+1],s[5]=t[e+2],s[4]=t[e+3],s[3]=t[e+4],s[2]=t[e+5],s[1]=t[e+6],s[0]=t[e+7],o[0]}var o=new Float64Array([-0]),s=new Uint8Array(o.buffer),a=128===s[7];t.writeDoubleLE=a?e:r,t.writeDoubleBE=a?r:e,t.readDoubleLE=a?n:i,t.readDoubleBE=a?i:n}():function(){function e(t,e,r,n,i,o){var s=n<0?1:0;if(s&&(n=-n),0===n)t(0,i,o+e),t(1/n>0?0:2147483648,i,o+r);else if(isNaN(n))t(0,i,o+e),t(2146959360,i,o+r);else if(n>1.7976931348623157e308)t(0,i,o+e),t((s<<31|2146435072)>>>0,i,o+r);else{var a;if(n<2.2250738585072014e-308)a=n/5e-324,t(a>>>0,i,o+e),t((s<<31|a/4294967296)>>>0,i,o+r);else{var u=Math.floor(Math.log(n)/Math.LN2);1024===u&&(u=1023),a=n*Math.pow(2,-u),t(4503599627370496*a>>>0,i,o+e),t((s<<31|u+1023<<20|1048576*a&1048575)>>>0,i,o+r)}}}function r(t,e,r,n,i){var o=t(n,i+e),s=t(n,i+r),a=2*(s>>31)+1,u=s>>>20&2047,f=4294967296*(1048575&s)+o;return 2047===u?f?NaN:a*(1/0):0===u?5e-324*a*f:a*Math.pow(2,u-1075)*(f+4503599627370496)}t.writeDoubleLE=e.bind(null,n,0,4),t.writeDoubleBE=e.bind(null,i,4,0),t.readDoubleLE=r.bind(null,o,0,4),t.readDoubleBE=r.bind(null,s,4,0)}(),t}function n(t,e,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24}function i(t,e,r){e[r]=t>>>24,e[r+1]=t>>>16&255,e[r+2]=t>>>8&255,e[r+3]=255&t}function o(t,e){return(t[e]|t[e+1]<<8|t[e+2]<<16|t[e+3]<<24)>>>0}function s(t,e){return(t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3])>>>0}e.exports=r(r)},{}],7:[function(t,e,r){function n(t){try{var e=eval("quire".replace(/^/,"re"))(t);if(e&&(e.length||Object.keys(e).length))return e}catch(t){}return null}e.exports=n},{}],8:[function(t,e,r){var n=r,i=n.isAbsolute=function(t){return/^(?:\/|\w+:)/.test(t)},o=n.normalize=function(t){t=t.replace(/\\/g,"/").replace(/\/{2,}/g,"/");var e=t.split("/"),r=i(t),n="";r&&(n=e.shift()+"/");for(var o=0;o<e.length;)".."===e[o]?o>0&&".."!==e[o-1]?e.splice(--o,2):r?e.splice(o,1):++o:"."===e[o]?e.splice(o,1):++o;return n+e.join("/")};n.resolve=function(t,e,r){return r||(e=o(e)),i(e)?e:(r||(t=o(t)),(t=t.replace(/(?:\/|^)[^\/]+$/,"")).length?o(t+"/"+e):e)}},{}],9:[function(t,e){function r(t,e,r){var n=r||8192,i=n>>>1,o=null,s=n;return function(r){if(r<1||r>i)return t(r);s+r>n&&(o=t(n),s=0);var a=e.call(o,s,s+=r);return 7&s&&(s=1+(7|s)),a}}e.exports=r},{}],10:[function(t,e,r){var n=r;n.length=function(t){for(var e=0,r=0,n=0;n<t.length;++n)r=t.charCodeAt(n),r<128?e+=1:r<2048?e+=2:55296==(64512&r)&&56320==(64512&t.charCodeAt(n+1))?(++n,e+=4):e+=3;return e},n.read=function(t,e,r){if(r-e<1)return"";for(var n,i=null,o=[],s=0;e<r;)n=t[e++],n<128?o[s++]=n:n>191&&n<224?o[s++]=(31&n)<<6|63&t[e++]:n>239&&n<365?(n=((7&n)<<18|(63&t[e++])<<12|(63&t[e++])<<6|63&t[e++])-65536,o[s++]=55296+(n>>10),o[s++]=56320+(1023&n)):o[s++]=(15&n)<<12|(63&t[e++])<<6|63&t[e++],s>8191&&((i||(i=[])).push(String.fromCharCode.apply(String,o)),s=0);return i?(s&&i.push(String.fromCharCode.apply(String,o.slice(0,s))),i.join("")):String.fromCharCode.apply(String,o.slice(0,s))},n.write=function(t,e,r){for(var n,i,o=r,s=0;s<t.length;++s)n=t.charCodeAt(s),n<128?e[r++]=n:n<2048?(e[r++]=n>>6|192,e[r++]=63&n|128):55296==(64512&n)&&56320==(64512&(i=t.charCodeAt(s+1)))?(n=65536+((1023&n)<<10)+(1023&i),++s,e[r++]=n>>18|240,e[r++]=n>>12&63|128,e[r++]=n>>6&63|128,e[r++]=63&n|128):(e[r++]=n>>12|224,e[r++]=n>>6&63|128,e[r++]=63&n|128);return r-o}},{}],11:[function(t,e){function r(t,e){n.test(t)||(t="google/protobuf/"+t+".proto",e={nested:{google:{nested:{protobuf:{nested:e}}}}}),r[t]=e}e.exports=r;var n=/\/|\./;r("any",{Any:{fields:{type_url:{type:"string",id:1},value:{type:"bytes",id:2}}}});var i;r("duration",{Duration:i={fields:{seconds:{type:"int64",id:1},nanos:{type:"int32",id:2}}}}),r("timestamp",{Timestamp:i}),r("empty",{Empty:{fields:{}}}),r("struct",{Struct:{fields:{fields:{keyType:"string",type:"Value",id:1}}},Value:{oneofs:{kind:{oneof:["nullValue","numberValue","stringValue","boolValue","structValue","listValue"]}},fields:{nullValue:{type:"NullValue",id:1},numberValue:{type:"double",id:2},stringValue:{type:"string",id:3},boolValue:{type:"bool",id:4},structValue:{type:"Struct",id:5},listValue:{type:"ListValue",id:6}}},NullValue:{values:{NULL_VALUE:0}},ListValue:{fields:{values:{rule:"repeated",type:"Value",id:1}}}}),r("wrappers",{DoubleValue:{fields:{value:{type:"double",id:1}}},FloatValue:{fields:{value:{type:"float",id:1}}},Int64Value:{fields:{value:{type:"int64",id:1}}},UInt64Value:{fields:{value:{type:"uint64",id:1}}},Int32Value:{fields:{value:{type:"int32",id:1}}},UInt32Value:{fields:{value:{type:"uint32",id:1}}},BoolValue:{fields:{value:{type:"bool",id:1}}},StringValue:{fields:{value:{type:"string",id:1}}},BytesValue:{fields:{value:{type:"bytes",id:1}}}}),r.get=function(t){return r[t]||null}},{}],12:[function(t,e,r){function n(t,e,r,n){if(e.resolvedType)if(e.resolvedType instanceof s){t("switch(d%s){",n);for(var i=e.resolvedType.values,o=Object.keys(i),a=0;a<o.length;++a)e.repeated&&i[o[a]]===e.typeDefault&&t("default:"),t("case%j:",o[a])("case %i:",i[o[a]])("m%s=%j",n,i[o[a]])("break");t("}")}else t('if(typeof d%s!=="object")',n)("throw TypeError(%j)",e.fullName+": object expected")("m%s=types[%i].fromObject(d%s)",n,r,n);else{var u=!1;switch(e.type){case"double":case"float":t("m%s=Number(d%s)",n,n);break;case"uint32":case"fixed32":t("m%s=d%s>>>0",n,n);break;case"int32":case"sint32":case"sfixed32":t("m%s=d%s|0",n,n);break;case"uint64":u=!0;case"int64":case"sint64":case"fixed64":case"sfixed64":t("if(util.Long)")("(m%s=util.Long.fromValue(d%s)).unsigned=%j",n,n,u)('else if(typeof d%s==="string")',n)("m%s=parseInt(d%s,10)",n,n)('else if(typeof d%s==="number")',n)("m%s=d%s",n,n)('else if(typeof d%s==="object")',n)("m%s=new util.LongBits(d%s.low>>>0,d%s.high>>>0).toNumber(%s)",n,n,n,u?"true":"");break;case"bytes":t('if(typeof d%s==="string")',n)("util.base64.decode(d%s,m%s=util.newBuffer(util.base64.length(d%s)),0)",n,n,n)("else if(d%s.length)",n)("m%s=d%s",n,n);break;case"string":t("m%s=String(d%s)",n,n);break;case"bool":t("m%s=Boolean(d%s)",n,n)}}return t}function i(t,e,r,n){if(e.resolvedType)e.resolvedType instanceof s?t("d%s=o.enums===String?types[%i].values[m%s]:m%s",n,r,n,n):t("d%s=types[%i].toObject(m%s,o)",n,r,n);else{var i=!1;switch(e.type){case"double":case"float":t("d%s=o.json&&!isFinite(m%s)?String(m%s):m%s",n,n,n,n);break;case"uint64":i=!0;case"int64":case"sint64":case"fixed64":case"sfixed64":t('if(typeof m%s==="number")',n)("d%s=o.longs===String?String(m%s):m%s",n,n,n)("else")("d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s",n,n,n,n,i?"true":"",n);break;case"bytes":t("d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s",n,n,n,n,n);break;default:t("d%s=m%s",n,n)}}return t}var o=r,s=t(15),a=t(37);o.fromObject=function(t){var e=t.fieldsArray,r=a.codegen(["d"],t.name+"$fromObject")("if(d instanceof this.ctor)")("return d");if(!e.length)return r("return new this.ctor");r("var m=new this.ctor");for(var i=0;i<e.length;++i){var o=e[i].resolve(),u=a.safeProp(o.name);o.map?(r("if(d%s){",u)('if(typeof d%s!=="object")',u)("throw TypeError(%j)",o.fullName+": object expected")("m%s={}",u)("for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){",u),n(r,o,i,u+"[ks[i]]")("}")("}")):o.repeated?(r("if(d%s){",u)("if(!Array.isArray(d%s))",u)("throw TypeError(%j)",o.fullName+": array expected")("m%s=[]",u)("for(var i=0;i<d%s.length;++i){",u),n(r,o,i,u+"[i]")("}")("}")):(o.resolvedType instanceof s||r("if(d%s!=null){",u),n(r,o,i,u),o.resolvedType instanceof s||r("}"))}return r("return m")},o.toObject=function(t){var e=t.fieldsArray.slice().sort(a.compareFieldsById);if(!e.length)return a.codegen()("return {}");for(var r=a.codegen(["m","o"],t.name+"$toObject")("if(!o)")("o={}")("var d={}"),n=[],o=[],s=[],u=0;u<e.length;++u)e[u].partOf||(e[u].resolve().repeated?n:e[u].map?o:s).push(e[u]);var f,l,p=!1;for(u=0;u<e.length;++u){var f=e[u],c=t.b.indexOf(f),l=a.safeProp(f.name);f.map?(p||(p=!0,r("var ks2")),r("if(m%s&&(ks2=Object.keys(m%s)).length){",l,l)("d%s={}",l)("for(var j=0;j<ks2.length;++j){"),i(r,f,c,l+"[ks2[j]]")("}")):f.repeated?(r("if(m%s&&m%s.length){",l,l)("d%s=[]",l)("for(var j=0;j<m%s.length;++j){",l),i(r,f,c,l+"[j]")("}")):(r("if(m%s!=null&&m.hasOwnProperty(%j)){",l,f.name),i(r,f,c,l),f.partOf&&r("if(o.oneofs)")("d%s=%j",a.safeProp(f.partOf.name),f.name)),r("}")}return r("return d")}},{15:15,37:37}],13:[function(t,r){function n(t){return"missing required '"+t.name+"'"}function i(t){var r=a.codegen(["r","l"],t.name+"$decode")("if(!(r instanceof Reader))")("r=Reader.create(r)")("var c=l===undefined?r.len:r.pos+l,m=new this.ctor"+(t.fieldsArray.filter(function(t){return t.map}).length?",k":""))("while(r.pos<c){")("var t=r.uint32()");t.group&&r("if((t&7)===4)")("break"),r("switch(t>>>3){");for(var i=0;i<t.fieldsArray.length;++i){var u=t.b[i].resolve(),f=u.resolvedType instanceof o?"int32":u.type,l="m"+a.safeProp(u.name);r("case %i:",u.id),u.map?(r("r.skip().pos++")("if(%s===util.emptyObject)",l)("%s={}",l)("k=r.%s()",u.keyType)("r.pos++"),s.long[u.keyType]!==e?s.basic[f]===e?r('%s[typeof k==="object"?util.longToHash(k):k]=types[%i].decode(r,r.uint32())',l,i):r('%s[typeof k==="object"?util.longToHash(k):k]=r.%s()',l,f):s.basic[f]===e?r("%s[k]=types[%i].decode(r,r.uint32())",l,i):r("%s[k]=r.%s()",l,f)):u.repeated?(r("if(!(%s&&%s.length))",l,l)("%s=[]",l),s.packed[f]!==e&&r("if((t&7)===2){")("var c2=r.uint32()+r.pos")("while(r.pos<c2)")("%s.push(r.%s())",l,f)("}else"),s.basic[f]===e?r(u.resolvedType.group?"%s.push(types[%i].decode(r))":"%s.push(types[%i].decode(r,r.uint32()))",l,i):r("%s.push(r.%s())",l,f)):s.basic[f]===e?r(u.resolvedType.group?"%s=types[%i].decode(r)":"%s=types[%i].decode(r,r.uint32())",l,i):r("%s=r.%s()",l,f),r("break")}for(r("default:")("r.skipType(t&7)")("break")("}")("}"),i=0;i<t.b.length;++i){var p=t.b[i];p.required&&r("if(!m.hasOwnProperty(%j))",p.name)("throw util.ProtocolError(%j,{instance:m})",n(p))}return r("return m")}r.exports=i;var o=t(15),s=t(36),a=t(37)},{15:15,36:36,37:37}],14:[function(t,r){function n(t,e,r,n){return e.resolvedType.group?t("types[%i].encode(%s,w.uint32(%i)).uint32(%i)",r,n,(e.id<<3|3)>>>0,(e.id<<3|4)>>>0):t("types[%i].encode(%s,w.uint32(%i).fork()).ldelim()",r,n,(e.id<<3|2)>>>0)}function i(t){for(var r,i,u=a.codegen(["m","w"],t.name+"$encode")("if(!w)")("w=Writer.create()"),f=t.fieldsArray.slice().sort(a.compareFieldsById),r=0;r<f.length;++r){var l=f[r].resolve(),p=t.b.indexOf(l),c=l.resolvedType instanceof o?"int32":l.type,h=s.basic[c];i="m"+a.safeProp(l.name),l.map?(u("if(%s!=null&&m.hasOwnProperty(%j)){",i,l.name)("for(var ks=Object.keys(%s),i=0;i<ks.length;++i){",i)("w.uint32(%i).fork().uint32(%i).%s(ks[i])",(l.id<<3|2)>>>0,8|s.mapKey[l.keyType],l.keyType),h===e?u("types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()",p,i):u(".uint32(%i).%s(%s[ks[i]]).ldelim()",16|h,c,i),u("}")("}")):l.repeated?(u("if(%s!=null&&%s.length){",i,i),l.packed&&s.packed[c]!==e?u("w.uint32(%i).fork()",(l.id<<3|2)>>>0)("for(var i=0;i<%s.length;++i)",i)("w.%s(%s[i])",c,i)("w.ldelim()"):(u("for(var i=0;i<%s.length;++i)",i),h===e?n(u,l,p,i+"[i]"):u("w.uint32(%i).%s(%s[i])",(l.id<<3|h)>>>0,c,i)),u("}")):(l.optional&&u("if(%s!=null&&m.hasOwnProperty(%j))",i,l.name),h===e?n(u,l,p,i):u("w.uint32(%i).%s(%s)",(l.id<<3|h)>>>0,c,i))}return u("return w")}r.exports=i;var o=t(15),s=t(36),a=t(37)},{15:15,36:36,37:37}],15:[function(t,r){function n(t,e,r){if(i.call(this,t,r),e&&"object"!=typeof e)throw TypeError("values must be an object");if(this.valuesById={},this.values=Object.create(this.valuesById),this.comments={},e)for(var n=Object.keys(e),o=0;o<n.length;++o)"number"==typeof e[n[o]]&&(this.valuesById[this.values[n[o]]=e[n[o]]]=n[o])}r.exports=n;var i=t(24);((n.prototype=Object.create(i.prototype)).constructor=n).className="Enum";var o=t(37);n.fromJSON=function(t,e){return new n(t,e.values,e.options)},n.prototype.toJSON=function(){return o.toObject(["options",this.options,"values",this.values])},n.prototype.add=function(t,r,n){if(!o.isString(t))throw TypeError("name must be a string");if(!o.isInteger(r))throw TypeError("id must be an integer");if(this.values[t]!==e)throw Error("duplicate name");if(this.valuesById[r]!==e){if(!this.options||!this.options.allow_alias)throw Error("duplicate id");this.values[t]=r}else this.valuesById[this.values[t]=r]=t;return this.comments[t]=n||null,this},n.prototype.remove=function(t){if(!o.isString(t))throw TypeError("name must be a string");var r=this.values[t];if(r===e)throw Error("name does not exist");return delete this.valuesById[r],delete this.values[t],delete this.comments[t],this}},{24:24,37:37}],16:[function(t,r){function n(t,r,n,o,s,l){if(u.isObject(o)?(l=o,o=s=e):u.isObject(s)&&(l=s,s=e),i.call(this,t,l),!u.isInteger(r)||r<0)throw TypeError("id must be a non-negative integer");if(!u.isString(n))throw TypeError("type must be a string");if(o!==e&&!f.test(o=(""+o).toLowerCase()))throw TypeError("rule must be a string rule");if(s!==e&&!u.isString(s))throw TypeError("extend must be a string");this.rule=o&&"optional"!==o?o:e,this.type=n,this.id=r,this.extend=s||e,this.required="required"===o,this.optional=!this.required,this.repeated="repeated"===o,this.map=!1,this.message=null,this.partOf=null,this.typeDefault=null,this.defaultValue=null,this.long=!!u.Long&&a.long[n]!==e,this.bytes="bytes"===n,this.resolvedType=null,this.extensionField=null,this.declaringField=null,this.c=null}r.exports=n;var i=t(24);((n.prototype=Object.create(i.prototype)).constructor=n).className="Field";var o,s=t(15),a=t(36),u=t(37),f=/^required|optional|repeated$/;n.fromJSON=function(t,e){return new n(t,e.id,e.type,e.rule,e.extend,e.options)},Object.defineProperty(n.prototype,"packed",{get:function(){return null===this.c&&(this.c=!1!==this.getOption("packed")),this.c}}),n.prototype.setOption=function(t,e,r){return"packed"===t&&(this.c=null),i.prototype.setOption.call(this,t,e,r)},n.prototype.toJSON=function(){return u.toObject(["rule","optional"!==this.rule&&this.rule||e,"type",this.type,"id",this.id,"extend",this.extend,"options",this.options])},n.prototype.resolve=function(){if(this.resolved)return this;if((this.typeDefault=a.defaults[this.type])===e&&(this.resolvedType=(this.declaringField?this.declaringField.parent:this.parent).lookupTypeOrEnum(this.type),this.resolvedType instanceof o?this.typeDefault=null:this.typeDefault=this.resolvedType.values[Object.keys(this.resolvedType.values)[0]]),this.options&&null!=this.options.default&&(this.typeDefault=this.options.default,this.resolvedType instanceof s&&"string"==typeof this.typeDefault&&(this.typeDefault=this.resolvedType.values[this.typeDefault])),this.options&&(!0!==this.options.packed&&(this.options.packed===e||!this.resolvedType||this.resolvedType instanceof s)||delete this.options.packed,Object.keys(this.options).length||(this.options=e)),this.long)this.typeDefault=u.Long.fromNumber(this.typeDefault,"u"===this.type.charAt(0)),Object.freeze&&Object.freeze(this.typeDefault);else if(this.bytes&&"string"==typeof this.typeDefault){var t;u.base64.test(this.typeDefault)?u.base64.decode(this.typeDefault,t=u.newBuffer(u.base64.length(this.typeDefault)),0):u.utf8.write(this.typeDefault,t=u.newBuffer(u.utf8.length(this.typeDefault)),0),this.typeDefault=t}return this.map?this.defaultValue=u.emptyObject:this.repeated?this.defaultValue=u.emptyArray:this.defaultValue=this.typeDefault,this.parent instanceof o&&(this.parent.ctor.prototype[this.name]=this.defaultValue),i.prototype.resolve.call(this)},n.d=function(t,e,r,i){return"function"==typeof e?e=u.decorateType(e).name:e&&"object"==typeof e&&(e=u.decorateEnum(e).name),function(o,s){u.decorateType(o.constructor).add(new n(s,t,e,r,{default:i}))}},n.e=function(t){o=t}},{15:15,24:24,36:36,37:37}],17:[function(t,e){function r(t,e,r){return"function"==typeof e?(r=e,e=new i.Root):e||(e=new i.Root),e.load(t,r)}function n(t,e){return e||(e=new i.Root),e.loadSync(t)}var i=e.exports=t(18);i.build="light",i.load=r,i.loadSync=n,i.encoder=t(14),i.decoder=t(13),i.verifier=t(40),i.converter=t(12),i.ReflectionObject=t(24),i.Namespace=t(23),i.Root=t(29),i.Enum=t(15),i.Type=t(35),i.Field=t(16),i.OneOf=t(25),i.MapField=t(20),i.Service=t(33),i.Method=t(22),i.Message=t(21),i.wrappers=t(41),i.types=t(36),i.util=t(37),i.ReflectionObject.e(i.Root),i.Namespace.e(i.Type,i.Service),i.Root.e(i.Type),i.Field.e(i.Type)},{12:12,13:13,14:14,15:15,16:16,18:18,20:20,21:21,22:22,23:23,24:24,25:25,29:29,33:33,35:35,36:36,37:37,40:40,41:41}],18:[function(t,e,r){function n(){i.Reader.e(i.BufferReader),i.util.e()}var i=r;i.build="minimal",i.Writer=t(42),i.BufferWriter=t(43),i.Reader=t(27),i.BufferReader=t(28),i.util=t(39),i.rpc=t(31),i.roots=t(30),i.configure=n,i.Writer.e(i.BufferWriter),n()},{27:27,28:28,30:30,31:31,39:39,42:42,43:43}],19:[function(t,e){var r=e.exports=t(17);r.build="full",r.tokenize=t(34),r.parse=t(26),r.common=t(11),r.Root.e(r.Type,r.parse,r.common)},{11:11,17:17,26:26,34:34}],20:[function(t,r){function n(t,e,r,n,o){if(i.call(this,t,e,n,o),!s.isString(r))throw TypeError("keyType must be a string");this.keyType=r,this.resolvedKeyType=null,this.map=!0}r.exports=n;var i=t(16);((n.prototype=Object.create(i.prototype)).constructor=n).className="MapField";var o=t(36),s=t(37);n.fromJSON=function(t,e){return new n(t,e.id,e.keyType,e.type,e.options)},n.prototype.toJSON=function(){return s.toObject(["keyType",this.keyType,"type",this.type,"id",this.id,"extend",this.extend,"options",this.options])},n.prototype.resolve=function(){if(this.resolved)return this;if(o.mapKey[this.keyType]===e)throw Error("invalid key type: "+this.keyType);return i.prototype.resolve.call(this)},n.d=function(t,e,r){return"function"==typeof r?r=s.decorateType(r).name:r&&"object"==typeof r&&(r=s.decorateEnum(r).name),function(i,o){s.decorateType(i.constructor).add(new n(o,t,e,r))}}},{16:16,36:36,37:37}],21:[function(t,e){function r(t){if(t)for(var e=Object.keys(t),r=0;r<e.length;++r)this[e[r]]=t[e[r]]}e.exports=r;var n=t(39);r.create=function(t){return this.$type.create(t)},r.encode=function(t,e){return this.$type.encode(t,e)},r.encodeDelimited=function(t,e){return this.$type.encodeDelimited(t,e)},r.decode=function(t){return this.$type.decode(t)},r.decodeDelimited=function(t){return this.$type.decodeDelimited(t)},r.verify=function(t){return this.$type.verify(t)},r.fromObject=function(t){return this.$type.fromObject(t)},r.toObject=function(t,e){return this.$type.toObject(t,e)},r.prototype.toJSON=function(){return this.$type.toObject(this,n.toJSONOptions)}},{39:39}],22:[function(t,r){function n(t,r,n,s,a,u,f){if(o.isObject(a)?(f=a,a=u=e):o.isObject(u)&&(f=u,u=e),r!==e&&!o.isString(r))throw TypeError("type must be a string");if(!o.isString(n))throw TypeError("requestType must be a string");if(!o.isString(s))throw TypeError("responseType must be a string");i.call(this,t,f),this.type=r||"rpc",this.requestType=n,this.requestStream=!!a||e,this.responseType=s,this.responseStream=!!u||e,this.resolvedRequestType=null,this.resolvedResponseType=null}r.exports=n;var i=t(24);((n.prototype=Object.create(i.prototype)).constructor=n).className="Method";var o=t(37);n.fromJSON=function(t,e){return new n(t,e.type,e.requestType,e.responseType,e.requestStream,e.responseStream,e.options)},n.prototype.toJSON=function(){return o.toObject(["type","rpc"!==this.type&&this.type||e,"requestType",this.requestType,"requestStream",this.requestStream,"responseType",this.responseType,"responseStream",this.responseStream,"options",this.options])},n.prototype.resolve=function(){return this.resolved?this:(this.resolvedRequestType=this.parent.lookupType(this.requestType),this.resolvedResponseType=this.parent.lookupType(this.responseType),i.prototype.resolve.call(this))}},{24:24,37:37}],23:[function(t,r){function n(t){if(!t||!t.length)return e;for(var r={},n=0;n<t.length;++n)r[t[n].name]=t[n].toJSON();return r}function i(t,r){s.call(this,t,r),this.nested=e,this.f=null}function o(t){return t.f=null,t}r.exports=i;var s=t(24);((i.prototype=Object.create(s.prototype)).constructor=i).className="Namespace";var a,u,f=t(15),l=t(16),p=t(37);i.fromJSON=function(t,e){return new i(t,e.options).addJSON(e.nested)},i.arrayToJSON=n,Object.defineProperty(i.prototype,"nestedArray",{get:function(){return this.f||(this.f=p.toArray(this.nested))}}),i.prototype.toJSON=function(){return p.toObject(["options",this.options,"nested",n(this.nestedArray)])},i.prototype.addJSON=function(t){var r=this;if(t)for(var n,o=Object.keys(t),s=0;s<o.length;++s)n=t[o[s]],r.add((n.fields!==e?a.fromJSON:n.values!==e?f.fromJSON:n.methods!==e?u.fromJSON:n.id!==e?l.fromJSON:i.fromJSON)(o[s],n));return this},i.prototype.get=function(t){return this.nested&&this.nested[t]||null},i.prototype.getEnum=function(t){if(this.nested&&this.nested[t]instanceof f)return this.nested[t].values;throw Error("no such enum")},i.prototype.add=function(t){if(!(t instanceof l&&t.extend!==e||t instanceof a||t instanceof f||t instanceof u||t instanceof i))throw TypeError("object must be a valid nested object");if(this.nested){var r=this.get(t.name);if(r){if(!(r instanceof i&&t instanceof i)||r instanceof a||r instanceof u)throw Error("duplicate name '"+t.name+"' in "+this);for(var n=r.nestedArray,s=0;s<n.length;++s)t.add(n[s]);this.remove(r),this.nested||(this.nested={}),t.setOptions(r.options,!0)}}else this.nested={};return this.nested[t.name]=t,t.onAdd(this),o(this)},i.prototype.remove=function(t){if(!(t instanceof s))throw TypeError("object must be a ReflectionObject");if(t.parent!==this)throw Error(t+" is not a member of "+this);return delete this.nested[t.name],Object.keys(this.nested).length||(this.nested=e),t.onRemove(this),o(this)},i.prototype.define=function(t,e){if(p.isString(t))t=t.split(".");else if(!Array.isArray(t))throw TypeError("illegal path");if(t&&t.length&&""===t[0])throw Error("path must be relative");for(var r=this;t.length>0;){var n=t.shift();if(r.nested&&r.nested[n]){if(!((r=r.nested[n])instanceof i))throw Error("path conflicts with non-namespace objects")}else r.add(r=new i(n))}return e&&r.addJSON(e),r},i.prototype.resolveAll=function(){for(var t=this.nestedArray,e=0;e<t.length;)t[e]instanceof i?t[e++].resolveAll():t[e++].resolve();return this.resolve()},i.prototype.lookup=function(t,r,n){if("boolean"==typeof r?(n=r,r=e):r&&!Array.isArray(r)&&(r=[r]),p.isString(t)&&t.length){if("."===t)return this.root;t=t.split(".")}else if(!t.length)return this;if(""===t[0])return this.root.lookup(t.slice(1),r);var o=this.get(t[0]);if(o){if(1===t.length){if(!r||r.indexOf(o.constructor)>-1)return o}else if(o instanceof i&&(o=o.lookup(t.slice(1),r,!0)))return o}else for(var s=0;s<this.nestedArray.length;++s)if(this.f[s]instanceof i&&(o=this.f[s].lookup(t,r,!0)))return o;return null===this.parent||n?null:this.parent.lookup(t,r)},i.prototype.lookupType=function(t){var e=this.lookup(t,[a]);if(!e)throw Error("no such type");return e},i.prototype.lookupEnum=function(t){var e=this.lookup(t,[f]);if(!e)throw Error("no such Enum '"+t+"' in "+this);return e},i.prototype.lookupTypeOrEnum=function(t){var e=this.lookup(t,[a,f]);if(!e)throw Error("no such Type or Enum '"+t+"' in "+this);return e},i.prototype.lookupService=function(t){var e=this.lookup(t,[u]);if(!e)throw Error("no such Service '"+t+"' in "+this);return e},i.e=function(t,e){a=t,u=e}},{15:15,16:16,24:24,37:37}],24:[function(t,r){function n(t,e){if(!o.isString(t))throw TypeError("name must be a string");if(e&&!o.isObject(e))throw TypeError("options must be an object");this.options=e,this.name=t,this.parent=null,this.resolved=!1,this.comment=null,this.filename=null}r.exports=n,n.className="ReflectionObject";var i,o=t(37);Object.defineProperties(n.prototype,{root:{get:function(){for(var t=this;null!==t.parent;)t=t.parent;return t}},fullName:{get:function(){for(var t=[this.name],e=this.parent;e;)t.unshift(e.name),e=e.parent;return t.join(".")}}}),n.prototype.toJSON=function(){throw Error()},n.prototype.onAdd=function(t){this.parent&&this.parent!==t&&this.parent.remove(this),this.parent=t,this.resolved=!1;var e=t.root;e instanceof i&&e.g(this)},n.prototype.onRemove=function(t){var e=t.root;e instanceof i&&e.h(this),this.parent=null,this.resolved=!1},n.prototype.resolve=function(){return this.resolved?this:(this.root instanceof i&&(this.resolved=!0),this)},n.prototype.getOption=function(t){return this.options?this.options[t]:e},n.prototype.setOption=function(t,r,n){return n&&this.options&&this.options[t]!==e||((this.options||(this.options={}))[t]=r),this},n.prototype.setOptions=function(t,e){if(t)for(var r=Object.keys(t),n=0;n<r.length;++n)this.setOption(r[n],t[r[n]],e);return this},n.prototype.toString=function(){var t=this.constructor.className,e=this.fullName;return e.length?t+" "+e:t},n.e=function(t){i=t}},{37:37}],25:[function(t,r){function n(t,r,n){if(Array.isArray(r)||(n=r,r=e),o.call(this,t,n),r!==e&&!Array.isArray(r))throw TypeError("fieldNames must be an Array");this.oneof=r||[],this.fieldsArray=[]}function i(t){if(t.parent)for(var e=0;e<t.fieldsArray.length;++e)t.fieldsArray[e].parent||t.parent.add(t.fieldsArray[e])}r.exports=n;var o=t(24);((n.prototype=Object.create(o.prototype)).constructor=n).className="OneOf";var s=t(16),a=t(37);n.fromJSON=function(t,e){return new n(t,e.oneof,e.options)},n.prototype.toJSON=function(){return a.toObject(["options",this.options,"oneof",this.oneof])},n.prototype.add=function(t){if(!(t instanceof s))throw TypeError("field must be a Field");return t.parent&&t.parent!==this.parent&&t.parent.remove(t),this.oneof.push(t.name),this.fieldsArray.push(t),t.partOf=this,i(this),this},n.prototype.remove=function(t){if(!(t instanceof s))throw TypeError("field must be a Field");var e=this.fieldsArray.indexOf(t);if(e<0)throw Error(t+" is not a member of "+this);return this.fieldsArray.splice(e,1),e=this.oneof.indexOf(t.name),e>-1&&this.oneof.splice(e,1),t.partOf=null,this},n.prototype.onAdd=function(t){o.prototype.onAdd.call(this,t);for(var e=this,r=0;r<this.oneof.length;++r){var n=t.get(this.oneof[r]);n&&!n.partOf&&(n.partOf=e,e.fieldsArray.push(n))}i(this)},n.prototype.onRemove=function(t){for(var e,r=0;r<this.fieldsArray.length;++r)(e=this.fieldsArray[r]).parent&&e.parent.remove(e);o.prototype.onRemove.call(this,t)},n.d=function(){for(var t=Array(arguments.length),e=0;e<arguments.length;)t[e]=arguments[e++];return function(e,r){a.decorateType(e.constructor).add(new n(r,t)),Object.defineProperty(e,r,{get:a.oneOfGetter(t),set:a.oneOfSetter(t)})}}},{16:16,24:24,37:37}],26:[function(t,r){function n(t,r,A){function S(t,e,r){var i=n.filename;return r||(n.filename=null),Error("illegal "+(e||"token")+" '"+t+"' ("+(i?i+", ":"")+"line "+Y.line+")")}function T(){var t,e=[];do{if('"'!==(t=tt())&&"'"!==t)throw S(t);e.push(tt()),nt(t),t=rt()}while('"'===t||"'"===t);return e.join("")}function E(t){var e=tt();switch(e){case"'":case'"':return et(e),T();case"true":case"TRUE":return!0;case"false":case"FALSE":return!1}try{return F(e,!0)}catch(r){if(t&&j.test(e))return e;throw S(e,"value")}}function N(t,e){var r,n;do{!e||'"'!==(r=rt())&&"'"!==r?t.push([n=L(tt()),nt("to",!0)?L(tt()):n]):t.push(T())}while(nt(",",!0));nt(";")}function F(t,e){var r=1;switch("-"===t.charAt(0)&&(r=-1,t=t.substring(1)),t){case"inf":case"INF":case"Inf":return r*(1/0);case"nan":case"NAN":case"Nan":case"NaN":return NaN;case"0":return 0}if(y.test(t))return r*parseInt(t,10);if(m.test(t))return r*parseInt(t,16);if(b.test(t))return r*parseInt(t,8);if(O.test(t))return r*parseFloat(t);throw S(t,"number",e)}function L(t,e){switch(t){case"max":case"MAX":case"Max":return 536870911;case"0":return 0}if(!e&&"-"===t.charAt(0))throw S(t,"id");if(v.test(t))return parseInt(t,10);if(g.test(t))return parseInt(t,16);if(w.test(t))return parseInt(t,8);throw S(t,"id")}function I(t,e){switch(e){case"option":return z(t,e),nt(";"),!0;case"message":return J(t,e),!0;case"enum":return V(t,e),!0;case"service":return H(t,e),!0;case"extend":return Z(t,e),!0}return!1}function B(t,e,r){var i=Y.line;if(t&&(t.comment=it(),t.filename=n.filename),nt("{",!0)){for(var o;"}"!==(o=tt());)e(o);nt(";",!0)}else r&&r(),nt(";"),t&&"string"!=typeof t.comment&&(t.comment=it(i))}function J(t,e){if(!k.test(e=tt()))throw S(e,"type name");var r=new s(e);B(r,function(t){if(!I(r,t))switch(t){case"map":$(r);break;case"required":case"optional":case"repeated":R(r,t);break;case"oneof":P(r,t);break;case"extensions":N(r.extensions||(r.extensions=[]));break;case"reserved":N(r.reserved||(r.reserved=[]),!0);break;default:if(!st||!j.test(t))throw S(t);et(t),R(r,"optional")}}),t.add(r)}function R(t,r,n){var i=tt();if("group"===i)return void D(t,r);if(!j.test(i))throw S(i,"type");var o=tt();if(!k.test(o))throw S(o,"name");o=ut(o),nt("=");var s=new a(o,L(tt()),i,r,n);B(s,function(t){if("option"!==t)throw S(t);z(s,t),nt(";")},function(){U(s)}),t.add(s),st||!s.repeated||h.packed[i]===e&&h.basic[i]!==e||s.setOption("packed",!1,!0)}function D(t,e){var r=tt();if(!k.test(r))throw S(r,"name");var i=d.lcFirst(r);r===i&&(r=d.ucFirst(r)),nt("=");var o=L(tt()),u=new s(r);u.group=!0;var f=new a(i,o,r,e);f.filename=n.filename,B(u,function(t){switch(t){case"option":z(u,t),nt(";");break;case"required":case"optional":case"repeated":R(u,t);break;default:throw S(t)}}),t.add(u).add(f)}function $(t){nt("<");var r=tt();if(h.mapKey[r]===e)throw S(r,"type");nt(",");var n=tt();if(!j.test(n))throw S(n,"type");nt(">");var i=tt();if(!k.test(i))throw S(i,"name");nt("=");var o=new u(ut(i),L(tt()),r,n);B(o,function(t){if("option"!==t)throw S(t);z(o,t),nt(";")},function(){U(o)}),t.add(o)}function P(t,e){if(!k.test(e=tt()))throw S(e,"name");var r=new f(ut(e));B(r,function(t){"option"===t?(z(r,t),nt(";")):(et(t),R(r,"optional"))}),t.add(r)}function V(t,e){if(!k.test(e=tt()))throw S(e,"name");var r=new l(e);B(r,function(t){"option"===t?(z(r,t),nt(";")):q(r,t)}),t.add(r)}function q(t,e){if(!k.test(e))throw S(e,"name");nt("=");var r=L(tt(),!0),n={};B(n,function(t){if("option"!==t)throw S(t);z(n,t),nt(";")},function(){U(n)}),t.add(e,r,n.comment)}function z(t,e){var r=nt("(",!0);if(!j.test(e=tt()))throw S(e,"name");var n=e;r&&(nt(")"),n="("+n+")",e=rt(),x.test(e)&&(n+=e,tt())),nt("="),C(t,n)}function C(t,e){if(nt("{",!0))do{if(!k.test(Q=tt()))throw S(Q,"name");"{"===rt()?C(t,e+"."+Q):(nt(":"),M(t,e+"."+Q,E(!0)))}while(!nt("}",!0));else M(t,e,E(!0))}function M(t,e,r){t.setOption&&t.setOption(e,r)}function U(t){if(nt("[",!0)){do{z(t,"option")}while(nt(",",!0));nt("]")}return t}function H(t,e){if(!k.test(e=tt()))throw S(e,"service name");var r=new p(e);B(r,function(t){if(!I(r,t)){if("rpc"!==t)throw S(t);_(r,t)}}),t.add(r)}function _(t,e){var r=e;if(!k.test(e=tt()))throw S(e,"name");var n,i,o,s,a=e;if(nt("("),nt("stream",!0)&&(i=!0),!j.test(e=tt()))throw S(e);if(n=e,nt(")"),nt("returns"),nt("("),nt("stream",!0)&&(s=!0),!j.test(e=tt()))throw S(e);o=e,nt(")");var u=new c(a,r,n,o,i,s);B(u,function(t){if("option"!==t)throw S(t);z(u,t),nt(";")}),t.add(u)}function Z(t,e){if(!j.test(e=tt()))throw S(e,"reference");var r=e;B(null,function(e){switch(e){case"required":case"repeated":case"optional":R(t,e,r);break;default:if(!st||!j.test(e))throw S(e);et(e),R(t,"optional",r)}})}r instanceof o||(A=r,r=new o),A||(A=n.defaults);for(var W,K,G,X,Q,Y=i(t),tt=Y.next,et=Y.push,rt=Y.peek,nt=Y.skip,it=Y.cmnt,ot=!0,st=!1,at=r,ut=A.keepCase?function(t){return t}:d.camelCase;null!==(Q=tt());)switch(Q){case"package":if(!ot)throw S(Q);!function(){if(W!==e)throw S("package");if(W=tt(),!j.test(W))throw S(W,"name");at=at.define(W),nt(";")}();break;case"import":if(!ot)throw S(Q);!function(){var t,e=rt();switch(e){case"weak":t=G||(G=[]),tt();break;case"public":tt();default:t=K||(K=[])}e=T(),nt(";"),t.push(e)}();break;case"syntax":if(!ot)throw S(Q);!function(){if(nt("="),X=T(),!(st="proto3"===X)&&"proto2"!==X)throw S(X,"syntax");nt(";")}();break;case"option":if(!ot)throw S(Q);z(at,Q),nt(";");break;default:if(I(at,Q)){ot=!1;continue}throw S(Q)}return n.filename=null,{package:W,imports:K,weakImports:G,syntax:X,root:r}}r.exports=n,n.filename=null,n.defaults={keepCase:!1};var i=t(34),o=t(29),s=t(35),a=t(16),u=t(20),f=t(25),l=t(15),p=t(33),c=t(22),h=t(36),d=t(37),y=/^[1-9][0-9]*$/,v=/^-?[1-9][0-9]*$/,m=/^0[x][0-9a-fA-F]+$/,g=/^-?0[x][0-9a-fA-F]+$/,b=/^0[0-7]+$/,w=/^-?0[0-7]+$/,O=/^(?![eE])[0-9]*(?:\.[0-9]*)?(?:[eE][+-]?[0-9]+)?$/,k=/^[a-zA-Z_][a-zA-Z_0-9]*$/,j=/^(?:\.?[a-zA-Z_][a-zA-Z_0-9]*)+$/,x=/^(?:\.[a-zA-Z][a-zA-Z_0-9]*)+$/},{15:15,16:16,20:20,22:22,25:25,29:29,33:33,34:34,35:35,36:36,37:37}],27:[function(t,e){function r(t,e){return RangeError("index out of range: "+t.pos+" + "+(e||1)+" > "+t.len)}function n(t){this.buf=t,this.pos=0,this.len=t.length}function i(){var t=new f(0,0),e=0;if(!(this.len-this.pos>4)){for(;e<3;++e){if(this.pos>=this.len)throw r(this);if(t.lo=(t.lo|(127&this.buf[this.pos])<<7*e)>>>0,this.buf[this.pos++]<128)return t}return t.lo=(t.lo|(127&this.buf[this.pos++])<<7*e)>>>0,t}for(;e<4;++e)if(t.lo=(t.lo|(127&this.buf[this.pos])<<7*e)>>>0,this.buf[this.pos++]<128)return t;if(t.lo=(t.lo|(127&this.buf[this.pos])<<28)>>>0,t.hi=(t.hi|(127&this.buf[this.pos])>>4)>>>0,this.buf[this.pos++]<128)return t;if(e=0,this.len-this.pos>4){for(;e<5;++e)if(t.hi=(t.hi|(127&this.buf[this.pos])<<7*e+3)>>>0,this.buf[this.pos++]<128)return t}else for(;e<5;++e){if(this.pos>=this.len)throw r(this);if(t.hi=(t.hi|(127&this.buf[this.pos])<<7*e+3)>>>0,this.buf[this.pos++]<128)return t}throw Error("invalid varint encoding")}function o(t,e){return(t[e-4]|t[e-3]<<8|t[e-2]<<16|t[e-1]<<24)>>>0}function s(){if(this.pos+8>this.len)throw r(this,8);return new f(o(this.buf,this.pos+=4),o(this.buf,this.pos+=4))}e.exports=n;var a,u=t(39),f=u.LongBits,l=u.utf8,p="undefined"!=typeof Uint8Array?function(t){if(t instanceof Uint8Array||Array.isArray(t))return new n(t);throw Error("illegal buffer")}:function(t){if(Array.isArray(t))return new n(t);throw Error("illegal buffer")};n.create=u.Buffer?function(t){return(n.create=function(t){return u.Buffer.isBuffer(t)?new a(t):p(t)})(t)}:p,n.prototype.i=u.Array.prototype.subarray||u.Array.prototype.slice,n.prototype.uint32=function(){var t=4294967295;return function(){if(t=(127&this.buf[this.pos])>>>0,this.buf[this.pos++]<128)return t;if(t=(t|(127&this.buf[this.pos])<<7)>>>0,this.buf[this.pos++]<128)return t;if(t=(t|(127&this.buf[this.pos])<<14)>>>0,this.buf[this.pos++]<128)return t;if(t=(t|(127&this.buf[this.pos])<<21)>>>0,this.buf[this.pos++]<128)return t;if(t=(t|(15&this.buf[this.pos])<<28)>>>0,this.buf[this.pos++]<128)return t;if((this.pos+=5)>this.len)throw this.pos=this.len,r(this,10);return t}}(),n.prototype.int32=function(){return 0|this.uint32()},n.prototype.sint32=function(){var t=this.uint32();return t>>>1^-(1&t)|0},n.prototype.bool=function(){return 0!==this.uint32()},n.prototype.fixed32=function(){if(this.pos+4>this.len)throw r(this,4);return o(this.buf,this.pos+=4)},n.prototype.sfixed32=function(){if(this.pos+4>this.len)throw r(this,4);return 0|o(this.buf,this.pos+=4)},n.prototype.float=function(){if(this.pos+4>this.len)throw r(this,4);var t=u.float.readFloatLE(this.buf,this.pos);return this.pos+=4,t},n.prototype.double=function(){if(this.pos+8>this.len)throw r(this,4);var t=u.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,t},n.prototype.bytes=function(){var t=this.uint32(),e=this.pos,n=this.pos+t;if(n>this.len)throw r(this,t);return this.pos+=t,Array.isArray(this.buf)?this.buf.slice(e,n):e===n?new this.buf.constructor(0):this.i.call(this.buf,e,n)},n.prototype.string=function(){var t=this.bytes();return l.read(t,0,t.length)},n.prototype.skip=function(t){if("number"==typeof t){if(this.pos+t>this.len)throw r(this,t);this.pos+=t}else do{if(this.pos>=this.len)throw r(this)}while(128&this.buf[this.pos++]);return this},n.prototype.skipType=function(t){switch(t){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;;){if(4==(t=7&this.uint32()))break;this.skipType(t)}break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+t+" at offset "+this.pos)}return this},n.e=function(t){a=t;var e=u.Long?"toLong":"toNumber";u.merge(n.prototype,{int64:function(){return i.call(this)[e](!1)},uint64:function(){return i.call(this)[e](!0)},sint64:function(){return i.call(this).zzDecode()[e](!1)},fixed64:function(){return s.call(this)[e](!0)},sfixed64:function(){return s.call(this)[e](!1)}})}},{39:39}],28:[function(t,e){function r(t){n.call(this,t)}e.exports=r;var n=t(27);(r.prototype=Object.create(n.prototype)).constructor=r;var i=t(39);i.Buffer&&(r.prototype.i=i.Buffer.prototype.slice),r.prototype.string=function(){var t=this.uint32();return this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+t,this.len))}},{27:27,39:39}],29:[function(t,r){function n(t){s.call(this,"",t),this.deferred=[],this.files=[]}function i(){}function o(t,r){var n=r.parent.lookup(r.extend);if(n){var i=new l(r.fullName,r.id,r.type,r.rule,e,r.options);return i.declaringField=r,r.extensionField=i,n.add(i),!0}return!1}r.exports=n;var s=t(23);((n.prototype=Object.create(s.prototype)).constructor=n).className="Root";var a,u,f,l=t(16),p=t(15),c=t(25),h=t(37);n.fromJSON=function(t,e){return e||(e=new n),t.options&&e.setOptions(t.options),e.addJSON(t.nested)},n.prototype.resolvePath=h.path.resolve,n.prototype.load=function t(r,n,o){function s(t,e){if(o){var r=o;if(o=null,c)throw t;r(t,e)}}function a(t,e){try{if(h.isString(e)&&"{"===e.charAt(0)&&(e=JSON.parse(e)),h.isString(e)){u.filename=t;var r,i=u(e,p,n),o=0;if(i.imports)for(;o<i.imports.length;++o)(r=p.resolvePath(t,i.imports[o]))&&l(r);if(i.weakImports)for(o=0;o<i.weakImports.length;++o)(r=p.resolvePath(t,i.weakImports[o]))&&l(r,!0)}else p.setOptions(e.options).addJSON(e.nested)}catch(t){s(t)}c||d||s(null,p)}function l(t,e){var r=t.lastIndexOf("google/protobuf/");if(r>-1){var n=t.substring(r);n in f&&(t=n)}if(!(p.files.indexOf(t)>-1)){if(p.files.push(t),t in f)return void(c?a(t,f[t]):(++d,setTimeout(function(){--d,a(t,f[t])})));if(c){var i;try{i=h.fs.readFileSync(t).toString("utf8")}catch(t){return void(e||s(t))}a(t,i)}else++d,h.fetch(t,function(r,n){if(--d,o)return r?void(e?d||s(null,p):s(r)):void a(t,n)})}}"function"==typeof n&&(o=n,n=e);var p=this;if(!o)return h.asPromise(t,p,r,n);var c=o===i,d=0;h.isString(r)&&(r=[r]);for(var y,v=0;v<r.length;++v)(y=p.resolvePath("",r[v]))&&l(y);return c?p:(d||s(null,p),e)},n.prototype.loadSync=function(t,e){if(!h.isNode)throw Error("not supported");return this.load(t,e,i)},n.prototype.resolveAll=function(){if(this.deferred.length)throw Error("unresolvable extensions: "+this.deferred.map(function(t){return"'extend "+t.extend+"' in "+t.parent.fullName}).join(", "));return s.prototype.resolveAll.call(this)};var d=/^[A-Z]/;n.prototype.g=function(t){if(t instanceof l)t.extend===e||t.extensionField||o(this,t)||this.deferred.push(t);else if(t instanceof p)d.test(t.name)&&(t.parent[t.name]=t.values);else if(!(t instanceof c)){if(t instanceof a)for(var r=0;r<this.deferred.length;)o(this,this.deferred[r])?this.deferred.splice(r,1):++r;for(var n=0;n<t.nestedArray.length;++n)this.g(t.f[n]);d.test(t.name)&&(t.parent[t.name]=t)}},n.prototype.h=function(t){if(t instanceof l){if(t.extend!==e)if(t.extensionField)t.extensionField.parent.remove(t.extensionField),t.extensionField=null;else{var r=this.deferred.indexOf(t);r>-1&&this.deferred.splice(r,1)}}else if(t instanceof p)d.test(t.name)&&delete t.parent[t.name];else if(t instanceof s){for(var n=0;n<t.nestedArray.length;++n)this.h(t.f[n]);d.test(t.name)&&delete t.parent[t.name]}},n.e=function(t,e,r){a=t,u=e,f=r}},{15:15,16:16,23:23,25:25,37:37}],30:[function(t,e){e.exports={}},{}],31:[function(t,e,r){r.Service=t(32)},{32:32}],32:[function(t,r){function n(t,e,r){if("function"!=typeof t)throw TypeError("rpcImpl must be a function");i.EventEmitter.call(this),this.rpcImpl=t,this.requestDelimited=!!e,this.responseDelimited=!!r}r.exports=n;var i=t(39);(n.prototype=Object.create(i.EventEmitter.prototype)).constructor=n,n.prototype.rpcCall=function t(r,n,o,s,a){if(!s)throw TypeError("request must be specified");var u=this;if(!a)return i.asPromise(t,u,r,n,o,s);if(!u.rpcImpl)return setTimeout(function(){a(Error("already ended"))},0),e;try{return u.rpcImpl(r,n[u.requestDelimited?"encodeDelimited":"encode"](s).finish(),function(t,n){if(t)return u.emit("error",t,r),a(t);if(null===n)return u.end(!0),e;if(!(n instanceof o))try{n=o[u.responseDelimited?"decodeDelimited":"decode"](n)}catch(t){return u.emit("error",t,r),a(t)}return u.emit("data",n,r),a(null,n)})}catch(t){return u.emit("error",t,r),setTimeout(function(){a(t)},0),e}},n.prototype.end=function(t){return this.rpcImpl&&(t||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit("end").off()),this}},{39:39}],33:[function(t,r){function n(t,e){o.call(this,t,e),this.methods={},this.j=null}function i(t){return t.j=null,t}r.exports=n;var o=t(23);((n.prototype=Object.create(o.prototype)).constructor=n).className="Service";var s=t(22),a=t(37),u=t(31);n.fromJSON=function(t,e){var r=new n(t,e.options);if(e.methods)for(var i=Object.keys(e.methods),o=0;o<i.length;++o)r.add(s.fromJSON(i[o],e.methods[i[o]]));return e.nested&&r.addJSON(e.nested),r},n.prototype.toJSON=function(){var t=o.prototype.toJSON.call(this);return a.toObject(["options",t&&t.options||e,"methods",o.arrayToJSON(this.methodsArray)||{},"nested",t&&t.nested||e])},Object.defineProperty(n.prototype,"methodsArray",{get:function(){return this.j||(this.j=a.toArray(this.methods))}}),n.prototype.get=function(t){return this.methods[t]||o.prototype.get.call(this,t)},n.prototype.resolveAll=function(){for(var t=this.methodsArray,e=0;e<t.length;++e)t[e].resolve();return o.prototype.resolve.call(this)},n.prototype.add=function(t){if(this.get(t.name))throw Error("duplicate name '"+t.name+"' in "+this);return t instanceof s?(this.methods[t.name]=t,t.parent=this,i(this)):o.prototype.add.call(this,t)},n.prototype.remove=function(t){if(t instanceof s){if(this.methods[t.name]!==t)throw Error(t+" is not a member of "+this);return delete this.methods[t.name],t.parent=null,i(this)}return o.prototype.remove.call(this,t)},n.prototype.create=function(t,e,r){for(var n,i=new u.Service(t,e,r),o=0;o<this.methodsArray.length;++o)i[a.lcFirst((n=this.j[o]).resolve().name)]=a.codegen(["r","c"],a.lcFirst(n.name))("return this.rpcCall(m,q,s,r,c)")({m:n,q:n.resolvedRequestType.ctor,s:n.resolvedResponseType.ctor});return i}},{22:22,23:23,31:31,37:37}],34:[function(t,r){function n(t){return t.replace(p,function(t,e){switch(e){case"\\":case"":return e;default:return c[e]||""}})}function i(t){function r(t){return Error("illegal "+t+" (line "+w+")")}function i(){var e="'"===S?a:s;e.lastIndex=g-1;var i=e.exec(t);if(!i)throw r("string");return g=e.lastIndex,d(S),S=null,n(i[1])}function p(e){return t.charAt(e)}function c(e,r){O=t.charAt(e++),j=w,x=!1;var n,i=e-3;do{if(--i<0||"\n"===(n=t.charAt(i))){x=!0;break}}while(" "===n||"\t"===n);for(var o=t.substring(e,r).split(f),s=0;s<o.length;++s)o[s]=o[s].replace(u,"").trim();k=o.join("\n").trim()}function h(){if(A.length>0)return A.shift();if(S)return i();var e,n,s,a,u;do{if(g===b)return null;for(e=!1;l.test(s=p(g));)if("\n"===s&&++w,++g===b)return null;if("/"===p(g)){if(++g===b)throw r("comment");if("/"===p(g)){for(u="/"===p(a=g+1);"\n"!==p(++g);)if(g===b)return null;++g,u&&c(a,g-1),++w,e=!0}else{if("*"!==(s=p(g)))return"/";u="*"===p(a=g+1);do{if("\n"===s&&++w,++g===b)throw r("comment");n=s,s=p(g)}while("*"!==n||"/"!==s);++g,u&&c(a,g-2),e=!0}}}while(e);var f=g;if(o.lastIndex=0,!o.test(p(f++)))for(;f<b&&!o.test(p(f));)++f;var h=t.substring(g,g=f);return'"'!==h&&"'"!==h||(S=h),h}function d(t){A.push(t)}function y(){if(!A.length){var t=h();if(null===t)return null;d(t)}return A[0]}function v(t,e){var n=y();if(n===t)return h(),!0;if(!e)throw r("token '"+n+"', '"+t+"' expected");return!1}function m(t){var r=null;return t===e?j!==w-1||"*"!==O&&!x||(r=k):(j<t&&y(),j!==t||x||"/"!==O||(r=k)),r}t=""+t;var g=0,b=t.length,w=1,O=null,k=null,j=0,x=!1,A=[],S=null;return Object.defineProperty({next:h,peek:y,push:d,skip:v,cmnt:m},"line",{get:function(){return w}})}r.exports=i;var o=/[\s{}=;:[\],'"()<>]/g,s=/(?:"([^"\\]*(?:\\.[^"\\]*)*)")/g,a=/(?:'([^'\\]*(?:\\.[^'\\]*)*)')/g,u=/^ *[*\/]+ */,f=/\n/g,l=/\s/,p=/\\(.?)/g,c={0:"\0",r:"\r",n:"\n",t:"\t"};i.unescape=n},{}],35:[function(t,r){function n(t,r){o.call(this,t,r),this.fields={},this.oneofs=e,this.extensions=e,this.reserved=e,this.group=e,this.k=null,this.b=null,this.l=null,this.o=null}function i(t){return t.k=t.b=t.l=null,delete t.encode,delete t.decode,delete t.verify,t}r.exports=n;var o=t(23);((n.prototype=Object.create(o.prototype)).constructor=n).className="Type";var s=t(15),a=t(25),u=t(16),f=t(20),l=t(33),p=t(21),c=t(27),h=t(42),d=t(37),y=t(14),v=t(13),m=t(40),g=t(12),b=t(41);Object.defineProperties(n.prototype,{fieldsById:{get:function(){if(this.k)return this.k;this.k={};for(var t=Object.keys(this.fields),e=0;e<t.length;++e){var r=this.fields[t[e]],n=r.id;if(this.k[n])throw Error("duplicate id "+n+" in "+this);this.k[n]=r}return this.k}},fieldsArray:{get:function(){return this.b||(this.b=d.toArray(this.fields))}},oneofsArray:{get:function(){return this.l||(this.l=d.toArray(this.oneofs))}},ctor:{get:function(){return this.o||(this.ctor=n.generateConstructor(this)())},set:function(t){var e=t.prototype;e instanceof p||((t.prototype=new p).constructor=t,d.merge(t.prototype,e)),t.$type=t.prototype.$type=this,d.merge(t,p,!0),this.o=t;for(var r=0;r<this.fieldsArray.length;++r)this.b[r].resolve();var n={};for(r=0;r<this.oneofsArray.length;++r)n[this.l[r].resolve().name]={get:d.oneOfGetter(this.l[r].oneof),set:d.oneOfSetter(this.l[r].oneof)};r&&Object.defineProperties(t.prototype,n)}}}),n.generateConstructor=function(t){for(var e,r=d.codegen(["p"],t.name),n=0;n<t.fieldsArray.length;++n)(e=t.b[n]).map?r("this%s={}",d.safeProp(e.name)):e.repeated&&r("this%s=[]",d.safeProp(e.name));return r("if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)")("this[ks[i]]=p[ks[i]]")},n.fromJSON=function(t,r){var i=new n(t,r.options);i.extensions=r.extensions,i.reserved=r.reserved;for(var p=Object.keys(r.fields),c=0;c<p.length;++c)i.add((e!==r.fields[p[c]].keyType?f.fromJSON:u.fromJSON)(p[c],r.fields[p[c]]));if(r.oneofs)for(p=Object.keys(r.oneofs),c=0;c<p.length;++c)i.add(a.fromJSON(p[c],r.oneofs[p[c]]));if(r.nested)for(p=Object.keys(r.nested),c=0;c<p.length;++c){var h=r.nested[p[c]];i.add((h.id!==e?u.fromJSON:h.fields!==e?n.fromJSON:h.values!==e?s.fromJSON:h.methods!==e?l.fromJSON:o.fromJSON)(p[c],h))}return r.extensions&&r.extensions.length&&(i.extensions=r.extensions),r.reserved&&r.reserved.length&&(i.reserved=r.reserved),r.group&&(i.group=!0),i},n.prototype.toJSON=function(){var t=o.prototype.toJSON.call(this);return d.toObject(["options",t&&t.options||e,"oneofs",o.arrayToJSON(this.oneofsArray),"fields",o.arrayToJSON(this.fieldsArray.filter(function(t){return!t.declaringField}))||{},"extensions",this.extensions&&this.extensions.length?this.extensions:e,"reserved",this.reserved&&this.reserved.length?this.reserved:e,"group",this.group||e,"nested",t&&t.nested||e])},n.prototype.resolveAll=function(){for(var t=this.fieldsArray,e=0;e<t.length;)t[e++].resolve();var r=this.oneofsArray;for(e=0;e<r.length;)r[e++].resolve();return o.prototype.resolveAll.call(this)},n.prototype.get=function(t){return this.fields[t]||this.oneofs&&this.oneofs[t]||this.nested&&this.nested[t]||null},n.prototype.add=function(t){if(this.get(t.name))throw Error("duplicate name '"+t.name+"' in "+this);if(t instanceof u&&t.extend===e){if(this.k?this.k[t.id]:this.fieldsById[t.id])throw Error("duplicate id "+t.id+" in "+this);if(this.isReservedId(t.id))throw Error("id "+t.id+" is reserved in "+this);if(this.isReservedName(t.name))throw Error("name '"+t.name+"' is reserved in "+this);return t.parent&&t.parent.remove(t),this.fields[t.name]=t,t.message=this,t.onAdd(this),i(this)}return t instanceof a?(this.oneofs||(this.oneofs={}),this.oneofs[t.name]=t,t.onAdd(this),i(this)):o.prototype.add.call(this,t)},n.prototype.remove=function(t){if(t instanceof u&&t.extend===e){if(!this.fields||this.fields[t.name]!==t)throw Error(t+" is not a member of "+this);return delete this.fields[t.name],t.parent=null,t.onRemove(this),i(this)}if(t instanceof a){if(!this.oneofs||this.oneofs[t.name]!==t)throw Error(t+" is not a member of "+this);return delete this.oneofs[t.name],t.parent=null,t.onRemove(this),i(this)}return o.prototype.remove.call(this,t)},n.prototype.isReservedId=function(t){if(this.reserved)for(var e=0;e<this.reserved.length;++e)if("string"!=typeof this.reserved[e]&&this.reserved[e][0]<=t&&this.reserved[e][1]>=t)return!0;return!1},n.prototype.isReservedName=function(t){if(this.reserved)for(var e=0;e<this.reserved.length;++e)if(this.reserved[e]===t)return!0;return!1},n.prototype.create=function(t){return new this.ctor(t)},n.prototype.setup=function(){for(var t=this.fullName,e=[],r=0;r<this.fieldsArray.length;++r)e.push(this.b[r].resolve().resolvedType);this.encode=y(this)({Writer:h,types:e,util:d}),this.decode=v(this)({Reader:c,types:e,util:d}),this.verify=m(this)({types:e,util:d}),this.fromObject=g.fromObject(this)({types:e,util:d}),this.toObject=g.toObject(this)({types:e,util:d});var n=b[t];if(n){var i=Object.create(this);i.fromObject=this.fromObject,this.fromObject=n.fromObject.bind(i),i.toObject=this.toObject,this.toObject=n.toObject.bind(i)}return this},n.prototype.encode=function(t,e){return this.setup().encode(t,e)},n.prototype.encodeDelimited=function(t,e){return this.encode(t,e&&e.len?e.fork():e).ldelim()},n.prototype.decode=function(t,e){return this.setup().decode(t,e)},n.prototype.decodeDelimited=function(t){return t instanceof c||(t=c.create(t)),this.decode(t,t.uint32())},n.prototype.verify=function(t){return this.setup().verify(t)},n.prototype.fromObject=function(t){return this.setup().fromObject(t)},n.prototype.toObject=function(t,e){return this.setup().toObject(t,e)},n.d=function(t){return function(e){d.decorateType(e,t)}}},{12:12,13:13,14:14,15:15,16:16,20:20,21:21,23:23,25:25,27:27,33:33,37:37,40:40,41:41,42:42}],36:[function(t,e,r){function n(t,e){var r=0,n={};for(e|=0;r<t.length;)n[s[r+e]]=t[r++];return n}var i=r,o=t(37),s=["double","float","int32","uint32","sint32","fixed32","sfixed32","int64","uint64","sint64","fixed64","sfixed64","bool","string","bytes"];i.basic=n([1,5,0,0,0,5,5,0,0,0,1,1,0,2,2]),i.defaults=n([0,0,0,0,0,0,0,0,0,0,0,0,!1,"",o.emptyArray,null]),i.long=n([0,0,0,1,1],7),i.mapKey=n([0,0,0,5,5,0,0,0,1,1,0,2],2),i.packed=n([1,5,0,0,0,5,5,0,0,0,1,1,0])},{37:37}],37:[function(t,r){var n,i,o=r.exports=t(39),s=t(30);o.codegen=t(3),o.fetch=t(5),o.path=t(8),o.fs=o.inquire("fs"),o.toArray=function(t){if(t){for(var e=Object.keys(t),r=Array(e.length),n=0;n<e.length;)r[n]=t[e[n++]];return r}return[]},o.toObject=function(t){for(var r={},n=0;n<t.length;){var i=t[n++],o=t[n++];o!==e&&(r[i]=o)}return r};var a=/\\/g,u=/"/g;o.safeProp=function(t){return'["'+t.replace(a,"\\\\").replace(u,'\\"')+'"]'},o.ucFirst=function(t){return t.charAt(0).toUpperCase()+t.substring(1)};var f=/_([a-z])/g;o.camelCase=function(t){return t.substring(0,1)+t.substring(1).replace(f,function(t,e){return e.toUpperCase()})},o.compareFieldsById=function(t,e){return t.id-e.id},o.decorateType=function(e,r){if(e.$type)return r&&e.$type.name!==r&&(o.decorateRoot.remove(e.$type),e.$type.name=r,o.decorateRoot.add(e.$type)),e.$type;n||(n=t(35));var i=new n(r||e.name);return o.decorateRoot.add(i),i.ctor=e,Object.defineProperty(e,"$type",{value:i,enumerable:!1}),Object.defineProperty(e.prototype,"$type",{value:i,enumerable:!1}),i};var l=0;o.decorateEnum=function(e){if(e.$type)return e.$type;i||(i=t(15));var r=new i("Enum"+l++,e);return o.decorateRoot.add(r),Object.defineProperty(e,"$type",{value:r,enumerable:!1}),r},Object.defineProperty(o,"decorateRoot",{get:function(){return s.decorated||(s.decorated=new(t(29)))}})},{15:15,29:29,3:3,30:30,35:35,39:39,5:5,8:8}],38:[function(t,e){function r(t,e){this.lo=t>>>0,this.hi=e>>>0}e.exports=r;var n=t(39),i=r.zero=new r(0,0);i.toNumber=function(){return 0},i.zzEncode=i.zzDecode=function(){return this},i.length=function(){return 1};var o=r.zeroHash="\0\0\0\0\0\0\0\0";r.fromNumber=function(t){if(0===t)return i;var e=t<0;e&&(t=-t);var n=t>>>0,o=(t-n)/4294967296>>>0;return e&&(o=~o>>>0,n=~n>>>0,++n>4294967295&&(n=0,++o>4294967295&&(o=0))),new r(n,o)},r.from=function(t){if("number"==typeof t)return r.fromNumber(t);if(n.isString(t)){if(!n.Long)return r.fromNumber(parseInt(t,10));t=n.Long.fromString(t)}return t.low||t.high?new r(t.low>>>0,t.high>>>0):i},r.prototype.toNumber=function(t){if(!t&&this.hi>>>31){var e=1+~this.lo>>>0,r=~this.hi>>>0;return e||(r=r+1>>>0),-(e+4294967296*r)}return this.lo+4294967296*this.hi},r.prototype.toLong=function(t){return n.Long?new n.Long(0|this.lo,0|this.hi,!!t):{low:0|this.lo,high:0|this.hi,unsigned:!!t}};var s=String.prototype.charCodeAt;r.fromHash=function(t){return t===o?i:new r((s.call(t,0)|s.call(t,1)<<8|s.call(t,2)<<16|s.call(t,3)<<24)>>>0,(s.call(t,4)|s.call(t,5)<<8|s.call(t,6)<<16|s.call(t,7)<<24)>>>0)},r.prototype.toHash=function(){return String.fromCharCode(255&this.lo,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,255&this.hi,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)},r.prototype.zzEncode=function(){var t=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^t)>>>0,this.lo=(this.lo<<1^t)>>>0,this},r.prototype.zzDecode=function(){var t=-(1&this.lo);return this.lo=((this.lo>>>1|this.hi<<31)^t)>>>0,this.hi=(this.hi>>>1^t)>>>0,this},r.prototype.length=function(){var t=this.lo,e=(this.lo>>>28|this.hi<<4)>>>0,r=this.hi>>>24;return 0===r?0===e?t<16384?t<128?1:2:t<2097152?3:4:e<16384?e<128?5:6:e<2097152?7:8:r<128?9:10}},{39:39}],39:[function(r,n,i){function o(t,r,n){for(var i=Object.keys(r),o=0;o<i.length;++o)t[i[o]]!==e&&n||(t[i[o]]=r[i[o]]);return t}function s(t){function e(t,r){if(!(this instanceof e))return new e(t,r);Object.defineProperty(this,"message",{get:function(){return t}}),Error.captureStackTrace?Error.captureStackTrace(this,e):Object.defineProperty(this,"stack",{value:Error().stack||""}),r&&o(this,r)}return(e.prototype=Object.create(Error.prototype)).constructor=e,Object.defineProperty(e.prototype,"name",{get:function(){return t}}),e.prototype.toString=function(){return this.name+": "+this.message},e}var a=i;a.asPromise=r(1),a.base64=r(2),a.EventEmitter=r(4),a.float=r(6),a.inquire=r(7),a.utf8=r(10),a.pool=r(9),a.LongBits=r(38),a.emptyArray=Object.freeze?Object.freeze([]):[],a.emptyObject=Object.freeze?Object.freeze({}):{},a.isNode=!!(t.process&&t.process.versions&&t.process.versions.node),a.isInteger=Number.isInteger||function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t},a.isString=function(t){return"string"==typeof t||t instanceof String},a.isObject=function(t){return t&&"object"==typeof t},a.isset=a.isSet=function(t,e){var r=t[e];return!(null==r||!t.hasOwnProperty(e))&&("object"!=typeof r||(Array.isArray(r)?r.length:Object.keys(r).length)>0)},a.Buffer=function(){try{var t=a.inquire("buffer").Buffer;return t.prototype.utf8Write?t:null}catch(t){return null}}(),a.p=null,a.u=null,a.newBuffer=function(t){return"number"==typeof t?a.Buffer?a.u(t):new a.Array(t):a.Buffer?a.p(t):"undefined"==typeof Uint8Array?t:new Uint8Array(t)},a.Array="undefined"!=typeof Uint8Array?Uint8Array:Array,a.Long=t.dcodeIO&&t.dcodeIO.Long||a.inquire("long"),a.key2Re=/^true|false|0|1$/,a.key32Re=/^-?(?:0|[1-9][0-9]*)$/,a.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/,a.longToHash=function(t){return t?a.LongBits.from(t).toHash():a.LongBits.zeroHash},a.longFromHash=function(t,e){var r=a.LongBits.fromHash(t);return a.Long?a.Long.fromBits(r.lo,r.hi,e):r.toNumber(!!e)},a.merge=o,a.lcFirst=function(t){return t.charAt(0).toLowerCase()+t.substring(1)},a.newError=s,a.ProtocolError=s("ProtocolError"),a.oneOfGetter=function(t){for(var r={},n=0;n<t.length;++n)r[t[n]]=1;return function(){for(var t=Object.keys(this),n=t.length-1;n>-1;--n)if(1===r[t[n]]&&this[t[n]]!==e&&null!==this[t[n]])return t[n]}},a.oneOfSetter=function(t){return function(e){for(var r=0;r<t.length;++r)t[r]!==e&&delete this[t[r]]}},a.toJSONOptions={longs:String,enums:String,bytes:String,json:!0},a.e=function(){var t=a.Buffer;if(!t)return void(a.p=a.u=null);a.p=t.from!==Uint8Array.from&&t.from||function(e,r){return new t(e,r)},a.u=t.allocUnsafe||function(e){return new t(e)}}},{1:1,10:10,2:2,38:38,4:4,6:6,7:7,9:9}],40:[function(t,e){function r(t,e){return t.name+": "+e+(t.repeated&&"array"!==e?"[]":t.map&&"object"!==e?"{k:"+t.keyType+"}":"")+" expected"}function n(t,e,n,i){if(e.resolvedType)if(e.resolvedType instanceof s){t("switch(%s){",i)("default:")("return%j",r(e,"enum value"));for(var o=Object.keys(e.resolvedType.values),a=0;a<o.length;++a)t("case %i:",e.resolvedType.values[o[a]]);t("break")("}")}else t("{")("var e=types[%i].verify(%s);",n,i)("if(e)")("return%j+e",e.name+".")("}");else switch(e.type){case"int32":case"uint32":case"sint32":case"fixed32":case"sfixed32":t("if(!util.isInteger(%s))",i)("return%j",r(e,"integer"));break;case"int64":case"uint64":case"sint64":case"fixed64":case"sfixed64":t("if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))",i,i,i,i)("return%j",r(e,"integer|Long"));break;case"float":case"double":t('if(typeof %s!=="number")',i)("return%j",r(e,"number"));break;case"bool":t('if(typeof %s!=="boolean")',i)("return%j",r(e,"boolean"));break;case"string":t("if(!util.isString(%s))",i)("return%j",r(e,"string"));break;case"bytes":t('if(!(%s&&typeof %s.length==="number"||util.isString(%s)))',i,i,i)("return%j",r(e,"buffer"))}return t}function i(t,e,n){switch(e.keyType){case"int32":case"uint32":case"sint32":case"fixed32":case"sfixed32":t("if(!util.key32Re.test(%s))",n)("return%j",r(e,"integer key"));break;case"int64":case"uint64":case"sint64":case"fixed64":case"sfixed64":t("if(!util.key64Re.test(%s))",n)("return%j",r(e,"integer|Long key"));break;case"bool":t("if(!util.key2Re.test(%s))",n)("return%j",r(e,"boolean key"))}return t}function o(t){var e=a.codegen(["m"],t.name+"$verify")('if(typeof m!=="object"||m===null)')("return%j","object expected"),o=t.oneofsArray,s={};o.length&&e("var p={}");for(var u=0;u<t.fieldsArray.length;++u){var f=t.b[u].resolve(),l="m"+a.safeProp(f.name);if(f.optional&&e("if(%s!=null&&m.hasOwnProperty(%j)){",l,f.name),f.map)e("if(!util.isObject(%s))",l)("return%j",r(f,"object"))("var k=Object.keys(%s)",l)("for(var i=0;i<k.length;++i){"),i(e,f,"k[i]"),n(e,f,u,l+"[k[i]]")("}");else if(f.repeated)e("if(!Array.isArray(%s))",l)("return%j",r(f,"array"))("for(var i=0;i<%s.length;++i){",l),n(e,f,u,l+"[i]")("}");else{if(f.partOf){var p=a.safeProp(f.partOf.name);1===s[f.partOf.name]&&e("if(p%s===1)",p)("return%j",f.partOf.name+": multiple values"),s[f.partOf.name]=1,e("p%s=1",p)}n(e,f,u,l)}f.optional&&e("}")}return e("return null")}e.exports=o;var s=t(15),a=t(37)},{15:15,37:37}],41:[function(t,e,r){var n=r,i=t(21);n[".google.protobuf.Any"]={fromObject:function(t){if(t&&t["@type"]){var e=this.lookup(t["@type"]);if(e){var r="."===t["@type"].charAt(0)?t["@type"].substr(1):t["@type"];return this.create({type_url:r,value:e.encode(e.fromObject(t)).finish()})}}return this.fromObject(t)},toObject:function(t,e){if(e&&e.json&&t.type_url&&t.value){var r=this.lookup(t.type_url);r&&(t=r.decode(t.value))}if(!(t instanceof this.ctor)&&t instanceof i){var n=t.$type.toObject(t,e);return n["@type"]=t.$type.fullName,n}return this.toObject(t,e)}}},{21:21}],42:[function(t,r){function n(t,r,n){this.fn=t,this.len=r,this.next=e,this.val=n}function i(){}function o(t){this.head=t.head,this.tail=t.tail,this.len=t.len,this.next=t.states}function s(){this.len=0,this.head=new n(i,0,0),this.tail=this.head,this.states=null}function a(t,e,r){e[r]=255&t}function u(t,e,r){for(;t>127;)e[r++]=127&t|128,t>>>=7;e[r]=t}function f(t,r){this.len=t,this.next=e,this.val=r}function l(t,e,r){for(;t.hi;)e[r++]=127&t.lo|128,t.lo=(t.lo>>>7|t.hi<<25)>>>0,t.hi>>>=7;for(;t.lo>127;)e[r++]=127&t.lo|128,t.lo=t.lo>>>7;e[r++]=t.lo}function p(t,e,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24}r.exports=s;var c,h=t(39),d=h.LongBits,y=h.base64,v=h.utf8;s.create=h.Buffer?function(){return(s.create=function(){return new c})()}:function(){return new s},s.alloc=function(t){return new h.Array(t)},h.Array!==Array&&(s.alloc=h.pool(s.alloc,h.Array.prototype.subarray)),s.prototype.v=function(t,e,r){return this.tail=this.tail.next=new n(t,e,r),this.len+=e,this},f.prototype=Object.create(n.prototype),f.prototype.fn=u,s.prototype.uint32=function(t){return this.len+=(this.tail=this.tail.next=new f((t>>>=0)<128?1:t<16384?2:t<2097152?3:t<268435456?4:5,t)).len,this},s.prototype.int32=function(t){return t<0?this.v(l,10,d.fromNumber(t)):this.uint32(t)},s.prototype.sint32=function(t){return this.uint32((t<<1^t>>31)>>>0)},s.prototype.uint64=function(t){var e=d.from(t);return this.v(l,e.length(),e)},s.prototype.int64=s.prototype.uint64,s.prototype.sint64=function(t){var e=d.from(t).zzEncode();return this.v(l,e.length(),e)},s.prototype.bool=function(t){return this.v(a,1,t?1:0)},s.prototype.fixed32=function(t){return this.v(p,4,t>>>0)},s.prototype.sfixed32=s.prototype.fixed32,s.prototype.fixed64=function(t){var e=d.from(t);return this.v(p,4,e.lo).v(p,4,e.hi)},s.prototype.sfixed64=s.prototype.fixed64,s.prototype.float=function(t){return this.v(h.float.writeFloatLE,4,t)},s.prototype.double=function(t){return this.v(h.float.writeDoubleLE,8,t)};var m=h.Array.prototype.set?function(t,e,r){e.set(t,r)}:function(t,e,r){for(var n=0;n<t.length;++n)e[r+n]=t[n]};s.prototype.bytes=function(t){var e=t.length>>>0;if(!e)return this.v(a,1,0);if(h.isString(t)){var r=s.alloc(e=y.length(t));y.decode(t,r,0),t=r}return this.uint32(e).v(m,e,t)},s.prototype.string=function(t){var e=v.length(t);return e?this.uint32(e).v(v.write,e,t):this.v(a,1,0)},s.prototype.fork=function(){return this.states=new o(this),this.head=this.tail=new n(i,0,0),this.len=0,this},s.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new n(i,0,0),this.len=0),this},s.prototype.ldelim=function(){var t=this.head,e=this.tail,r=this.len;return this.reset().uint32(r),r&&(this.tail.next=t.next,this.tail=e,this.len+=r),this},s.prototype.finish=function(){for(var t=this.head.next,e=this.constructor.alloc(this.len),r=0;t;)t.fn(t.val,e,r),r+=t.len,t=t.next;return e},s.e=function(t){c=t}},{39:39}],43:[function(t,e){function r(){i.call(this)}function n(t,e,r){t.length<40?o.utf8.write(t,e,r):e.utf8Write(t,r)}e.exports=r;var i=t(42);(r.prototype=Object.create(i.prototype)).constructor=r;var o=t(39),s=o.Buffer;r.alloc=function(t){return(r.alloc=o.u)(t)};var a=s&&s.prototype instanceof Uint8Array&&"set"===s.prototype.set.name?function(t,e,r){e.set(t,r)}:function(t,e,r){if(t.copy)t.copy(e,r,0,t.length);else for(var n=0;n<t.length;)e[r++]=t[n++]};r.prototype.bytes=function(t){o.isString(t)&&(t=o.p(t,"base64"));var e=t.length>>>0;return this.uint32(e),e&&this.v(a,e,t),this},r.prototype.string=function(t){var e=s.byteLength(t);return this.uint32(e),e&&this.v(n,e,t),this}},{39:39,42:42}]},{},[19])}("object"==typeof window&&window||"object"==typeof self&&self||this);
//# sourceMappingURL=protobuf.min.js.map
