<template>
  <section class="main fixed white_bg" style="position: fixed">
    <t-header title="双向见证" @back="back"></t-header>
    <article v-show="pageState == 1" class="content">
      <div class="video_tippage">
        <div class="title spel">
          <h3>将由见证人员与您进行视频连线</h3>
          <p>请做好以下准备</p>
        </div>
        <ul class="witness_list">
          <li>
            <i class="icon"></i>
            <p>光线充足的位置</p>
          </li>
          <li>
            <i class="icon"></i>
            <p>推荐使用WIFI网络</p>
          </li>
          <li>
            <i class="icon"></i>
            <p>周遭环境安静</p>
          </li>
          <!-- <li>
            <i class="icon"></i>
            <p>视频认证时间<em class="imp">周一至周五 9:00 ~ 22:00</em></p>
          </li> -->
        </ul>
      </div>
    </article>
    <article v-show="pageState == 3" class="content">
      <div class="result_page">
        <div class="result_tips">
          <div class="icon vedio_ok"></div>
          <h5>恭喜! 视频见证已通过</h5>
          <p>您可以继续下一步</p>
        </div>
      </div>
    </article>
    <article v-show="pageState == 4" class="content">
      <div class="result_page">
        <div class="result_tips">
          <div class="icon vedio_error"></div>
          <h5>视频见证未通过</h5>
          <p>如有疑问请联系客服热线 956011</p>
        </div>
        <div class="reject_txtinfo">
          <h5 class="title">原因</h5>
          <p v-for="(item, index) in rejectReason" :key="index">
            {{ index + 1 }}、{{ item }}
          </p>
        </div>
      </div>
    </article>
    <footer v-if="pageState != 2" class="footer">
      <div class="ce_btn">
        <a v-if="pageState == 1" class="p_button" @click="toRegist">开始认证</a>
        <a v-else-if="pageState == 3" class="p_button" @click="nextClick"
          >下一步</a
        >
        <a v-else-if="pageState == 4" class="p_button" @click="rejectNext">{{
          notNext ? '重新见证' : '重新提交'
        }}</a>
      </div>
    </footer>
    <witnessStatus
      v-if="pageState == 2"
      :user-info="regResult"
      @videoCallBack="videoCallBack"
      @cancel="pageState = 1"
    ></witnessStatus>
  </section>
</template>

<script>
import { videoRegist, getVideoResult } from '@/service/service.js';
import witnessStatus from './witnessAuth/two/witnessStatus';
import { EVENT_NAME } from '@/common/formEnum';
export default {
  name: 'WitnessTwo',
  inject: ['eventMessage'],
  components: {
    witnessStatus
  },
  data() {
    return {
      pageState: 1, // 1准备页面  2排队（h5）  3成功页面  4失败页面
      regResult: {},
      rejectReason: [],
      notNext: ''
    };
  },
  // created() {
  //   getDictData().then((data) => {
  //     if (data.code == 0) {
  //       this.dictData = data.data;
  //     } else {
  //       _hvueAlert({ mes: data.msg });
  //     }
  //   });
  // },
  deactivated() {
    this.pageState = 1;
  },
  methods: {
    back() {
      console.log(123);
    },

    startTwoVideo() {
      if ($hvue.platform == 0) {
        this.pageState = 2;
        return;
      }
      window.videoCallBack = this.videoCallBack;
      var param = {
        userId: this.regResult.regFlowNo, // 用户编号
        userName: this.regResult.clientName, // 用户名称
        orgId: this.regResult.branchNo, // 营业部编号
        netWorkStatus: 'WIFI', // 网络状态 (可不填
        url:
          $hvue.customConfig.video.videoServer +
          '/wa-queue-server/servlet/json?', // 连接排队BUS的服务器地址 (ios必须要在url加上?不然无法识别
        moduleName: $hvue.customConfig.moduleName, // 必须为open
        funcNo: '60005', // 双向视频见证
        isRejectToH5: '1', //原生是否处理透明通道信息 默认为0
        isNewView: '0', // 是否使用vue版本界面
        isShowHeadRect: '0', // 是否视频见证中显示头像取景框方便客户对准	N	1：显示；其他都隐藏（目前4.0视频界面使用）
        version: '4.0', // 排队bus版本
        mainColor: '#fa443a', //视频见证主题颜色
        // 统一视频兼容
        user_id: this.regResult.regFlowNo,
        branch_id: this.regResult.branchNo,
        branchno: this.regResult.branchNo,
        user_name: this.regResult.clientName,
        client_id: this.regResult.regFlowNo,
        client_name: this.regResult.clientName,

        // id_no: this.flowOutputInfo.inProperty.idNo,
        // mobile_no: this.flowOutputInfo.inProperty.mobileTel,
        appId: $hvue.customConfig.video.anychat.appId,
        origin: $hvue.platform,
        videoType: $hvue.customConfig.video.videoType,
        requestParam: `videoType=${$hvue.customConfig.video.videoType}`,
        biz_type: this.regResult.bizType,
        bizType: this.regResult.bizType,
        business_type: this.regResult.bizType,
        business_code: this.regResult.bizType,
        useTsyp: $hvue.customConfig.video.useTysp,
        requestHeaders: {
          'tk-jwt-authorization': this.regResult.jwtToken
        }
      };
      let result = $h.callMessageNative(param);
      if (result.error_no !== '0') {
        console.log(result.error_info);
      }
    },

    toRegist() {
      this.eventMessage(this, 'closeReject');
      let param = {
        flowToken: sessionStorage.getItem('TKFlowToken'),
        custId: $h.getSession('authorization'),
        // businessType: this.flowOutputInfo.inProperty.bizType,
        // custName: this.flowOutputInfo.inProperty.clientName,
        // branchNo: this.branchNo,
        // preBranchNo: this.branchNo,
        operationType: '1' // 操作类别，1、见证；2、审核
        // regData: JSON.stringify(this.translateDict(regData))
      };
      videoRegist(param)
        .then((data) => {
          if (data.code == '0') {
            if (!data.data) {
              _hvueAlert({ mes: '注册失败' });
              return;
            }
            this.regResult = Object.assign(this, data.data);
            this.regResult = Object.assign(
              this.regResult,
              data.data.clientInfo
            );
            // this.regResult.branchNo = this.regResult.preBranchNo;
            $h.setSession('videoToken', this.regResult.jwtToken);
            this.startTwoVideo();
          } else {
            _hvueAlert({ mes: data.msg });
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },

    videoCallBack(msg = {}) {
      this.pageState = 1;
      console.log(msg);
      let message = JSON.parse(msg.message || '');
      if (message.msgNo == 0 || message.msgNo == 1) {
        this.getVideoResult();
      }
    },

    nextClick() {
      this.eventMessage(this, EVENT_NAME.NEXT_STEP);
    },

    rejectNext() {
      if (!this.notNext) {
        this.nextClick();
      } else {
        this.pageState = 1;
      }
    },

    rejectHandler(msgArr, notNext) {
      // let rejectReason = '';
      // msgArr.forEach((a, index) => {
      //   rejectReason += `${index + 1}、${a}<br>`;
      // });
      this.eventMessage(this, EVENT_NAME.BACK_BTN, { display: false });
      this.pageState = 4;
      this.notNext = notNext;
      this.rejectReason = msgArr;

      // _hvueAlert({
      //   mes: `<div style="text-align:left;">驳回：<br>${rejectReason}</div>`,
      //   callback: () => {
      //     if (!notNext) {
      //       this.nextClick();
      //     }
      //   }
      // });
    },
    getVideoResult() {
      getVideoResult({
        regFlowNo: this.regResult.regFlowNo,
        flowToken: sessionStorage.getItem('TKFlowToken'),
        source: $hvue.platform === '0' ? '3' : $hvue.platform
      }).then((data) => {
        if (data.code == 0) {
          let result = data.data;
          this.eventMessage(this, EVENT_NAME.BACK_BTN, { display: true });
          if (result.opFlowStatus == 2) {
            // 1待处理 2通过 3驳回
            this.pageState = 3;
          } else if (result.opFlowStatus == 3) {
            this.rejectHandler(result.witnessMsg);
          }
        } else {
          if (data.code == 2002) {
            //仅仅驳回了视频，算组件内错误，不需要调用流程引擎下一步接口
            this.rejectHandler(data.msg.split(';'), true);
          } else {
            _hvueAlert({ mes: data.msg });
          }
        }
      });
    }
  }
};
</script>

<style></style>
