<template>
  <section class="main fixed" data-page="home">
    <article class="content">
      <div class="com_title">
        <h5>请选择新增的证券账户</h5>
      </div>
      <div>
        <div
          v-for="(item, index) in accountList"
          :key="index"
          class="acct_status_item"
        >
          <div class="acct_add_tit">
            <p>{{ item.exchangeTypeName }}</p>
            <div class="opea">
              <span
                class="icon_radio"
                :class="{
                  disabled: !item.operateTypeInfos.filter(
                    (it) => it.operateType === '1'
                  )[0].available,
                  checked: item.operateTypeInfos.filter(
                    (it) => it.operateType === '1'
                  )[0].checked
                }"
                @click="zkAccount(item, index)"
                >新增</span
              >
              <span
                class="icon_radio"
                :class="{
                  disabled: !item.operateTypeInfos.filter(
                    (it) => it.operateType === '2'
                  )[0].available,
                  checked: item.operateTypeInfos.filter(
                    (it) => it.operateType === '2'
                  )[0].checked
                }"
                @click="jgAccount(item, index)"
                >加挂</span
              >
            </div>
          </div>
          <div
            v-if="
              item.operateTypeInfos.filter((it) => it.operateType === '2')[0]
                .checked
            "
            class="acct_add_cont"
          >
            <div class="input_text">
              <input
                v-if="
                  item.operateTypeInfos.filter(
                    (it) => it.operateType === '2'
                  )[0].stockAccList.length === 0
                "
                v-model="
                  item.operateTypeInfos.filter(
                    (it) => it.operateType === '2'
                  )[0].stockAccount
                "
                class="t1"
                type="text"
                :placeholder="'请填写' + item.exchangeTypeName"
              />
              <div
                v-if="
                  item.operateTypeInfos.filter(
                    (it) => it.operateType === '2'
                  )[0].stockAccList.length > 0
                "
                class="dropdown"
                :placeholder="'请选择' + item.exchangeTypeName"
                @click="
                  chooseAccount(
                    item.operateTypeInfos.filter(
                      (it) => it.operateType === '2'
                    )[0].stockAccList,
                    index
                  )
                "
              >
                <span
                  v-if="
                    item.operateTypeInfos.filter(
                      (it) => it.operateType === '2'
                    )[0].stockAccount
                  "
                  >{{
                    item.operateTypeInfos.filter(
                      (it) => it.operateType === '2'
                    )[0].stockAccount
                  }}</span
                >
              </div>
            </div>
          </div>
          <div v-if="item.tips" class="imp_c_tips">
            <p class="warn">{{ item.tips }}</p>
          </div>
        </div>
      </div>
      <div class="tip_txtbox">
        <p>温馨提示：</p>
        <p>1、如您从未开立过证券账户，请选择新开所需类别证券账户。</p>
        <p>
          2、如已开立，建议加挂已开立的证券账户，加挂已开立上海证券账户，请确定已在原指定券商办理撤销指定交易手续，否则可能影响交易。
        </p>
        <p>
          3、如您通过撤指定或转托管等方式转入资产，请务必加挂转入资产所登记的证券账户。加挂完成后，请务必查询转入资产的入账情况。如未未入账，请尽快联系956006或开户营业部。
        </p>
      </div>
    </article>
    <div v-show="showSelect">
      <div class="dialog_overlay"></div>
      <div class="layer_box" style="position: fixed">
        <div class="layer_tit">
          <h3>请选择已有账号</h3>
          <a class="close" @click="showSelect = false"></a>
        </div>
        <div class="layer_cont">
          <ul class="select_list">
            <li
              v-for="(item, index) in selectList"
              :key="index"
              @click="chcekAccount(item)"
            >
              <span>{{ item.stockAccount }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import { createAndHangStockAccountQry } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'ZkjgAccountSelection',
  inject: ['eventMessage'],
  data() {
    return {
      acodeAccount: '',
      showSelect: false,
      selectList: [],
      nowIndex: 0,
      accountList: []
    };
  },
  computed: {
    openAccList() {
      let arr = [];
      this.accountList.forEach((item) => {
        for (let {
          checked,
          exchangeType,
          assetProp,
          holderKind,
          operateType,
          stockAccount
        } of item.operateTypeInfos) {
          if (checked) {
            arr.push({
              exchangeType,
              stockAccount,
              assetProp,
              holderKind,
              operateType
            });
          }
        }
      });
      // if (arr.length > 0) {
      //   this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: true });
      // }
      return arr;
    }
  },
  watch: {
    openAccList(val) {
      console.log(val);
      // 是否加挂存在未填写账户
      let flag = false;
      let jgArr = val.filter((item) => {
        if (item.operateType === '2') {
          if (item.stockAccount) {
            return false;
          } else {
            return true;
          }
        }
        return false;
      });
      console.log(jgArr);
      if (jgArr.length > 0) {
        // 存在未填写账户
        flag = true;
      } else {
        flag = false;
      }
      if (JSON.parse(JSON.stringify(val)).length > 0) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: true });
      } else {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
      }
      if (flag) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          display: true,
          btnStatus: 2,
          data: () => {
            _hvueToast({ mes: '加挂账号不能为空' });
          }
        });
        return;
      }
      this.$emit('change', {
        acodeAccount: this.acodeAccount,
        selectedAccountsData: JSON.stringify(val)
      });
    }
  },
  mounted() {
    if (this.openAccList.length === 0) {
      this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    }
    this.renderingView();
  },
  methods: {
    renderingView() {
      createAndHangStockAccountQry().then((res) => {
        this.acodeAccount = res.data.acodeAccount;
        this.accountList = res.data.stockAccList;
      });
    },

    chooseAccount(accountList, index) {
      this.nowIndex = index;
      this.selectList = accountList;
      this.showSelect = true;
    },

    chcekAccount(item) {
      this.accountList[this.nowIndex].operateTypeInfos.forEach((it) => {
        if (it.operateType === '2') {
          this.$set(it, 'stockAccount', item.stockAccount);
        }
      });
      this.showSelect = false;
    },

    zkAccount(item, index) {
      if (
        !this.accountList[index].operateTypeInfos.filter(
          (it) => it.operateType === '1'
        )[0].available
      ) {
        return;
      }
      this.accountList[index].operateTypeInfos.forEach((it) => {
        if (it.operateType === '1') {
          if (it.checked) {
            this.$set(it, 'checked', false);
          } else {
            this.$set(it, 'checked', true);
          }
        } else {
          this.$set(it, 'checked', false);
        }
      });
    },

    jgAccount(item, index) {
      if (
        !this.accountList[index].operateTypeInfos.filter(
          (it) => it.operateType === '2'
        )[0].available
      ) {
        return;
      }
      this.accountList[index].operateTypeInfos.forEach((it) => {
        if (it.operateType === '2') {
          if (it.checked) {
            this.$set(it, 'checked', false);
          } else {
            this.$set(it, 'checked', true);
          }
        } else {
          this.$set(it, 'checked', false);
        }
      });
    }
  }
};
</script>
