<template>
  <div>
    <div class="survey_wrapper" ref="surveyList">
      <div class="surveyHint" v-show="this.surveyData.surveyTitle">
        1-4题填写说明：账户资金规模填写截至报告日期账户中用于股票（基金、存托凭证）交易的资产总额，包括自有资金和杠杆资金，以万元为单位。对于产品账户，资金规模、来源、占比均按产品维度进行填报。
        <br />资金来源从下列选项中选择填写，可以选择多项：（1）自有资金;（2）募集资金;（3）杠杆资金;（4）其他。产品管理人跟投产品的资金视作募集资金。
      </div>
      <template v-for="(item, i) in questionArr">
        <div :key="item.questionId" class="question_wrapper" :class="{ error: item.questionId == errInfo.id }"
          v-show="!item.hidden" :ref="`question${item.questionId}`">
          <div class="questionTitle" :class="{
            disabledTitle:
              disabledIds.includes(item.canDisabledId) || item.disabled
          }">
            <i>*</i>{{ i }}、<span :ref="`title${item.questionId}`"></span>
          </div>
          <template v-if="item.type == 'input'">
            <van-field :disabled="disabledIds.includes(item.canDisabledId) || item.disabled
              " :value="item.data" @input="changeVal($event, item, i)" label="" :type="item.inputType"
              placeholder="请输入" autosizeId :maxlength="item.maxLength" :border="false"
              @blur="blurCheck($event, item, i)" />
          </template>
          <template v-else-if="item.type == 'radio'">
            <van-radio-group :disabled="disabledIds.includes(item.canDisabledId)" v-model="item.data"
              @change="changeRadio($event)">
              <van-radio v-for="(option, o) in item.options" :disabled="isquan &&
                item.questionTitle.includes('主策略类型：当选择“其他”时') &&
                o === 0" :key="item.questionId + (o + 1)" :name="item.questionId + (o + 1)"
                :class="{ checked: item.data === item.questionId + (o + 1) }" checked-color="#FF2840">{{ option
                }}</van-radio>
            </van-radio-group>
            <div v-if="item.options[Number(String(item.data).substr(3)) - 1] === '其他'
              ">
              <van-field :value="item.other" @input="changeVal($event, item, i)" label="" :type="item.inputType"
                placeholder="请输入" autosize :maxlength="item.maxLength" :border="false" />
            </div>
          </template>
          <template v-else-if="item.type == 'checkbox'">
            <van-checkbox-group v-model="item.data" :disabled="item.disabled || disabledIds.includes(item.canDisabledId)"
              :max="item.max || 0" @change="changeCheckbox($event, item, i)">
              <van-checkbox v-for="(option, o) in item.options" :key="item.questionId + (o + 1)"
                :disabled="checkboxDisables.includes(option)" :name="item.questionId + (o + 1)"
                :class="{ checked: item.data.includes(item.questionId + (o + 1)) }" shape="square"
                checked-color="#FF2840">{{ option }} </van-checkbox>
            </van-checkbox-group>
            <div v-if="item.data.filter((v) => item.options[Number(v.substr(3)) - 1] === '其他').length">
              <van-field :value="item.other" @input="changeVal($event, item, i)" label="" :type="item.inputType"
                placeholder="请输入" autosize :maxlength="item.maxLength" :border="false" />
            </div>
          </template>
        </div>
      </template>
      <div class="button" @click="beforeCommit" v-if="this.surveyData.surveyTitle">
        提交
      </div>
    </div>
  </div>
</template>

<script>
import survey1 from './components/survey1';
import survey2 from './components/survey2';
import survey3 from './components/survey3';
import defaultAnswer from './components/defaultAnswer';
import {
  clientInfoQryV2,
  surveySubmit,
  stockAccountList,
  surveyJourQuery,
  surveyJourSubmit,
  agreementSignStatus,
  addCommonMark // 留痕
} from '@/service/service';
import { MARK_TYPE } from '@/common/enumeration';
import { jumpThirdPartyUrl, getInstantToken, toFixedMoneyFormat } from '@/common/util';
import { SYS_CONFIG } from '@/common/enumeration';

export default {
  data() {
    return {
      disabledIds: [],
      checkboxDisables: [],
      surveyData: {},
      questionArr: [],
      submitData: {},
      errInfo: {},
      isFastRender: false, // 是否是一键抄写首次渲染
      isquan: true, //是否是量化交易是否选择了是
      questionIds: 122,
      onAlert: false,
    };
  },
  props: {
    //系统编号
    sysNo: {
      type: String,
      default: ''
    }
  },
  watch: {
    questionArr: {
      handler(newval) {
        // 以下为出发部分题无法操作的条件
        let disItem1 = newval?.filter(
          (v) => v.questionTitle === '是否量化交易'
        )[0];
        let arr = [...this.disabledIds];
        if (disItem1.data?.slice(-1) === '2') {
          arr.push('是否量化交易');
          arr = [...new Set(arr)];
        } else {
          arr = arr.filter((v) => v !== '是否量化交易');
        }

        // 仅当第4题其他资金规模大于0时才需要输入第5题
        let disItem2 = newval?.filter((v) =>
          v.questionTitle.includes('其他资金规模')
        )[0];
        if ((disItem2.data || 0) == 0) {
          arr.push('其他资金规模');
          arr = [...new Set(arr)];
        } else {
          arr = arr.filter((v) => v !== '其他资金规模');
        }

        // 仅当第3题杠杆资金规模大于0时才需要输入第6题
        let disItem3 = newval?.filter((v) =>
          v.questionTitle.includes('杠杆资金规模')
        )[0];
        if ((disItem3.data || 0) == 0) {
          arr.push('杠杆资金规模');
          arr = [...new Set(arr)];
        } else {
          arr = arr?.filter((v) => v !== '杠杆资金规模');
        }
        this.disabledIds = arr;
      },
      deep: true
    }
  },
  mounted() {
    const initialData = this.$options.data();
    Object.keys(initialData).forEach((key) => {
      if (!this[key] || this[key] === null || this[key] === undefined) {
        this[key] = initialData[key];
      }
    });
    this.init();
  },
  methods: {
    init() {
      clientInfoQryV2()
        .then((res) => {
          let survey = {};
          if (res.data.organFlag === '0') {
            this.questionIds = 122;
            // 个人
            survey = this.cloneDeep(survey1);
            this.checkboxDisables = ['国金恒生PB-PF1.0V202001.03', '国金迅投PB-V4.0.0', '国金络町-络町V2.1']
          } else if (['1', '2', '6'].includes(res.data.organFlag)) {
            // 机构户
            survey = this.cloneDeep(survey2);
            this.questionIds = 224;
          } else if (['3', '5'].includes(res.data.organFlag)) {
            // 产品户
            survey = this.cloneDeep(survey3);
            this.questionIds = 326;
          }
          survey.clientId = res.data.clientId;
          survey.fundAccount = res.data.clientId;
          this.surveyData = survey;
          this.questionArr = survey.questionArr;
          setTimeout(() => {
            survey.questionArr.forEach((item, i) => {
              this.$refs[`title${item.questionId}`][0].innerHTML =
                item.questionTitle;
            });
          }, 500);
          this.getStockAccountList();
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    },

    getStockAccountList() {
      stockAccountList()
        .then((res) => {
          let shArr = [];
          let szArr = [];
          if (res.data) {
            res.data.forEach((item) => {
              if (
                item.exchangeType === '1' &&
                item.holderStatus === '0' &&
                ['0', '1', '4'].includes(item.holderKind) // 1:封闭式基金
              ) {
                // 上海正常股东户
                shArr.push(item);
              } else if (
                item.exchangeType === '2' &&
                item.holderStatus === '0' &&
                ['0', '1', '4'].includes(item.holderKind) // 1:封闭式基金
              ) {
                // 深圳正常股东户
                szArr.push(item);
              }
            });
            if (shArr.length === 0 && szArr.length === 0) {
              // 用户没有深A和沪A
              this.$TAlert({
                title: '温馨提示',
                tips: '您当前没有可正常使用的股东账户，还请先新开/下挂股东户后再来填报。',
                confirm: () => {
                  if (vm.$store.state.user.appId) {
                    import('@/common/flowMixinV2.js').then((a) => {
                      a.initFlow.call(this, {
                        bizType: '010044',
                        initJumpMode: '0'
                      });
                    });
                  } else {
                    location.href =
                      'https://linkm.yongjinbao.com.cn/download/app-download.html?action=%7B%22type%22%3A%22web%22%2C%22param%22%3A%7B%22url%22%3A%22https%3A%2F%2Fwebapps.yongjinbao.com.cn%2Fyjbwebmoc%2Fmoc%2Fweb%2Fmoc-pro%2Fbuild%2FgoGroupView.html%3FgroupName%3DstockAForWeb%26app_id%3Dyjb3.0%22%7D%2C%22precondition%22%3A%7B%22needToken%22%3A1%2C%22loginType%22%3A1%7D%7D&useDownload=1';
                  }
                }
              });
              return;
            } else if (shArr.length === 0 || szArr.length === 0) {
              // 用户没有深A或沪A
              _hvueConfirm({
                title: '温馨提示',
                mes: `${shArr.length > 0 ? '深圳' : '上海'
                  }市场股东户暂未开通。您可以先进行填报，或完善股东户后再来填报。`,
                opts: [
                  {
                    txt: '完善账户',
                    color: '#333333',
                    callback: () => {
                      if (vm.$store.state.user.appId) {
                        import('@/common/flowMixinV2.js').then((a) => {
                          a.initFlow.call(this, {
                            bizType: '010044',
                            initJumpMode: '0'
                          });
                        });
                      } else {
                        location.href =
                          'https://linkm.yongjinbao.com.cn/download/app-download.html?action=%7B%22type%22%3A%22web%22%2C%22param%22%3A%7B%22url%22%3A%22https%3A%2F%2Fwebapps.yongjinbao.com.cn%2Fyjbwebmoc%2Fmoc%2Fweb%2Fmoc-pro%2Fbuild%2FgoGroupView.html%3FgroupName%3DstockAForWeb%26app_id%3Dyjb3.0%22%7D%2C%22precondition%22%3A%7B%22needToken%22%3A1%2C%22loginType%22%3A1%7D%7D&useDownload=1';
                      }
                    }
                  },
                  {
                    txt: '继续填报',
                    callback: () => {
                      this.historicalQuery();
                    }
                  }
                ]
              });
              return;
            }
            // 历史问卷填写查询
            this.historicalQuery();
          }
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    },
    historicalQuery() {
      const { surveyId, version, clientId } = this.surveyData;
      let param = {
        surveyId,
        version,
        clientId
      };
      console.log('进入历史查询')
      surveyJourQuery(param).then((res) => {
        console.log('进入历史查询res==', res)
        if (res.data && res.data.content && res.data.surveyId === surveyId) {
          _hvueConfirm({
            title: '温馨提示',
            mes: `您已做过程序化交易报备问卷，您可以点击下方快速填报按钮，在原先问卷的基础上修改答案，您也可以重新填报问卷。`,
            opts: [
              {
                txt: '重新填报',
                color: '#333333'
              },
              {
                txt: '快速填报',
                callback: () => {
                  try {
                    this.isFastRender = true;
                    this.surveyData = JSON.parse(res.data.content);
                    let arr = this.surveyData.questionArr;
                    // arr[0].data = `${surveyId}002`;
                    // 清空三个题
                    if (this.surveyData.version <= 5) {
                      this.questionArr = arr.map(item => {
                        if (['119', '219', '321',].includes(item.questionId)) {
                          item.data = [];
                          item.answerArr = [];
                          item.options = ['国金PTrade-PTrade1.0-ClientV202302',
                            '国金QMT-V1.0.0',
                            '国金卡方ATX-国金证券ATXV2.0.4',
                            '国金万得宏汇交易系统-GJZQWtt4.10_2023',
                            '国金恒生PB-PF1.0V202001.03',
                            '国金迅投PB-V4.0.0',
                            '国金络町-络町V2.1',
                            '国金证券智能算法平台-i2-ALGO1.0V202401.03.004',
                            // '其他'
                            '国金佣金宝-V8',
                            '国金全能行远航版-V9',
                            '同花顺-V9',
                            '国金太阳网上交易系统至强版经典款-V10',
                            '全能行APP端-V9',
                            '同花顺APP-V11',];
                          item.other = '';
                        } else if (['120', '220', '322',].includes(item.questionId)) {
                          item.data = [];
                          item.answerArr = [];
                          item.options = ['国金PTrade：杭州云纪网络科技有限公司',
                            '国金QMT：北京睿智融科控股有限公司',
                            '国金卡方ATX：上海卡方信息科技有限公司',
                            '国金万得宏汇交易系统：上海万得宏汇信息技术有限公司',
                            '国金恒生PB：恒生电子股份有限公司',
                            '国金迅投PB：北京睿智融科控股有限公司',
                            '国金络町：杭州络町软件科技有限责任公司',
                            '国金证券智能算法平台：恒生电子股份有限公司',
                            // '其他'
                            '国金佣金宝：国金证券股份有限公司',
                            '国金全能行远航版：浙江核新同花顺网络信息股份有限公司',
                            '同花顺：浙江核新同花顺网络信息股份有限公司',
                            '国金太阳网上交易系统至强版经典款：深圳市财富趋势科技股份有限公司',
                            '全能行APP端：浙江核新同花顺网络信息股份有限公司',
                            '同花顺APP：浙江核新同花顺网络信息股份有限公司',];
                          item.other = '';
                        } else if (['121', '221', '323'].includes(item.questionId)) {
                          item.data = [];
                          item.answerArr = [];
                          item.options = ['上海市浦东新区荷丹路130号世纪互联外高桥保税区荷丹数据中心', '上海浦东新区龙沪路399号金桥数据中心'];
                          item.other = '';
                        }
                        return item;
                      })
                    } else if (this.surveyData.version <= 6) {
                      this.questionArr = arr.map(item => {
                        if (['107', '207', '309',].includes(item.questionId)) {
                          item.data = [];
                          item.answerArr = [];
                          item.other = '';
                          item.options = ['股票', '基金', '存托凭证'];
                        } else if (['121', '221', '323'].includes(item.questionId)) {
                          item.data = [];
                          item.answerArr = [];
                          item.other = '';
                          item.hidden = true;
                          item.options = ['基本面分析策略（Alpha策略）', '日内回转策略', '套利类策略', '趋势类策略', '做市类策略', '其他'];
                        }
                        return item;
                      })
                    } else {
                      this.questionArr = arr;
                    }

                  } catch (error) { }
                }
              }
            ]
          });
        } else {
          console.log('进入自动填充')
          this.autoFill();
        }
      });
    },
    // 底仓增强自动填充答案
    autoFill() {
      console.log('defaultAnswer=====', defaultAnswer);
      if (this.sysNo == SYS_CONFIG.DCZQCL) {
        this.isFastRender = true;
        console.info('开始赋值=====');
        this.questionArr = this.cloneDeep(defaultAnswer);
      }
    },
    changeVal(e, item, i) {
      // 判断是否含有emoji表情
      const iconRule =
        /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi;
      if (iconRule.test(e)) {
        e = e.replace(iconRule, '');
      }

      let questionArr = [...this.questionArr];
      // 数字类型保留两位小数
      if (item.inputType === 'number') {
        e = e == '00' ? '0' : e;
        const regex = /^0\d+/;
        if (regex.test(e)) {
          e = String(Number(e));
        }
        e = e.indexOf('.') > 0 ? e.substr(0, e.indexOf('.') + 3) : e;
      }
      if (item.type === 'input') {
        item.data = e.replace(/；/g, ';');
      } else {
        item.other = e.replace(/；/g, ';');
      }

      questionArr = questionArr.map((v) => {
        if (v.questionId === item.questionId) {
          return item;
        }
        return v;
      });

      if (item.questionTitle.includes('其他资金规模') && e == 0) {
        questionArr = questionArr.map((v) => {
          if (v.canDisabledId === '其他资金规模') {
            v.data = '';
          }
          return v;
        });
      }
      if (item.questionTitle.includes('杠杆资金规模') && e == 0) {
        questionArr = questionArr.map((v) => {
          if (v.canDisabledId === '杠杆资金规模') {
            v.other = '';
            v.data = [];
          }
          return v;
        });
      }

      this.questionArr = questionArr;
      // this.$set(this.questionArr, i, item);
    },
    //
    changeCheckbox(e, item, i) {
      // 判断是否是可转债交易
      let questionArr = [...this.questionArr];

      if (item.questionTitle.includes('程序化交易软件名称及版本号')) {
        questionArr = questionArr.map(items => {
          if (items.questionId == (Number(item.questionId) + 1)) {

            items.data = item.data?.map(inItem => {
              return inItem.replace(item.questionId, items.questionId)
            });

          }
          if (items.questionId == (Number(item.questionId) + 2)) {
            let first = false;
            let sencond = false;
            let tempData = [];
            item.data?.map(inItem => {
              if (Number(inItem.slice(3, 10)) < 8) {
                first = true;
              }
              if (Number(inItem.slice(3, 10)) >= 8) {
                sencond = true;
              }
            });
            if (first) {
              tempData.push(items.questionId + '1')
            }
            if (sencond) {
              tempData.push(items.questionId + '2')
            }
            items.data = tempData;
          }
          return items;
        })
      }
      console.log('questionArr===============', questionArr);
      this.questionArr = questionArr;

    },
    // 通用弹框留痕
    alertCommon(opId, text) {
      this.$TAlert({
        title: '请您再次确认',
        tips: text,
        hasCancel: true,
        cancelBtn: '确认',
        confirmBtn: '重新选择',
        cancel: () => {
          this.onAlert = false;
          // 点击了确认，进行留痕
          addCommonMark({
            bizType: "999999",
            opId: opId,
            opInfo: '确认',
            addition: text,
          }).catch((err) => {
            _hvueToast({
              mes: '提交失败，请稍后重试。'
            });
          });
        },
        confirm: () => {
          // this.onAlert = false;
          // let questionArr = [...this.questionArr];
          // questionArr = questionArr.map((v) => {
          //   if (v.questionId === item.questionId) {
          //     return { ...item, data: 0 };
          //   }
          //   return v;
          // });
          // this.questionArr = questionArr;
        }
      });
    },
    // 弹框留痕
    alerts(item, value, name) {
      this.$TAlert({
        title: '请您再次确认',
        tipsHtml: `<p>您填写的${name}为<br/><b style='color:red;font-weight: bold;'>${Number(value).toLocaleString()}</b>万元</p>`,
        // tips: '您填写的' + name + '为' +<p  style="color:red;font-weight: bold;" >toFixedMoneyFormat(value,2)</p>+  + '万元',
        hasCancel: true,
        cancelBtn: '确认',
        confirmBtn: '去修改',
        cancel: () => {
          this.onAlert = false;
          // 点击了确认，进行留痕
          addCommonMark({
            bizType: "999999",
            opId: 1105120,
            opInfo: '确认',
            addition: '您填写的' + name + '为' + Number(value).toLocaleString() + '万元',
          }).catch((err) => {
            _hvueToast({
              mes: '提交失败，请稍后重试。'
            });
          });
        },
        confirm: () => {
          this.onAlert = false;
          let questionArr = [...this.questionArr];
          questionArr = questionArr.map((v) => {
            if (v.questionId === item.questionId) {
              return { ...item, data: 0 };
            }
            return v;
          });
          this.questionArr = questionArr;
        }
      });
    },
    blurCheck(e, item, i) {
      let value = e.target.value;
      if (item.questionTitle.includes('自有资金规模') && value >= 1000 && this.onAlert == false) {
        this.onAlert = true;
        this.alerts(item, value, '自有资金规模');
      }
      if (item.questionTitle.includes('募集资金规模') && value >= 1000 && this.onAlert == false) {
        this.onAlert = true;
        this.alerts(item, value, '募集资金规模');
      }
      if (item.questionTitle.includes('杠杆资金规模') && value >= 1000 && this.onAlert == false) {
        this.onAlert = true;
        this.alerts(item, value, '杠杆资金规模');
      }
      if (item.questionTitle.includes('其他资金规模') && value >= 1000 && this.onAlert == false) {
        this.onAlert = true;
        this.alerts(item, value, '其他资金规模');
      }
    },
    changeRadio(e) {
      setTimeout(() => {
        this.isFastRender = false;
      }, 1000);
      if (this.isFastRender) return;
      let arr = [...this.questionArr];
      if (e === '1082') {
        arr[9].data = '1091';
        arr[10].data = '非量化交易';
        arr[11].data = ['1111'];
        arr[12].data = '非量化交易';
        this.questionArr = arr;
        this.isquan = false;
      } else if (e === '1081') {
        arr[9].data = '';
        arr[10].data = '';
        arr[11].data = [];
        arr[12].data = '';
        this.questionArr = arr;
        this.isquan = true;
      } else if (e === '2082') {
        arr[9].data = '2091';
        arr[10].data = '非量化交易';
        arr[11].data = ['2111'];
        arr[12].data = '非量化交易';
        this.questionArr = arr;
        this.isquan = false;
      } else if (e === '2081') {
        arr[9].data = '';
        arr[10].data = '';
        arr[11].data = [];
        arr[12].data = '';
        this.questionArr = arr;
        this.isquan = true;
      } else if (e === '3102') {
        arr[11].data = '3111';
        arr[12].data = '非量化交易';
        arr[13].data = ['3131'];
        arr[14].data = '非量化交易';
        this.questionArr = arr;
        this.isquan = false;
      } else if (e === '3101') {
        arr[11].data = '';
        arr[12].data = '';
        arr[13].data = [];
        arr[14].data = '';
        this.questionArr = arr;
        this.isquan = true;
      } else if (['1171', '1172', '2171', '2172', '3191', '3192'].includes(e)) {
        // 弹框留痕
        const text = ['1171', '2171', '3191'].includes(e) ? '您勾选的账户最高申报速率（笔/秒）为：500笔及以上' : '您勾选的账户最高申报速率（笔/秒）为：300笔至499笔'
        this.alertCommon('1105123', text)
      } else if (['1181', '1182', '2181', '2182', '3201', '3202'].includes(e)) {
        // 弹框留痕
        const text = ['1181', '2181', '3201'].includes(e) ? '您勾选的账户单日最高申报笔数为：25000笔及以上' : '您勾选的账户单日最高申报笔数为：20000笔至24999笔'
        this.alertCommon('1105122', text)
      } else if (['1231', '2251', '3271'].includes(e)) {
        this.questionArr = arr.map(items => {
          if (items.questionId == this.questionIds) {
            items.hidden = false;
            // console.log('设置false');
          }
          return items;
        })
      } else if (['1232', '2252', '3272'].includes(e)) {
        this.questionArr = arr.map(items => {
          if (items.questionId == this.questionIds) {
            items.hidden = true;
            items.data = [];
            items.answerArr = [];
            items.other = '';
          }
          return items;
        })
      }
    },
    verifyData() {
      let err = { id: '', msg: '' };
      let flag = false;
      for (let i = 0; i < this.questionArr.length; i++) {
        let item = this.questionArr[i];
        // 排除可不用输入的题
        if (this.disabledIds.includes(item.canDisabledId) || item.hidden) {
          continue;
        }
        //data里没值
        if (item.data == '' || item.data == null || item.data.length === 0) {
          err = {
            id: item.questionId,
            msg: `您的第${Number(item.questionId.slice(-2))}题未完成`
          };
          break;
        }
        // data有值但是选择了其他，且其他没有值
        let haveOtherR =
          item.type === 'radio' &&
          item.options[Number(String(item.data).substr(3)) - 1] === '其他';
        let haveOtherC =
          item.type === 'checkbox' &&
          item.data.filter(
            (v) => item.options[Number(v.substr(3)) - 1] === '其他'
          ).length > 0;
        if ((haveOtherR || haveOtherC) && !item.other) {
          err = {
            id: item.questionId,
            msg: `您的第${Number(
              item.questionId.substr(1)
            )}题未完成，请填写补充内容`
          };
          break;
        }
        // 输入框为金额时需要大于0,或大于限制的最小值
        if (
          item.inputType === 'number' &&
          (haveOtherR || haveOtherC || item.type === 'input')
        ) {
          let val = item.type === 'input' ? item.data : item.other;
          let min = item.min || 0;
          if (eval(`${val}${item.minType || '<='}${min}`)) {
            // 兼容有些题允许为0
            err = {
              id: item.questionId,
              msg: `第${Number(
                item.questionId.substr(1)
              )}题输入内容需大于${min}`
            };
            break;
          }
        }
        if (
          item.questionTitle.includes('主策略类型：') &&
          ['3', '4', '6'].includes(item.data.substring(3))
        ) {
          flag = true;
        }
        if (
          item.questionTitle.includes('辅策略类型：') &&
          item.data.filter((v) => ['3', '4', '6'].includes(v.substring(3)))
            .length > 0
        ) {
          flag = true;
        }
        if (
          (item.questionTitle.includes('期货市场账户名称：') ||
            item.questionTitle.includes('期货市场账户代码：')) &&
          item.data.substring(3) === '1' &&
          flag
        ) {
          err = {
            id: item.questionId,
            msg: `您的期货市场账户名称、期货市场账户代码不可勾选无。您可以修改量化策略类型，或提供期货账户信息。`,
            useAlert: true
          };
          break;
        }
      }
      if (err.id) {
        if (err.useAlert) {
          this.$TAlert({ tips: err.msg });
        } else {
          _hvueToast({ mes: err.msg });
        }
        this.errInfo = err;
        console.log(this.$refs[`question${err.id}`][0].offsetTop);
        this.$refs['surveyList'].scrollTop =
          this.$refs[`question${err.id}`][0].offsetTop - 22;
        return false;
      }
      return true;
    },
    numFormat(num) {
      // 去除最后一位小数点
      let last = num.slice(-1);
      if (last === '.') {
        num = num.substr(0, num.length - 1);
      }
      return num;
    },
    beforeCommit() {
      if (!this.verifyData()) return;
      this.$TAlert({
        title: '',
        tips: '本人承诺所填信息真实有效，并自愿承担相应责任',
        hasCancel: true,
        cancelBtn: '返回修改',
        confirmBtn: '确认',
        cancel: () => { },
        confirm: () => {
          // 点击了确认，进行留痕
          addCommonMark({
            bizType: "999999",
            opId: 1105121,
            opInfo: '确认',
            addition: '本人承诺所填信息真实有效，并自愿承担相应责任。',
          }).then(() => {
            this.commit();
          }).catch((err) => {
            _hvueToast({
              mes: '提交失败，请稍后重试。'
            });
          });
        }
      });
    },
    commit() {
      let arr = this.questionArr;
      let originArr = this.questionArr;
      console.log('arr=======', arr)
      arr = arr.map((v) => {
        if (v.type === 'input') {
          v.answerArr = [
            {
              answerId: '',
              answerSn: '',
              answerText:
                v.inputType === 'number' ? this.numFormat(v.data) : v.data,
              answerSubText: ''
            }
          ];
        } else if (v.type === 'radio') {
          const text = v.options[Number(v.data.slice(3)) - 1];
          v.answerArr = [
            {
              answerId: v.data,
              answerSn: v.data.slice(3),
              answerText: text,
              answerSubText:
                text === '其他'
                  ? v.inputType === 'number'
                    ? this.numFormat(v.other)
                    : v.other
                  : ''
            }
          ];
        } else if (v.type === 'checkbox') {
          if (v.data.length) {
            v.answerArr = v.data.map((d) => {
              return {
                answerId: d,
                answerSn: d.slice(3),
                answerText: v.options[Number(d.slice(3)) - 1],
                answerSubText:
                  v.options[Number(d.slice(3)) - 1] === '其他'
                    ? v.inputType === 'number'
                      ? this.numFormat(v.other)
                      : v.other
                    : ''
              };
            });
          } else {
            v.answerArr = [
              {
                answerId: '',
                answerSn: '',
                answerText: '',
                answerSubText: ''
              }
            ];
          }
        }
        const { questionId, questionTitle, answerArr } = v;
        return {
          questionId,
          questionTitle,
          answerArr
        };
      });
      //判断是否客户选择了开通系统，添加包含逻辑（如Ptrade/QMT权限开通业务）
      if (this.sysNo !== '') {
        let checkErrorQuesArr = []; // 存放不合格的题目索引
        let regExp;
        if (this.sysNo === SYS_CONFIG.PTRADE) {
          regExp = /(.*ptrade)/i;
        } else if (this.sysNo === SYS_CONFIG.QMT) {
          regExp = /(.*qmt)/i;
        } else {
          regExp = /国金证券智能算法平台|恒生电子股份有限公司/;
        }
        arr.forEach(({ questionId, answerArr }, i) => {
          const sysQuesList = ['119', '120', '219', '210', '321', '322']; //包含系统校验的题目id
          if (sysQuesList.includes(questionId)) {
            const f = answerArr.some(
              ({ answerText, answerSubText }) =>
                regExp.test(answerText) || regExp.test(answerSubText)
            );
            if (!f) checkErrorQuesArr.push(questionId.slice(-2));
          }
        });
        if (checkErrorQuesArr.length !== 0) {
          this.$TAlert({
            title: '提示',
            tips: `检查到第${checkErrorQuesArr.join(
              '，'
            )}题与本次申请开通系统不匹配，请重新修改`,
            confirmBtn: '返回修改'
          });
          return;
        }
      }

      this.surveyData.questionArr = arr;
      surveySubmit(this.surveyData)
        .then(({ code }) => {
          if (code === 0) {
            // 存在sysNo，说明在权限开通业务中，无需重复签署协议
            if (this.sysNo === '' && this.surveyData.surveyId === '1') {
              // 仅个人户有签协议的逻辑
              this.checkSign();
            } else {
              this.$emit('success');
            }
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
      this.surveyData.questionArr = originArr;
      const { surveyId, clientId, version } = this.surveyData;
      let param = {
        surveyId,
        clientId,
        version,
        content: JSON.stringify(this.surveyData)
      };
      // 提交操作流水
      surveyJourSubmit(param);
    },
    checkSign() {
      // 是否签署过协议
      let param = {
        channel: '20001',
        agreementNo: location.host.includes('yjbtest.com')
          ? '**************'
          : '**************',
        accountId: this.surveyData.clientId,
        accountType: '2',
        entrustType: ''
      };
      agreementSignStatus(param).then(async (res) => {
        console.log(res);
        if (res?.data?.signStatus === '1') {
          this.$emit('success');
        } else {
          let url = location.host.includes('yjbtest.com')
            ? 'https://fzwebapps.yjbtest.com/yjbwebmoc/moc/web/moc-pro/build/contractSignView.html?agreementNo=**************'
            : 'https://webapps.yongjinbao.com.cn/yjbwebmoc/moc/web/moc-pro/build/contractSignView.html?agreementNo=**************';
          getInstantToken().then((res) => {
            const { appId, opStation, instantToken } = res;
            jumpThirdPartyUrl({
              url: `${url}&app_id=${appId}&op_station=${opStation}&instant_token=${instantToken}`
            });
          });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
@import './routinization.less';
</style>
