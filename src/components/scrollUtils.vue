<template>
  <div ref="container" class="list-container" :style="wrapperHeight">
    <div ref="wrapper" class="list-wrapper">
      <div class="scroll-content" :style="scrollContentStyle">
        <div>
          <div class="loadingWord pullDown" v-show="!inPullDown">
            <span class="iconfont icon-shangla">{{ beforePullDownWord }}</span>
          </div>
          <div class="loading pullDown" v-show="inPullDown">
            <img
              src="../../assets/images/project/load/loading.gif"
              alt=""
            /><span>{{ PullingDownWord }}</span>
          </div>
        </div>
        <slot></slot>
        <div>
          <div v-if="isNoData">
            <div class="loadingWord" v-show="!inPullUp && dataList.length > 0">
              <span class="iconfont icon-shangla" v-show="!inPullDown">{{
                beforePullUpWord
              }}</span>
            </div>
            <div class="loading" v-show="inPullUp">
              <img
                src="../../assets/images/project/load/loading.gif"
                alt=""
              /><span>{{ PullingUpWord }}</span>
            </div>
          </div>
          <div v-else>
            <div class="loadingWord">
              <span>{{ noDataWord }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="empty_box " v-show="clearData" style="">
            <div>
                <div class="no_data">
                    <img src="../../assets/images/no_data.png">
                    <h4>暂无数据～</h4>
                </div>
            </div>
        </div> -->
    <div class="no_data_page" v-show="clearData">
      <div class="no_data_box" style="padding-bottom: .3rem">
        <img src="@/assets/images/no_data.png" />
        <p>暂无数据~</p>
      </div>
    </div>
  </div>
</template>
<script>
import BScroll from 'better-scroll'

const PullingUpWord = '正在拼命加载中...'
const beforePullUpWord = '上拉加载更多'
const beforePullDownWord = '下拉加载更多'
const PullingDownWord = '加载中...'
const afterPullWord = '松开立即刷新'
const noDataWord = '没有更多数据了'
export default {
  props: {
    dataList: {
      type: Array,
      default: () => {}
    },
    probeType: {
      type: Number,
      default: 3
    },
    click: {
      type: Boolean,
      default: true
    },
    pullDownRefresh: {
      type: null,
      default: false
    },
    pullUpLoad: {
      type: null,
      default: false
    },
    height: {
      type: null,
      default: ''
    },
    footerHeight: {
      type: null,
      default: '0'
    }
  },
  data () {
    return {
      scroll: null, // 组件
      inPullUp: false, // 是否上拉
      inPullDown: false, // 是否下拉
      beforePullDownWord, // 下拉加载更多
      beforePullUpWord, // 上拉加载更新
      PullingUpWord, // 上拉加载提示
      PullingDownWord, // 下拉加载提示
      afterPullWord, // 松开提示
      isNoData: true, // 是否展示无数据
      noDataWord, // 无数据提示
      DOWN_CONFIG: {
        threshold: 60, // 下拉距离超过60px触发pullingDown事件
        stop: 40 // loading图高度
      },
      UP_CONFIG: {
        threshold: -60 // 上拉距离超过60px触发pullingUp事件
      },
      wrapperHeight: '', // 元素的高度
      scrollContentStyle: '', // 滚动框样式
      curPage: 1, // 当前页码
      numPerPage: 20, // 每页显示条数
      maxPage: 0, // 最大页数
      loadTime: 300, // 加载中停顿时间
      clearData: false
    }
  },
  created () {},
  mounted () {
    setTimeout(() => {
      /**
       * 动态获取盒子高度
       */
      let wraHeight = 0
      if (this.height) {
        wraHeight = this.height
      } else {
        wraHeight =
          document.documentElement.clientHeight -
          this.$refs.container.getBoundingClientRect().top
        if (this.footerHeight) {
          wraHeight =
            document.documentElement.clientHeight -
            this.$refs.container.getBoundingClientRect().top -
            Number(this.footerHeight)
        }
      }
      // 初始化滑动组件父元素高度
      this.wrapperHeight = {
        height: wraHeight + 'px'
      }
      // 初始化滑动组件高度（最小高度要大于父元素高度，否则数量不够时无法滑动）
      this.scrollContentStyle = {
        //  minHeight: wraHeight + 10 + "px"
      }
      this.initScroll()
      /**
       * 监听滚动距离
       */
      this.scroll.on('scroll', pos => {
        if (pos.y > this.DOWN_CONFIG.threshold) {
          // 下拉
          this.beforePullDownWord = afterPullWord
        } else if (
          pos.y - this.DOWN_CONFIG.threshold <
          this.DOWN_CONFIG.threshold
        ) {
          this.beforePullDownWord = beforePullDownWord
        }
        if (this.isNoData) {
          if (this.scroll.maxScrollY > pos.y + -this.UP_CONFIG.threshold) {
            // 上拉
            this.beforePullUpWord = afterPullWord
          } else if (
            this.scroll.maxScrollY - (pos.y + -this.UP_CONFIG.threshold) <
            -this.UP_CONFIG.threshold
          ) {
            this.beforePullUpWord = beforePullUpWord
          }
        }
      })

      // touchEnd（手指离开以后触发） 通过这个方法来监听下拉刷新
      this.scroll.on('touchEnd', pos => {
        // 下拉动作
        if (pos.y > this.DOWN_CONFIG.threshold) {
          if (this.inPullDown) {
            return false
          }
          this.inPullDown = true
          setTimeout(() => {
            this.beforePullDown()
            this.$emit('onPullDown', '当前状态：下拉加载更多')
          }, this.loadTime)
        }
        if (this.isNoData) {
          // 上拉加载 总高度>下拉的高度+100 触发加载更多
          if (this.scroll.maxScrollY > pos.y + -this.UP_CONFIG.threshold) {
            if (this.inPullUp) {
              return false
            }
            this.inPullUp = true
            setTimeout(() => {
              this.beforePullUp()
              this.$emit('onPullUp', '当前状态：上拉加载')
            }, this.loadTime)
          }
        }
      })
    }, 20)
  },
  methods: {
    initScroll () {
      if (!this.$refs.wrapper) {
        return
      }
      this.scroll = new BScroll(this.$refs.wrapper, {
        probeType: this.probeType,
        click: this.click,
        pullDownRefresh: this.pullDownRefresh || this.DOWN_CONFIG,
        pullUpLoad: this.pullUpLoad || this.UP_CONFIG
      })
    },
    beforePullUp () {
      this.beforePullUpWord = beforePullUpWord
      this.PullingUpWord = PullingUpWord
      this.curPage += 1
    },
    beforePullDown () {
      this.disable()
      this.curPage = 1
    },
    finish (type) {
      this['finish' + type]()
      this.enable()
      this['in' + type] = false
    },
    pageInitScroll (recordLen, isRefreshHeight) {
      // 隐藏暂无数据
      this.clearData = false
      // 当查询的数量小于分页的数据 或者 当前页码等于最大页数时，显示“没有更多数据”
      if (recordLen < this.numPerPage || this.curPage == this.maxPage) {
        this.isNoData = false
      } else {
        this.isNoData = true
      }
      this.finishPullDown()
      this.finishPullUp()
      this.enable()
      // 是否需要重新初始化高度，用于解决TAB切换时2个列表的高度不一致的问题
      // if(isRefreshHeight){
      //
      // }
      this.refreshHeight()
      setTimeout(() => {
        this.inPullDown = false
        this.inPullUp = false
      }, 200)
    },
    refreshHeight () {
      let wraHeight =
        document.documentElement.clientHeight -
        this.$refs.container.getBoundingClientRect().top
      if (this.footerHeight) {
        wraHeight = wraHeight - Number(this.footerHeight)
      }
      // 初始化滑动组件父元素高度
      this.wrapperHeight = {
        height: wraHeight + 'px'
      }
    },
    showViscNoData () {
      if (this.curPage == '1') {
        this.disable()
        this.clearData = true
        this.isNoData = true
        this.inPullDown = false
        this.inPullUp = false
      } else {
        this.pageInitScroll(0)
      }
    },
    resetNodataStatus: function () {
      this.clearData = false
    },
    disable () {
      this.scroll && this.scroll.disable()
    },
    enable () {
      this.scroll && this.scroll.enable()
    },
    refresh () {
      this.scroll && this.scroll.refresh()
    },
    finishPullDown () {
      this.scroll && this.scroll.finishPullDown()
    },
    finishPullUp () {
      this.scroll && this.scroll.finishPullUp()
    },
    scrollTo () {
      setTimeout(() => {
        this.scroll.scrollTo(0, 0)
      }, 0)
    }
  },
  watch: {
    dataList (val) {
      this.$nextTick(() => {
        this.refresh()
      })
    }
  }
}
</script>

<style>
.loadingWord {
  text-align: center;
  z-index: 100;
  width: 100%;
  height: 0.4rem;
  line-height: 0.4rem;
}

.loadingWord span {
  vertical-align: middle;
  margin-left: 0.14rem;
  font-size: 14px;
}

.loading {
  text-align: center;
  z-index: 100;
  width: 100%;
  height: 0.4rem;
  line-height: 0.4rem;
}

.loading img {
  margin: 0 auto;
  width: 0.2rem;
  height: 0.2rem;
  display: inline;
  vertical-align: middle;
}

.loading span {
  vertical-align: middle;
  font-size: 14px;
}

.list-container {
  position: relative;
}

.list-wrapper {
  position: absolute;
  z-index: 1;
  top: 0px;
  bottom: 0px;
  left: 0px;
  width: 100%;
  overflow: hidden;
}

.list-content {
  position: relative;
  z-index: 10;
  background: #fff;
}

.list-item {
  height: 60px;
  line-height: 60px;
  font-size: 18px;
  padding-left: 20px;
  border-bottom: 1px solid #e5e5e5;
}

.pulldown-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all;
}

.pullup-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
}

.pullDown {
  position: absolute;
  top: -40px;
  left: 0;
}

.pullDown-enter-active {
  transition: all 0.2s;
}

.pullDown-enter,
.pullDown-leave-active {
  transform: translateY(-100%);
  transition: all 0.2s;
}

.no_data {
  text-align: center;
  padding: 0.36rem 0.15rem 0.56rem;
  margin-top: 0rem;
  padding-top: 1rem;
}

.no_data > img {
  display: block;
  width: 1.7rem;
  margin: 0 auto;
}

.no_data h4 {
  line-height: 0.22rem;
  color: #999;
  font-weight: normal;
}
.no_data_page {
  text-align: center;
}
.no_data_page .status_msg img {
  margin-top: 1rem;
}
</style>
