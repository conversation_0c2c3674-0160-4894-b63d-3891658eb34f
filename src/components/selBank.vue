<template>
  <div v-if="isShow">
    <div class="dialog_overlay" @click="close"></div>
    <div class="layer_box">
      <div class="layer_tit">
        <h3>所属银行</h3>
        <a class="close" @click="close"></a>
      </div>
      <div class="layer_cont">
        <slot>
          <ul class="select_list">
            <li
              v-for="(d, index) in dataList"
              :key="index"
              :class="{ active: selMap[index] }"
              @click="selClick(d, index)"
            >
              <span><img :src="d.bankLogo" />{{ d.bankName }}</span>
            </li>
          </ul>
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SelBank',
  model: {
    prop: 'isShow',
    event: 'change'
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    showSearch: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '请选择'
    },
    category: {
      type: String,
      default: ''
    },
    initData: {
      type: Array,
      default: () => []
    },
    defaultStr: {
      type: String,
      default: ''
    },
    mult: {
      // 是否多选
      type: Boolean,
      default: false
    },
    idString: {
      // 指定id的key
      type: String,
      default: 'key'
    }
  },
  data() {
    return {
      dataList: [],
      selMap: {}
    };
  },
  watch: {
    isShow(data) {
      if (data) {
        this.updataData();
      }
    }
  },
  created() {
    this.dataList = this.initData;
    this.initDefault();
  },
  destroyed() {},
  methods: {
    updataData() {
      this.dataList = this.initData;
      this.initDefault();
    },
    close() {
      this.$emit('change', false);
    },
    selClick(item, index) {
      if (this.mult) {
        this.$set(this.selMap, index, !this.selMap[index]);
      } else {
        this.$emit('selCallback', { data: item, index: index });
        this.close();
      }
    },
    confirmClick() {
      var sel = [];
      for (let index in this.selMap) {
        sel.push(this.dataList[index]);
      }
      if (sel.length === 0) {
        return;
      }
      this.$emit('selCallback', sel);
      this.close();
    },
    initDefault() {
      let that = this;
      that.dataList.forEach((a, b) => {
        that.defaultStr.split(';').forEach((c) => {
          if (c === a[that.idString]) {
            that.selMap[b] = true;
          }
        });
      });
    }
  }
};
</script>
