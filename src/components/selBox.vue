<template>
  <div v-if="isShow">
    <div class="dialog_overlay" @click="close"></div>
    <div class="layer_box">
      <div class="layer_tit">
        <h3>{{ title }}</h3>
        <a class="close" @click="close"></a>
      </div>
      <div v-if="showSearch" class="search_box">
        <div class="search_input">
          <i class="icon"></i>
          <input
            class="t1"
            type="text"
            placeholder="可直接搜索职业关键字，如“ 金融”"
          />
        </div>
      </div>
      <div class="layer_cont">
        <slot>
          <ul class="select_list">
            <li
              v-for="(d, index) in dataList"
              :key="index"
              :class="{ active: selMap[index] }"
              @click="selClick(d, index)"
            >
              <span>{{ d.label }}</span>
            </li>
          </ul>
        </slot>
      </div>
      <div v-if="mult" class="ce_btn" @click="confirmClick">
        <a class="p_button">确认提交</a>
      </div>
    </div>
  </div>
</template>

<script>
import { getDictData } from '@/service/commonService';
export default {
  name: 'SelBox',
  model: {
    prop: 'isShow',
    event: 'change'
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    showSearch: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '请选择'
    },
    category: {
      type: String,
      default: ''
    },
    initData: {
      type: Array,
      default: () => []
    },
    defaultStr: {
      type: String,
      default: ''
    },
    mult: {
      // 是否多选
      type: Boolean,
      default: false
    },
    idString: {
      // 指定id的key
      type: String,
      default: 'key'
    }
  },
  data() {
    return {
      dataList: [],
      selMap: {}
    };
  },
  watch: {
    isShow(data) {
      if (data) {
        this.updataData();
      }
    }
  },
  created() {
    if (this.category) {
      getDictData({ dictType: this.category }).then((data) => {
        this.dataList = data.data[this.category].map((item) => ({
          label: item.dictLabel,
          value: item.dictValue
        }));
      });
    } else {
      this.dataList = this.initData;
      this.initDefault();
    }
  },
  destroyed() {},
  methods: {
    updataData() {
      if (this.category) {
        getDictData({ dictType: this.category }).then((data) => {
          this.dataList = data.data[this.category].map((item) => ({
            label: item.dictLabel,
            value: item.dictValue
          }));
        });
      } else {
        this.dataList = this.initData;
        this.initDefault();
      }
    },
    close() {
      this.$emit('change', false);
    },
    selClick(item, index) {
      if (this.mult) {
        this.$set(this.selMap, index, !this.selMap[index]);
      } else {
        this.$emit('selCallback', { data: item, index: index });
        this.close();
      }
    },
    confirmClick() {
      var sel = [];
      for (let index in this.selMap) {
        sel.push(this.dataList[index]);
      }
      if (sel.length === 0) {
        return;
      }
      this.$emit('selCallback', sel);
      this.close();
    },
    initDefault() {
      let that = this;
      that.dataList.forEach((a, b) => {
        that.defaultStr.split(';').forEach((c) => {
          if (c === a[that.idString]) {
            that.selMap[b] = true;
          }
        });
      });
    }
  }
};
</script>
