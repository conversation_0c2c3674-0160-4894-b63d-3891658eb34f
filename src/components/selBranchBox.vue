<template>
  <div v-if="isShow" class="branchBox">
    <!-- 遮罩层 -->
    <div class="dialog_overlay" style="display: block" @click="closeBox"></div>
    <!-- 弹出层 -->
    <div class="sele_layer show">
      <div class="sele_lytit">
        <h3>请选择营业部</h3>
        <a class="cancel" href="javascript:void(0);" @click.stop="closeBox"></a>
      </div>
      <div class="sele_lyinfo">
        <span
          v-show="showItem >= 1"
          :class="{ now: showItem === 1 }"
          @click="showItem = 1"
          v-text="selProvinceName"
        ></span>
        <span
          v-show="showItem >= 2"
          :class="{ now: showItem === 2 }"
          @click="showItem = 2"
          v-text="selCityName"
        ></span>
        <span
          v-show="showItem >= 3"
          :class="{ now: showItem === 3 }"
          @click="showItem = 3"
          v-text="selBranchName"
        ></span>
      </div>
      <div class="sele_lycont fixed">
        <ul v-show="showItem === 1" class="select_list">
          <li
            v-for="p in allData.provinceList"
            :key="p.province_no"
            :class="{ active: p.province_no === selInfo.provinceNo }"
            @click="selProvinceClick(p)"
          >
            <span>{{ p.province_name }}</span>
          </li>
        </ul>
        <ul v-show="showItem === 2" class="select_list">
          <li
            v-for="c in selCityList"
            :key="c.city_no"
            :class="{ active: c.city_no === selInfo.cityNo }"
            @click="selCityClick(c)"
          >
            <span>{{ c.city_name }}</span>
          </li>
        </ul>
        <ul v-show="showItem === 3" class="network_list">
          <li
            v-for="b in selBranchList"
            :key="b.branch_no"
            :class="{ active: b.branch_no === selInfo.branchNo }"
            @click="selBranchClick(b)"
          >
            <h5>{{ b.branch_name }}</h5>
            <p>{{ b.address }}</p>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import { getBranches } from '@/service/openService';
import { filterInvalidProvince } from '@/common/util';

export default {
  name: 'SelBranchBox',
  model: {
    prop: 'isShow',
    event: 'change'
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    defaultBranchNo: {
      type: String
    }
  },
  data() {
    return {
      allData: {
        // 保存所有的营业部 城市 省份数据
        branchList: [],
        cityList: [],
        provinceList: []
      },
      selCityList: [], // 当前可选择的城市列表
      selBranchList: [], // 当前可选择的营业部列表
      selInfo: {
        // 已选择的信息
        provinceNo: '',
        cityNo: '',
        branchNo: '',
        branchName: '',
        commission: ''
      },
      selProvinceName: '请选择',
      selCityName: '请选择',
      selBranchName: '请选择',
      showItem: 1 // 展示第几列
    };
  },
  created() {
    getBranches()
      .then((data) => {
        if (data.error_no === '0') {
          this.allData.branchList = data.branchList;
          this.allData.cityList = data.cityList;
          this.allData.provinceList = data.provinceList;
          if ($hvue.config.is_filter_city) {
            filterInvalidProvince(this.allData);
          }
          this.defaultBranchNo && this.echoSelInfo();
        } else {
          return Promise.reject(data.error_info);
        }
      })
      .catch((err) => {
        _hvueToast({ mes: err });
      });
  },
  methods: {
    closeBox() {
      this.$emit('change', false);
    },
    selProvinceClick(e) {
      let that = this;
      this.selInfo.provinceNo = e.province_no;
      this.selCityName = '请选择';
      this.selProvinceName = e.province_name;
      this.selCityList = [];
      this.allData.cityList.forEach((a) => {
        if (a.province_no === that.selInfo.provinceNo) {
          that.selCityList.push(a);
        }
      });
      this.showItem = 2;
    },
    selCityClick(e) {
      let that = this;
      this.selInfo.cityNo = e.city_no;
      this.selCityName = e.city_name;
      this.selBranchName = '请选择';
      this.selBranchList = [];
      this.allData.branchList.forEach((a) => {
        if (a.city_no === that.selInfo.cityNo) {
          that.selBranchList.push(a);
        }
      });
      this.showItem = 3;
    },
    selBranchClick(e) {
      this.selInfo.branchNo = e.branch_no;
      this.selInfo.branchName = e.branch_name;
      this.selInfo.commission = e.commission;
      this.closeBox();
      this.$emit('selComplate', this.selInfo);
    },
    echoSelInfo() {
      // 回显选择的营业部信息
      let that = this;
      that.allData.branchList.some((a) => {
        if (that.defaultBranchNo === a.branch_no) {
          let selCity;
          let selProvince;
          that.selBranchName = a.branch_name;
          that.selInfo.branchName = a.branch_name;
          that.selInfo.branchNo = a.branch_no;
          that.selInfo.commission = a.commission;
          that.allData.branchList.forEach((b) => {
            if (b.city_no === a.city_no) {
              that.selBranchList.push(b);
            }
          });
          that.allData.cityList.some((b) => {
            if (b.city_no === a.city_no) {
              selCity = b;
              that.selCityName = b.city_name;
              that.selInfo.cityNo = b.city_no;
              return true;
            }
          });

          that.allData.cityList.forEach((b) => {
            if (b.province_no === selCity.province_no) {
              that.selCityList.push(b);
            }
          });
          that.allData.provinceList.some((b) => {
            if (b.province_no === selCity.province_no) {
              selProvince = b;
              that.selProvinceName = b.province_name;
              that.selInfo.provinceNo = b.province_no;
              return true;
            }
          });
          that.allData.provinceList.some((b) => {
            if (b.province_no === selProvince.province_no) {
              return true;
            }
          });
          that.showItem = 3;
          return true;
        }
      });
      that.allData.branchList.forEach((a) => {
        if (that.defaultBranchNo === a.branch_no) {
          that.allData.cityList.forEach((b) => {
            if (a.province_no === b.province_no) {
              that.allData.provinceList.some((c) => {
                if (b.province_no === c.province_no) {
                  return true;
                }
              });
              return true;
            }
          });
          return true;
        }
      });
    }
  }
};
</script>
