<template>
  <div v-show="show">
    <!-- 遮罩层 -->
    <div class="dialog_overlay" style="display: block" @click="close"></div>
    <!-- 弹出层 -->
    <div class="upload_select show">
      <h5>请选择</h5>
      <ul>
        <li @click="hanldclick(1)">
          <a>高德地图</a>
        </li>
        <li @click="hanldclick(2)">
          <a>百度地图</a>
        </li>
        <!-- <li @click="hanldclick(3)">
					<a>腾讯地图</a>
				</li> -->
      </ul>
      <a class="cancel" @click="close">取消</a>
    </div>
  </div>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      show: false
    };
  },

  methods: {
    showSelect() {
      this.show = true;
    },
    close() {
      this.show = false;
    },
    hanldclick(val) {
      if (val === 1) {
        // 高德地图
        window.location.href =
          'https://uri.amap.com/marker?position=116.473195,39.993253&name=首开广场&src=mypage&coordinate=gaode&callnative=1';
      } else if (val === 2) {
        //  百度地图
        window.location.href =
          'http://api.map.baidu.com/marker?location=40.047669,116.313082&title=我的位置&content=百度奎科大厦&output=html&src=webapp.baidu.openAPIdemo';
      } else if (val === 3) {
        // window.location.href =
        // 	'https://apis.map.qq.com/uri/v1/search?keyword=酒店&region=北京&referer=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77';
      }
    },
    selType(type) {}
  }
};
</script>
