<template>
  <div />
</template>

<script>
import '@/nativeShell/nativeCallH5';

export default {
  props: {},
  data() {
    return {};
  },
  methods: {
    getImg() {
      window.imgCallBack = this.getImgCallBack;
      let phoneConfig = {
        funcNo: '60028',
        moduleName: $hvue.customConfig.moduleName,
        mainColor: '#fa443a',
        takeTip: '请保证脸部清晰无遮挡进行拍摄',
        previewDetailTip: '• 需为正面免冠照片\n• 需完整面部出镜，无反光、遮挡、五官不清晰等情形\n'
      };
      console.log(phoneConfig);
      let result = $h.callMessageNative(phoneConfig);
      if (result.error_no !== '0') {
        console.log({ mes: result.error_info });
      }
    },
    getImgCallBack(data) {
      console.log('进入免冠照回调');
      this.$emit('getImgCallBack', {
        base64: this.filterBase64Pre(data.base64)
      });
    },
    filterBase64Pre(ndata) {
      let arr = ndata.split('base64,');
      return arr[arr.length - 1];
    }
  }
};
</script>
