<template>
  <div>
    <input
      ref="input"
      @change="seledFile"
      v-if="createVideoSelect"
      type="file"
      accept="video/*"
      capture="user"
    />
  </div>
</template>

<script>
export default {
  name: 'videorecord',
  data() {
    return {
      createVideoSelect: true
    };
  },

  props: {
    maxSize: {
      type: Number,
      default: 20 * 1024 * 1024
    },
    minSize: {
      type: Number,
      default: 0.5 * 1024 * 1024
    }
  },
  methods: {
    getVideo() {
      // 选择文件点击事件
      this.createVideoSelect = false;
      this.$nextTick(() => {
        this.createVideoSelect = true;
        this.$nextTick(() => {
          this.$refs.input.click();
        });
      });
    },
    seledFile(o) {
      // 选择完文件回调
      let that = this;
      let file = o.currentTarget.files[0];
      if (this.check(file)) {
        that.$emit('getVideoCallBack', {
          error_no: '0',
          file: file
        });
      }
    },
    check(file) {
      var whiteSuffixList = [
          'mp4',
          'avi',
          '3gp',
          'flv',
          'm4v',
          'divx',
          'mov',
          'mpeg',
          'mpg',
          'ogm',
          'ogv',
          'ogx',
          'rm',
          'rmvb',
          'smil',
          'wmv',
          'webm'
        ],
        fileSuffix = file.name.match(/\.([\d\w]+)$/i)[1] || '';

      // 文件格式验证
      if (
        !fileSuffix ||
        whiteSuffixList.indexOf(fileSuffix.toLowerCase()) < 0
      ) {
        this.$emit('onerror', { code: -1, msg: '视频文件格式不正确' });
        return false;
      }

      // 判断文件大小
      if (file.size > this.maxSize) {
        this.$emit('onerror', {
          code: -2,
          msg: '视频文件过大, 请控制录制时间!'
        });
        return false;
      }
      if (file.size < this.minSize) {
        this.$emit('onerror', {
          code: -3,
          msg: '视频文件过小, 请延长录制时间!'
        });
        return false;
      }
      return true;
    }
  }
};
</script>
<style scoped>
input {
  width: 0px;
  height: 0px;
  position: absolute;
  top: 0;
  left: 0;
  display: none;
}
</style>
