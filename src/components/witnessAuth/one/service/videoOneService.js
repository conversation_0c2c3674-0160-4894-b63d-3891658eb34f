/**
 * @desc 接口请求定义
 * <AUTHOR>
 */

import { request, md5Encrypt } from 'thinkive-hvue';

let requestURL = $hvue.customConfig.video.videoServer + '/auth-common-server';

/**
 * 所有请求的公用方法 可以添加公用参数 或者其他公用操作
 * @param {String} funcNo 功能号
 * @param {Object} params 接口的业务参数
 * @param {Object} options 此次请求的相关设置 如 请求方法 是否加签， 具体定义见netIntercept.js
 */
function commService(funcNo, params, options = {}) {
  let _params = {
    funcNo: funcNo,
    origin: $hvue.platform == 0 ? '3' : $hvue.platform,
    source: $hvue.platform === '0' ? '3' : $hvue.platform,
    opSource: $hvue.platform === '0' ? '3' : $hvue.platform
  };
  let paramMD5;
  if (options.cache === true) {
    // 结果是否缓存到session
    paramMD5 = md5Encrypt(JSON.stringify(Object.assign(_params, params)));
    let res = $h.getSession(paramMD5);
    if (res) {
      if (res.code === 0) {
        return Promise.resolve(res);
      }
    }
  }
  let _options = {
    // 默认的请求设置
    url: requestURL,
    sign: false, // 是否加签
    encode: false, // 是否默认编码
    method: 'POST', // 请求方法
    loading: true, // 是否显示loading
    headers: {
      Accept: '*/*',
      'Content-Type': 'application/json',
      'tk-jwt-authorization': $h.getSession('videoToken')
    }
  };
  return request({
    _params,
    params,
    options,
    _options
  }).then(
    (res) => {
      if (options.cache === true) {
        $h.setSession(paramMD5, res);
      }
      res.funcNo = funcNo;
      return Promise.resolve(res);
    },
    (err) => {
      return Promise.reject(err);
    }
  );
}

/**
 * 活体留痕
 * @param {Object} params 业务参数
 * {
 *    user_id   用户id
 * }
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function submitLivingLog(params, options) {
  return commService('faceLiving/saveAppLiving', params, options);
}

/**
 * 单向人脸识别接口
 * @param {Object} params 业务参数
 * {
 *    user_id   用户id
 * }
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function faceRecognition(params, options) {
  return commService('faceLiving/faceCompare', params, options);
}

/**
 * 单向人脸识别接口
 * @param {Object} params 业务参数
 * {
 *    user_id   用户id
 * }
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function getAppNativeVideoConfig(params, options) {
  return commService(
    'faceLiving/getAppNativeVideoConfig',
    params,
    Object.assign({ method: 'GET' }, options)
  );
}

/**
 * 人脸检测
 * @param {Object} params 业务参数
 * {
 *    user_id   用户id
 * }
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function faceCheck(params, options) {
  return commService('faceLiving/faceDetect', params, options);
}

/**
 * 获取房间号
 * @param {Object} params 业务参数
 * {
 *    user_id   用户id
 * }
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function getRoomNo(params, options) {
  return commService(
    'faceLiving/sercurityNo',
    params,
    Object.assign({ method: 'GET' }, options)
  );
}

/**
 * 单向活体检测接口
 * @param {Object} params 业务参数
 * {
 *    user_id   用户id
 * }
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function livingCheck(params, options) {
  return commService('faceLiving/livingCheck', params, options);
}

/**
 * 综合对比
 * @param {Object} params 业务参数
 * {
 *    user_id   用户id
 * }
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function livingCheckAndCompare(params, options) {
  return commService('faceLiving/faceDetectAndCompare', params, options);
}

/**
 * 保存视频信息
 * @param {Object} params 业务参数
 * {
 *    user_id   用户id
 * }
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function submitH5VideoInfo(params, options) {
  return commService('faceLiving/saveVideo', params, options);
}
