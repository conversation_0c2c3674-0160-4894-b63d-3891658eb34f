/**
 * @desc 接口请求定义
 * <AUTHOR>
 */

import { request, md5Encrypt } from 'thinkive-hvue';

let requestURL = $hvue.customConfig.video.videoServer + '/wa-queue-server';

/**
 * 所有请求的公用方法 可以添加公用参数 或者其他公用操作
 * @param {String} funcNo 功能号
 * @param {Object} params 接口的业务参数
 * @param {Object} options 此次请求的相关设置 如 请求方法 是否加签， 具体定义见netIntercept.js
 */
function commService(funcNo, params, options = {}) {
  let _params = {
    funcNo: funcNo,
    origin: $hvue.platform == 0 ? '3' : $hvue.platform,
    source: $hvue.platform === '0' ? '3' : $hvue.platform,
    opSource: $hvue.platform === '0' ? '3' : $hvue.platform
  };
  let paramMD5;
  if (options.cache === true) {
    // 结果是否缓存到session
    paramMD5 = md5Encrypt(JSON.stringify(Object.assign(_params, params)));
    let res = $h.getSession(paramMD5);
    if (res) {
      if (res.code === 0) {
        return Promise.resolve(res);
      }
    }
  }
  let _options = {
    // 默认的请求设置
    url: requestURL,
    sign: false, // 是否加签
    encode: false, // 是否默认编码
    method: 'POST', // 请求方法
    loading: true, // 是否显示loading
    headers: {
      Accept: '*/*',
      'Content-Type': 'application/json',
      'tk-jwt-authorization': $h.getSession('videoToken')
    }
  };
  return request({
    _params,
    params,
    options,
    _options
  }).then(
    (res) => {
      if (options.cache === true) {
        $h.setSession(paramMD5, res);
      }
      res.funcNo = funcNo;
      return Promise.resolve(res);
    },
    (err) => {
      return Promise.reject(err);
    }
  );
}

/**
 * @desc 开始排队
 * @param {Object} params 业务参数
 * {
 *    user_id   :{String} 客户编号  Y
 *    user_name :{String} 客户名称  Y
 *    branch_id :{String} 营业部id  Y
 *    level     :{int}  客户等级  N 默认0，此外1为最高级，依次递减
 *    origin    :{int}  来源信息  Y 0:web,1:android,2:ios,3:移动端h5
 * }
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function videoQueue(params, options) {
  return commService('customer/join', params, options);
}

/**
 * @desc 取消排队
 * @param {Object} params 业务参数
 * {
 *    user_id       :{String} 客户编号  Y
 *    biz_type      :{String} 业务类型  N 默认是1：开户
 *    branch_id     :{String} 营业部id  Y
 *    abnormal_exit :{int}  是否异常退出  N 默认是0：正常退出，1：异常退出（有插队资格
 * }
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function cancelQueue(params, options) {
  return commService('customer/exit', params, options);
}
