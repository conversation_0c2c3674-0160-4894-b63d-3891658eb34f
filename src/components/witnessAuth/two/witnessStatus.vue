<template>
  <section class="main fixed">
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click="cancelQueueClick"></a>
        <h1 class="title">{{ title || $route.meta.title }}</h1>
      </div>
      <div v-if="showProgressBar" class="step_box">
        <div class="item">
          <b :style="{ width: percent + '%' }">
            <span>{{ percent + '%' }}</span>
          </b>
        </div>
      </div>
    </header>
    <article class="content">
      <div v-if="queueStatus === 1" class="queue_box">
        <div class="queue_level">
          <i class="bg"></i>
          <div class="pic">
            <img src="./images/queue_cs.png" />
          </div>
        </div>
        <h5>坐席忙碌中，请稍等…</h5>
      </div>
      <div v-else-if="queueStatus === 2" class="queue_box">
        <div class="queue_level">
          <i class="bg"></i>
          <span class="num">{{ queueLocation }}</span>
        </div>
        <h5>正在排队中…</h5>
        <p>您当前排在第{{ queueLocation }}位</p>
      </div>
      <div v-else-if="queueStatus === 3 || queueStatus === 4" class="queue_box">
        <div class="queue_level">
          <i class="bg"></i>
          <div class="pic">
            <img src="./images/queue_cs.png" />
          </div>
        </div>
        <h5>
          {{ queueStatus === 3 ? '等待坐席进入…' : '等待坐席确认...' }}
        </h5>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn" @click="cancelQueueClick">
        <a>取消排队</a>
      </div>
    </footer>

    <witnessTchat
      v-if="platform === 'browser' && videoType == '0' && queueStatus == 3"
      ref="browser"
      :user-info="userInfo"
      @videoCallBack="videoCallBack"
    ></witnessTchat>
  </section>
</template>

<script>
import { videoQueue, cancelQueue } from './service/videoTwoService';
import witnessTchat from './witness_tchat';
export default {
  name: 'WitnessStatus',
  components: {
    witnessTchat
  },
  data() {
    return {
      queueLocation: '-',
      queueInterval: null,
      queueStatus: 1 // 排队状态 1：客服忙碌中 2：排队中 3:接入中 4：坐席确认中
    };
  },
  props: {
    userInfo: {
      type: Object,
      default: () => {}
    },
    videoType: {
      type: String,
      default: '0' // 1anychat 0 tchat
    },
    platform: {
      type: String,
      default: navigator.userAgent.includes('thinkive') ? 'thinkive' : 'browser'
    },
    title: {
      type: String,
      default: ''
    },
    percent: {
      type: Number,
      default: 0
    },
    showProgressBar: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    this.start();
  },
  destroyed() {
    this.cleanData();
  },
  deactivated() {
    this.cleanData();
  },
  methods: {
    start() {
      this.queue();
      this.queueInterval = setInterval(this.queue, 2000);
    },
    queue() {
      let param = {
        regFlowNo: this.userInfo.regFlowNo,
        clientId: this.userInfo.regFlowNo,
        custName: this.userInfo.clientName,
        orgNo: this.userInfo.branchNo,
        bizType: this.userInfo.bizType,
        level: this.userInfo.level || 0,
        origin:
          this.platform == 'browser' ? '3' : $hvue.iBrowser.android ? '1' : '2',
        videoType: this.videoType
      };
      videoQueue(param, {
        loading: false
      })
        .then((data) => {
          if (data.code == '0') {
            let result = data.data;
            if (result.staffExist === true) {
              if (result.queueLocation == '0') {
                // 开始视频
                this.queueStatus = 3;
                clearInterval(this.queueInterval);
                let roomInfoArr = result.serverRoomNo.split(':');
                if (this.platform === 'ths') {
                  this.$refs.ths.start(roomInfoArr);
                } else if (this.platform === 'browser') {
                  this.$nextTick(() => {
                    this.$refs.browser.start(roomInfoArr, result);
                  });
                }
                // else if (this.platform === 'thinkive') {
                //   this.$refs.thinkive.start(roomInfoArr);
                // }
              } else if (result.queueLocation == '-1') {
                this.queueStatus = 4; // 坐席确认中
              } else {
                this.queueLocation = result.queueLocation; // 排队中
                this.queueStatus = 2;
              }
            } else {
              this.queueStatus = 1; // 无坐席
            }
          } else {
            _hvueToast({
              mes: data.msg
            });
          }
        })
        .catch((e) => {
          if (e.message === 'Network Error') {
            clearInterval(this.queueInterval);
            this.$router.replace({
              name: 'witnessTwo'
            });
          } else {
            console.log(e);
          }
        });
    },
    cancelQueueClick() {
      var param = {
        regFlowNo: this.userInfo.regFlowNo,
        orgNo: this.userInfo.branchNo,
        bizType: this.userInfo.bizType,
        abnormalExit: '0'
      };
      cancelQueue(param)
        .then((data) => {
          if (data.code == '0') {
            this.$emit('cancel');
          } else {
            _hvueToast({ mes: data.msg });
          }
        })
        .catch((e) => {
          if (e.message === 'Network Error') {
            clearInterval(this.queueInterval);
          }
          _hvueToast({ mes: e.message });
          this.$emit('cancel');
        });
    },
    cleanData() {
      clearInterval(this.queueInterval);
      this.queueStatus = 1;
    },
    videoCallBack(param) {
      if (param.hungup) {
        this.cancelQueueClick();
      } else {
        this.$emit('videoCallBack', { message: JSON.stringify(param) }); // 和app返回参数一致
      }
    }
  }
};
</script>
<style scoped>
body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
textarea,
p,
blockquote,
th,
td {
  margin: 0;
  padding: 0;
}

fieldset,
img {
  border: 0 none;
}

address,
caption,
cite,
code,
dfn,
em,
th,
var,
b,
h1,
h2,
h3 {
  font-style: normal;
  font-weight: normal;
}

ol,
ul,
li {
  list-style-type: none;
}
body {
  font-size: 0.14rem;
  line-height: 1.8;
  font-family: Hiragino Sans GB, Helvetica, STHeiti STXihei, Microsoft YaHei,
    Arial;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  color: #666;
  background: #f3f3f6;
}
/*-- 布局grid --*/

.main.fixed {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  -webkit-flex-direction: column;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
}

.main.fixed article.content {
  -moz-box-flex: 1;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  height: 100%;
  -webkit-overflow-scrolling: auto;
  position: relative;
}

section.main.fixed article.content::-webkit-scrollbar {
  width: 0;
  display: none;
}

/*-- 头部header --*/
.header_inner {
  background: #0354c2;
  position: relative;
  height: 0.45rem;
  line-height: 0.44rem;
}

.header_inner > h1.title {
  font-size: 0.18rem;
  font-weight: 500;
  color: #fff;
  position: relative;
  z-index: 0;
  text-align: center;
}

.icon_text {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 0.15rem;
  padding: 0 0.15rem;
  color: #fff;
  z-index: 50;
}

.icon_back {
  width: 0.44rem;
  height: 0.44rem;
  background: url(./images/icon_back.png) no-repeat center;
  background-size: 0.24rem;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 50;
}
/*-- 进度条step  --*/

.step_box {
  background: #0354c2;
  padding: 0.08rem 0.2rem 0.25rem;
}

.step_box .item {
  height: 4px;
  background: rgba(0, 0, 0, 0.15);
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
}

.step_box .item b {
  display: block;
  height: 4px;
  background: rgba(255, 255, 255, 0.5);
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
  position: relative;
}

.step_box .item b:before {
  content: '';
  width: 8px;
  height: 8px;
  background: #fff;
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
  position: absolute;
  top: -2px;
  right: -4px;
}

.step_box .item b span {
  width: 0.28rem;
  text-align: center;
  height: 0.13rem;
  line-height: 0.13rem;
  font-size: 0.09rem;
  color: #fff;
  background: rgba(255, 255, 255, 0.3);
  -moz-border-radius: 0.02rem;
  -webkit-border-radius: 0.02rem;
  border-radius: 0.02rem;
  position: absolute;
  bottom: -0.2rem;
  right: -0.11rem;
}

.step_box .item b span:before {
  content: '';
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 03px 3px;
  border-color: transparent transparent rgba(255, 255, 255, 0.3);
  position: absolute;
  top: -3px;
  left: 50%;
  margin-left: -3px;
}
.queue_box {
  background: #ffffff;
  padding: 0.5rem 0.2rem 0.2rem;
  min-height: 3.1rem;
  margin-bottom: 0.1rem;
  text-align: center;
  line-height: 0.2rem;
  color: #999999;
}

.queue_box h5 {
  font-size: 0.24rem;
  line-height: 0.32rem;
  font-weight: normal;
  color: #333333;
  margin-bottom: 0.1rem;
}

.queue_level {
  width: 1.2rem;
  height: 1.2rem;
  margin: 0 auto 0.3rem;
  position: relative;
}

.queue_level .bg {
  display: block;
  width: 100%;
  height: 100%;
  background: url(./images/queue_bg.png) no-repeat center;
  background-size: 100%;
  -webkit-animation: allrotate 1.6s infinite linear;
  animation: allrotate 1.6s infinite linear;
}

.queue_level .pic {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 50;
}

.queue_level .pic img {
  display: block;
  width: 100%;
  height: 100%;
}

@-webkit-keyframes allrotate {
  from {
    -webkit-transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes allrotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.queue_level .num {
  width: 100%;
  text-align: center;
  height: 1.2rem;
  line-height: 1.2rem;
  font-size: 0.62rem;
  color: #0354c2;
  font-weight: 500;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 50;
}

.ce_btn {
  width: 100%;
  display: table;
  table-layout: fixed;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 100;
}

.ce_btn a {
  display: table-cell;
  height: 0.5rem;
  line-height: 0.5rem;
  text-align: center;
  font-size: 0.18rem;
  background: #fff;
  color: #0354c2;
  border-right: 1px solid #eaeaef;
  font-weight: 500;
}

.ce_btn a:last-child {
  border-right: 0 none;
}

.ce_btn a.cancel {
  font-weight: normal;
}

.ce_btn a.disable {
  color: #cccccc;
}
</style>
