<template>
  <div class="video_main">
    <div id="localDiv" class="common_video"></div>
    <div id="remoteDiv" class="small_video"></div>
    <div class="video_title">
      <h3>视频见证中，请不要随意挂断</h3>
    </div>
    <a class="hangup_btn" @click="hungup(false)">挂断</a>
    <div class="video_infobox">
      <ul v-if="staffInfo.staffName || staffInfo.staffTips" class="cs_info">
        <li v-if="staffInfo.staffTips" v-html="staffInfo.staffTips"></li>
        <li v-if="staffInfo.staffName">
          <span>见证人员：</span>
          <strong>{{ staffInfo.staffName }}</strong>
        </li>
        <li v-if="staffInfo.staffUid">
          <span>员工编号：</span>
          <strong>{{ staffInfo.staffUid }}</strong>
        </li>
        <li v-if="staffInfo.staffCode">
          <span>执证编号：</span>
          <strong>{{ staffInfo.staffCode }}</strong>
        </li>
      </ul>
      <div v-show="sitMsg" ref="sitMsg" class="cs_msg" v-html="sitMsg">
        <!-- <h5>与 客服何敏 的聊天</h5> -->
      </div>
    </div>
    <!-- 弹出层 -->
    <div v-show="showConfirm">
      <div class="dialog_overlay"></div>
      <div class="qx_layer">
        <div class="qx_lycont">
          <p>坐席即将与您视频连线，<br />请确认开始视频</p>
        </div>
        <div class="qx_lybtn">
          <a class="border disabled" @click="hungup()"
            >{{ confirmCount }}秒后取消</a
          >
          <a @click="startConnect">开始</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import './tchat/protobuf.min.js';
import './tchat/md5.js';
import './tchat/sdp-transform.js';
import './tchat/TChatRTC.js';

export default {
  name: 'WitnessTchat',
  props: {
    userInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      room_no: '',
      sitMsg: '', // 坐席发送的消息
      showConfirm: false,
      confirmIntv: null,
      confirmMaxTime: 20, // 倒计时时长
      confirmCount: 20, // 展示的倒计时
      staffInfo: {} // 坐席员工信息
    };
  },
  methods: {
    startConnect() {
      this.showConfirm = false;
      window.clearInterval(this.confirmIntv);
      let tchatParamConfig = {
          signalServer: $hvue.customConfig.video.tchat.signalServer, //信令服务器地址
          protobufFileLoaction:
            $hvue.customConfig.video.tchat.protobufFileLoaction, //pb文件地址
          secretkey: $hvue.customConfig.video.tchat.tchat_verifier_secret_key, //信令服务器认证秘钥
          roomid: this.room_no //排队获取的房间号
        },
        that = this;
      TChatRTC.init(
        {
          addNewUser: function () {}, //新用户加入
          exitRoomNotify: this.exitRoomNotify, //坐席离开房间
          onMsgNotify: this.onMsgNotify, //坐席发送文本消息回调
          onTransBufferNotify: this.onTransBufferNotify, //坐席发送指派指令
          onRemoteStreamAdd: function () {
            //远程坐席视频流加入回调
            that.siteName = TChatRTC.user.peerusername; //显示坐席名称
          },
          timeout: function () {
            //信令服务器超时
            _hvueToast({ mes: '连接信令服务器超时' });
            that.hungup();
          },
          netError: function () {
            //网络错误
            _hvueToast({ mes: '您的网络异常' });
            that.hungup();
          },
          socketError: function () {
            //坐席网络异常
            _hvueToast({ mes: '网络连接异常，请退出重试' });
            that.hungup();
          },
          destory: function () {}
        },
        tchatParamConfig,
        that.videoInitSuccessBack
      );
    },
    start(roomInfoArr, result) {
      if (result && result.staffParam) {
        this.staffInfo = JSON.parse(result.staffParam);
      } else if (result.staffTips) {
        this.staffInfo = {
          staffTips: result.staffTips.replace('\n', '<br>')
        };
      }

      if (this.userInfo.video_hint) {
        this.sitMsg = this.userInfo.video_hint;
      }
      this.room_no = roomInfoArr[2];
      this.showConfirm = true;
      this.confirmCount = this.confirmMaxTime;
      this.confirmIntv = window.setInterval(() => {
        this.confirmCount--;
        if (this.confirmCount <= 0) {
          window.clearInterval(this.confirmIntv);
          this.$emit('videoCallBack', { hungup: true });
        }
      }, 1000);
    },
    hungup(isErr) {
      window.clearInterval(this.confirmIntv);
      if (!isErr) {
        TChatRTC.sendMsg({ msg: 'SYS:10002' }); //主动挂断
      }
      TChatRTC.closeAll();
      this.$emit('videoCallBack', { hungup: true });
    },
    videoInitSuccessBack(data) {
      var that = this;
      if (data.errcode == 0) {
        TChatRTC.login(
          {
            //登录信令服务器
            username: this.userInfo.regFlowNo
          },
          function (data) {
            if (data.errcode == 0) {
              TChatRTC.enterRoom({ roomid: that.room_no }, function (data) {
                //进入房间
                if (data.errcode == 0) {
                  TChatRTC.openLocalCamera(
                    function () {
                      TChatRTC.startWebRtc(data.result);
                    },
                    function (e) {
                      if (e.name == 'NotAllowedError') {
                        _hvueToast({ mes: '无视频权限' });
                      } else {
                        that.hungup();
                        _hvueToast({ mes: '获取摄像头失败' });
                      }
                    }
                  );
                } else {
                  _hvueToast({ mes: data.errmsg });
                  that.hungup();
                }
              });
            } else {
              _hvueToast({ mes: data.errmsg });
              that.hungup();
            }
          }
        );
      } else {
        if (data.errcode == -1003) {
          data.errmsg = '座席端网络连接异常，请退出重试!';
        }
        _hvueToast({ mes: data.errmsg });
        that.hungup();
      }
    },
    exitRoomNotify() {
      this.hungup();
    },
    onTransBufferNotify(data) {
      if (data.errcode == '0') {
        var msg = decodeURI(
          String.fromCharCode.apply(null, data.result.cmdmsg)
        );
        msg = msg.replace(/\\&quot;/g, '"'); //将返回信息中的引用还原
        msg = msg.replace(/&quot;/g, '"'); //将返回json中多余的引号去掉
        msg = JSON.parse(msg);
        // let result = {};
        // if (msg.msgNo === 0) {
        //   result = {
        //     error_no: 0,
        //     error_info: '视频通过'
        //   };
        // } else {
        //   result = {
        //     error_no: 1,
        //     error_info: '视频驳回',
        //     result: msg.msgInfo
        //   };
        // }
        TChatRTC.closeAll();
        this.$emit('videoCallBack', msg);
      } else {
        _hvueToast({ mes: '透明通道信息错误' });
        this.hungup();
      }
    },
    onMsgNotify(data) {
      if (data.errcode == '0') {
        let msg = new TextDecoder('utf-8').decode(data.result.msg);
        msg = msg.replace(/\\&quot;/g, '"'); //将返回信息中的引用还原
        this.sitMsg += '<br>' + msg.replace(/&quot;/g, '"');
        this.$nextTick(() => {
          this.$refs['sitMsg'].scrollTop = this.$refs['sitMsg'].scrollHeight;
        });
        // setTimeout(() => {
        //   this.sitMsg = '';
        // }, 5000);
      } else {
        _hvueToast({ mes: data.errmsg });
      }
    },
    onUserExitRoom() {
      _hvueToast({ mes: '坐席断开连接' });
      this.hungup();
    }
  }
};
</script>
<style scoped>
/*-- add 20210909 权限接入弹层 --*/
.dialog_overlay {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 5000;
}
.qx_layer {
  width: 2.76rem;
  background: #fff url(./images/qx_lybg.png) no-repeat center top;
  background-size: 100% auto;
  border-radius: 0.1rem;
  padding: 0.92rem 0 0.3rem;
  position: fixed;
  top: 50%;
  left: 50%;
  margin-left: -1.38rem;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  z-index: 6000;
}
.qx_lycont {
  padding: 0 0.35rem;
  text-align: center;
  font-size: 0.18rem;
  line-height: 0.3rem;
  color: #333333;
}
.qx_lybtn {
  padding: 0 0.18rem;
  display: flex;
  margin-top: 0.36rem;
}
.qx_lybtn a {
  display: block;
  flex: 1;
  height: 0.4rem;
  text-align: center;
  line-height: 0.4rem;
  background: #0354c2;
  border-radius: 0.5rem;
  font-size: 0.16rem;
  color: #fff;
  margin-left: 0.15rem;
}
.qx_lybtn a:first-child {
  margin-left: 0;
}
.qx_lybtn a.border {
  background: #fff;
  border: 1px solid #0354c2;
  color: #0354c2;
  line-height: 0.38rem;
}
.qx_lybtn a.disabled {
  background: #999999;
}
.qx_lybtn a.border.disabled {
  background: #fff;
  border: 1px solid #999999;
  color: #999999;
}

/*-- add 20191016 h5视频 --*/
.video_main {
  width: 100%;
  height: 100%;
  background: #000;
  position: absolute;
  top: 0rem;
  bottom: 0;
  left: 0;
  z-index: 100;
}
.common_video {
  width: 100%;
  height: 100%;
  position: relative;
  perspective: 1000;
  -webkit-perspective: 1000;
}
.common_video >>> video {
  width: 100%;
  height: 100%;
  /* object-fit: fill; */
}
.common_video >>> canvas {
  /*使用canvas渲染时的样式设置*/
  position: absolute;
  z-index: 100;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  top: 50%;
  right: 50%;
  transform: scale(-1, 1) translateX(-50%) translateY(-50%);
  -webkit-transform: scale(-1, 1) translateX(-50%) translateY(-50%);
}
.small_video {
  width: 1.6rem;
  position: absolute;
  bottom: 0.9rem;
  right: 0.15rem;
  z-index: 300;
}
.small_video >>> video {
  width: 100%;
  height: 100%;
}
.small_video >>> canvas {
  /*使用canvas渲染时的样式设置*/
  position: absolute;
  width: 1.6rem;
  bottom: 0;
  right: 0;
  z-index: 300;
  top: 0;
  transform: translateY(0);
  -webkit-transform: translateY(0);
}
.video_title {
  width: 100%;
  height: 0.44rem;
  font-size: 0.14rem;
  color: #fff;
  background: rgba(0, 0, 0, 0.3);
  background-image: -webkit-linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.05) 100%
  );
  background-image: -moz-linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.05) 100%
  );
  background-image: -o-linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.05) 100%
  );
  background-image: -webkit-gradient(
    linear,
    100% 0,
    0 0,
    from(rgba(0, 0, 0, 0.7)),
    to(rgba(0, 0, 0, 0.05))
  );
  background-image: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.05) 100%
  );
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
}
.video_title h3 {
  font-weight: normal;
  text-align: center;
  font-size: 0.14rem;
  line-height: 0.44rem;
}
.video_title .data {
  height: 0.32rem;
  line-height: 0.16rem;
  font-size: 0.1rem;
  position: absolute;
  top: 0.06rem;
  left: 0.15rem;
  opacity: 0.9;
}
.video_title .data span {
  display: block;
}
.video_title .data span em {
  font-style: normal;
}
.hangup_btn {
  height: 0.24rem;
  line-height: 0.24rem;
  padding: 0 0.11rem;
  font-size: 0.14rem;
  color: #ffffff;
  background: #fc5c49;
  border-radius: 0.5rem;
  position: absolute;
  top: 0.1rem;
  right: 0.15rem;
  z-index: 300;
}
.video_infobox {
  width: 100%;
  height: 1.8rem;
  padding: 0.3rem 0.15rem 0.1rem;
  background: rgba(0, 0, 0, 0.3);
  background-image: -webkit-linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.05) 0%,
    rgba(0, 0, 0, 0.7) 100%
  );
  background-image: -moz-linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.05) 0%,
    rgba(0, 0, 0, 0.7) 100%
  );
  background-image: -o-linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.05) 0%,
    rgba(0, 0, 0, 0.7) 100%
  );
  background-image: -webkit-gradient(
    linear,
    100% 0,
    0 0,
    from(rgba(0, 0, 0, 0.05)),
    to(rgba(0, 0, 0, 0.7))
  );
  background-image: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.05) 0%,
    rgba(0, 0, 0, 0.7) 100%
  );
  color: #fff;
  font-size: 0.14rem;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 100;
  overflow: hidden;
}
.cs_info li {
  padding: 0.05rem 0;
  line-height: 0.2rem;
  min-height: 0.6rem;
}
.cs_info li strong {
  font-weight: normal;
}
.cs_msg {
  /* padding-right: 1.75rem; */
  overflow: auto;
  height: 0.6rem;
  line-height: 0.2rem;
}
.cs_msg h5 {
  border-bottom: 1px solid rgba(255, 255, 255, 0.5);
  line-height: 0.2rem;
  font-weight: normal;
  padding-bottom: 0.1rem;
  font-size: 0.14rem;
  margin-bottom: 0.08rem;
}
.cs_msg p {
  line-height: 0.2rem;
  opacity: 0.9;
}
</style>
