<template>
  <section class="main fixed">
    <t-header
      v-show="faceRecognitionStat != 11 && faceRecognitionStat != 55"
      :title="
        faceRecognitionStat === 5 || faceRecognitionStat === 6
          ? '智能见证'
          : '人脸识别'
      "
      @back="backEvent"
    ></t-header>

    <article
      v-show="faceRecognitionStat != 11 && faceRecognitionStat != 55"
      class="content"
    >
      <div v-show="faceRecognitionStat === 1">
        <div class="fc_basebox">
          <h5>请进行人脸识别来认证身份</h5>
          <p>为了确保本人操作我们将进行人脸识别</p>
          <div class="pic"><img src="@/assets/images/video_face2.png" /></div>
        </div>
        <div class="lz_tipbox">
          <h5 class="title">视频环节请注意以下事项：</h5>
          <ul>
            <li>
              <i><img src="@/assets/images/fc_tp01.png" /></i
              ><span>确保光线清晰</span>
            </li>
            <li>
              <i><img src="@/assets/images/fc_tp04.png" /></i
              ><span>远离嘈杂环境</span>
            </li>
            <li>
              <i><img src="@/assets/images/fc_tp03.png" /></i
              ><span>不能戴帽子</span>
            </li>
          </ul>
        </div>
      </div>

      <div
        v-show="
          faceRecognitionStat === 2 ||
          faceRecognitionStat === 3 ||
          faceRecognitionStat === 4
        "
      >
        <div class="fc_sbbox">
          <div
            class="fc_imgbox"
            :class="{
              ing: faceRecognitionStat === 2,
              ok: faceRecognitionStat === 3,
              error: faceRecognitionStat === 4
            }"
          >
            <div class="pic">
              <img ref="faceImg" src="@/assets/images/video_face.png" />
            </div>
          </div>
          <h5 :class="{ error: faceRecognitionStat === 4 }">
            {{
              faceRecognitionStat === 2
                ? '识别中…'
                : faceRecognitionStat === 3
                ? '人脸识别通过'
                : '人脸识别不通过'
            }}
          </h5>
          <p v-if="faceRecognitionStat !== 2">
            {{
              faceRecognitionStat === 3
                ? '您可以继续下一步'
                : '自拍照与证件照不是同一人'
            }}
          </p>
        </div>
      </div>

      <div v-show="faceRecognitionStat === 5 || faceRecognitionStat === 6">
        <div v-show="faceRecognitionStat === 5">
          <div class="lz_basebox">
            <h3>表达开户意愿</h3>
            <p class="p1">
              请以清晰话术回答视频中的问题，我们将根据您的回答确认您的开户意愿。
            </p>
            <div class="pic"><img src="@/assets/images/video_face2.png" /></div>
          </div>
          <div class="lz_tipbox">
            <h5 class="title">视频环节请注意以下事项：</h5>
            <ul>
              <li>
                <i><img src="@/assets/images/fc_tp01.png" /></i
                ><span>确保光线清晰</span>
              </li>
              <li>
                <i><img src="@/assets/images/fc_tp04.png" /></i
                ><span>远离嘈杂环境</span>
              </li>
              <li>
                <i><img src="@/assets/images/fc_tp03.png" /></i
                ><span>不能戴帽子</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </article>
    <div class="video_start">
      <a v-if="faceRecognitionStat === 1" @click="start">开始认证 </a>
      <a v-if="faceRecognitionStat === 4" @click="start">重新认证</a>
      <a
        v-if="faceRecognitionStat === 3"
        :class="{ disable: faceRecognitionStat === 2 }"
        @click="nextClick"
        >下一步</a
      >
      <a v-if="faceRecognitionStat === 5" @click="startRecord">开始录制</a>
    </div>

    <getVideoBoxBrowser
      v-if="faceRecognitionStat === 55"
      ref="getVideoBoxBrowser"
      :user-info="userInfo"
      @getVideoCallBack="getVideoCallBack"
      @onerror="onerror"
    ></getVideoBoxBrowser>
    <livingRecognitionBrowser
      v-if="faceRecognitionStat === 11"
      ref="livingRecognitionBrowser"
      :user-info="userInfo"
      @cancel="livingCancel"
      @getImgCallBack="getImgCallBack"
    ></livingRecognitionBrowser>
  </section>
</template>

<script>
import getVideoBoxBrowser from '@/components/getVideo_webrtcSmartRecord';
import livingRecognitionBrowser from '@/components/livingRecognition_browser';
import { videoOneRegist, getVideoOneResult } from '@/service/service.js';
import { filterBase64Pre } from '@/common/util.js';
import {
  faceRecognition,
  submitH5VideoInfo
} from '@/service/videoOneService.js';
import flowMixin from '@/common/flowMixin';
export default {
  name: 'WitnessSmartOneBrowser',
  components: {
    getVideoBoxBrowser,
    livingRecognitionBrowser
  },
  mixins: [flowMixin],
  data() {
    return {
      faceRecognitionStat: 1, //人脸识别状态 1人脸识别准备 2识别中 3识别成功 4识别失败 5视频录制准备 6视频上传中
      faceFailCount: 0,
      regResult: {},
      userInfo: {}
    };
  },
  deactivated() {
    this.faceRecognitionStat = 1;
  },
  activated() {
    this.faceFailCount = 0;
  },
  methods: {
    livingCancel() {
      this.faceRecognitionStat = 1;
    },
    backEvent() {
      if (
        this.faceRecognitionStat === 2 ||
        this.faceRecognitionStat === 3 ||
        this.faceRecognitionStat === 4 ||
        this.faceRecognitionStat === 5
      ) {
        this.faceRecognitionStat = 1;
      } else {
        this.$router.replace({ name: 'videoApprove' });
      }
    },
    onerror(o) {
      o && o.msg && _hvueToast({ mes: o.msg });
      this.faceRecognitionStat = 5;
    },
    getImgCallBack(data) {
      this.faceRecognitionStat = 2;
      this.$refs.faceImg.src =
        'data:image/jpeg;base64,' + filterBase64Pre(data.base64);

      let param = {
        flowNo: this.userInfo.flow_no,
        faceImageData: filterBase64Pre(data.base64),
        livingImagePath: data.faceImage
      };
      return faceRecognition(param, { loading: false })
        .then((data) => {
          if (data.code == 0 && data.data.pass) {
            this.faceRecognitionStat = 5;
          } else {
            if (
              this.faceFailCount >=
              $hvue.customConfig.video.faceFailNum - 1
            ) {
              this.faceRecognitionStat = 4;
              _hvueAlert({
                title: '温馨提示',
                mes: '由于长时间人脸比对不通过，我们为您切换到一对一人工见证服务。',
                callback: () => {
                  this.$router.replace({ name: 'witnessTwo' });
                }
              });
            } else {
              this.faceFailCount++;
              this.faceRecognitionStat = 4;
            }
          }
        })
        .catch((e) => {
          this.faceRecognitionStat = 4;
          _hvueAlert({ mes: e.message });
        });
    },
    getVideoCallBack(data) {
      if (data.error_no != 0) {
        if (data.error_no == '-1') {
          _hvueAlert({
            title: '温馨提示',
            mes: '由于长时间未完成视频录制，我们为您切换到一对一人工见证服务',
            callback: () => {
              this.$router.replace({ name: 'witnessTwo' });
            }
          });
          return;
        } else {
          _hvueAlert({ mes: data.error_info });
        }
        return;
      }
      // 提交通过视频节点
      submitH5VideoInfo({
        videoPath: data.videoPath,
        totalTime: data.video_length
      })
        .then((data) => {
          if (data.code == 0) {
            getVideoOneResult({
              flowToken: sessionStorage.getItem('TKFlowToken')
            }).then((data) => {
              if (data.code == 0) {
                this.nextFlow({}); // 接口处理数据
              } else {
                this.faceRecognitionStat = 5;
                _hvueAlert({ mes: data.msg });
              }
            });
          } else {
            return Promise.reject(data.msg);
          }
        })
        .catch((e) => {
          _hvueToast({ mes: e });
          this.faceRecognitionStat = 5;
        });
    },
    nextClick() {
      this.faceRecognitionStat = 5;
    },
    startRecord() {
      this.faceRecognitionStat = 55;
    },
    /**
     * 产生随机动作
     * num 动作个数
     */
    getRandAction(num) {
      var actionArr = ['0', '1', '2', '3']; // 0:眨眼 1:上下点头 2:张嘴 3:左右转头
      var resultArr = [];
      if (!num) num = 1;
      if (num > actionArr.length) num = actionArr.length;
      for (var i = 0; i < num; i++) {
        var rand = Math.floor(Math.random() * actionArr.length);
        resultArr.push(actionArr.splice(rand, 1));
      }
      return resultArr.join(',');
    },
    async start() {
      let result = await videoOneRegist({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        idCardPortrait: JSON.parse(
          this.flowOutputInfo.inProperty['idCardPortrait']
        ).src
      });
      if (result.code == 0) {
        this.regResult = result.data;
        $h.setSession('videoToken', this.regResult.jwtToken);
        this.userInfo = Object.assign(this.userInfo, {
          flow_no: this.regResult.regFlowNo,
          authorization: this.regResult.jwtToken,
          confirm_tips: `我是${this.flowOutputInfo.inProperty.clientName}，已知晓期货市场风险，已阅读并充分理解网上开户协议条款，自愿选择在思迪开立账户，并承诺所提供的信息及证件真实、合法、有效。`,
          tips: [
            {
              tip_content:
                '尊敬的客户，请您在“滴”声后大声使用“是”或“不是”回答以下问题。'
            },
            {
              tip_content:
                '您是否为<em style="color:red;">' +
                this.flowOutputInfo.inProperty.clientName +
                '</em>本人，且已知晓期货市场风险，已阅读并充分理解网上开户协议条款，自愿在思迪开户？',
              standardans: '/^((?!不).)*[是对4事四思室市氏士似]((?!不).)*$/',
              wait_time: '5',
              prompt: '请清晰回答“<b>是或否</b>”',
              failans: '/[不否部布步]/' // 错误回答
            },
            {
              tip_content: '录制完毕，感谢您的配合。'
            }
          ]
        });
        this.faceRecognitionStat = 11;
      } else {
        _hvueAlert({ mes: result.msg });
      }
    }
  }
};
</script>
