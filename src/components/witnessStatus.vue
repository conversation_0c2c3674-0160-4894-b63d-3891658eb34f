<template>
  <section class="main fixed">
    <t-header :percent="40" @back="cancelQueueClick"></t-header>
    <article class="content">
      <div v-if="queueStatus === 1" class="queue_box">
        <div class="queue_level">
          <i class="bg"></i>
          <div class="pic">
            <img src="@/assets/images/queue_cs.png" />
          </div>
        </div>
        <h5>坐席忙碌中，请稍等…</h5>
      </div>
      <div v-else-if="queueStatus === 2" class="queue_box">
        <div class="queue_level">
          <i class="bg"></i>
          <span class="num">{{ queueLocation }}</span>
        </div>
        <h5>正在排队中…</h5>
        <p>您当前排在第{{ queueLocation }}位</p>
      </div>
      <div v-else-if="queueStatus === 3 || queueStatus === 4" class="queue_box">
        <div class="queue_level">
          <i class="bg"></i>
          <div class="pic">
            <img src="@/assets/images/queue_cs.png" />
          </div>
        </div>
        <h5>
          {{ queueStatus === 3 ? '等待坐席进入…' : '等待坐席确认...' }}
        </h5>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn" @click="cancelQueueClick">
        <a class="p_button">取消排队</a>
      </div>
    </footer>

    <witnessTchat
      v-if="platform === 'browser' && videoType == '0' && queueStatus == 3"
      ref="browser"
      :user-info="userInfo"
      @videoCallBack="videoCallBack"
    ></witnessTchat>
  </section>
</template>

<script>
import { videoQueue, cancelQueue } from '@/service/videoTwoService';
import witnessTchat from '@/components/witness_tchat';

export default {
  name: 'WitnessStatus',
  components: {
    witnessTchat
  },
  props: {
    userInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      platform: $hvue.env, // 渠道类型
      videoType: $hvue.customConfig.video.videoType || 0, // 1anychat 0 tchat
      queueLocation: '-',
      queueInterval: null,
      queueStatus: 1 // 排队状态 1：客服忙碌中 2：排队中 3:接入中 4：坐席确认中
    };
  },
  mounted() {
    this.start();
  },
  destroyed() {
    this.cleanData();
  },
  deactivated() {
    this.cleanData();
  },
  methods: {
    start() {
      this.queue();
      this.queueInterval = setInterval(this.queue, 2000);
    },
    queue() {
      let param = {
        regFlowNo: this.userInfo.regFlowNo,
        clientId: this.userInfo.regFlowNo,
        custName: this.userInfo.clientName,
        orgNo: this.userInfo.branchNo,
        bizType: this.userInfo.bizType,
        level: this.userInfo.level || 0,
        origin: $hvue.platform === '0' ? '3' : $hvue.platform,
        videoType: this.videoType
      };
      videoQueue(param, {
        loading: false
      })
        .then((data) => {
          console.log('platform=====>' + this.platform);
          if (data.code == '0') {
            let result = data.data;
            if (result.staffExist === true) {
              if (result.queueLocation == '0') {
                // 开始视频
                this.queueStatus = 3;
                clearInterval(this.queueInterval);
                let roomInfoArr = result.serverRoomNo.split(':');
                if (this.platform === 'ths') {
                  this.$refs.ths.start(roomInfoArr);
                } else if (this.platform === 'browser') {
                  this.$nextTick(() => {
                    this.$refs.browser.start(roomInfoArr, result);
                  });
                }
                // else if (this.platform === 'thinkive') {
                //   this.$refs.thinkive.start(roomInfoArr);
                // }
              } else if (result.queueLocation == '-1') {
                this.queueStatus = 4; // 坐席确认中
              } else {
                this.queueLocation = result.queueLocation; // 排队中
                this.queueStatus = 2;
              }
            } else {
              this.queueStatus = 1; // 无坐席
            }
          } else {
            _hvueToast({
              mes: data.msg
            });
          }
        })
        .catch((e) => {
          if (e.message === 'Network Error') {
            clearInterval(this.queueInterval);
            this.$router.replace({
              name: 'witnessTwo'
            });
          } else {
            console.log(e);
          }
        });
    },
    cancelQueueClick() {
      var param = {
        regFlowNo: this.userInfo.regFlowNo,
        orgNo: this.userInfo.branchNo,
        bizType: this.userInfo.bizType,
        abnormalExit: '0'
      };
      cancelQueue(param)
        .then((data) => {
          if (data.code == '0') {
            this.$emit('cancel');
          } else {
            _hvueToast({ mes: data.msg });
          }
        })
        .catch((e) => {
          if (e.message === 'Network Error') {
            clearInterval(this.queueInterval);
            this.$router.replace({
              name: 'witnessTwo'
            });
          } else {
            console.log(e);
          }
        });
    },
    cleanData() {
      clearInterval(this.queueInterval);
      this.queueStatus = 1;
    },
    videoCallBack(param) {
      if (param.hungup) {
        this.cancelQueueClick();
      } else {
        this.$emit('videoCallBack', { message: JSON.stringify(param) }); // 和app返回参数一致
      }
    }
  }
};
</script>

<style>
.queue_box {
  background: #ffffff;
  padding: 0.5rem 0.2rem 0.2rem;
  min-height: 3.1rem;
  margin-bottom: 0.1rem;
  text-align: center;
  line-height: 0.2rem;
  color: #999999;
}

.queue_box h5 {
  font-size: 0.24rem;
  line-height: 0.32rem;
  font-weight: normal;
  color: #333333;
  margin-bottom: 0.1rem;
}
</style>
