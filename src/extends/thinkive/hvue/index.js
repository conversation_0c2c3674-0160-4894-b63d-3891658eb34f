import Vue from 'vue';
import hvue from 'thinkive-hvue';
import 'thinkive-hui/lib/theme-default/hui.css'; // 引入样式库

// 这里要特别引起重视是的window._hvueToast/window._hvueLoading两个全局变量
// 因为底层库hvue中用window._hvueToast/window._hvueLoading来调用Toast/Loading这两组件，
// 所以，必须在这里赋值它们。这样做是为让ui库与hvue库解藕，大家可以根据业务用其他ui库，只要把这两组件赋值给底层调用即可.
import {
  Toast,
  Loading,
  Alert,
  Confirm,
  DateTime,
  Picker,
  Password,
  Keypanel,
  Uploader
} from 'thinkive-hui';

hvue.setSession = (function () {
  // 生产环境本地session加密存储
  var setSession = hvue.setSession;
  return function (key, value, options) {
    setSession(
      key,
      value,
      Object.assign({ encrypt: false }, options)
    );
  };
})();
hvue.getSession = (function () {
  var getSession = hvue.getSession;
  return function (key, options) {
    return getSession(
      key,
      Object.assign({ decrypt: false }, options)
    );
  };
})();
// console.log(Loading);
Vue.prototype.$h = window.$h = hvue;
Vue.use(DateTime);
Vue.use(Picker);
Vue.use(Password);
Vue.use(Keypanel);
Vue.use(Uploader);
Vue.prototype.$huiOpts = {
  loadingText: '', // loading组件默认提示内容
  loadingType: 'hoop', // loading组件type默认值
  loadingColor: '#000000', // loading组件color默认值
  loadingTransparentBackground: false // loading组件loadingTransparentBackground默认值
};

window._hvueToast = Vue.prototype.$toast = Toast;
window._hvueLoading = Vue.prototype.$loading = Loading;
// {
//   open: function () {
//     Loading.open('', {
//       type: 'circular'
//     });
//   },
//   close: function () {
//     Loading.close();
//   }
// };

window._hvueConfirm = Vue.prototype.$confirm = Confirm;
window._hvueAlert = Vue.prototype.$alert = Alert;
