import Vue from 'vue';
import Vee<PERSON>alidate, { Validator } from 'vee-validate';
import validate from '@/common/validate';
import { rules } from '@/common/rule';
// import zh from 'vee-validate/dist/locale/zh_CN';

// 配置表单校验中文
// Validator.addLocale(zh);

const config = {
  errorBagName: 'errors',
  fieldsBagName: 'fieldBags',
  delay: 0,
  strict: true,
  enableAutoClasses: false,
  locale: 'zh_CN',
  classNames: {
    touched: '',
    untouched: '',
    valid: '',
    invalid: '',
    pristine: '',
    dirty: ''
  },
  events: 'blur',
  inject: true
};
Vue.use(VeeValidate, config);
Vue.prototype.$validate = validate;
for (let key in rules) {
  key && Validator.extend(key, rules[key]);
}
