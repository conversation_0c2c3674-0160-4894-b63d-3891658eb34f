import Vue from 'vue';
import App from './App';
import router from './router';
import store from './store';
import Fragment from 'vue-fragment';
import * as filters from '@/common/filter';
import xss from 'xss';
import TAlert from '@/components/TAlert/alert.js';
// 引入拓展库
import '@/extends/index';
// import { HKeypanel } from 'thinkive-hui';

// 项目css文件
// import './assets/BJExchange/css/style.css';
// import './assets/BJExchange/css/common.css'
import './assets/css/iconfont.css';
import './assets/css/style.less';

// import './assets/css/style_h5.css';
Vue.prototype.$TAlert = TAlert;
Vue.use(Fragment.Plugin);
Vue.directive('throttle', {
  // 添加指令用于对点击事件节流
  bind: function (el, binding) {
    let time = binding.value || 1000;
    let preTime = new Date().getTime();
    el.addEventListener('touchstart', (e) => {
      const nowTime = new Date().getTime();
      if (preTime && nowTime - preTime < time) {
        e.preventDefault();
        e.stopImmediatePropagation();
      } else {
        preTime = nowTime;
      }
    });
  }
});

Vue.prototype.xss = xss;
Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key]);
});

Vue.config.productionTip = false;
// 解决ios下css: active伪类无效果问题
document.addEventListener('touchstart', function () {}, { passive: false });
/* eslint-disable no-new */

function getNetworkRouter() {
  const origin = window.location.origin;
  const host = window.location.host;
  const sitHost = 'fzsdbusiness.yjbtest.com';
  const uatHost = 'uatfzsdbusiness.yjbtest.com:8443';
  const preProdHost = 'spacctbiz.yongjinbao.com.cn';
  const prodHost = 'acctbiz.yongjinbao.com.cn';
  const grayEnvHost = 'preacctbiz.yongjinbao.com.cn'; //灰度环境
  let options = {
    dev: PACK_ENV === 'local' || host === sitHost,
    target: 'https://fzwebapps.yjbtest.com',
    server: 'https://fzsdbusiness.yjbtest.com',
    financialServer: 'https://fzmall.yjbtest.com/mall-web/financial-market/quoted',
    financingFacilityServer: 'https://fzwx3g.yjbtest.com', // 两融开户地址
    channelDomain :'https://fzfinder.yjbtest.com', // 火山埋点上报
  };
  if (origin.indexOf(sitHost) !== -1 || origin.indexOf(uatHost) !== -1) {
    options.server = origin;
  } else if (
    origin.indexOf(prodHost) !== -1 ||
    origin.indexOf(preProdHost) !== -1 ||
    origin.indexOf(grayEnvHost) !== -1
  ) {
    options.target = 'https://webapps.yongjinbao.com.cn';
    options.server = origin;
    options.financialServer = 'https://mall.yongjinbao.com.cn/mall-web/financial-market/quoted';
    options.financingFacilityServer = 'https://wx3g.yongjinbao.com.cn';
    options.channelDomain = 'https://finder.yongjinbao.com.cn';
  }
  return options;
}
window.serviceOptions = getNetworkRouter();

// 注册Vue实例
loadJS('/bc-h5-view/views/configuration.js?t=' + new Date().getTime())
  .then(() => {
    loadJS('/bc-h5-view/views/theme.js?t=' + new Date().getTime())
      .then(() => {
        window.vm = new Vue({
          router,
          store,
          render: (h) => h(App)
        });
        window.vm.$mount('#app');
      })
      .catch((e) => {
        if (confirm('theme.js错误' + e.name)) {
          window.location.reload();
        }
      });
  })
  .catch((e) => {
    if (confirm('加载configuration.js错误' + e.name)) {
      window.location.reload();
    }
  });

/**
 * 添加日期格式化方法
 */
// eslint-disable-next-line no-extend-native
Date.prototype.format = function (fmt) {
  var o = {
    'M+': this.getMonth() + 1, // 月份
    'd+': this.getDate(), // 日
    'h+': this.getHours(), // 小时
    'm+': this.getMinutes(), // 分
    's+': this.getSeconds(), // 秒
    'q+': Math.floor((this.getMonth() + 3) / 3), // 季度
    S: this.getMilliseconds() // 毫秒
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (this.getFullYear() + '').substr(4 - RegExp.$1.length)
    );
  }
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      );
    }
  }
  return fmt;
};

function loadJS(urls) {
  if (!Array.isArray(urls)) {
    urls = [urls];
  }
  let success = 0;
  return new Promise(function (resolve, reject) {
    urls.every((url) => {
      createScriptElement(
        url,
        function () {
          ++success;
          if (success === urls.length) {
            resolve();
          }
        },
        function (e) {
          reject(e);
        }
      );
      return true;
    });
  });
}

function createScriptElement(url, success, fail) {
  var script = document.createElement('script');
  script.type = 'text/javascript';
  script.onload = function () {
    success();
  };
  script.onerror = function (e) {
    fail(e);
  };
  script.src = url;
  document.getElementsByTagName('head')[0].appendChild(script);
}
