/**
 * @description 对接鸿蒙系统，h5调用方法
 */
import vm from 'vue';

const baseHandler = function (funcNo = '', params = {}) {
  console.info('baseHandler:',funcNo, params);
  return new Promise((resolve, reject) => {
    window.callNative(funcNo, JSON.stringify(params), (data) => {
      console.info('hm callNative回调：', data);
      if (data && data.code == 0) {
        resolve(data);
      } else {
        reject(data);
      }
    });
  });
};

const h5ToHarmonyOS = async function (funcNo = '', it) {
  try {
    return await baseHandler(funcNo, it);
  } catch (error) {
    vm.$TAlert({
      tips: error
    });
    console.error(error);
  }
};

/**
 * @description 关闭当前webview
 */
export const closeWebHM = function (params) {
  return h5ToHarmonyOS(2009, params);
};

/**
 * @description 获取客户端系统参数
 */
export const getSysParamsHM = function (params) {
  return h5ToHarmonyOS(2000, params);
};

/**
 * @description 设置头部标题文案
 */
export const setTitleHM = function (params) {
  return h5ToHarmonyOS(2012, params);
};

/**
 * @description 设置头部标题背景色
 */
export const setTitleBGColorHM = function (params) {
  return h5ToHarmonyOS(3016, params);
};

/**
 * @description 关闭风测业务
 */
export const closeRiskFromLogin = function (params) {
  return h5ToHarmonyOS(4009, params);
};

/**
 * @description 拨打电话
 */
export const callPhoneHM = function (params) {
  return h5ToHarmonyOS(3000, params);
};

/**
 * @description 新开webview跳转
 */
export const callPushHM = function (url) {
  return h5ToHarmonyOS(2008, {
    leftType: 1,
    url,
    rightType: 99,
    rightText: "",
    animated: 0,
});
};
