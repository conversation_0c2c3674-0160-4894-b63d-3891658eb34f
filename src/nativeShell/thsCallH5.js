window.rspWeb = function (data) {
  data = JSON.parse(data);
  switch (data.action) {
    case 'getTgInfo':
      window.getTgInfoCallBack && window.getTgInfoCallBack(data.param);
      break;
    case 'takePhoto':
      window.imgCallBack && window.imgCallBack(data.param);
      break;
    case 'pickAlbum':
      window.imgCallBack && window.imgCallBack(data.param);
      break;
    case 'showDatePick':
      window.pickDateCallBack && window.pickDateCallBack(data.param);
      break;
    case 'videoWitness':
      window.videoCallBack && window.videoCallBack(data.param);
      break;
    case 'card_local_ocr':
      window.bankOcrCallBack && window.bankOcrCallBack(data.param);
      break;
    case 'stepSync':
      console.log('步骤上报回调:' + data.param.errorInfo);
      break;
    default:
      console.warn('未处理的同花顺回调:' + this.JSON.stringify(data));
  }
};
