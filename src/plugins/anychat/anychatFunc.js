let userId;
let roomNo;
let isRejected;

var mSelfUserId = -1; // 本地用户ID
var mTargetUserId = -1; // 目标用户ID（请求了对方的音视频）

var functionListener = {
  onTransBufferNotify: null, // 接收透明通道消息
  onMsgNotify: null, // 接收文本消息
  onUserExitRoom: null, // 对方离开房间（包含断网等异常情况）
  onLinkClose: null // 连接断开 包含服务器异常和客户端网络异常
};

function init(funcCallBack) {
  // 获取浏览器信息，并匹配Edge浏览器
  var errorCode = BRAC_InitSDK(1);
  if (errorCode == GV_ERR_BROWSERNOWEBRTC) {
    alert('不支持的浏览器');
    return false; // 不支持webrtc
  }

  funcCallBack = funcCallBack || {};
  for (var fun in functionListener) {
    // 注入回调方法
    if (!funcCallBack[fun]) {
      alert('请在初始化anychat时注入回调方法:【 ' + fun + ' 】');
    } else {
      functionListener[fun] = funcCallBack[fun];
    }
  }
}

/**
 * @description 登录服务器，视频连接
 * @param [{String}] server_roomNo 格式：IP:PORT:房间号
 * @param {String} user_id 用户编号
 */
function videoConnect(server_roomNo, user_id, branch_name, appid) {
  userId = user_id;
  isRejected = false;
  if (server_roomNo) {
    if (Object.prototype.toString.call(server_roomNo) != '[object Array]') {
      server_roomNo = [server_roomNo];
    }
    BRAC_SetSDKOption(BRAC_SO_CLOUD_APPGUID, appid);
    BRAC_SetSDKOption(BRAC_SO_NETWORK_IPV6DNS, 1);
    for (let i = 0; i < server_roomNo.length; i++) {
      var serverInfoArr = server_roomNo[i].split(':');
      roomNo = serverInfoArr[2];
      BRAC_Connect(serverInfoArr[0], serverInfoArr[1]);
    }
  }
}

function videoLogin() {
  BRAC_Login('user' + userId, '123456', 0);
  // BRAC_LoginEx("user" + userId, -1, "user" + userId, appid, 0, '', '');
}

function enterRoom() {
  BRAC_EnterRoom(roomNo, '', 0);
}

/**
 * @description 离开房间
 */
function leaveRoom() {
  BRAC_SendTextMessage(-1, -1, 'SYS:10002');

  // 离开房间
  BRAC_LeaveRoom(-1);
}

/**
 * @description 退出
 */
function logout() {
  // 退出登录，释放SDK资源
  BRAC_Logout();
}

function GetID(id) {
  if (document.getElementById) {
    return document.getElementById(id);
  } else if (window[id]) {
    return window[id];
  }
  return null;
}

// 打开指定用户的音视频
function RequestOtherUserVideo(userid, isopen) {
  if (userid) {
    if (isopen == 1) {
      mTargetUserId = userid; // 设置被点用户ID为全局变量
      BRAC_UserCameraControlEx(userid, 1, 0, 0, ''); // 请求对方视频
      BRAC_UserSpeakControl(userid, 1); // 请求对方语音
      // 设置远程视频显示位置
      BRAC_SetVideoPos(
        userid,
        document.getElementById('remoteVideoDiv'),
        'remoteVideo'
      );
    } else {
      BRAC_UserCameraControlEx(userid, 0, 0, 0, '');
      BRAC_UserSpeakControl(userid, 0); // 关闭对方语音
      functionListener.onUserExitRoom();
    }
  }
}

/********************************************
 *        事件回调部分        *
 *******************************************/

// 收到文字消息
function OnAnyChatFuncTextMessage(
  dwFromUserId,
  dwToUserId,
  bSecret,
  lpMsgBuf,
  dwLen
) {
  functionListener.onMsgNotify(lpMsgBuf);
}

// 收到透明通道传输数据
function OnAnyChatFuncTransBuffer(dwUserId, lpBuf, dwLen) {
  isRejected = true;
  functionListener.onTransBufferNotify(lpBuf);
}
// 收到透明通道传输数据(扩展方法)
function OnAnyChatFuncTransBufferEx(
  dwUserId,
  lpBuf,
  dwLen,
  wParam,
  lParam,
  dwTaskId
) {
  isRejected = true;
  functionListener.onTransBufferNotify(lpBuf);
}

/********************************************
 *		AnyChat SDK核心业务流程				*
 *******************************************/

// 客户端连接服务器，bSuccess表示是否连接成功，errorcode表示出错代码
function OnAnyChatFuncConnect(bSuccess, errorcode) {
  if (errorcode == 0) {
    videoLogin();
  }
}

// 客户端登录系统，dwUserId表示自己的用户ID号，errorcode表示登录结果：0 成功，否则为出错代码，参考出错代码定义
function OnAnyChatFuncLoginSystem(dwUserId, errorcode) {
  if (errorcode == 0) {
    mSelfUserId = dwUserId;
    enterRoom();
  }
}

// 客户端进入房间，dwRoomId表示所进入房间的ID号，errorcode表示是否进入房间：0成功进入，否则为出错代码
function OnAnyChatFuncEnterRoom(dwRoomId, errorcode) {
  if (errorcode == 0) {
    var mDevices = BRAC_EnumDevices(BRAC_DEVICE_VIDEOCAPTURE);
    BRAC_SetUserStreamInfo(-1, 0, BRAC_SO_LOCALVIDEO_DEVICENAME, mDevices[0]);
    BRAC_UserCameraControlEx(mSelfUserId, 1, 0, 0, ''); // 打开本地视频
    BRAC_UserSpeakControl(mSelfUserId, 1); // 打开本地语音

    // 设置本地视频显示位置
    BRAC_SetVideoPos(
      mSelfUserId,
      document.getElementById('localVideoDiv'),
      'localVideo'
    );
  } else {
    mTargetUserId = -1;
    //	BRAC_UserCameraControl(mSelfUserId, 0);
    BRAC_UserCameraControlEx(mSelfUserId, 0, 0, 0, '');
    BRAC_UserSpeakControl(mSelfUserId, 0);
    mSelfUserId = -1;
    // 退出登录，释放SDK资源
    BRAC_Logout();
  }
}

// 收到当前房间的在线用户信息，进入房间后触发一次，dwUserCount表示在线用户数（包含自己），dwRoomId表示房间ID
function OnAnyChatFuncRoomOnlineUser(dwUserCount, dwRoomId) {
  var useridlist = BRAC_GetOnlineUser();
  console.log(
    'OnAnyChatFuncRoomOnlineUser:' +
      dwUserCount +
      ',' +
      dwRoomId +
      ',' +
      JSON.stringify(useridlist)
  );
  if (dwUserCount > 1) {
    // 请求其中一个用户的音视频
    for (var k = 0; k < useridlist.length; k++) {
      if (useridlist[k] == mSelfUserId) {
        continue;
      }
      RequestOtherUserVideo(useridlist[k], 1);
      break;
    }
  }
}

// 用户进入（离开）房间，dwUserId表示用户ID号，bEnterRoom表示该用户是进入（1）或离开（0）房间
function OnAnyChatUserFuncAtRoom(dwUserId, bEnterRoom) {
  console.log('OnAnyChatUserFuncAtRoom:' + dwUserId + ',' + bEnterRoom);
  if (bEnterRoom == 1) {
    RequestOtherUserVideo(dwUserId, 1);
  } else {
    RequestOtherUserVideo(dwUserId, 0);
  }
}

// 网络连接已关闭，该消息只有在客户端连接服务器成功之后，网络异常中断之时触发，reason表示连接断开的原因
function OnAnyChatFuncLinkClose(reason, errorcode) {
  console.log('OnAnyChatFuncLinkClose:' + new Date());
  if (isRejected) return;
  // reVideoDivSize();
  var errorMsg = '网络异常';
  if (reason == '0') {
    errorMsg = '网络中断或服务器被关闭';
  }
  if (reason == '1') {
    errorMsg = '该用户在其它计算机上登录，之前的连接被服务器断开';
  }
  if (reason == '2') {
    errorMsg = '服务器功能受限制（演示模式下不允许长时间连接服务器）';
  }
  if (reason == '4') {
    errorMsg = '客户端程序版本太旧，不允许连接';
  }
  if (reason == '5') {
    errorMsg = '连接服务器认证失败（服务器设置了认证密码）';
  }
  if (reason == '6') {
    errorMsg = '网络连接超时，服务器主动断开';
  }
  if (reason == '7') {
    errorMsg = '服务器不支持当前平台的连接，或连接用户数超过授权用户数';
  }
  BRAC_SetVideoPos(0, GetID('remoteVideoDiv'), 'remoteVideo');
  functionListener.onLinkClose(errorMsg, reason);
  console.error(errorMsg);
}

function transBuffer(message) {
  BRAC_TransBuffer(mTargetUserId, message);
}

function transBufferEx(message) {
  BRAC_TransBufferEx(mTargetUserId, message);
}

// 用户视频分辩率发生变化，dwUserId（INT）表示用户ID号，dwResolution（INT）表示用户的视频分辨率组合值（低16位表示宽度，高16位表示高度）
function OnAnyChatFuncVideoSizeChange(dwUserId, dwResolution) {
  if (dwUserId != mTargetUserId) {
    return;
  }
  var height = dwResolution >> 16;
  var width = dwResolution & 0x0000ffff;
  var divWidth = GetID('AnyChatRemoteVideoDiv').offsetWidth;
  var divHeight = GetID('AnyChatRemoteVideoDiv').offsetHeight;
  // 如果采用视频显示裁剪模式是动态模式，可根据分辨率的情况，动态改变div布局，使得画面不变形。
  if (width > height) {
    if (divWidth < divHeight) {
      // 竖屏切换到横屏情况
      GetID('AnyChatRemoteVideoDiv').style.width = (4.0 / 3) * divHeight + 'px';
      GetID('AnyChatRemoteVideoDiv').style.height = divHeight + 'px';
    }
  } else {
    if (divWidth > divHeight) {
      // 横屏切换到竖屏情况
      GetID('AnyChatRemoteVideoDiv').style.width = (3.0 / 4) * divHeight + 'px';
      GetID('AnyChatRemoteVideoDiv').style.height = divHeight + 'px';
    }
  }
}

window.OnAnyChatFuncConnect = OnAnyChatFuncConnect;
window.OnAnyChatFuncLoginSystem = OnAnyChatFuncLoginSystem;
window.OnAnyChatFuncEnterRoom = OnAnyChatFuncEnterRoom;
window.OnAnyChatFuncRoomOnlineUser = OnAnyChatFuncRoomOnlineUser;
window.OnAnyChatUserFuncAtRoom = OnAnyChatUserFuncAtRoom;
window.OnAnyChatFuncLinkClose = OnAnyChatFuncLinkClose;
window.OnAnyChatFuncVideoSizeChange = OnAnyChatFuncVideoSizeChange;
window.OnAnyChatFuncTextMessage = OnAnyChatFuncTextMessage;
window.OnAnyChatFuncTransBuffer = OnAnyChatFuncTransBuffer;
window.OnAnyChatFuncTransBufferEx = OnAnyChatFuncTransBufferEx;

export default {
  init,
  leaveRoom,
  logout,
  videoConnect,
  transBuffer: transBuffer,
  transBufferEx: transBufferEx
};
