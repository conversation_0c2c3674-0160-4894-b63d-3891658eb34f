/*创建时间2021-07-06 20:03:24 PM 作者：xhq*/
var TChatRTC={init:function(){},login:function(){},enterRoom:function(){},getUserList:function(){},getRoomList:function(){},videoCall:function(){},replayVideoCall:function(){},stopVideo:function(){},exitRoom:function(){},heartbeat:function(){},sendMsg:function(){},sendMsgByTransBuffer:function(){},startWebRtc:function(){},reConnect:function(){},wsClose:function(){},closeAll:function(){},openLocalCamera:function(){},closeMedia:function(){},listenerConfig:{addNewUser:null,videoCalled:null,exitRoomNotify:null,onMsgNotify:null,onTransBufferNotify:null,onRemoteStreamAdd:null,timeout:null,netError:null,socketError:null,destory:null},version:"2.2.0"};!function(a){function b(){try{E=new WebSocket(a.config.signalServer),E.binaryType="arraybuffer",E.onmessage=ib,E.onopen=jb,E.onerror=kb,E.onclose=lb}catch(b){alert("wsConnect err:"+JSON.stringify(b))}}function c(b,c){var d={msgtype:b,request:c};1==a.config.isDebug&&console.log("send:"+JSON.stringify(d));var e=S&&S.encode(S.fromObject(d)).finish();E&&1==E.readyState&&E.send(e)}function d(){if(!G){if(J=document.querySelector("#"+a.config.localId),!J)return alert(a.config.localId+"不存在"),void 0;G=document.createElement("video"),G.setAttribute("playsinline",""),G.setAttribute("id","tchatLocalVideo"),G.muted=!0,A||G.setAttribute("autoplay",""),J.appendChild(G)}}function f(a,b){a?protobuf.load(a,function(a,c){a?g(b):b(c)}):g(b)}function g(a){var b=new Error("err"),c=b.stack||b.sourceURL||b.stacktrace||"",d=/(?:http|https|file):\/\/.*?\/.+?.js/,e=(d.exec(c)||[])[0]||"";e=e.substring(0,e.lastIndexOf("/")+1).replace(location.protocol+"//"+location.host,""),protobuf.load(e+"sd.data_structure.proto",function(b,c){b?alert("加载protobuf文件报错:"+err+";"+b):a(c)})}function h(a){var b,c,d=new Array;b=a.length;for(var e=0;b>e;e++)c=a.charCodeAt(e),c>=65536&&1114111>=c?(d.push(c>>18&7|240),d.push(c>>12&63|128),d.push(c>>6&63|128),d.push(63&c|128)):c>=2048&&65535>=c?(d.push(c>>12&15|224),d.push(c>>6&63|128),d.push(63&c|128)):c>=128&&2047>=c?(d.push(c>>6&31|192),d.push(63&c|128)):d.push(255&c);return d}function i(b,c,d){G.srcObject=b,F=b,G.onloadedmetadata=function(){if(G.videoWidth<240)w("renderVideo fail ","error"),a.closeMedia(),G.srcObject=null,d({name:"render error"});else{if(b.addTrack){var e=b.getVideoTracks();w("video tracks length："+e.length);var f=b.getAudioTracks();w("audio tracks length："+f.length),b.getTracks().forEach(function(a){w("tracks prop:"+JSON.stringify({id:a.id,contentHint:a.contentHint,enabled:a.enabled,kind:a.kind,label:a.label,muted:a.muted,readonly:a.readonly,readyState:a.readyState,remote:a.remote}))})}else w("stream get success");G.style.webkitTransform="rotateY(180deg)",G.style.transform="rotateY(180deg)",G.play(),c()}}}function j(a,b,c,d){navigator.mediaDevices.getUserMedia(a).then(function(e){w("get media success. constraints："+JSON.stringify(a));var f=e.getVideoTracks();d&&f&&f[0]&&f[0].getSettings().facingMode&&"user"!=f[0].getSettings().facingMode?(w("camera is not front, retry front"),e.getTracks().forEach(function(a){a.stop()}),j({audio:!0,video:{facingMode:"user"}},b,c)):b(e)}).catch(function(b){var d="";Object.getOwnPropertyNames(b).forEach(function(a){d+=a+","+b[a]+";"}),w("get media error. constraints："+JSON.stringify(a)+","+d,"error"),w(JSON.stringify(b),"error"),c(b)})}function k(){l(),_=setInterval(function(){navigator.onLine?l():(window.clearInterval(_),window.clearInterval(W),window.clearInterval(cb),a.closeAll(),a.listenerConfig.netError&&a.listenerConfig.netError())},300*Z)}function l(){a.heartbeat();var b=new Date,c=(b.getTime()-$.getTime())/1e3;0==X&&c>Z&&(window.clearInterval(_),window.clearInterval(W),window.clearInterval(cb),a.closeAll(),setTimeout(function(){navigator.onLine?(X=!0,a.listenerConfig.timeout&&a.listenerConfig.timeout()):a.listenerConfig.netError&&a.listenerConfig.netError()},200))}function m(a){w("send ice"),c(8265,{eireq:{iceinfo:a,ownuserid:D.userid,peeruserid:D.peeruserid,roomid:D.roomid}})}function n(){var b=0,c=0;W=setInterval(function(){a.getStatsData(function(d){if(a.config.netInfoIsShow)o(d,b,c);else{var e=document.querySelector(".tchatNet");e&&e.parentNode.removeChild(e)}if(a.config.netInfoIsSend){var f={duration:a.config.netInfoSendTime,senddata:d.curTotalSend-b,recvdata:d.curTotalReceived-c};a.sendStats(f)}b=d.curTotalSend,c=d.curTotalReceived})},1e3*a.config.netInfoSendTime)}function o(b,c,d){function e(a){for(var b in a)i+=b+":&nbsp;"+a[b]+"<br>"}var f=(b.curTotalSend-c)/1024/a.config.netInfoSendTime,g=(b.curTotalReceived-d)/1024/a.config.netInfoSendTime,h={U:f.toFixed(2)+"kb/s &nbsp;&nbsp;D:"+g.toFixed(2)+"kb/s",roomid:D.roomid,audioSend:(b.audioBytesSent/1024).toFixed(2)+"kb",videoBytesSent:(b.videoBytesSent/1024).toFixed(2)+"kb",audioReceived:(b.audioBytesReceived/1024).toFixed(2)+"kb",videoReceived:(b.videoBytesReceived/1024).toFixed(2)+"kb",ua:navigator.userAgent},i="";e(h),F.getTracks().forEach(function(a){e(a.getSettings())});var j=document.querySelector(".tchatNet");j||(j=document.createElement("div"),j.style.position="absolute",j.style.top=a.config.netInfoTop||"0",j.style.left=a.config.netInfoLeft||"10px",j.style.bottom=a.config.netInfoBottom||"none",j.style.right=a.config.netInfoRight||"none",j.style.color="white",j.style.fontSize="13px",j.style.overflow="auto",j.style.opacity="0.5",j.style.background="black",j.style.pointerEvents="none",j.style.left="0",j.style.zIndex="9999",j.className="tchatNet",document.body.appendChild(j)),j.innerHTML=i}function p(b,c,d,e){a.config.isDataChannelSend?(a.config.isDataChannelSend&&1==e&&(b=b.replace(/sendrecv/g,"recvonly")),a.config.isDataChannelSend&&2==e&&(b=b.replace(/sendrecv/g,"sendonly"))):(b=b.replace(/sendonly/g,"sendrecv"),b=b.replace(/recvonly/g,"sendrecv"));for(var f=b.split("\n"),g=-1,h=0;h<f.length;h++)if(0===f[h].indexOf("m="+c)){g=h;break}if(-1===g)return w("sdp中不包含"+c+"信息","error"),b;for(g++;0===f[g].indexOf("i=")||0===f[g].indexOf("c=");)g++;for(;0===f[g].indexOf("b");)f.splice(g,1);return d="b=AS:"+(d||100),f.splice(g,0,d),f.join("\n")}function q(b,c){a.listMedia("",function(a){w("media list:"+JSON.stringify(a))});try{db&&eb||(w("you browser is not support webrtc"),c?c({errcode:"-2000",errmsg:"您的浏览器不支持webrtc"}):alert("您的浏览器不支持webrtc")),hb=[],B=!1;var d=[];b.vserverip.split("|").forEach(function(a){var c=a.split(";");c.length>1?d.push({urls:c[0],username:c[1],credential:c[2]}):d.push({urls:"turn:"+b.vserverip+":"+b.vserverport,username:"test1",credential:"12345678"})}),w("RTCPeerConnection iceServers:"+JSON.stringify(d));var e={iceServers:d};a.config.isFoceSturn&&(e.iceTransportPolicy="relay");try{T=new db(e)}catch(f){w("peerconnection create error:"+JSON.stringify(f)),c?c({errcode:"-2001",errmsg:"peerconnection create error:"+JSON.stringify(f)}):alert("peerconnection create error:"+JSON.stringify(f))}T.onicecandidate=function(a){a.candidate&&a.candidate&&(B?m(JSON.stringify(a.candidate)):hb.push(JSON.stringify(a.candidate)))},a.config.isDataChannelSend&&u(c);try{n()}catch(f){w("get net statu error:"+f.name)}T.addTrack?(T.ontrack=function(b){w("recive track:"+b.track.kind);var c=b.streams[0];a.config.isDeparted?"video"===b.track.kind?(H.srcObject=c,H.muted=!0,H.onloadedmetadata=function(){A&&H.play()}):(I.srcObject=c,I.onloadedmetadata=function(){A&&I.play()}):(H.srcObject=c,A&&(H.muted=!0),H.onloadedmetadata=function(){A&&(H.play(),H.muted=!1)}),a.listenerConfig.onRemoteStreamAdd(c)},F.getTracks().forEach(function(a){w("send track:"+a.kind),T.addTrack(a),w("local tracks prop:"+JSON.stringify({id:a.id,contentHint:a.contentHint,enabled:a.enabled,kind:a.kind,label:a.label,muted:a.muted,readonly:a.readonly,readyState:a.readyState,remote:a.remote})),w("local tracks setting:"+JSON.stringify(a.getSettings()))})):(T.onaddstream=function(b){w("recive stream");var c=b.stream;H.srcObject=c,H.onloadedmetadata=function(){A&&H.play()},a.listenerConfig.onRemoteStreamAdd(c)},w("send localStream"),T.addStream(F)),setTimeout(function(){a.getStatsData(function(a){E&&1==E.readyState&&w("information after 5 seconds:"+JSON.stringify(a))})},5e3),1==C&&(A?T.createOffer().then(function(a){r(a)}).catch(function(a){w("createOffer error:"+a,"error"),alert("Failure callback: "+a)}):T.createOffer(function(a){r(a)},function(a){w("createOffer error:"+a,"error")}))}catch(f){w("error:"+f.message+f.stack,"error")}}function r(b){a.config.isLimitNet&&(b.sdp=p(p(b.sdp,"video",a.config.reviceVideoBitrate,1),"audio",a.config.reviceAudioBitrate,1)),w("set local sdp"),b.sdp=b.sdp.replace("a=extmap-allow-mixed\r\n",""),T.setLocalDescription(new gb(b),function(){w("setLocalDescription success")},function(a){w("setLocalDescription error:"+a,"error")}),w("send sdp"),c(8263,{esreq:{sdpinfo:JSON.stringify(b),ownuserid:D.userid,peeruserid:D.peeruserid,roomid:D.roomid}})}function s(a){ab=new MediaRecorder(a,{bitsPerSecond:4e5,mimeType:"video/webm"}),ab.ondataavailable=function(a){bb.push(a.data)},ab.onerror=function(a){console.log("onerror: ",a)},ab.onstart=function(){w("start media prepare"),bb=[]},ab.onwarning=function(a){console.log("onwarning: "+a)},ab.onstop=function(){console.log("onstop: "+e)},ab.onresume=function(){console.log("onstop: "+e)},ab.start(20)}function t(){if(bb&&0!=bb.length&&"open"==U.readyState){var a=bb.shift(),b=new FileReader;b.onload=function(a){U.send(a.target.result)},b.readAsArrayBuffer(a)}}function u(a){try{w("start init media channel"),U=T.createDataChannel("ThinkiveMediaData",{ordered:!0,reliable:!0}),U.binaryType="arraybuffer",U.onopen=function(){w("media channel opened")},U.onerror=function(a){w("SendDatachannel onerror: "+a.toString(),"error")},U.onmessage=function(a){w("media channel onmessage"+a),"i_am_ready"==a.data&&(w("start Send media channel data"),s(F),cb=setInterval(t,20))},U.onclose=function(a){console.log("SendDatachannel onclose: "+a.toString())}}catch(b){w("init media channel error:"+JSON.stringify(b),"error"),a?a({errcode:"-2001",errmsg:"datachannel create error:"+JSON.stringify(b)}):alert("datachannel create error:"+JSON.stringify(b))}}function v(a){if(a){var b=a.srcObject;if(b){var c=b.getTracks();c.forEach(function(a){a.stop()})}a.srcObject=null}}function w(b,c){c=c||"info",console[c](b),b=b+"("+D.roomid+","+D.userid+","+D.peeruserid+","+D.peerusername+")",a.sendMsgByTransBuffer({msg:"h5report@"+c+"@"+b})}var x={ios:/iphone/.test(navigator.userAgent.toLocaleLowerCase()),android:/Android/.test(navigator.userAgent)||/Linux/.test(navigator.userAgent),safari:/^((?!chrome|android).)*safari/i.test(navigator.userAgent)};a.config={isDebug:!0,isFoceSturn:!1,isCallMode:!1,netInfoIsSend:!1,netInfoSendTime:2,netInfoIsShow:!1,isDataChannelSend:!1,isLimitNet:!0,sendVideoBitrate:350,sendAudioBitrate:30,reviceVideoBitrate:160,reviceAudioBitrate:30,signalServer:"",protobufFileLoaction:"",secretkey:"",localId:"localDiv",remoteId:"remoteDiv",isDeparted:!0};var y=navigator.userAgent.toLowerCase(),z=x.ios?"3":"2",A=x.ios||/mac\sos/.test(y),B=!1,C=1,D={userid:null,peeruserid:null,peerusername:null,roomid:null};a.user=D;var E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,ab,bb,cb,db=window.RTCPeerConnection||window.mozRTCPeerConnection||window.webkitRTCPeerConnection||"",eb=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia||navigator.mediaDevices||"",fb=window.mozRTCIceCandidate||window.RTCIceCandidate||"",gb=window.mozRTCSessionDescription||window.RTCSessionDescription||"",hb=[],ib=function(b){if(b.data){var d=S.decode(new Uint8Array(b.data)),e={errcode:d.errcode,errmsg:d.errmsg,msgtype:d.msgtype,result:null};switch(1==a.config.isDebug&&console.log("response:"+JSON.stringify(d)),d.msgtype){case R.MsgType_Client.CLIENT_LOGIN_RESP:1==a.config.isDebug&&console.log("CLIENT_LOGIN_RESP"),d.response&&(e.result=d.response.lresp),0==e.errcode&&(D.userid=e.result.userid,Z=e.result.timeout,$=new Date,k()),M(e);break;case R.MsgType_Client.CLIENT_ENTER_ROOM_RESP:1==a.config.isDebug&&console.log("CLIENT_ENTER_ROOM_RESP"),d.response&&(e.result=d.response.erresp),w(navigator.userAgent),a.config.isCallMode?N(e):a.getUserList({roomid:D.roomid},function(a){for(var b=!1,c=0;c<a.result.list.length;c++)a.result.list[c].userid!=D.userid&&(b=!0,D.peeruserid=a.result.list[c].userid,D.peerusername=a.result.list[c].username);w("exist other:"+b),!b&&(e.errcode=-1,e.errmsg="匹配坐席失败，请重新排队"),N(e)});break;case R.MsgType_Client.CLIENT_GET_USERLIST_RESP:1==a.config.isDebug&&console.log("CLIENT_GET_USERLIST_RESP"),d.response&&(e.result=d.response.guresp),O(e);break;case R.MsgType_Server.SERVER_INROOM_PUSH_REQ:1==a.config.isDebug&&console.log("SERVER_INROOM_PUSH_REQ"),d.request&&(e.result=d.request.irpreq),0==e.errcode&&8==e.result.roomid?a.listenerConfig.addNewUser(e):1==a.config.isDebug&&console.log(e.errmsg);break;case R.MsgType_Server.SERVER_VIDEO_CALL_PUSH_REQ:1==a.config.isDebug&&console.log("SERVER_VIDEO_CALL_PUSH_REQ"),d.request&&(e.result=d.request.vcpreq),a.listenerConfig.videoCalled(e);break;case R.MsgType_Server.SERVER_SEND_ENTERN_ROOM_REQ:if(1==a.config.isDebug&&console.log("SERVER_SEND_ENTERN_ROOM_REQ"),d.request&&(e.result=d.request.serreq),P(e),0==e.errcode){var f={roomid:e.result.roomid,password:e.result.password};D.peeruserid=e.result.userid,D.roomid=e.result.roomid,a.enterRoom(f,function(a){0==a.errcode?q(a.result):L({errcode:-1005,errmsg:a.errmsg})})}else 1==a.config.isDebug&&console.log(e.errmsg);break;case R.MsgType_Server.SERVER_OUTROOM_PUSH_REQ:1==a.config.isDebug&&console.log("SERVER_OUTROOM_PUSH_REQ"),d.request&&(e.result=d.request.orpreq),a.listenerConfig.exitRoomNotify(e);break;case R.MsgType_Client.CLIENT_EXCHANGE_SDP_REQ:w("receive sdp"),1==a.config.isDebug&&console.log("CLIENT_EXCHANGE_SDP_REQ"),e.result=d.request.esreq,e.result.sdpinfo=JSON.parse(e.result.sdpinfo),a.config.isLimitNet&&(e.result.sdpinfo.sdp=p(p(e.result.sdpinfo.sdp,"video",a.config.sendVideoBitrate,2),"audio",a.config.sendAudioBitrate,2)),T.setRemoteDescription(new gb(e.result.sdpinfo),function(){console.log("setRemoteDescription success")},function(a){console.error("setRemoteDescription error:"+a)}),B=!0,hb.forEach(function(a){m(a)}),1!=C&&T.createAnswer(function(b){a.config.isLimitNet&&(b.sdp=p(p(b.sdp,"video",a.config.reviceVideoBitrate,1),"audio",a.config.reviceAudioBitrate,1)),T.setLocalDescription(new gb(b),function(){console.log("setLocalDescription success")},function(a){console.error("setLocalDescription error:"+a)}),w("send sdp"),c(8263,{esreq:{sdpinfo:JSON.stringify(b),ownuserid:D.userid,peeruserid:D.peeruserid,roomid:D.roomid}})},function(b){1==a.config.isDebug&&console.log("Failure callback: "+b)});break;case R.MsgType_Client.CLIENT_EXCHANGE_ICE_REQ:w("receive ice"),1==a.config.isDebug&&console.log("CLIENT_EXCHANGE_ICE_REQ"),e.result=d.request.eireq,0==e.errcode?T.addIceCandidate(new fb(JSON.parse(e.result.iceinfo))):1==a.config.isDebug&&console.log(e.errmsg);break;case R.MsgType_Server.SERVER_MESSAGE_PUSH_REQ:1==a.config.isDebug&&console.log("SERVER_MESSAGE_PUSH_REQ"),d.request&&(e.result=d.request.mpreq),a.listenerConfig.onMsgNotify(e);break;case R.MsgType_Server.SERVER_TRANS_BUFFER_PUSH_REQ:1==a.config.isDebug&&console.log("SERVER_TRANS_BUFFER_PUSH_REQ"),d.request&&(e.result=d.request.tbpreq),a.listenerConfig.onTransBufferNotify(e);break;case R.MsgType_Client.CLIENT_USER_CAMERA_REQ:1==a.config.isDebug&&console.log("CLIENT_USER_CAMERA_REQ"),e.result=d.request.ucreq,console.log("对方摄像头是否打开："+e.result.isopen);break;case R.MsgType_Server.SERVER_PUSH_H5USER_CONNECTION_RESP:1==a.config.isDebug&&console.log("SERVER_PUSH_H5USER_CONNECTION_RESP"),e.result=d.response.hucresp,0==e.errcode&&e.result.success?L({errcode:"0"}):L({errcode:-1003,errmsg:e.errmsg});break;case R.MsgType_Client.CLIENT_SECRET_KEY_RESP:if(1==a.config.isDebug&&console.log("CLIENT_SECRET_KEY_RESP"),e.result=d.response.skresp,0==e.errcode){var g=e.result.secretkey+a.config.secretkey;g=MD5(g),g=g.substr(-16)+g.substr(0,16),g=MD5(g),g=g.substr(0,8),c(8239,{dvreq:{version:1,secretkey:g,roomid:D.roomid,userid:D.peeruserid}})}else L({errcode:-1002,errmsg:e.errmsg});break;case R.MsgType_Client.CLIENT_DATA_VERIFIER_RESP:1==a.config.isDebug&&console.log("DATA_VERIFIER_RESP"),0==e.errcode||L({errcode:-1004,errmsg:e.errmsg});break;case R.MsgType_Client.CLIENT_USER_HEARTBEAT_RESP:1==a.config.isDebug&&console.log("CLIENT_USER_HEARTBEAT_RESP"),$=new Date;break;case R.MsgType_Server.CLIENT_LEAVE_ROOM_RESP:Q&&Q(e);break;default:1==a.config.isDebug&&console.log("0x"+d.msgtype.toString(16)+"消息未处理")}}else 1==a.config.isDebug&&console.log("ws message no data"),alert("ws message no data")},jb=function(){X=!1,Y=!1,console.log("web socket init success"),c(8257,{skreq:{}})},kb=function(b){var c=document.querySelector(".tchatNet");c&&c.parentNode.removeChild(c),window.clearInterval(_),window.clearInterval(W),window.clearInterval(cb),a.closeAll();var d=b.code?",code:"+d:"";L({errcode:-10001,errmsg:"连接信令服务器异常"+d})},lb=function(b){var c=document.querySelector(".tchatNet");c&&c.parentNode.removeChild(c),window.clearInterval(_),window.clearInterval(W),window.clearInterval(cb),Y||(a.closeAll(),X?a.listenerConfig.timeout&&a.listenerConfig.timeout():a.listenerConfig.socketError&&a.listenerConfig.socketError()),console.log("web socket close:"+b.code)};a.reConnect=function(){a.closeAll(),b()},a.init=function(c,e,g){L=g,D.roomid=e.roomid,D.peeruserid=e.peer_userid,c=c||{},e=e||{};for(var h in c)this.listenerConfig[h]=c[h];for(var i in e)this.config[i]=e[i];d(),K=document.querySelector("#"+this.config.remoteId),J&&K||alert("not exist local/remote video parent element"),H=document.createElement("video"),H.setAttribute("playsinline",""),H.setAttribute("id","tchatRemoteVideo"),this.config.isDeparted&&(I=document.createElement("audio"),I.setAttribute("playsinline",""),I.setAttribute("controls","true"),I.setAttribute("id","tchatRemoteAudio")),A||(H.setAttribute("autoplay",""),this.config.isDeparted&&I.setAttribute("autoplay","")),K.appendChild(H),this.config.isDeparted&&K.appendChild(I),f(a.config.protobufFileLoaction,function(a){R=a,S=R.lookupType("Message"),b()})},a.login=function(a,b){M=b,c(8209,{lreq:{username:a.username,passwd:a.passwd,devicetype:z}})},a.enterRoom=function(a,b){N=b,D.roomid=a.roomid,a.content=navigator.userAgent,c(8215,{erreq:{roomid:a.roomid,password:a.password,roomname:a.roomname,content:a.content}})},a.getUserList=function(a,b){O=b,c(8227,{gureq:{roomid:a.roomid,content:a.content}})},a.videoCall=function(a,b){C=1,P=b,c(8231,{vcreq:{userid:a.userid,calltype:a.calltype||1}})},a.replayVideoCall=function(a,b){C=0,P=b,c(4146,{vcpresp:{userid:a.userid,isagree:a.isagree,calltype:a.calltype||1}})},a.stopVideo=function(){c(8241,{stvreq:{userid:D.peeruserid}})},a.exitRoom=function(){c(8217,{lrreq:{}})},a.sendMsg=function(a){c(8219,{smreq:{userid:D.peeruserid,msg:a.msg}})},a.sendMsgByTransBuffer=function(a){var b={msgtype:8225,request:{stbreq:{userid:D.peeruserid,cmdmsg:h(a.msg)}}},c=S&&S.encode(S.fromObject(b)).finish();E&&1==E.readyState&&E.send(c)},a.heartbeat=function(){c(8213,{uhreq:{userid:D.userid}})},a.sendStats=function(a){a.devicetype=z,a.username=D.userid,c(8271,{buireq:a})},a.wsClose=function(){Y=!0,window.clearInterval(_),window.clearInterval(W),window.clearInterval(cb),E&&E.close(),a.closeMedia(),a.hangup()},a.closeMedia=function(){if(!F)return console.log("change local media failed! local media is null"),!1;for(var a=F.getTracks(),b=0;b<a.length;b++)a[b].stop();F=null},a.hangup=function(){try{U&&U.close()&&(U=null),T&&T.close()&&(T=null)}catch(a){console.error(a)}},a.closeAll=function(){a.stopVideo(),a.exitRoom({},function(){}),a.wsClose(),G&&(G=document.getElementById("tchatLocalVideo"),v(G),G&&G.remove(),G=null),H&&(H=document.getElementById("tchatRemoteVideo"),v(H),H&&H.remove(),H=null),I&&(I=document.getElementById("tchatRemoteAudio"),v(I),I&&I.remove(),I=null),a.listenerConfig.destory&&a.listenerConfig.destory()},a.startWebRtc=function(a,b){q(a,b)},a.openLocalCamera=function(a,b){d();var c=320,e=240,f={audio:!0,video:!0},g={audio:!0,video:{width:{exact:c},height:{exact:e}}};navigator.mediaDevices.getSupportedConstraints().facingMode&&(f.video={facingMode:"user"},g.video.facingMode="user"),j(g,function(c){i(c,a,function(){j(f,function(c){i(c,a,function(){b({name:"获取摄像头失败"})})},b)})},function(){j(f,function(c){i(c,a,function(){b({name:"获取摄像头失败"})})},b)},!0)},a.getStatsData=function(a){T.getStats().then(function(b){var c=[];b.result?b.result().forEach(function(a){var b={};a.names().forEach(function(c){b[c]=a.stat(c)}),b.id=a.id,b.type=a.type,b.timestamp=a.timestamp,c.push(b)}):b.forEach(function(a){c.push(a)});var d,e,f,g,h,i;c.forEach(function(a){"outbound-rtp"==a.type?a.id.toLowerCase().indexOf("video")>-1&&a.bytesSent?e=parseInt(a.bytesSent):a.id.toLowerCase().indexOf("audio")>-1&&a.bytesSent&&(d=parseInt(a.bytesSent)):"inbound-rtp"==a.type?a.id.toLowerCase().indexOf("video")>-1&&a.bytesReceived?g=parseInt(a.bytesReceived):a.id.toLowerCase().indexOf("audio")>-1&&a.bytesReceived&&(f=parseInt(a.bytesReceived)):"ssrc"==a.type?"video"==a.mediaType?(a.bytesSent&&(e=parseInt(a.bytesSent)),a.bytesReceived&&(g=parseInt(a.bytesReceived))):"audio"==a.mediaType&&(a.bytesSent&&(d=parseInt(a.bytesSent)),a.bytesReceived&&(f=parseInt(a.bytesReceived))):"transport"==a.type&&(a.bytesSent&&(h=parseInt(a.bytesSent)),a.bytesReceived&&(i=parseInt(a.bytesReceived)))}),a({audioBytesSent:d,videoBytesSent:e,audioBytesReceived:f,videoBytesReceived:g,curTotalSend:h,curTotalReceived:i})}).catch(function(a){w("getStats error:"+JSON.stringify(a),"error")})},a.listMedia=function(a,b){return V?(a?b(V[a]):b(V.videoinput.concat(V.audioinput)),void 0):navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices?(navigator.mediaDevices.enumerateDevices().then(function(c){var d=[],e=[];c.forEach(function(a){"videoinput"==a.kind?d.push(a):"audioinput"==a.kind&&e.push(a)}),V={videoinput:d,audioinput:e},a?b(V[a]):b(V.videoinput.concat(V.audioinput))}).catch(function(a){alert(a.name+": "+a.message)}),void 0):(w("not support enumerateDevices"),[])},a.changeVideo=function(b){var c={audio:!0,video:{deviceId:{exact:b}}};j(c,function(b){w("change video get Stream success:"+JSON.stringify(c)),a.config.isDataChannelSend?w("DataChannelSend not support changeVideo"):(G.srcObject=b,b.getTracks().forEach(function(a){var b=T.getSenders().find(function(b){return b.track.kind==a.kind});b.replaceTrack(a)}),a.closeMedia(),F=b,G.onloadedmetadata=function(){A&&G.play()})},function(a){w("change video fail,constraints:"+JSON.stringify(c)),w(JSON.stringify(a))})},a.reGetMedia=function(b,c){a.closeMedia(),a.openLocalCamera(function(){F.getTracks().forEach(function(a){var c=T.getSenders().find(function(b){return b.track.kind==a.kind});c.replaceTrack(a),b&&b()})},function(a){c&&c(a)})}}(TChatRTC),window.TChatRTC=TChatRTC;
//# sourceMappingURL=./dest/js.map