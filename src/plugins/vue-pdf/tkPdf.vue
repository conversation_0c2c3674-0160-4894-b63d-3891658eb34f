<template>
  <div style="width: 100%; height: 100%">
    <!-- pdf js中viewer.html嵌入iframe中 -->
    <iframe ref="pdBox" :src="pdfSrc"></iframe>
  </div>
</template>
<script>
export default {
  props: {
    src: {
      type: String,
      default: ''
    },
    base64: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      pdfSrc: ''
    };
  },
  created() {
    if (this.base64 !== '') {
      sessionStorage.setItem('defaultBase64', this.base64);
      this.pdfSrc = `${window.location.origin}${process.env.BASE_URL}pdfjs/web/viewer.html`;
    } else {
      this.pdfSrc = `${window.location.origin}${process.env.BASE_URL}pdfjs/web/viewer.html?file=${this.src}`;
    }
  },
  destroyed() {
    sessionStorage.removeItem('defaultBase64');
  }
};
</script>
<style scoped>
iframe {
  width: 100%;
  height: 100%;
  border: 0px;
}
</style>
