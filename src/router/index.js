import Vue from 'vue';
import Router from 'vue-router';
import { setPageTitle } from 'thinkive-hvue';
import store from 'store';

// 可以根据菜单栏目 把页面路由分组到各栏目模块中，再引入来扩展

Vue.use(Router);

let routerTrigger = false;
class myRouter extends Router {
  push(location, onComplete, onAbort) {
    // push重写
    routerTrigger = true;
    store.commit('history/PUSH_ROUTE', super.resolve(location).resolved);
    super.push(location, onComplete, onAbort);
    // routerTrigger = false;
  }
  replace(location, onComplete, onAbort) {
    // replace重写
    routerTrigger = true;
    store.commit('history/REPLACE_ROUTE', super.resolve(location).resolved);
    super.replace(location, onComplete, onAbort);
    // routerTrigger = false;
  }
}

// 基础TKFlowEngine.jumpout方法，会调用history.go方法，所以需要被记录
const originHistoryGo = window.history.go;
window.history.go = function (n) {
  if (n !== 0) {
    console.log(`window.history.go：${n}`);
    routerTrigger = true;
    store.commit('history/POP_ROUTE', { count: n });
  }
  originHistoryGo.call(window.history, n);
};

window.navigateLocationHref = function ({ url = '', options = {} }) {
  console.log('全局导航: 准备跳转到', url);
  if (url === '') return;

  // 将 URL 字符串转换为类似于 Vue Router location 对象的格式
  const urlObj = new URL(url, window.location.origin);
  const path = urlObj.pathname;
  const query = {};
  // 解析查询参数
  for (const [key, value] of urlObj.searchParams.entries()) {
    query[key] = value;
  }
  // 构造类似于 Vue Router location 对象的结构
  const routeLocation = {
    path,
    fullPath: url.startsWith('http')
      ? url
      : urlObj.pathname + urlObj.search + urlObj.hash,
    query,
    hash: urlObj.hash
  };
  // 判断是否为本工程的URL
  const isCurrentModule = path.includes(`/${MODULE_NAME}/`);
  if (isCurrentModule) {
    // 如果是本工程的URL，使用Vue Router进行跳转
    const routerPath = path.replace(new RegExp(`.*/${MODULE_NAME}/views`), '');
    if (options.replace) {
      router.replace({
        path: routerPath || '/',
        query: query
      });
    } else {
      router.push({
        path: routerPath || '/',
        query: query
      });
    }
  } else {
    // 如果不是本工程的URL，使用原来的方式跳转
    if (options.replace) {
      store.commit('history/REPLACE_ROUTE', routeLocation);
      window.location.replace(url);
    } else {
      store.commit('history/PUSH_ROUTE', routeLocation);
      window.location.href = url;
    }
  }
};

// 动态自动引入当前文件夹下 除index.js的其他所有路由
const routerFile = require.context('.', true, /\.js$/);
let configRouters = [];

routerFile.keys().forEach((key) => {
  if (key === './index.js') return;
  configRouters = configRouters.concat(routerFile(key).default);
});

/*
  路由懒加载采用官方推荐的ES6 import()语法，
  webpackChunkName相同会打包成一个模块，不同则为不同模块
*/
const router = new myRouter({
  // 路由模式：history, hash. 默认设置为 history
  mode: 'history',
  // 采用history模式时，要设置base路径; hash模式不用设置(注释掉)
  // 环境变量ROUTER_BASE同步config.build.assetsPublicPath设置
  base: process.env.BASE_URL,
  scrollBehavior(to, from, savedPosition) {
    // 页面滚动行为, 保持缓存页面的滚动位置, 否则返回页面顶部
    if (savedPosition) {
      return savedPosition;
    } else {
      return {
        x: 0,
        y: 0
      };
    }
  },
  routes: [
    {
      path: '/index',
      name: 'index',
      component: () => import('@/views/index.vue'),
      children: configRouters
    },
    {
      path: '*',
      redirect: 'home'
    }
  ]
});
router.onError(() => {
  document.body.style.pointerEvents = 'auto';
});
router.beforeEach((to, from, next) => {
  document.body.style.pointerEvents = 'none';
  setTransitionName(to, from);
  next();
});

router.afterEach((route, from) => {
  console.log('~~~~~~afterEach~~~~~');
  // 页面切换更改title
  // 这里可以根据业务需求调整取title的顺序
  // 默认先取业务跳转参数query中title，再取路由元信息中title
  if (route.query.title) {
    setPageTitle(route.query.title);
  } else if (route.meta.title) {
    setPageTitle(route.meta.title);
  }
  if ($h.getSession('channelType') === '2005000000000') {
    callNativeHandler(
      'JSWangTingEvent',
      {
        action: 'SetWebVCTitle',
        param: {
          title: route.query.title || route.meta.title
        }
      },
      function () {}
    );
  }
  document.body.style.pointerEvents = 'auto';
  if (route.matched.length > 0 && store.state.history.records.length === 0) {
    console.log('~~~~~~history/PUSH_ROUTE~~~~~~~~');
    store.commit('history/PUSH_ROUTE', route);
  } else if (!routerTrigger && route.fullPath) {
    console.log('~~~~~~history/POP_ROUTE~~~~~~~~');
    store.commit('history/POP_ROUTE', {
      path: route.fullPath
    });
  }
  routerTrigger = false;
});

const originalPush = Router.prototype.push;
const originalReplace = Router.prototype.replace;
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};
Router.prototype.replace = function replace(location) {
  return originalReplace.call(this, location).catch((err) => err);
};

function setTransitionName(to, from) {
  let historyList = $h.getSession('NEW_ROUTER_HISTORY') || [];
  historyList = new Set(historyList);
  // 判断是否前进/返回行为
  if (historyList.has(to.path)) {
    if (!$hvue.config.noCachePage.includes(to.name)) {
      historyList.delete(from.path);
    } else {
      historyList = new Set([to.path]);
    }
    store.commit('router/updateDirection', 'right');
  } else {
    historyList.add(to.path);
    store.commit('router/updateDirection', 'left');
  }
  $h.setSession('NEW_ROUTER_HISTORY', [...historyList]);
}

window.$router = router;
export default router;
