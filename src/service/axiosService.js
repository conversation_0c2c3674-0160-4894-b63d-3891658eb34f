import axios from 'axios';
import intercept from 'netIntercept';

const requestURL = $hvue.customConfig.serverUrl;
const { request } = axios.create(intercept);

export async function axiosRequest(funcNo, params = {}, options = {}) {
  const url = `${requestURL}/${funcNo}`;
  const method = options.method || 'POST';
  const headers = {
    Accept:
      'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
    'Content-Type': 'application/json',
    Authorization: $h.getSession('authorization'),
    'tk-token-authorization': $h.getSession('authorization'),
    'tk-jwt-authorization': $h.getSession('jwtToken'),
    'tk-flow-token': sessionStorage.getItem('TKFlowToken'),
    'x-bus-id': $h.getSession('bizType'),
    'tk-two-factor-token': $h.getSession('tkTwoFactorToken'),
    merchantId: $hvue.customConfig.merchantId,
    isLoading: true
  };

  const config = {
    url,
    method,
    headers,
    ...(method.toUpperCase() === 'POST' ? { data: params } : { params })
  };

  Object.assign(config, options);

  try {
    const response = await request(config);
    if (!isJSON(response.data)) {
      return response;
    }
    const data = JSON.parse(response.data);

    if (data.code === 0) {
      return response;
    } else {
      throw new Error(data.note || 'Unknown error');
    }
  } catch (e) {
    throw new Error(e.message || 'Request failed');
  }
}

function isJSON(str) {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}
