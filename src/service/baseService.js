import { Request, md5Encrypt } from '@thinkive/axios';
import intercept from 'netIntercept';

let fetch = new Request({ intercept });
console.log('创建Request实例');

export function baseService({ url, params, options = {} }) {
  let _params = {};
  if (!options.private) {
    let opSource = '';
    let opStation = ''; // 站点信息登录后从网关获取
    // let opStation = getOpStation();
    let { android, ios, pc } = $hvue.iBrowser;
    let platform = $hvue.platform;
    if (platform === '0') {
      opSource = '4';
    } else {
      if (android) {
        opSource = '1';
      }
      if (ios) {
        opSource = '2';
      }
      if (pc) {
        opSource = '3';
      }
    }
    _params = {
      opEntrustWay: $hvue.customConfig.opEntrustWay, //TODO 联调测试
      merchantId: $hvue.customConfig.merchantId,
      opSource,
      // source: opSource,
      opStation
    };
  }
  let paramMD5;
  if (options.cache === true) {
    // 结果是否缓存到session
    paramMD5 = md5Encrypt(JSON.stringify(Object.assign(_params, params)));
    let res = $h.getSession(paramMD5);
    if (res) {
      if (res.code === 0) {
        return Promise.resolve(res);
      }
    }
  }
  console.log('~~~~serviceToken~~~~~===' + $h.getSession('authorization'))
  let _options = {
    // 默认的请求设置
    url: url,
    sign: false, // 是否加签
    encode: false, // 是否默认编码
    method: 'POST', // 请求方法
    loading: false, // 是否显示loading
    requestKey: Math.random().toString(36).slice(-8), // 随机标识用于清空当前请求
    headers: {
      Accept:
        'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
      'Content-Type': 'application/json',
      Authorization: $h.getSession('authorization'),
      'tk-token-authorization': $h.getSession('authorization'),
      'tk-jwt-authorization': $h.getSession('jwtToken'),
      'tk-flow-token': sessionStorage.getItem('TKFlowToken'),
      'x-bus-id': $h.getSession('bizType'),
      'tk-two-factor-token': $h.getSession('tkTwoFactorToken'),
      merchantId: $hvue.customConfig.merchantId,
      isLoading: true // 是否显示loading (此属性非基础提供，自定义化实现所需)
    }
  };
  params = Object.assign(_params, params);
  options = Object.assign(_options, options);
  return new Promise((resolve, reject) => {
    fetch({
      params,
      options
    }).then(
      (res) => {
        if (options.cache === true) {
          $h.setSession(paramMD5, res);
        }
        if (!options.filter && options.responseType !== 'blob') {
          if (res.code === 0) {
            resolve(res);
          } else {
            reject(res.msg);
          }
        } else {
          resolve(res);
        }
      },
      (err) => {
        reject(err);
      }
    );
  });
}
