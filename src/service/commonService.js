/**
 * @desc 公共接口请求定义
 */

import { baseService } from './baseService';
let requestURL = $hvue.customConfig.commonServerUrl;
/**
 * 所有请求的公用方法 可以添加公用参数 或者其他公用操作
 * @param {String} funcNo 功能号
 * @param {Object} params 接口的业务参数
 * @param {Object} options 此次请求的相关设置 如 请求方法 是否加签， 具体定义见netIntercept.js
 */
function commService(funcNo, params, options = {}) {
  let url = requestURL;
  if (funcNo) {
    url += `/${funcNo}`;
  }
  return baseService({
    url,
    params,
    options
  }).then(
    (res) => {
      return Promise.resolve(res);
    },
    (err) => {
      return Promise.reject(err);
    }
  );
}

/**
 * @desc 查询数据字典值
 * @param {Object} params 业务参数
 */
export function getDictData(params) {
  return commService('dict/map', params, {
    loading: false,
    method: 'GET',
    cache: true
  });
}

/**
 * @desc 查询省市区
 * @param {Object} params 业务参数
 * @param {Object} options 配置项
 */
export function getAdressTree(params, options = {}) {
  return commService(
    'address/tree',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    )
  );
}

/**
 * @desc 查询系统参数Map
 * @param {Object} params 业务参数
 */
export function getConfigMap(params) {
  return commService('config/map', params, {
    loading: false,
    method: 'GET',
    cache: true
  });
}
