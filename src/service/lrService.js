/**
 * @desc 接口请求定义
 * <AUTHOR>
 */
import { baseService } from './baseService';
import { trackEvent } from '@/common/util';
let requestURL = $hvue.customConfig.serverUrl;

/**
 * 所有请求的公用方法 可以添加公用参数 或者其他公用操作
 * @param {String} funcNo 功能号
 * @param {Object} params 接口的业务参数
 * @param {Object} options 此次请求的相关设置 如 请求方法 是否加签， 具体定义见netIntercept.js
 */
function commService(funcNo, params = {}, options = {}) {
  let url = requestURL;
  if (funcNo) {
    url += `/${funcNo}`;
  }
  if (funcNo.includes('bf-engine-server')) {
    url = funcNo;
  }
  return baseService({
    url,
    params,
    options
  }).then(
    (res) => {
      return Promise.resolve(res);
    },
    (err) => {
      trackEvent({
        // event_name: 'error',
        event_name: 'ywbl_show', // 曝光弹框
        page_name: '',
        module_name: '报错提示',
        element_name: 'error_popup'
      });
      return Promise.reject(err);
    }
  );
}

/**
 * @desc 查询有无开通信用资金账户
 * @param {Object} params 业务参数
 */
export function fundAccountQry(params, options = {}) {
  return commService('account/fundAccountInfoQry', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 查询银行
 * @param {Object} params 业务参数
 */
export function selectBcBankBybankNo(params, options = {}) {
  return commService('credit/selectBcBankBybankNo', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 银行卡号查询银行
 * @param {Object} params 业务参数
 */
export function bankCardBINQuery(params, options) {
  return commService('credit/bankCardBINQuery', params, { method: 'GET' });
}

/**
 * @desc 获取当前流程状态
 * @param {Object} params 业务参数
 */
export function OrderOnTheWay(params, options) {
  return commService('credit/orderOnTheWay', params, { method: 'GET' });
}

/**
 * @desc 流程销毁
 * @param {Object} params 业务参数
 */
export function FlowinsCancel(params, options) {
  return commService('/bf-engine-server/flowins/inner/cancel', params, {
    method: 'POST'
  });
}

/**
 * @desc 获取流程状态
 * @param {Object} params 业务参数
 */
export function CreditAccount(params, options) {
  return commService('credit/creditAccountAndBankQuery', params, {
    method: 'Get'
  });
}

/**
 * @desc 获取订单时间
 * @param {Object} params 业务参数
 */
export function QueryExpireDate(params, options) {
  return commService('/credit/queryExpireDate', params, {
    method: 'Get'
  });
}

/**
 * @desc 信用资金账户和存管银行信息查询
 * @param {Object} params 业务参数
 */
export function creditAccountAndBankQueryV2(params, options) {
  return commService('credit/creditAccountAndBankQueryV2', params, {
    method: 'Get',
    ...options
  });
}
