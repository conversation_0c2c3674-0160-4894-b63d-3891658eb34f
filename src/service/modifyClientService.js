/**
 * @desc 接口请求定义
 * <AUTHOR>
 */
import { baseService } from './baseService';
import { trackEvent } from '@/common/util';
let requestURL = $hvue.customConfig.serverUrl;

/**
 * 所有请求的公用方法 可以添加公用参数 或者其他公用操作
 * @param {String} funcNo 功能号
 * @param {Object} params 接口的业务参数
 * @param {Object} options 此次请求的相关设置 如 请求方法 是否加签， 具体定义见netIntercept.js
 */
function commService(funcNo, params = {}, options = {}) {
  let url = requestURL;
  if (funcNo) {
    url += `/${funcNo}`;
  }
  return baseService({
    url,
    params,
    options
  }).then(
    (res) => {
      return Promise.resolve(res);
    },
    (err) => {
      trackEvent({
        // event_name: 'error',
        event_name: 'ywbl_show', // 曝光弹框
        page_name: '',
        module_name: '报错提示',
        element_name: 'error_popup'
      });
      return Promise.reject(err);
    }
  );
}

/**
 */
export function sysAddressParse(params, options = {}) {
  return commService('/common/sysAddressParse', params, {
    ...options,
    method: 'GET'
  });
}

/**
 */
export function mobilePreSubmit(params, options = {}) {
  return commService('/client/mobilePreSubmit', params, {
    ...options,
    method: 'GET'
  });
}

/**
 */
export function peofessionPreSubmit(params, options = {}) {
  return commService('/client/peofessionPreSubmit', params, {
    ...options,
    method: 'GET'
  });
}

/**
 */
export function addressPreSubmit(params, options = {}) {
  return commService('/client/addressPreSubmit', params, {
    ...options,
    method: 'GET'
  });
}

/**
 */
export function riskFlagQuery(params, options = {}) {
  return commService('/egli/riskFlagQuery', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 客户基本信息查询
 * @param {Object} params 业务参数
 */
export function clientOccTip(params, options = {}) {
  return commService(
    'client/clientOccTip',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    )
  );
}

/**
 * @desc 获取邮编
 * @param {Object} params 业务参数
 */
export function postcode(params, options = {}) {
  return commService(
    'common/postcode',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    )
  );
}
