/**
 * @desc 请求时的默认设置及接口请求前后的拦截公用方法（框架自动加载）
 * @Author: xhq
 * @Date: 2019-10-08 17:14:59
 * @Last Modified by: chenjm
 * @Last Modified time: 2021-08-08 19:29:24
 */
import { useService } from '@/common/utils/service';
import router from 'router';
const { countServiceAdd, countServiceSub } = useService();
export default {
  // 请求是否显示loading，(统一设置:单个请求的入参会覆盖这里，下同),默认 true
  loading: false,
  // 请求参数签名(统一设置), 默认false
  sign: false,
  // 请求方法, 默认 get
  method: 'GET',
  // 请求超时时间(统一设置), 默认 30000(ms)
  timeout: 60000,
  // 跨域是否带cookie验证，默认为true
  withCredentials: true,
  // 参数是否utf-8编码, 默认true
  encode: true,
  // 后端接口版本(统一设置)，默认1.0，目前是中台产品线需要用2.0，统一封装支持。
  servletVersion: '2.0',
  // 是否原生代理请求(统一设置), 默认false
  nativeProxy: false,
  // 是否允许同时发出相同请求，默认true
  allowRepeat: false,
  // 原生代理 请求加密模式，对应50118插件号的入参:mode, 默认0(正常不加密)
  nativeProxyMode: 0,
  // 请求头，默认{}
  headers: {},
  // 请求前统一拦截处理方法
  interceptRequest: function (requestConfig) {
    // 记录请求
    countServiceAdd(requestConfig);
  },
  // 请求响应统一拦截处理方法, 返回false则中断promise链
  interceptResponse: function (res, data, requestConfig) {
    // 监听请求
    countServiceSub(requestConfig);
    // 登录拦截统一处理
    if (res && res.code === 920) {
      if ($hvue.platform !== '0') {
        _hvueAlert({
          mes: '用户未登录或已超时，请重新登录。',
          callback: () => {
            $h.callMessageNative({
              funcNo: '50114',
              moduleName: $hvue.customConfig.moduleName,
              targetModule: $hvue.customConfig.moduleName
            });
          }
        });
      } else {
        _hvueAlert({
          mes: '用户未登录或已超时，请重新登录。',
          callback: () => {
            $h.setSession('isFlowBack', true);
            // 按照路由堆栈，宝哥流程内有多少个堆栈加上已经过的流程外页面的堆栈总数
            // 返回由前端维护的路由堆栈数量
            // let history = $h.getSession('history_list').records;
            // index为当前页面在路由堆栈中的位置
            let index = $h.getSession('history_list').index;
            if(index > 0) {
              window.history.go(-index);
            }else{
              window.history.go(-1);
            }
            // 需要清空宝哥堆栈信息
            TKFlowEngine.goHome('0');
          }
        });
      }

      /*          if ($hvue.platform !== '0') {
              _hvueAlert({
                mes: res.msg,
                callback: () => {
                  $h.callMessageNative({
                    funcNo: '50114',
                    moduleName: 'open'
                  });
                }
              });
              return false;
              // const reqParam60099 = {
              //   funcNo: '60099',
              //   actionType: '1'
              // };
              // console.log(reqParam60099);
              // console.log(`请求参数为: ~~${JSON.stringify(reqParam60099)}`);
              // const res = $h.callMessageNative(reqParam60099);
              // console.log(`请求结果为: ~~${JSON.stringify(res)}`);
            } else {
              window.vm.$store.commit('user/setUserInfo');
              console.log(window.$hvue);
              let hostName = window.$hvue.customConfig.loginHostUrl; //对应环境业务域名
              let channleType = 'test'; //渠道编码，运营分配
              let loginUrl = `https://${hostName}/yjbweblogin/login/web/build/index.html?
      channleType=${channleType}&backUrl=${encodeURIComponent(
                'https://fzsdbusiness.yjbtest.com/bc-h5-view/views/home'
              )}`; //拼装交易登录页url
              location.replace(loginUrl);
            }*/
      return false;
    } else if (res && res.code === '-10001') {
      //   _hvuePopup({
      //     message: '您已在其他设备登录！',
      //     confirmFunc: () => {
      //       window.vm.$store.commit('user/setUserInfo')
      //       router.replace({ path: '/login' });
      //     }
      //   });
      return false;
    } /*  else if (
      res &&
      res.code !== 0 &&
      res.code !== 50001 &&
      res.code !== 920 &&
      res.code !== 3000
    ) {
      if (res.msg !== '密码错误') {
        _hvueToast({
          mes: res.msg
        });
      }
    } */ else if (res && res.code === 50001) {
      _hvueAlert({
        mes: res.msg,
        callback: () => {
          $h.callMessageNative({
            funcNo: '50114',
            moduleName: $hvue.customConfig.moduleName,
            targetModule: $hvue.customConfig.moduleName
          });
        }
      });
      return false;
    }
  },
  // 请求服务器报错时处理方法(即没有正常返回响应结果时进入)
  handleError: handleError
};

function handleError(err) {
  // 监听请求
  countServiceSub(err.config);
  if (err && err.response) {
    switch (err.response.status) {
      case 401:
        _hvueToast({ mes: '访问未授权(401)' });
        break;
      case 403:
        _hvueToast({ mes: '访问被拒绝(403)' });
        break;
      case 404:
        _hvueToast({ mes: '请求地址或接口不存在(404)' });
        break;
      case 500:
        _hvueToast({ mes: '服务器内部服务错误(500)' });
        break;
      case 501:
        _hvueToast({ mes: '请求服务未实现(501)' });
        break;
      case 502:
        _hvueToast({ mes: 'Web服务器故障(502)' });
        break;
      case 503:
        _hvueToast({ mes: '服务不可用(503)' });
        break;
      case 504:
        _hvueToast({ mes: '网关超时(504)' });
        break;
      case 505:
        _hvueToast({ mes: '服务器不支持</br>请求HTTP版本(505)' });
        break;
      default:
        _hvueToast({ mes: `网络异常(${err.response.status})` });
    }
  } else {
    if (err.message.indexOf('Network Error') !== -1) {
      _hvueToast({ mes: '网络异常或中断' });
      return true;
    } else if (err.message.indexOf('timeout of') !== -1) {
      _hvueToast({ mes: '请求超时(timeout)' });
      return true;
    } else {
      _hvueToast({ mes: `请求错误: ${err.message}` });
    }
  }
  // 返回false则中断promise
  // return false;
}
