/**
 * @desc 接口请求定义
 * <AUTHOR>
 */
import { baseService } from './baseService';
import { trackEvent } from '@/common/util';
let requestURL = $hvue.customConfig.serverUrl;

/**
 * 所有请求的公用方法 可以添加公用参数 或者其他公用操作
 * @param {String} funcNo 功能号
 * @param {Object} params 接口的业务参数
 * @param {Object} options 此次请求的相关设置 如 请求方法 是否加签， 具体定义见netIntercept.js
 */
function commService(funcNo, params = {}, options = {}) {
  let url = requestURL;
  if (funcNo) {
    url += `/${funcNo}`;
  }
  return baseService({
    url,
    params,
    options
  }).then(
    (res) => {
      return Promise.resolve(res);
    },
    (err) => {
      trackEvent({
        // event_name: 'error',
        event_name: 'ywbl_show', // 曝光弹框
        page_name: '',
        module_name: '报错提示',
        element_name: 'error_popup'
      });
      return Promise.reject(err);
    }
  );
}

/**
 * @desc 本地问卷剩余次数查询
 * @param {Object} params 业务参数
 */
export function questionPreSubmit(params, options) {
  return commService('egli/questionPreSubmit', params);
}

/**
 * @desc 本地问卷剩余次数查询
 * @param {Object} params 业务参数
 */
export function riskFlagQuery(params, options) {
  return commService(
    'egli/riskFlagQuery',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    )
  );
}

/**
 * @desc
 * @param {Object} params 业务参数
 */
export function answerHisDetail(params, options) {
  return commService(
    'egli/answerHisDetail',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    )
  );
}
