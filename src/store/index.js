import Vue from 'vue';
import Vuex from 'vuex';
import mutations from './mutations';
import actions from './actions';
import router from './modules/router';
import user from './modules/user';
import flow from './modules/flow';
import business from './modules/business';
import pageContext from './modules/pageContext';
import history from './modules/history';

Vue.use(Vuex);

const state = {};

export default new Vuex.Store({
  state,
  mutations,
  actions,
  modules: {
    router,
    user,
    flow,
    pageContext,
    business,
    history
  }
});
