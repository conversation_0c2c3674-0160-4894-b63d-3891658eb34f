export default {
  namespaced: true,
  state: {
    flowNodeNo: '',
    flowBack: false,
    isWhiteBg: false
  },
  getters: {
    flowNodeNo: (state) => state.flowNodeNo,
    flowBack: (state) => state.flowBack
  },
  mutations: {
    setFlowNode(state, no) {
      if (no) {
        state.flowNodeNo = no;
      } else {
        state.flowNodeNo = '';
      }
    },
    setFlowBack(state, flag) {
      state.flowBack = flag;
    },
    setWhiteBg(state, isWhiteBg) {
      state.isWhiteBg = isWhiteBg;
    }
  }
};
