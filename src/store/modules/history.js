//假定route的meta里包含keepAlive和componentName属性
const formRecord = (vueRoute) => {
  return {
    name: vueRoute.name,
    path: vueRoute.fullPath
  };
};

export default {
  namespaced: true,
  state: {
    records: [], //历史路由数组
    index: 0, //当前路由索引
    direction: '' //history变化方向, forward/backward
  },
  getters: {
    routes: (state) => {
      const { records, index } = state;
      if (records.length > 0 && index < records.length) {
        return records.slice(0, index + 1);
      }
      return [];
    }
  },
  mutations: {
    //记录 router.push
    PUSH_ROUTE(state, vueRoute) {
      let record = formRecord(vueRoute);
      let { records, index } = state;
      if (index + 1 < records.length) {
        records = records.slice(0, index + 1);
      }
      records.push(record);
      state.records = records;
      state.index = records.length - 1;
      state.direction = 'forward';
      console.log('~~~~~~~PUSH_ROUTE~~~~~~~');
      $h.setSession('history_list', state);
    },
    //记录 router.replace
    REPLACE_ROUTE(state, vueRoute) {
      let record = formRecord(vueRoute);
      let { records, index } = state;
      if (index + 1 < records.length) {
        records = records.slice(0, index + 1);
      }
      records.pop();
      records.push(record);
      state.records = records;
      state.index = index;
      state.direction = 'forward';
      console.log('~~~~~~~REPLACE_ROUTE~~~~~~~');
      $h.setSession('history_list', state);
    },
    //记录 router.pop 前进/后退
    //count是跳跃的历史记录数, >0是前进, <0是回退，path是当前的location.href
    POP_ROUTE(state, { count, path }) {
      let { records, index, direction } = state;
      console.log('POP_ROUTE records');
      console.log(records);
      console.log('POP_ROUTE index');
      console.log(index);
      console.log('POP_ROUTE path');
      console.log(path);
      console.log('POP_ROUTE count');
      console.log(count);
      if (count) {
        direction = count > 0 ? 'forward' : 'backward';
        index += count;
        index = Math.min(records.length, index);
        index = Math.max(0, index);
      } else {
        if (index > 0 && records[index - 1].path === path) {
          // 后退
          direction = 'backward';
          index -= 1;

          let listarr = $h.getSession('history_list').records;
          let listIndex = $h.getSession('history_list').index;
          // 后退操作时 路由已发生变化
          // isFlowBack用于判断是有没有走宝哥流程引擎返回还是手动左上角返回，true为手动流程引擎返回。false为浏览器返回
          // 走宝哥流程引擎返回时，宝哥自己会处理宝哥堆栈信息也会history.go(-n),此时什么都不要做。否则则是浏览器返回需要走宝哥的方法处理宝哥堆栈信息，浏览器自己go(-1)
          if (!$h.getSession('isFlowBack')) {
            // 需先判断当前页面是否为流程内页面，如果不是说明已经操作过宝哥堆栈不需要再次操作
            if (!listarr[listIndex].path.includes('Flow_')) {
            } else {
              // 浏览器返回，需要执行宝哥返回方法，但不操作history.go(-n)
              let PreNode = $h.getSession('PreNode');
              TKFlowEngine.goback(PreNode, '0');
            }
          } else {
            // 调用宝哥返回方法，先返回了，后执行到这里,宝哥堆栈已更新
          }
        } else if (
          index < records.length - 1 &&
          records[index + 1].path === path
        ) {
          // 前进
          direction = 'forward';
          index += 1;
        } else if (index === 0 && records.length > 0) {
          console.log('无上一页');
          console.log(records);
          if ($hvue.platform == 0) {
            // h5
          } else {
            // app
            if ($h.getSession('optType') === '1') {
              let reqParams = {
                funcNo: '60099',
                actionType: '5',
                params: {
                  optType: '1',
                  isSuccess: '0'
                }
              };
              const res = $h.callMessageNative(reqParams);
              console.log(`请求结果为: ~~${JSON.stringify(res)}`);
              $h.callMessageNative({
                funcNo: '50114',
                moduleName: $hvue.customConfig.moduleName
              });
            } else if ($h.getSession('optType') === 'loginSuccess') {
              let reqParams = {
                funcNo: '60099',
                actionType: '5',
                params: {
                  optType: '1',
                  isSuccess: '0'
                }
              };
              const res = $h.callMessageNative(reqParams);
              console.log(`请求结果为: ~~${JSON.stringify(res)}`);
              $h.callMessageNative({
                funcNo: '50114',
                moduleName: $hvue.customConfig.moduleName
              });
            } else {
              console.log(`请求结果为:退出APP`);
              $h.callMessageNative({
                funcNo: '50114',
                moduleName: $hvue.customConfig.moduleName
              });
            }
          }
        }
      }
      console.log(index);
      state.records = records;
      state.index = index;
      state.direction = direction;
      console.log('~~~~~~~POP_ROUTE~~~~~~~');
      $h.setSession('history_list', state);
    }
  }
};
