export default {
  namespaced: true,
  state: {
    subpageData: {}
  },
  getters: {
    compontId: (state) => state.subpageData?.$compontId,
    formData: (state) => state.subpageData?.$formData,
    saveContext: (state) => state.subpageData?.$saveContext,
    saveContextId: (state) => state.subpageData?.saveContextId,
    targetPage: (state) => state.subpageData?.$targetPage,
    targetRefPageContent: (state) => state.subpageData?.$targetRefPageContent,
    targetRefPageTitle: (state) => state.subpageData?.$targetRefPageTitle,
    targetRefPageTemplate: (state) => state.subpageData?.$targetRefPageTemplate
  },
  mutations: {
    setSubpageData(state, data) {
      if (data && Object.keys(data).length !== 0) {
        state.subpageData = Object.assign({}, data);
      } else {
        state.subpageData = {};
      }
    }
  }
};
