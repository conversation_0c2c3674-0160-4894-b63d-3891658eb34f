// initial state
const state = {
  userInfo: null,
  ssoLoginFlag: false,
  appId: ''
};

const getters = {
  userInfo: (state) => state.userInfo,
  ssoLoginFlag: (state) => state.ssoLoginFlag,
  appId: (state) => state.appId,
};

// mutations
const mutations = {
  setAppId(state, appId) {
    if (appId) {
      state.appId = appId;
    } else {
      state.appId = '';
    }
  },
  setUserInfo(state, userInfo) {
    if (userInfo) {
      state.userInfo = userInfo;
    } else {
      state.userInfo = null;
      $h.clearSession('store');
    }
  },
  setSsoLoginFlag(state, flag) {
    if (flag) {
      state.ssoLoginFlag = flag;
    } else {
      state.ssoLoginFlag = false;
      $h.clearSession('store');
    }
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations
};
