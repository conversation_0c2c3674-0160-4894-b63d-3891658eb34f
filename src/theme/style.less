/*-- public set  --*/
@bg-color: var(--bgColor, #F2F4F8);
@border-color: var(--border<PERSON>olor, rgba(229, 229, 229, 0.5));
@main-color: var(--mainColor, #F93838);
@t-color-normal: var(--tColor<PERSON>ormal, #0F1826);
@t-color-gray: var(--tColor<PERSON>ray, #55555E);
@t-color-lightgray: var(--tColorLightgray, #87878D);
@link-color: var(--linkColor, #338AFF);
@error-color: var(--errorColor, #FF7015);
@tips-color: var(--tipsColor, #FF8533);
@imp_color: var(--impColor, #FF8533);
@button-bg-1: var(--buttonBg1, #F93838);
@button-bg-2: var(--buttonBg2, #F93838);
@button-border-bg: var(--buttonBorderBg, #FFF0F0);
@button-assist-bg: var(--buttonAssistBg, #FFF0F0);
@text-tip-color: var(--textTipColor, #CCCCCC);

/*-- vant set  --*/
@number-keyboard-button-background-color: #1989fa;
@cell-group-inset-padding:0 0 0.08rem 0;

@black: #000;
@white: #fff;
@gray-1: #f7f8fa;
@gray-2: #f2f3f5;
@gray-3: #ebedf0;
@gray-4: #dcdee0;
@gray-5: #c8c9cc;
@gray-6: #969799;
@gray-7: #646566;
@gray-8: #323233;
@red: #ee0a24;
@blue: #1989fa;
@orange: #ff976a;
@orange-dark: #ed6a0c;
@orange-light: #fffbe8;
@green: #07c160;
@font-form-size: 0.16rem;
@font-base-size: 0.14rem;
// Gradient Colors
@gradient-red: linear-gradient(to right, #ff6034, #ee0a24);
@gradient-orange: linear-gradient(to right, #ffd01e, #ff8917);
@field-label-width: 1.2rem;
.hui-keypanel {
  padding: 0 0.16rem;
  .hui-keypanel-input{
    // display: block;
    // width: 100%;
    height: 0.56rem;
    padding: 0.16rem 0;
    line-height: 1.5;
    font-size: 0.16rem;
    display: flex;
    align-items: center;
    .hui-keypanel-input-dot{
      margin: 0px 2px;
    }
  }
}
.hui-keypanel-board-head {
  padding: 0;
  background: #e2e5eb;
  height: 40px;
  padding: 1px 0px;
  border-radius: 5px 5px 0 0;
  line-height: 40px;
  .icon-keypanel-keyboard{
    background: #ffffff;
    padding-right: 5px;
  }
  .safe-head{
    width: 100%;
    background: #ffffff;
    img {
      width: 26px;
      margin-right: 10px;
    }
    .right-icon{
      position: absolute;
      right: 3px;
      top: 7px;
    }
  }
}
.cond_list{
	padding: 0 0.16rem;

	li{
		&.warning{
			.tit h5:before{
				content: "\e61e";
				color: #ffa900;
			}
		}
	}
}
.hui-loading {
  background: rgba(0,0,0,.3);
}
.hui-keypanel-input-placeholder{
  color:var(--tColorNormal, #777777);
}
.hui-keypanel-input-text.cursor-style:after{
  background: #000000;
  height: 0.25rem;
  top: -0.1rem;
}
.hui-datetime-head > a:last-child {
  color: var(--mainColor, #1061ff);
}
.white_bg_sc {
  background: var(--bgColor, #f8f8f8) !important;
}
.van-cell{
  font-size: 0.16rem;
  line-height: 1.5;
  min-height: 0.56rem;
  display: flex;
  align-items: center;
  .van-cell__title {
  }
}
.van-cell::after{
  border-bottom: 1px solid rgba(229, 229, 229, 0.9);
}
.opea_ctbox{
  border-top:none
}



// .van-cell-group {
//   margin-top: 8px;
// }
@uploader-icon-color:@tips-color;
@uploader-text-color:@tips-color;
@uploader-upload-background-color:@button-assist-bg;
@uploader-upload-active-color:@button-border-bg;

// Font
@font-size-xs: 10px;
@font-size-sm: 12px;
@font-size-md: 0.16rem;
@font-size-lg: 16px;
@font-weight-bold: 500;

// Component Colors
@text-color: var(--tColorNormal, #333333);
@field-label-color: var(--tColorNormal, #333333);
@active-color: @gray-2;
@active-opacity: 0.7;
@disabled-opacity: 0.5;
@background-color: @gray-1;
@background-color-light: #fafafa;
@text-link-color: var(--linkColor, #1061ff);

// Button 按钮样式变量替换
@button-info-color: @white;
@button-info-background-color: var(--mainColor, #1061ff);
@button-info-border-color: var(--mainColor, #1061ff);
@button-danger-color: @white;
@button-danger-background-color: var(--errorColor, #f52929);
@button-danger-border-color: var(--errorColor, #f52929);
@button-warning-color: @white;
@button-warning-background-color: var(--impColor, #ffa900);
@button-warning-border-color: var(--impColor, #ffa900);

// Checkbox
@checkbox-label-color: var(--tColorNormal, #333333);
@checkbox-checked-icon-color: var(--mainColor, #1061ff);
@checkbox-disabled-icon-color: var(--tColorLightgray, #888888);
@checkbox-disabled-label-color: var(--tColorLightgray, #888888);

// Picker
@picker-confirm-action-color: var(--tipsColor, #ff4848);

// :export {
//   name: 'less';
//   bgColor: @bg-color;
//   borderColor: @border-color;
//   mainColor: @main-color;
//   tColorNormal: @t-color-normal;
//   tColorGray: @t-color-gray;
//   tColorLightgray: @t-color-lightgray;
//   linkColor: @link-color;
//   errorColor: @error-color;
//   tipsColor: @tips-color;
//   impColor: @imp_color;
//   buttonBg1: @button-bg-1;
//   buttonBg2: @button-bg-2;
//   buttonBorderBg: @button-border-bg;
//   buttonAssistBg: @button-assist-bg;
//   textTipColor: @text-tip-color;
// }
.appro_txt_wrap {
  padding: 0.2rem 0;
  border-bottom: 1px solid @border-color;

  .item {
    font-size: @font-base-size;
    line-height: 1.5;
    color: @t-color-lightgray;
    margin-top: 0.15rem;

    &:first-child {
      margin-top: 0;
    }
    h5 {
      color: @t-color-normal;
      font-size: @font-form-size;
      font-weight: 500;
      margin-bottom: 0.08rem;
    }
  }
  a {
    color: @link-color;
  }
}
.appro_tips {
  a {
    color: @link-color;
  }
}

.test_level_module{
	background: #ffffff;
	border-bottom: 0.08rem solid @bg-color;
	overflow: auto;
	word-break: break-all;
	
	table{
		width: 100%;
		font-size: @font-base-size;
		line-height: 1.5;
		border: 0 none;
		
		th{
			text-align: center;
			font-weight: normal;
			vertical-align: middle;
			padding: 0.15rem;
			border: 0 none;
			border-left: 1px solid @border-color;
			min-width: 1.2rem;
			
			&:first-child{
				border-left: 0 none;
			}
		}
		td{
			vertical-align: middle;
			padding: 0.15rem;
			border: 0 none;
			border-left: 1px solid @border-color;
			border-top: 1px solid @border-color;
			
			&:first-child{
				border-left: 0 none;
				text-align: center;
			}
		}
		
	}
}
.test_level_tips{
	background: #ffffff;
	padding: 0.15rem;
	font-size: @font-base-size;
	line-height: 1.5;
}
.appro_rulebox{
	border-top: 1px solid @border-color;
	
	.title{
		font-size: @font-form-size;
		line-height: 1.5;
		padding: 0.15rem;
		font-weight: 500;
		border-bottom: 1px solid @border-color;
	}
	
	ul{
		padding: 0.15rem;
		
		li{
			padding-left: 0.16rem;
			position: relative;
			margin-top: 0.1rem;
			font-size: @font-base-size;
			line-height: 1.5;
			
			&:first-child{
				margin-top: 0;
			}
			&:before{
				content: '';
				width: 0.06rem;
				height: 0.06rem;
				background: @main-color;
				border-radius: 50%;
				position: absolute;
				top: 0.08rem;
				left: 0;
			}
		}
	}
	.tips{
		margin: 0 0.15rem;
		border-top: 1px solid @border-color;
		padding: 0.15rem 0;
		font-size: 0.14rem;
		line-height: 1.5;
		color: @t-color-lightgray;
	}
}

.specialField{
	.van-field__error-message {
		color: #969799;
	  }
}

.special{
	.form_tit_right .input_text.text .t1:focus{
		padding-left: 1.2rem !important;
	}
}

.redErrField{
	input::placeholder {
		color: #ee0a24
	}
	textarea::placeholder {
		color: #ee0a24
	}
}

.van-toast {
	width: 0.4rem;
	min-height: 0.2rem;
	background-color: rgba(0,0,0,.3);
	padding: 0.08rem 0.1rem;
	border-radius: 0.04rem;
}
