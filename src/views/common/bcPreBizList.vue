<template>
  <section
    class="main fixed"
    :class="{ white_bg: isWhiteBg }"
    data-page="home"
    v-if="!loading"
  >
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click="back"></a>
        <h1 class="title">预约业务</h1>
        <a v-show="pageType !== 1" class="icon_text" @click="pageType = 1"
          >历史记录</a
        >
      </div>
    </header>
    <article class="content" v-if="pageType === 0">
      <div v-if="bizList.length === 0" class="acct_nodata">
        <div class="icon">
          <img :src="require('@/assets/images/noData2.svg')" />
        </div>
        <p></p>
        <h5>您当前没有已预约的待办业务</h5>
        <p>如果您已提出申请，请耐心等待，稍后再来试试</p>
      </div>
      <div v-else>
        <h5 class="com_title">尊敬的客户，您当前己预约如下业务</h5>
        <ul class="bus_yy_list">
          <li
            v-for="(item, index) in bizList"
            :key="index"
            @click="togoDetail(item)"
          >
            <div class="base">
              <h5>{{ item.preBizName }}</h5>
              <p class="imp_span">{{ item.expireTimeDesc }}</p>
            </div>
            <span class="state">{{ item.statusDesc }}</span>
            <i class="arrow"></i>
          </li>
        </ul>
      </div>
    </article>
    <article class="content" v-else-if="pageType === 1">
      <div v-if="hisList.length === 0" class="acct_nodata">
        <div class="icon">
          <img :src="require('@/assets/images/noData2.svg')" />
        </div>
        <h5>您当前暂无历史预约记录</h5>
        <p>温馨提示：仅展示近一年办理的预约业务</p>
      </div>
      <div v-else>
        <h5 class="com_title">尊敬的客户，您的历史预约业务如下</h5>
        <ul class="bus_yy_list">
          <li v-for="(item, index) in hisList" :key="index">
            <div class="base">
              <h5>{{ item.preBizName }}</h5>
              <p class="imp_span">{{ item.expireTimeDesc }}</p>
            </div>
            <span class="state">{{ item.statusDesc }}</span>
          </li>
        </ul>
        <div class="tip_txtbox">
          <p>温馨提示：仅展示近一年办理的预约业务</p>
        </div>
      </div>
    </article>
    <footer
      v-show="pageType === 0 && bizList.length === 0"
      class="footer white_bg"
    >
      <div class="ce_btn black">
        <a class="p_button border" @click="back">返回</a>
      </div>
    </footer>
  </section>
</template>

<script>
import {
  bcPreBizList,
  bcPreBizHisList,
  flowQueryIns,
  bcPreBizUpdate
} from '@/service/service';
import { exitApp, checkedUserAgent } from '@/common/util';
export default {
  data() {
    return {
      pageType: 0, // 0预约列表 1历史记录
      loading: true,
      bizList: [],
      hisList: [],
      lryyBizType: '010277' // 两融预约业务编号
    };
  },
  computed: {
    isWhiteBg() {
      return (
        (this.pageType === 0 && this.bizList.length === 0) ||
        (this.pageType === 1 && this.hisList.length === 0)
      );
    },
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo?.clientId;
    }
  },
  watch: {
    pageType(type) {
      if (type === 1) {
        this.queryHisList();
      }
    },
    ssoLoginFlag: {
      handler: function (clientId) {
        console.log(clientId);
        if (clientId) {
          this.renderingView();
        }
      },
      immediate: true
    }
  },
  methods: {
    renderingView() {
      const { fundAccount } = this.$store.state.user.userInfo;
      const { bizType: pageBizType = '' } = this.$route.params;
      bcPreBizList({ fundAccount })
        .then(({ code, msg, data }) => {
          if (code === 0) {
            this.bizList = data.bcPreBizList;
            const weixinEntry = checkedUserAgent().weixin;
            const jdChannel = this.bizList.some(
              ({ channelType = '' }) => channelType === '1'
            );

            if (pageBizType !== '') {
              const bizTypeList = this.bizList.filter(
                ({ bizType }) => bizType === pageBizType
              );
              if (bizTypeList?.length > 0) {
                this.togoDetail(bizTypeList[0]);
              } else {
                return Promise.reject(
                  '您还未申请两融开户，请登录电脑端业务办理，完成开户步骤。'
                );
              }
            } else if (jdChannel && weixinEntry) {
              //微信入口，京东渠道开户客户仅展示预约两融开户
              this.bizList = this.bizList.filter(
                ({ bizType }) => bizType === this.lryyBizType
              );
              this.loading = false;
            } else {
              this.loading = false;
            }
          } else {
            return Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err,
            confirm: this.backtrack
          });
        });
    },
    queryHisList() {
      const { fundAccount } = this.$store.state.user.userInfo;
      bcPreBizHisList({ fundAccount })
        .then(({ code, msg, data }) => {
          if (code === 0) {
            this.loading = false;
            this.hisList = data.bcPreBizList;
          } else {
            return Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },
    togoDetail(item = {}) {
      // 两融预约办理完成，不能重新创建流程
      if (item.bizType === this.lryyBizType && item.status === '3') {
        this.$TAlert({
          tips: '您申请两融开户时录制的视频已审核通过，请重新登录电脑端业务办理，点击“继续开户”完成后续开户步骤。',
          confirm: this.backtrack
        });
        return;
      }
      const initCallBack = function ({ flowToken }) {
        return new Promise((resolve, reject) => {
          flowQueryIns({ flowToken })
            .then(({ data }) => {
              const { id, bizType } = data;
              $h.setSession('bizType', bizType);
              return bcPreBizUpdate({
                flowInsId: id,
                preBizId: item.preBizId
              });
            })
            .then(() => {
              resolve({ code: 0 });
            })
            .catch((err) => {
              reject({ code: 1000, msg: err });
            });
        });
      };
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, {
          bizType: item.bizType,
          isReset:
            item.flowInsId === '' || item.flowInsStatus === '2' ? 1 : null, //没有受理单或者受理作废强制新开流程
          contextParam: JSON.stringify({
            preBizName: item.preBizName
          }),
          flowInsId: item.flowInsStatus === '2' ? '' : item.flowInsId,
          extendParam: { initCallBack }
        });
      });
    },
    backtrack() {
      if ($hvue.platform === '0') {
        this.$router.back();
      } else {
        exitApp();
      }
    },
    back() {
      if (this.pageType === 0) {
        this.$router.back();
      } else {
        this.pageType--;
      }
    }
  },
  created() {},
  mounted() {}
};
</script>

<style scoped>
.bus_yy_list li {
  background: #ffffff;
  display: flex;
  align-items: center;
  padding: 0.16rem;
  margin-top: 0.08rem;
}
.bus_yy_list li:first-child {
  margin-top: 0;
}
.bus_yy_list li .base {
  flex: 1;
  min-width: 0;
}
.bus_yy_list li .base h5 {
  font-size: 0.16rem;
  line-height: 1.5;
  font-weight: normal;
}
.bus_yy_list li .base p {
  margin-top: 0.05rem;
  color: #999999;
}
.bus_yy_list li .base .imp_span {
  color: #ffb415;
}
.bus_yy_list li .state {
  font-size: 0.14rem;
  line-height: 1.5;
  color: #999999;
  margin-left: 0.16rem;
}
.bus_yy_list li .arrow {
  margin-left: 0.1rem;
  font-family: 'wt-iconfont' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #bbbbbb;
  width: 0.16rem;
  height: 0.16rem;
  font-size: 0.16rem;
  line-height: 0.16rem;
}
.bus_yy_list li .arrow:before {
  content: '\e619';
}
.tip_txtbox {
  background: #f5f6fa;
}
</style>
