<template>
  <section class="main fixed" data-page="home">
    <t-header @back="back" title="我的资料"></t-header>
    <article class="content">
      <div
        v-if="doubtAddress === '1'"
        class="reject_box"
        style="position: fixed; z-index: 1000"
      >
        <div style="padding: 7px 15px 7px 15px">
          <p>
            根据监管关于金融机构客户身份信息识别相关要求，我司需登记您准确的常住地址。请及时修改或确认您的常住地址，否则您的账户可能会被采取禁止银行支取等限制措施。
          </p>
        </div>
      </div>
      <div v-if="doubtAddress === '1'" class="reject_box">
        <div style="padding: 7px 15px 7px 15px">
          <p>
            根据监管关于金融机构客户身份信息识别相关要求，我司需登记您准确的常住地址。请及时修改或确认您的常住地址，否则您的账户可能会被采取禁止银行支取等限制措施。
          </p>
        </div>
      </div>
      <van-form ref="formList">
        <div class="com_title">
          <h5>证件信息</h5>
        </div>
        <div class="com_box">
          <van-field
            :value="format(form.clientName, 'clientName')"
            label="姓名"
            placeholder="请补充"
            input-align="right"
            readonly
          />
          <van-field
            v-if="form.idKind !== '0'"
            :value="format(form.idKind, 'idKindOptions')"
            label="证件类型"
            placeholder="请补充"
            input-align="right"
            readonly
            error-message-align="right"
          />
          <van-field
            :value="format(form.idNo, 'idNo')"
            label="证件号码"
            placeholder="请补充"
            input-align="right"
            readonly
            error-message-align="right"
            :error-message="idNoCheckTips"
          />
          <van-field
            v-if="form.idKind === '0'"
            :value="format(form.idAddress, 'idAddress')"
            label="证件地址"
            placeholder="请补充"
            type="textarea"
            input-align="right"
            rows="1"
            readonly
            autosize
            error-message-align="right"
          />
          <van-field
            :value="`${formatDate(form.idBegindate)}-${formatDate(
              form.idEnddate
            )}`"
            label="到期日"
            placeholder="请补充"
            input-align="right"
            readonly
            error-message-align="right"
            :error-message="dateCheckTips"
          />
          <van-field
            v-if="form.idKind === '0'"
            :value="form.issuedDepart"
            label="签发机关"
            type="textarea"
            placeholder="请补充"
            input-align="right"
            rows="1"
            readonly
            autosize
          />
          <div class="opea_ctbox">
            <a class="com_btn" @click="toBizType('010006', '0-3059')"
              >更新证件信息</a
            >
          </div>
        </div>
        <div class="com_title">
          <h5>风险测评</h5>
        </div>
        <div class="com_box">
          <van-field
            :value="riskLevelNameTips"
            label="风险评级"
            placeholder="请补充"
            input-align="right"
            readonly
          />
          <van-field
            v-if="showRiskDate"
            :value="`${formatDate(riskInfo.corpBeginDate)}-${formatDate(
              riskInfo.corpEndDate
            )}`"
            label="有效期限"
            placeholder="请补充"
            input-align="right"
            readonly
            error-message-align="right"
            :error-message="riskCheckTips"
          />
          <div v-if="riskBtn !== ''" class="opea_ctbox">
            <a class="com_btn" @click="toBizType('010013', '0-3060')">{{
              riskBtn
            }}</a>
          </div>
        </div>
        <div class="com_title">
          <h5>基本信息</h5>
        </div>
        <div class="com_box">
          <van-field
            :value="format(form.mobileTel, 'mobileTel')"
            label="手机号"
            placeholder="请补充"
            right-icon="arrow"
            input-align="right"
            type="textarea"
            rows="1"
            readonly
            autosize
            class="redErrField"
            error-message-align="right"
            :error-message="vaildTips.mobileTel"
            @click="clickInput('mobileTel')"
          >
            <div slot="right-icon">
              <span class="arrow"></span>
            </div>
          </van-field>
          <van-field
            :value="format(form.address, 'address')"
            type="textarea"
            label="常住地址"
            placeholder="请补充"
            right-icon="arrow"
            input-align="right"
            rows="1"
            readonly
            autosize
            class="redErrField"
            error-message-align="right"
            :error-message="vaildTips.address"
            @click="clickInput('address')"
          >
            <div slot="right-icon">
              <span class="arrow"></span>
            </div>
          </van-field>
          <van-field
            :value="form.zipcode"
            label="邮政编码"
            placeholder="请补充"
            right-icon="arrow"
            input-align="right"
            type="textarea"
            rows="1"
            readonly
            autosize
            class="redErrField"
            @click="clickInput('address')"
          >
            <div slot="right-icon">
              <span class="arrow"></span>
            </div>
          </van-field>
          <van-field
            v-if="creditFundAccount !== ''"
            :value="format(form.appendEmail, 'appendEmail')"
            label="追保邮箱"
            placeholder="请补充"
            input-align="right"
            type="textarea"
            rows="1"
            readonly
            autosize
            error-message-align="right"
            :error-message="vaildTips.appendEmail"
            @click="clickInput('appendEmail')"
          >
            <div slot="right-icon">
              <span class="arrow"></span>
            </div>
          </van-field>
          <van-field
            :value="format(form.professionCode, 'professionCodeOptions')"
            label="职业"
            placeholder="请补充"
            right-icon="arrow"
            input-align="right"
            type="textarea"
            rows="1"
            readonly
            autosize
            class="redErrField"
            error-message-align="right"
            :error-message="vaildTips.professionCode"
            @click="clickInput('professionCode')"
          >
            <div slot="right-icon">
              <span class="arrow"></span>
            </div>
          </van-field>
          <van-field
            :value="format(form.taxResidentPerson, 'taxResidentPersonOptions')"
            label="税收身份"
            placeholder="请补充"
            right-icon="arrow"
            input-align="right"
            type="textarea"
            rows="1"
            readonly
            autosize
            class="redErrField"
            @click="clickInput('taxResidentPerson')"
          >
            <div slot="right-icon">
              <span class="arrow"></span>
            </div>
          </van-field>
          <van-field
            :value="format(form.phonecode, 'phonecode')"
            label="固定电话"
            right-icon="arrow"
            input-align="right"
            type="textarea"
            rows="1"
            readonly
            autosize
            class="specialField"
            error-message-align="right"
            :error-message="vaildTips.phonecode"
            @click="clickInput('phonecode')"
          >
            <div slot="right-icon">
              <span class="arrow"></span>
            </div>
          </van-field>
          <van-field
            :value="format(form.email, 'email')"
            label="电子邮箱"
            placeholder="请补充"
            right-icon="arrow"
            input-align="right"
            type="textarea"
            rows="1"
            readonly
            autosize
            error-message-align="right"
            :error-message="vaildTips.email"
            @click="clickInput('email')"
          >
            <div slot="right-icon">
              <span class="arrow"></span>
            </div>
          </van-field>
        </div>
        <div class="com_title">
          <h5>其他信息</h5>
        </div>
        <div class="com_box">
          <van-field
            :value="format(form.controlPerson, 'controlPersonOption')"
            label="实际控制投资者自然人"
            placeholder="请补充"
            right-icon="arrow"
            input-align="right"
            type="textarea"
            rows="1"
            readonly
            autosize
            class="redErrField"
            @click="clickInput('controlPerson')"
          >
            <div slot="right-icon">
              <span class="arrow"></span>
            </div>
          </van-field>
          <van-field
            :value="format(form.benefitPerson, 'benefitPersonOption')"
            label="交易的实际受益人"
            placeholder="请补充"
            right-icon="arrow"
            input-align="right"
            type="textarea"
            rows="1"
            readonly
            autosize
            class="redErrField"
            @click="clickInput('benefitPerson')"
          >
            <div slot="right-icon">
              <span class="arrow"></span>
            </div>
          </van-field>
          <van-field
            :value="format(form.creditRecord, 'creditRecordOption')"
            label="诚信记录（含交易合规情况）"
            placeholder="请补充"
            right-icon="arrow"
            input-align="right"
            type="textarea"
            rows="1"
            readonly
            autosize
            class="redErrField"
            @click="clickInput('creditRecord')"
          >
            <div slot="right-icon">
              <span class="arrow"></span>
            </div>
          </van-field>
          <van-field
            :value="format(form.secRelationName, 'secRelationName')"
            label="第二联系人"
            placeholder="请补充"
            right-icon="arrow"
            input-align="right"
            type="textarea"
            rows="1"
            readonly
            autosize
            class="redErrField"
            @click="clickInput('secRelationName')"
          >
            <div slot="right-icon">
              <span class="arrow"></span>
            </div>
          </van-field>
          <van-field
            v-model="branchName"
            label="开户营业部"
            placeholder="请补充"
            input-align="right"
            type="textarea"
            rows="1"
            readonly
            autosize
          />
        </div>
      </van-form>
    </article>
  </section>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
import {
  clientInfoQry,
  csdcInfoSubmit,
  riskQuery,
  clientInfoTipOff
} from '@/service/service';
import {
  riskFlagQuery,
  // addClientCritMark,
  peofessionPreSubmit
} from '@/service/modifyClientService';
import {
  clientOccTip
  // addClientCritMark,
  // peofessionPreSubmit
} from '@/service/modifyClientService';
import { queryDictProps } from '@/common/util';
import { exitApp } from '@/common/util';
import { ID_KIND } from '@/common/enumeration';
import {
  checkHKLiveCard,
  checkHKPassCard,
  idCardToBirthday,
  computeGetYears
} from '@/common/util';

// 获取两个对象里值不同的key，对比修改前和修改后，有哪些key做了修改
function findDifferentKeys(obj1, obj2) {
  let differentKeys = [];
  for (let key in obj1) {
    if (obj1.hasOwnProperty(key) && obj2.hasOwnProperty(key)) {
      if (obj1[key] !== obj2[key]) {
        differentKeys.push(key);
      }
    }
  }
  return differentKeys;
}
export default {
  name: 'ClientInfoModify',
  data() {
    return {
      ID_KIND,
      creditFundAccount: '',
      profFlag: '',
      validDateFlag: '',
      idDateCheckFlag: '',
      idNoCheckFlag: '0',
      doubtAddress: '',
      csdcAddressFlag: '',
      vaildTips: {},
      requestData: {}, //请求出来的defaule数据
      idKindOptions: [],
      degreeCodeOption: [],
      professionCodeOptions: [], //职业下拉框
      taxResidentPersonOptions: [], //税务人下拉框
      benefitPersonOption: [],
      controlPersonOption: [],
      creditRecordOption: [],
      showProfessionCode: false,
      clientAge: '',
      degreeCode: '',
      riskInfo: {
        riskLevelName: '',
        corpRiskLevel: ''
      },
      branchName: '',
      // 修改后表单中的数据
      form: {
        csdcAddressFlag: '',
        csdcPhoneFlag: '',
        clientName: '',
        idNo: '',
        idAddress: '',
        issuedDepart: '',
        idBegindate: '',
        idEnddate: '',
        idKind: '',
        mobileTel: '',
        address: '',
        zipcode: '',
        appendEmail: '',
        professionCode: '',
        phonecode: '',
        email: '',
        creditRecord: '',
        secRelationName: '',
        secRelationPhone: '',
        socialralType: '',
        benefitPerson: '',
        controlPerson: '',
        benefitClientName: '',
        benefitIdNo: '',
        benefitIdBegindate: '',
        benefitIdEnddate: '',
        benefitAddress: '',
        benefitMobileTel: '',
        benefitEmail: '',
        benefitProfessionCode: '',
        ctrPersonClientName: '',
        ctrPersonIdNo: '',
        ctrPersonIdBegindate: '',
        ctrPersonIdEnddate: '',
        ctrPersonAddress: '',
        ctrPersonMobileTel: '',
        ctrPersonEmail: '',
        ctrPersonProfessionCode: '',
        taxResidentPerson: '',
        birthday: '',
        clientSurname: '',
        engAddress: '',
        engCity: '',
        engCountry: '',
        engProvince: '',
        livingAddressEn: '',
        livingCity: '',
        livingCountry: '',
        livingProvince: '',
        personalName: '',
        taxResidentData: ''
      }
    };
  },
  watch: {
    clientAge() {
      if (this.clientAge <= 44 && this.clientAge >= 30) {
        if (
          this.degreeCode === '1' ||
          this.degreeCode === '2' ||
          this.degreeCode === '3'
        ) {
          this.professionCodeOptions = this.professionCodeOptions.filter(
            (item) => item.dictValue !== '9'
          );
        }
      }
    },

    ssoLoginFlag: {
      handler: function (clientId) {
        console.log(clientId);
        if (clientId) {
          this.renderingView();
        }
      },
      immediate: true
    },

    'requestData.csdcFlag': {
      handler(val) {
        /* 中登同步需求 start */
        const { csdcPhoneFlag, csdcAddressFlag } = this.requestData;
        //1-弹窗提醒、0-无须弹窗
        if (val === '1') {
          let checkList = [];
          //联系电话中登比对0-一致，1-不一致
          if (csdcPhoneFlag === '1') {
            checkList.push('手机号');
          }
          // 联系地址中登比对0-一致，1-不一致
          if (csdcAddressFlag === '1') {
            checkList.push('联系地址');
          }
          this._clientInfoTipOff();
          this.$TAlert({
            title: '温馨提示',
            tips: `您当前的${checkList.join(
              '、'
            )}信息与中登留存不一致，请确认是否需要修改。`,
            hasCancel: true,
            confirmBtn: '确认无误',
            cancelBtn: '立即修改',
            cancel: () => {
              if (csdcPhoneFlag === '1') {
                this.clickInput('mobileTel');
              } else {
                this.clickInput('address');
              }
            },
            confirm: () => {
              csdcInfoSubmit({
                address: this.requestData.address,
                mobileTel: this.requestData.mobileTel
              })
                .then(({ code, msg }) => {
                  if (code !== 0) {
                    return Promise.reject(msg);
                  } else {
                    this.renderingView();
                  }
                })
                .catch((err) => {
                  this.$TAlert({
                    tips: err
                  });
                });
            }
          });
        }
        /* 中登同步需求 end */
      },
      immediate: true
    }
  },
  computed: {
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo?.clientId;
    },

    hkMacTaiwanPass() {
      const { HK_MACAU_PASS, TAIWAN_PASS } = this.ID_KIND; // 港澳台通行证
      return [HK_MACAU_PASS, TAIWAN_PASS].includes(this.form.idKind);
    },
    hkMacTaiwanId() {
      const { HK_ID, MACAU_ID, HK_MACAU_TAIWAN_ID } = this.ID_KIND; // 港澳台居民居住证
      return [HK_ID, MACAU_ID, HK_MACAU_TAIWAN_ID].includes(this.form.idKind);
    },

    riskLevelNameTips() {
      // riskInfo.corpRiskLevel
      console.log(this.riskInfo);
      if (this.riskInfo.corpEndDate === '0') {
        return '非最新版本，请重新测评';
      }
      if (
        this.riskInfo.corpRiskLevel === '0' &&
        this.riskInfo.minRankFlag === '0'
      ) {
        return '未评级';
      }
      if (this.profFlag === '1') {
        return '专业投资者无需测评';
      }
      return `${this.riskInfo.riskLevelName}（C${this.riskInfo.corpRiskLevel}）`;
    },

    showRiskDate() {
      if (this.riskInfo.corpEndDate === '0') {
        return false;
      }
      if (
        this.riskInfo.corpRiskLevel === '0' &&
        this.riskInfo.minRankFlag === '0'
      ) {
        return false;
      }
      if (this.profFlag === '1') {
        return false;
      }
      return true;
    },

    riskBtn() {
      if (this.riskInfo.corpEndDate === '0') {
        return '重新测评';
      }
      if (
        this.riskInfo.corpRiskLevel === '0' &&
        this.riskInfo.minRankFlag === '0'
      ) {
        return '前往测评';
      }
      if (this.profFlag === '1') {
        return '';
      }
      return '重新测评';
    },

    riskCheckTips() {
      if (this.validDateFlag === '2') {
        return '已过期';
      } else if (this.validDateFlag === '1') {
        return '即将过期';
      } else {
        return '';
      }
    },

    dateCheckTips() {
      if (this.idDateCheckFlag !== '0') {
        // 0: 正常 1：即将过期 2：已过期 3：格式不正确
        if (this.idDateCheckFlag === '1') {
          return '即将过期';
        } else if (this.idDateCheckFlag === '2') {
          return '已过期';
        } else if (this.idDateCheckFlag === '3') {
          return '格式不正确';
        }
      } else {
        return '';
      }
    },

    idNoCheckTips() {
      // if (this.idNoCheckFlag !== '0') {
      //   // 0: 正常 1：异常
      //   return '格式不正确';
      // } else {
      //   return '';
      // }
      if (!['0', 'i', 'j', 'G', 'H'].includes(this.form.idKind)) {
        return '';
      }
      let idNo = this.form.idNo;
      let matchFlag = true;
      // if (idNo === '' || clientName === '') {
      //   this.$TAlert({
      //     title: '温馨提示',
      //     tips: '请补充身份信息后再提交变更申请。'
      //   });
      //   return false;
      // }
      let idNoRegex = /^([\d]{17}[\dXx]|[\d]{15})$/;
      let idNoLenTest = idNo.length === 18;
      let idNoTest = idNoRegex.test(idNo);
      let idNoTestErrMsg = '格式不正确';
      console.log(this.hkMacTaiwanId);
      console.log(this.hkMacTaiwanPass);
      if (this.hkMacTaiwanId) {
        idNoTest = checkHKLiveCard(idNo);
        idNoTestErrMsg = '格式不正确';
        if (idNo.length !== 18) {
          idNoLenTest = false;
          idNoTestErrMsg = '格式不正确';
        } else {
          idNoLenTest = true;
        }
      } else if (this.hkMacTaiwanPass) {
        idNoTest = checkHKPassCard(idNo);
        idNoTestErrMsg = '格式不正确';
        if (
          this.form.idKind === this.ID_KIND.HK_MACAU_PASS &&
          idNo.length !== 9
        ) {
          idNoTestErrMsg = '格式不正确';
          idNoLenTest = false;
        } else if (
          this.form.idKind === this.ID_KIND.TAIWAN_PASS &&
          idNo.length !== 8
        ) {
          idNoTestErrMsg = '格式不正确';
          idNoLenTest = false;
        } else {
          idNoLenTest = true;
        }
      }
      if (!idNoTest || !idNoLenTest) {
        // this.$TAlert({
        //   title: '温馨提示',
        //   tips: idNoTestErrMsg
        // });
        // _hvueToast({
        //   mes: idNoTestErrMsg
        // });
        return idNoTestErrMsg;
      } else {
        return '';
      }
    }
  },
  created() {
    $h.setSession('showRejectResult', null);
    $h.setSession('clientInfoModify', true);
    // this.renderingView();
    trackEvent({
      event_name: 'ywbl_view',
      page_name: '修改个人资料',
      module_name: '页面展示',
      element_name: 'init'
    });
  },
  mounted() {},
  methods: {
    _clientInfoTipOff() {
      clientInfoTipOff({}).then(() => {});
    },
    back() {
      $h.clearSession('clientInfoModify');
      if ($hvue.platform === '0') {
        this.$router.back();
      } else {
        if ($h.getSession('history_list').index === 0) {
          exitApp();
        } else {
          this.$router.back();
        }
      }
    },

    toBizType(bizType, flowNo) {
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType, flowNo, initJumpMode: '0' });
      });
    },

    formatDate(dateString) {
      var year = dateString ? dateString.substring(0, 4) : '';
      var month = dateString ? dateString.substring(4, 6) : '';
      var day = dateString ? dateString.substring(6, 8) : '';
      return year + '.' + month + '.' + day;
    },

    format(val, key) {
      if (key === 'creditRecordOption') {
        if (val === '0') {
          return this[key].filter((item) => {
            return item.value === val;
          }).length > 0
            ? this[key].filter((item) => {
                return item.value === val;
              })[0].label
            : '';
        } else {
          if (val) {
            return '有不良诚信记录';
          } else {
            return '';
          }
        }
      }
      if (
        [
          'clientName',
          'idNo',
          'idAddress',
          'mobileTel',
          'address',
          'appendEmail',
          'phonecode',
          'email',
          'secRelationName'
        ].includes(key)
      ) {
        if (key === 'clientName') {
          let str = '';
          for (let i = 0; i < val.length - 1; i++) {
            str += '*';
          }
          return val.substring(0, 1) + str;
        }
        if (key === 'idNo') {
          let str = '';
          for (let i = 0; i < val.length - 6; i++) {
            str += '*';
          }
          if (val.length > 8) {
            return val.substring(0, 3) + str + val.substring(val.length - 3);
          } else {
            return val ? val : '';
          }
        }
        if (key === 'idAddress') {
          let str = '';
          for (let i = 0; i < val.length - 6; i++) {
            str += '*';
          }
          return val ? val.substring(0, 6) + str : '';
        }
        if (key === 'mobileTel') {
          if (val.length > 7) {
            return val.substring(0, 3) + '****' + val.substring(7);
          } else {
            return val ? val : '';
          }
        }
        if (key === 'address') {
          let str = '';
          for (let i = 0; i < val.length - 6; i++) {
            str += '*';
          }
          return val ? val.substring(0, 6) + str : '';
        }
        if (key === 'appendEmail') {
          // if (val.includes('@')) {
          //   return val ? val.substring(0, 3) + '****@' + val.split('@')[1] : '';
          // } else {
          //   return val;
          // }

          if (val && val.includes('@') && val.split('@')[0].length > 3) {
            let str = '';
            for (let i = 0; i < val.split('@')[0].length - 3; i++) {
              str += '*';
            }
            return val.substring(0, 3) + str + '@' + val.split('@')[1];
          } else {
            return val;
          }
        }
        if (key === 'phonecode') {
          if (val.length > 6) {
            return (
              val.substring(0, 3) + '*******' + val.substring(val.length - 3)
            );
          } else {
            return val ? val : '';
          }
        }
        if (key === 'email') {
          if (val && val.includes('@') && val.split('@')[0].length > 3) {
            let str = '';
            for (let i = 0; i < val.split('@')[0].length - 3; i++) {
              str += '*';
            }
            return val.substring(0, 3) + str + '@' + val.split('@')[1];
          } else {
            return val;
          }
        }
        if (key === 'secRelationName') {
          return val ? val : '';
        }
      }
      if (
        ![
          'clientName',
          'idNo',
          'idAddress',
          'mobileTel',
          'address',
          'appendEmail',
          'phonecode',
          'email',
          'secRelationName'
        ].includes(key) &&
        this[key].filter((item) => {
          return item.value === val;
        }).length > 0
      ) {
        if (key === 'professionCodeOptions' && val === '99') {
          return '';
        } else {
          return this[key].filter((item) => {
            return item.value === val;
          })[0].label;
        }
      } else {
        return '';
      }
    },

    getDict() {
      queryDictProps('bc.common.idKind').then((res) => {
        this.idKindOptions = res.map((item) => {
          return {
            label: item.dictLabel.trim(),
            value: item.dictValue,
            name: item.dictValue
          };
        });
      });
      queryDictProps('bc.common.taxResidentPerson').then((res) => {
        this.taxResidentPersonOptions = res.map((item) => {
          return {
            label: item.dictLabel.trim(),
            value: item.dictValue,
            name: item.dictValue
          };
        });
      });
      queryDictProps('bc.common.integrityRec').then((res) => {
        this.creditRecordOption = res.map((item) => {
          return {
            label: item.dictLabel.trim(),
            value: item.dictValue,
            name: item.dictValue
          };
        });
      });
      queryDictProps('bc.common.isMyself').then((res) => {
        this.benefitPersonOption = res.map((item) => {
          return {
            label: item.dictLabel.trim(),
            value: item.dictValue,
            name: item.dictValue
          };
        });
        this.controlPersonOption = res.map((item) => {
          return {
            label: item.dictLabel.trim(),
            value: item.dictValue,
            name: item.dictValue
          };
        });
      });
    },

    getProfessionCodeOpt() {
      queryDictProps('bc.common.professionCode').then((res) => {
        this.professionCodeOptions = res.map((item) => {
          let show = true;
          if (item.dictValue === '99') {
            show = false;
          }
          return {
            label: item.dictLabel.trim(),
            value: item.dictValue,
            name: item.dictValue,
            show: show
          };
        });
      });
    },

    clickInput(propType) {
      console.log(propType);
      // if (propType === 'appendEmail') {
      //   if (
      //     this.form.appendEmail &&
      //     /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(this.form.appendEmail)
      //   ) {
      //     return;
      //   }
      // }
      $h.setSession('initJumpMode', '1');
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, {
          bizType: '010004',
          flowNo: '0-3053',
          isReset: null,
          initJumpMode: '0',
          contextParam: JSON.stringify({
            extInitParams: JSON.stringify({
              modifyType: propType
            })
          })
        });
      });
    },

    getdefault(key, value) {
      console.log(value);
      this.requestData = { ...this.requestData, ...value.value };
      this.form = { ...this.form, ...value.value };
    },

    renderingView() {
      this.getProfessionCodeOpt();
      this.getDict();
      clientInfoQry().then((res) => {
        riskQuery({
          paperType: '1',
          userType: res.data.organFlag
        }).then((res) => {
          this.riskInfo = res.data;
        });
        riskFlagQuery({
          paperType: '1',
          userType: res.data.organFlag
        }).then((res) => {
          this.validDateFlag = res.data.validDateFlag;
        });
        this.creditFundAccount = res.data.creditFundAccount;
        this.profFlag = res.data.profFlag;
        this.idDateCheckFlag = res.data.idDateCheckFlag;
        this.idNoCheckFlag = res.data.idNoCheckFlag;
        this.doubtAddress = res.data.doubtAddress;
        this.csdcAddressFlag = res.data.csdcAddressFlag;
        this.clientAge = res.data.clientAge;
        this.degreeCode = res.data.degreeCode;
        this.branchName = res.data.branchName;
        this.requestData = { ...this.requestData, ...res.data };
        // const { inProperty } = this.tkFlowInfo();
        // Object.keys(this.form).forEach((key) => {
        //   this.form[key] = inProperty[key] || this.form[key];
        // });
        Object.keys(this.form).forEach((key) => {
          this.form[key] = res.data[key] || this.form[key];
        });
        this.form.benefitPerson = res.data.benefitIsMyself;
        this.form.controlPerson = res.data.controlIsMyself;
        this.validForm();
      });
    },

    validForm() {
      let vaildTips = {
        mobileTel: '',
        address: '',
        professionCode: '',
        phonecode: '',
        appendEmail: '',
        email: ''
      };
      clientOccTip().then((res) => {
        // 手机号校验
        if (this.form.mobileTel) {
          if (this.form.csdcPhoneFlag === '1') {
            vaildTips.mobileTel = '与在中国结算留存的不一致，请修改';
          } else {
            // 手机号格式校验
            if (
              !/1[3-9][\d]{9}/.test(this.form.mobileTel) &&
              this.form.mobileTel !== ''
            ) {
              vaildTips.mobileTel = '手机号格式不正确，请修改';
            }
          }
        }
        // 常住地址校验
        if (this.form.address) {
          if (
            this.form.address.includes('海外') &&
            this.form.address[0] === '海'
          ) {
          } else {
            let regExp =
              /[`~!@#$^&*()=|{}':;',\[\].<>?~！@#￥……&*——|{}【】‘；：”“'。，、？%+_]/g;
            let reg = /^(?!.*(.)\1{4})[^\r]{0,}$/;
            // let reg1 = /(自治区|省|市|特别行政区)/;
            // let reg2 = /(镇|组|街|乡|弄|路|区|座|层|号|排|栋|幢|巷|村|队|室)/;
            
            let reg1 = /(自治区|省|市|特别行政区)/;
            let reg2 = /(组|座|层|号|栋|幢|室|厦|队)/;
            if (
              this.form.address
                .replace(/\s+/g, '')
                .replace(/[\u4e00-\u9fa5]/g, 'xx').length < 5 ||
              this.form.address
                .replace(/\s+/g, '')
                .replace(/[\u4e00-\u9fa5]/g, 'xx').length > 120 ||
              regExp.test(this.form.address)
            ) {
              // _hvueToast({
              //   mes: '详细地址格式不正确'
              // });
              // return false;
              vaildTips.address = '格式不正确，请修改';
            }
            if (
              !reg.test(this.form.address) ||
              !reg1.test(this.form.address) ||
              !reg2.test(this.form.address)
            ) {
              // _hvueToast({
              //   mes: '详细地址格式不正确'
              // });
              // return false;
              vaildTips.address = '格式不正确，请修改';
            }
          }
          // this.doubtAddress = '1';
          // this.csdcAddressFlag = '1';
          if (this.doubtAddress === '1') {
            vaildTips.address = '不符合规范,请修改';
          }
          if (this.csdcAddressFlag === '1') {
            vaildTips.address = '与在中国结算留存的不一致，请修改';
          }
        }
        // 职业校验
        if (this.form.professionCode) {
          // type = 1 错误提示 type = 0 职业其他
          if (res.data.strategyResult === '0') {
            vaildTips.professionCode = JSON.parse(
              res.data.strategyResultMsg
            ).tips;
          }
        }
        // 固定电话校验
        if (this.form.phonecode) {
          let reg = /^0\d{2,3}-\d{7,8}$/;
          // 校验邮编格式是否正确
          if (!reg.test(this.form.phonecode)) {
            vaildTips.phonecode = '固定电话格式不正确，请修改';
          }
        }
        // 追保邮箱校验
        if (this.form.appendEmail) {
          let reg = /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/;
          // 校验邮编格式是否正确
          if (!reg.test(this.form.appendEmail)) {
            vaildTips.appendEmail = '追保邮箱格式不正确，请修改';
          }
        }
        // 电子邮箱校验
        if (this.form.email) {
          let reg = /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/;
          // 校验邮编格式是否正确
          if (!reg.test(this.form.email)) {
            vaildTips.email = '电子邮箱格式不正确，请修改';
          }
        }
        this.vaildTips = vaildTips;
        console.log(vaildTips);
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@mixin arrow() {
  content: '';
  width: 10px;
  height: 1.1px;
  position: absolute;
  background: #969799;
}

.arrow {
  position: relative;
  margin-right: 10px;
  display: inline-block;
  line-height: 12px;
  &::before {
    @include arrow();
    transform: rotate(45deg) translateX(-4.5px);
  }
  &::after {
    @include arrow();
    transform: rotate(-45deg) translateX(-4.5px);
  }
}
</style>
