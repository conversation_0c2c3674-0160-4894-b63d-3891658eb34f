<template>
  <a
    class="code_btn2"
    :class="{ disabled: countdown }"
    :style="cssCode"
    @click="sendSMS"
    >{{ countdown ? `重新发送${countdown}` : '获取验证码' }}
  </a>
</template>

<script>
import { retrieveFundAccountDefaultMobileSendCode } from '@/service/service';

export default {
  name: 'SmsCodeBtn',
  model: {
    prop: 'uuid',
    event: 'change'
  },
  props: {
    needImgCode: {
      type: Boolean,
      default: false
    },
    uuid: {
      type: String,
      default: ''
    },
    cssCode: {
      type: Object,
      default: () => {}
    },
    mobileNo: {
      type: String | Number,
      default: ''
    },
    bizType: {
      type: String,
      default: ''
    },
    captcha: {
      type: String,
      default: ''
    },
    captchaToken: {
      type: String,
      default: ''
    },
    // 发送短信前执行的方法
    sendBefore: {
      type: Function,
      default: () => {
        return true;
      }
    },
    // 是否手机号脱敏
    desensitize: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    // 发送短信参数(手机号脱敏时需要)
    sendParams: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      count: 0 // 短信验证码倒计时
    };
  },
  computed: {
    countdown() {
      if (this.count <= 0) {
        return false;
      } else {
        return `（${this.count}）`;
      }
    }
  },
  methods: {
    sendSMS() {
      const { clientName, idKind, idNo, mobileTel } = this.sendParams;
      if (!this.desensitize && !/^1[3-9][\d]{9}$/.test(this.mobileNo)) {
        _hvueToast({
          mes: '手机号格式不正确'
        });
        return false;
      }
      if (!this.sendBefore()) {
        this.$emit('send-result', false);
        return false;
      }
      if (this.countdown) return;
      // 发送短信验证码
      retrieveFundAccountDefaultMobileSendCode({
        mobileTel: this.desensitize ? mobileTel : this.mobileNo
      })
        .then((data) => {
          const uuid = data.data.serialNumber;
          this.count = $hvue.customConfig.sendSMSCount || 0;
          const timer = setInterval(() => {
            if (this.count <= 0) {
              clearInterval(timer);
            } else {
              this.count--;
            }
          }, 1000);
          this.$once('hook:deactivated', () => {
            clearInterval(timer);
          });
          this.$emit('change', uuid);
          this.$emit('send-result', true);
        })
        .catch((error) => {
          _hvueToast({
            mes: error
          });
          this.$emit('send-result', false);
        });
    }
  }
};
</script>
