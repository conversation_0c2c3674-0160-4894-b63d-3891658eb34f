<template>
  <section class="main fixed white_bg" data-page="home">
    <t-header @back="back" />
    <article class="content">
      <div class="info_compage">
        <div class="com_title">
          <h5>亲，为了确认您的身份，请输入以下个人信息：</h5>
        </div>
        <div class="input_form">
          <div class="input_text text">
            <span class="tit active">姓名</span>
            <input
              v-model="userName"
              class="t1"
              type="text"
              placeholder="请输入您的姓名"
            />
            <!-- <a class="code_btn" @click="jumpPage">找回账号</a> -->
          </div>
          <div class="input_text text">
            <span class="tit active">证件类型</span>
            <div class="dropdown" @click="showIdTypeMap = true">
              {{ idTypeName }}
            </div>
          </div>
          <div class="input_text text">
            <span class="tit active">证件号码</span>
            <input
              v-model="idNo"
              class="t1"
              type="text"
              placeholder="请输入您的证件号码"
            />
          </div>
          <!-- <div class="input_text text">
            <span class="tit active">预留手机号</span>
            <input
              v-model="mobileTel"
              class="t1"
              type="text"
              placeholder="请输入您在国金预留的手机号码"
            />
            <img
              class="icon_mark"
              src="@/assets/images/icon_mark.jpg"
              @click="showTips"
            />
          </div> -->
          <div class="input_text text code">
            <span class="tit active">图形验证码</span>
            <input
              v-model="imgCode"
              class="t1"
              maxlength="4"
              type="tel"
              placeholder="请输入图形验证码"
            />
            <a class="code_img" @click="imgClick">
              <img :src="imgSrc" />
            </a>
          </div>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="toNext">下一步</a>
      </div>
    </footer>
    <van-popup v-model="showIdTypeMap" round position="bottom">
      <v-picker
        v-model="idType"
        :columns="idTypeMap"
        @onConfirm="pickerCallback"
        @onCancel="showIdTypeMap = false"
      />
    </van-popup>
  </section>
</template>

<script>
import VPicker from '@/components/VPicker';
import { ID_KIND } from '@/common/enumeration';
import { retrieveFundAccountCheck, getImgCode } from '@/service/service.js';
import { exitApp, getOpStation } from '@/common/util';
import AlipayUtil from '@/common/AlipayUtil';
import { nativeFunc60094 } from '@/nativeShell/h5CallNative';

export default {
  components: {
    VPicker
  },
  data() {
    return {
      userName: '',
      idType: '',
      idTypeName: '',
      // mobileTel: '',
      showIdTypeMap: false,
      idTypeMap: [
        { label: '二代身份证', value: ID_KIND.PERSONAL_ID },
        { label: '港澳台居民居住证', value: ID_KIND.HK_MACAU_TAIWAN_ID },
        { label: '港澳台居民来往内地通行证', value: ID_KIND.HK_MACAU_PASS }
      ],
      idNo: '',
      imgCode: '',
      imgSrc: '',
      captchaToken: '',
      bizType: '010049',
      opStation: ''
    };
  },
  computed: {
    isApp() {
      return $hvue.platform !== '0';
    }
  },
  created() {
    nativeFunc60094({
      isInterceptScreenshot: '1',
      screenCaptureTip:
        '请妥善保存您的账号及密码，您保存的图片内容可能涉及到敏感信息，请请勿发送给他人'
    });
    document.addEventListener('visibilitychange', this.viewHideActivate);

    this.opStation = this.$route.query.op_station || '';
    $h.setSession('bizType', this.bizType);
    this.imgClick();
    this.idType = this.idTypeMap[0].value;
    this.idTypeName = this.idTypeMap[0].label;
    const alipayUtil = new AlipayUtil();
    if (alipayUtil.checkAlipay) alipayUtil.setTitle(this.$route.meta.title);
  },
  destroyed() {
    nativeFunc60094({
      isInterceptScreenshot: '0'
    });
    document.removeEventListener('visibilitychange', this.viewHideActivate);
  },
  methods: {
    viewHideActivate() {
      if (document.visibilityState === 'hidden') {
        // 页面进入后台时执行的代码
        this.userName = '';
        this.idNo = '';
        this.mobileTel = '';
        this.imgCode = '';
        console.log('页面进入后台');
      }
    },
    back() {
      if (this.isApp) {
        if ($h.getSession('history_list').index === 0) {
          exitApp();
        } else {
          this.$router.go(-1);
        }
      } else {
        this.$router.go(-1);
      }
    },

    imgClick() {
      getImgCode({}, { loading: false }).then((data) => {
        if (data.code === 0) {
          let results = data.data;
          this.imgSrc = results.image;
          this.captchaToken = results.token;
          this.imgCode = '';
        }
      });
    },

    toNext() {
      if (!this.userName) {
        _hvueToast({
          mes: '请输入您的姓名'
        });
        return false;
      }
      if (!this.idType) {
        _hvueToast({
          mes: '请选择证件类别'
        });
        return false;
      }
      if (!this.idNo) {
        _hvueToast({
          mes: '请输入证件号码'
        });
        return false;
      }
      /* if (!this.mobileTel) {
        _hvueToast({
          mes: '请输入手机号'
        });
        return false;
      } */
      if (!this.imgCode) {
        _hvueToast({
          mes: '请输入图形验证码'
        });
        return false;
      }
      if (this.imgCode.length !== 4) {
        _hvueToast({
          mes: '图形验证码格式不正确'
        });
        return false;
      }
      let idKind = this.idType;
      const regExp = /^\d{8}$/; // 台湾居民来往大陆通行证：8位阿拉伯数字
      if (idKind === ID_KIND.HK_MACAU_PASS && regExp.test(this.idNo)) {
        idKind = ID_KIND.TAIWAN_PASS;
      }
      retrieveFundAccountCheck({
        clientName: this.userName,
        idNo: this.idNo,
        idKind,
        imgCode: this.imgCode,
        captcha: this.imgCode,
        captchaToken: this.captchaToken,
        opStation: this.opStation !== '' ? this.opStation : getOpStation()
      })
        .then((res) => {
          if (res.code === 0) {
            let userInfo = Object.assign({}, res.data);
            $h.setSession(
              'tkTwoFactorToken',
              res.responseHeaders['tk-two-factor-token']
            );
            this.$store.commit('user/setUserInfo', userInfo);
            this.$router.push({
              name: 'fundAccRetPhoneConfirm'
            });
            /* let authorization = res.responseHeaders['tk-token-authorization'];
            $h.setSession('authorization', authorization);
            let userInfo = Object.assign({}, res.data);
            this.$store.commit('user/setUserInfo', userInfo);
            import('@/common/flowMixinV2.js').then((a) => {
              a.initFlow.call(this, { bizType: this.bizType });
            }); */
          }
        })
        .catch((err) => {
          this.imgClick();
          this.$TAlert({ title: '温馨提示', tips: err });
        });
    },
    pickerCallback({ label }) {
      this.idTypeName = label;
    },
    jumpPage() {
      if ($hvue.platform === '0') {
        window.location.href = $hvue.customConfig.thirdPartyUrl.retrieveAccount;
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          params: {
            url: $hvue.customConfig.thirdPartyUrl.retrieveAccount,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      }
    },
    showTips() {
      this.$TAlert({
        title: '温馨提示',
        tips: '预留手机号是您留在国金的联系方式，可能是您开户时使用的手机号。如果因为特殊原因您在国金证券没有预留手机号，或者预留手机号码已变更，请先至就近营业部变更或者致电95310咨询。'
      });
    }
  }
};
</script>
<style scoped>
.van-popup >>> .layer_cont {
  height: auto;
}
.icon_mark {
  position: absolute;
  width: 0.2rem;
  height: 0.2rem;
  right: 0.1rem;
  top: 0.2rem;
}
</style>
