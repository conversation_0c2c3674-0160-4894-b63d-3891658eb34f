<template>
  <section class="main fixed white_bg" data-page="home">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="info_compage">
        <div class="com_title">
          <h5>
            请确认您预留手机号码是否能接收短信，若不能可点击修改重新输入，我们将为您发送验证码
          </h5>
        </div>
        <div class="input_form">
          <div class="input_text text">
            <span class="tit active">手机号码</span>
            <input
              v-model="phoneNumInput"
              ref="phoneNumInput"
              class="t1"
              type="tel"
              maxlength="11"
              :disabled="desensitize"
            />
            <a class="code_btn2" @click="modifyMobile">修改</a>
          </div>
          <div class="input_text text code">
            <span class="tit active">验证码</span>
            <input
              id="smsCode"
              v-model="smsCode"
              @input="smsCode = smsCode.replace(/[^\d]/g, '')"
              class="t1"
              type="tel"
              maxlength="6"
              placeholder="请输入短信验证码"
              autocomplete="off"
            />
            <sms-code-btn
              v-model="uuid"
              :need-img-code="false"
              :mobile-no="phone"
              :biz-type="bizType"
              :desensitize="desensitize"
              :send-params="sendParams"
              @send-result="SMSCodeCallback"
            />
          </div>
        </div>
        <div class="cond_tips">
          短信验证码收不到？试试<a @click="smsVoice" class="com_link"
            >语音验证码</a
          >吧！
        </div>
        <div class="ce_btn">
          <a
            class="p_button"
            :class="{ disabled: disabledForm }"
            @click="submit"
            >下一步</a
          >
        </div>
      </div>
    </article>
  </section>
</template>

<script>
import SmsCodeBtn from './components/SmsCodeBtn.vue';
import { retrieveFundAccountDefaultMobileCodeVerify } from '@/service/service';
// import { formatMobileNo } from '@/common/filter';
import { mapGetters } from 'vuex';
import HmosUtil from '@/common/HmosUtil';
const hmosUtil = new HmosUtil({});

export default {
  components: {
    SmsCodeBtn
  },
  data() {
    return {
      gtPhone: '',
      phone: '',
      smsCode: '',
      imgSrc: '',
      uuid: '',
      bizType: '',
      desensitize: true //是否手机号脱敏
    };
  },
  computed: {
    ...mapGetters('user', ['userInfo']),
    isApp() {
      return $hvue.platform !== '0';
    },
    phoneNumInput: {
      get() {
        return this.phone;
        /* if (this.desensitize) {
          return formatMobileNo(this.phone);
        } else {
          return this.phone;
        } */
      },
      set(val) {
        this.phone = val;
      }
    },
    disabledForm() {
      if (this.smsCode === '' || this.phone === '') {
        return true;
      } else {
        return false;
      }
    },
    sendParams() {
      return {
        ...this.userInfo,
        bizType: this.bizType
      };
    }
  },
  mounted() {
    const { mobileTel } = this.userInfo;
    this.phone = mobileTel;
    this.gtPhone = mobileTel;
    this.bizType = $h.getSession('bizType');
  },
  methods: {
    back() {
      this.$router.back();
    },

    submit() {
      if (this.disabledForm) {
        return;
      }
      if (!this.uuid) {
        this.$TAlert({
          title: '温馨提示',
          tips: '请先获取短信验证码'
        });
        return false;
      }
      if (this.smsCode.length !== 6) {
        _hvueToast({
          mes: '短信验证码不匹配'
        });
        return false;
      }
      const { mobileTel } = this.sendParams;
      retrieveFundAccountDefaultMobileCodeVerify({
        mobileTel: this.desensitize ? mobileTel : this.phoneNumInput,
        opStation: sessionStorage.getItem('originOpStation'),
        captchaCode: this.smsCode,
        serialNumber: this.uuid
      })
        .then((res) => {
          if (res.data.verificationvFlag !== '1') {
            _hvueToast({ mes: '输入的验证码有误，请重新输入' });
            this.smsCode = '';
            return;
          }
          const mobileTel = res.data.mobileTel;
          let authorization = res.responseHeaders['tk-token-authorization'];
          $h.setSession('authorization', authorization);
          let userInfo = Object.assign(this.userInfo, res.data);
          this.$store.commit('user/setUserInfo', userInfo);
          const isGtCheck = this.gtPhone === this.phone ? '1' : '0';

          let native_version = '';
          if ($hvue.platform !== '0') {
            const result = $h.callMessageNative({
              funcNo: '50001'
            });
            result.results = result.results || [];
            let data = result.results[0];
            native_version = data.nativeVersion;
          }

          $h.setSession('showRejectResult', null);

          import('@/common/flowMixinV2.js').then((a) => {
            a.initFlow.call(this, {
              bizType: this.bizType,
              contextParam: JSON.stringify({
                verifMobileTel: mobileTel,
                origin: $hvue.platform,
                native_version,
                extInitParams: JSON.stringify({
                  isGtCheck
                })
              })
            });
          });
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    },

    smsVoice() {
      if (!this.uuid) {
        this.$TAlert({
          title: '温馨提示',
          tips: '请先获取短信验证码'
        });
        return false;
      }
      _hvueConfirm({
        title: '温馨提示',
        mes: '亲，您可以使用输入的手机号，拨打95310-按3-按1，收听语音验证码。',
        opts: [
          {
            txt: '取消',
            color: '#333333'
          },
          {
            txt: '立即拨打',
            callback: () => {
              if (hmosUtil.checkHM) {
                hmosUtil.callPhone('95310');
              } else if ($hvue.platform === '0') {
                window.location.href = 'tel:95310';
              } else {
                let reqParams = {
                  funcNo: '50220',
                  telNo: '95310',
                  callType: '0'
                };
                console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
                const res = $h.callMessageNative(reqParams);
                console.log(`请求结果为: ~~${JSON.stringify(res)}`);
              }
            }
          }
        ]
      });
    },

    modifyMobile() {
      this.desensitize = false;
      this.$nextTick(() => {
        this.phone = '';
        this.$refs.phoneNumInput.focus();
      });
    },

    SMSCodeCallback(flag) {
      console.log(flag);
      if (!flag) {
        this.uuid = '';
      }
    }
  }
};
</script>
