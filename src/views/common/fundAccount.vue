<template>
  <section class="main fixed white_bg" data-page="home">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="result_page">
        <div class="result_tips">
          <div class="icon ok"></div>
          <h5>找到账户如下</h5></h5>
          <p>为防止账户再次丢失，请您将帐号进行记录保存。</p>
        </div>
        <div class="result_info">
          <ul>
            <li v-for="(item,index) in clientIds" :key="index">
              <span class="tit">{{item}}</span>
              <p><span class="state" style="color:#1061ff" @click="copy(item)">复制</span></p>
            </li>
          </ul>
        </div>
        <div class="reject_txtinfo">
          <h5 class="title">
            注：您的账户已通过短信发送至您的预留手机号码，请留意查收。
          </h5>
        </div>
      </div>
      <div class="ce_btn mt20">
        <a class="p_button" @click="next">立即前往登录</a>
      </div>
    </article>
    <!-- <article class="content">
      <div class="zj_account_card">
        <div class="cont">
          <p>客户号</p>
          <div class="num">{{ this.clientId }}</div>
          <div class="btn"><a @click="copy">复制帐号</a></div>
        </div>
      </div>
      <div class="wx_tips">
        <p>为防止账户再次丢失，请您将帐号进行记录保存。</p>
        <p>注：您的账户已通过短信发送至您的预留手机号码，请留意查收。</p>
      </div>
      <div class="ce_btn mt20">
        <a class="p_button" @click="next">立即前往登录</a>
      </div>
    </article> -->
  </section>
</template>

<script>
import { exitApp,wakeLoginApp } from '@/common/util';

export default {
  data() {
    return {
      clientIds: JSON.parse(this.$route.query.id)
    };
  },
  computed: {
    isApp() {
      return $hvue.platform !== '0';
    }
  },
  methods: {
    back() {
      this.$router.back();
    },

    copyToClipboard(textToCopy) {
      // navigator clipboard 需要https等安全上下文
      if (navigator.clipboard && window.isSecureContext) {
        // navigator clipboard 向剪贴板写文本
        return navigator.clipboard.writeText(textToCopy);
      } else {
        // 创建text area
        let textArea = document.createElement('textarea');
        textArea.value = textToCopy;
        // 使text area不在viewport，同时设置不可见
        textArea.style.position = 'absolute';
        textArea.style.opacity = 0;
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        return new Promise((res, rej) => {
          // 执行复制命令并移除文本框
          document.execCommand('copy') ? res() : rej();
          textArea.remove();
        });
      }
    },

    copy(item) {
      this.copyToClipboard(item).then((res) => {
        _hvueToast({
          mes: '复制成功'
        });
      });
      // navigator.clipboard.writeText(this.$route.query.id).then((res) => {
      //   _hvueToast({
      //     mes: '复制成功'
      //   });
      // });
    },

    next() {
      this.$store.commit('user/setUserInfo', null);
      localStorage.removeItem('vuex');
      sessionStorage.clear();
      if (this.isApp) {
        // exitApp();
        wakeLoginApp();
      } else {
        this.$router.replace({
          path: '/login'
        });
      }
    }
  }
};
</script>
