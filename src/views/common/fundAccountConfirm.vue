<template>
  <section class="main fixed white_bg" data-page="home">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="info_compage">
        <div class="com_title">
          <h5>请输入真实身份信息</h5>
        </div>
        <div class="input_form">
          <div class="input_text text">
            <span class="tit">真实姓名</span>
            <input
              class="t1"
              type="text"
              placeholder="请输入"
              v-model="clientName"
            />
          </div>
          <div class="input_text text">
            <span class="tit">身份证号</span>
            <input
              class="t1"
              type="text"
              placeholder="请输入"
              maxlength="18"
              v-model="idNo"
            />
          </div>
        </div>
      </div>
      <div class="ce_btn mt20">
        <a class="p_button" @click="next">提交验证</a>
      </div>
    </article>
  </section>
</template>

<script>
import { retrieveAccountPreCheck } from '@/service/service';
import { exitApp } from '@/common/util';

export default {
  data() {
    return {
      clientName: '',
      idNo: ''
    };
  },
  computed: {
    isApp() {
      return $hvue.platform !== '0';
    }
  },
  methods: {
    back() {
      this.$store.commit('user/setUserInfo', null);
      localStorage.removeItem('vuex');
      sessionStorage.clear();
      if (this.isApp) {
        exitApp();
      } else {
        this.$router.replace({
          path: '/login'
        });
      }
    },

    next() {
      if (!this.clientName) {
        _hvueToast({
          mes: '请输入姓名'
        });
        return false;
      }
      if (!this.idNo) {
        _hvueToast({
          mes: '请输入身份证号'
        });
        return false;
      }
      if (this.clientName.length < 2 || this.clientName.length > 60) {
        _hvueToast({
          mes: '姓名格式不正确'
        });
        return false;
      }
      if (!/^([\d]{17}[\dXx]|[\d]{15})$/.test(this.idNo)) {
        _hvueToast({
          mes: '身份证格式不正确'
        });
        return false;
      }
      retrieveAccountPreCheck({
        clientName: this.clientName,
        idNo: this.idNo
      })
        .then((res) => {
          if (res.code === 0) {
            let mobileTel = JSON.stringify(res.data.mobileTels);
            $h.setSession('mobileTel', mobileTel);
            $h.setSession('clientName', this.clientName);
            this.$router.push({
              name: 'fundAccountPhoneConfirm',
              query: { idNo: this.idNo }
            });
          }
        })
        .catch((err) => {
          this.$TAlert({
            title: '身份信息有误',
            tips: '系统中不存在此用户或输入的信息有误，请重新输入'
          });
        });
    }
  }
};
</script>
