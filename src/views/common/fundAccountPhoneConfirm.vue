<template>
  <section class="main fixed white_bg" data-page="home">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="info_compage">
        <div class="com_title">
          <h5>请填写您预留的手机号码</h5>
        </div>
        <div class="input_form">
          <div class="input_text text">
            <span class="tit active">手机号码</span>
            <input
              class="t1"
              type="tel"
              maxlength="11"
              v-model="phone"
              placeholder="请输入手机号码"
            />
          </div>
          <div class="input_text text code">
            <span class="tit active">图形码</span>
            <input
              v-model="imgCode"
              class="t1"
              maxlength="4"
              type="tel"
              placeholder="请输入图形验证码"
            />
            <a class="code_img" href="javascript:void(0);" @click="imgClick">
              <img :src="imgSrc" />
            </a>
          </div>
          <div class="input_text text code">
            <span class="tit active">短信验证码</span>
            <input
              id="smsCode"
              v-model="smsCode"
              class="t1"
              type="tel"
              maxlength="6"
              placeholder="请输入短信验证码"
              autocomplete="off"
            />
            <a class="code_btn2">获取验证码</a>
            <sms-code-btn
              v-model="uuid"
              :need-img-code="true"
              :mobile-no="phone"
              :captcha="imgCode"
              :captcha-token="captchaToken"
              :sendBefore="checkPhone"
              biz-type="010049"
              @send-result="SMSCodeCallback"
            />
          </div>
        </div>
        <div class="wx_tips">
          <p>
            温馨提示：如果预留手机号不正确或无法接收验证码，请致电
            <span class="imp_span">************</span
            >转人工服务或者前往就近营业部办理
          </p>
        </div>
      </div>
      <div class="ce_btn mt20">
        <a class="p_button" @click="toNext">下一步</a>
      </div>
    </article>
  </section>
</template>

<script>
import SmsCodeBtn from '@/components/SmsCodeBtn.vue';
import { retrieveAccountCheck } from '@/service/service';
import { getImgCode } from '@/service/service.js';

export default {
  components: {
    SmsCodeBtn
  },
  data() {
    return {
      phone: '',
      smsCode: '',
      imgCode: '',
      imgSrc: '',
      captchaToken: '',
      uuid: ''
    };
  },
  computed: {
    isApp() {
      return $hvue.platform !== '0';
    }
  },
  created() {
    $h.setSession('bizType', '010049');
    // this.phone = this.$route.query.mobileTel;
    this.imgClick();
  },
  activated() {
    this.imgCode = '';
    this.captchaToken = '';
    this.uuid = '';
    this.imgClick();
  },
  methods: {
    back() {
      this.$router.back();
    },

    SMSCodeCallback(flag) {
      if (!flag) {
        this.imgCode = '';
        this.captchaToken = '';
        this.imgSrc = '';
        this.uuid = '';
        this.imgClick();
      }
    },

    imgClick() {
      getImgCode({}, { loading: false }).then((data) => {
        if (data.code === 0) {
          let results = data.data;
          this.imgSrc = results.image;
          this.captchaToken = results.token;
          this.imgCode = '';
        }
      });
    },

    checkPhone() {
      // let arrs = JSON.parse(this.$route.query.mobileTel);
      let arrs = JSON.parse($h.getSession('mobileTel'));
      if (!this.phone) {
        this.$TAlert({
          tips: '请输入手机号'
        });
        return false;
      }
      if (arrs.includes(this.phone)) {
        return true;
      } else {
        this.$TAlert({
          tips: '输入的号码不是之前预留的手机号'
        });
        return false;
      }
    },

    toNext() {
      if (!this.uuid) {
        _hvueToast({
          mes: '请先发送短信验证码'
        });
        return false;
      }
      if (!this.imgCode) {
        _hvueToast({
          mes: '请输入图形验证码'
        });
        return false;
      }
      if (!this.smsCode) {
        _hvueToast({
          mes: '请输入短信验证码'
        });
        return false;
      }
      retrieveAccountCheck({
        clientName: $h.getSession('clientName'),
        idNo: this.$route.query.idNo,
        mobileTel: this.phone,
        code: this.smsCode,
        uuid: this.uuid
      })
        .then((res) => {
          if (res.code !== 0) {
            _hvueToast({ mes: '验证码错误，请重新输入' });
            this.smsCode = '';
            return;
          }
          let clientId = JSON.stringify(res.data.clientIds);
          this.$router.push({ name: 'fundAccount', query: { id: clientId } });
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    }
  }
};
</script>
