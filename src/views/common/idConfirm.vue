<template>
  <section class="main fixed white_bg" data-page="home">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="info_compage">
        <div class="com_title">
          <h5>请填写以下信息</h5>
        </div>
        <div class="input_form">
          <div class="input_text text">
            <span class="tit active">客户号</span>
            <input
              v-model="clientId"
              class="t1"
              type="tel"
              maxlength="12"
              placeholder="请输入客户号"
            />
          </div>
          <div class="input_text text">
            <span class="tit active">姓名</span>
            <input
              v-model="clientName"
              class="t1"
              type="text"
              placeholder="请输入姓名"
            />
          </div>
          <div class="input_text text">
            <span class="tit active">身份证号</span>
            <input
              v-model="idNo"
              class="t1"
              type="text"
              maxlength="18"
              placeholder="请输入身份证号"
            />
          </div>
          <div class="input_text text code">
            <span class="tit active">图形码</span>
            <input
              v-model="imgCode"
              class="t1"
              maxlength="4"
              type="tel"
              placeholder="请输入图形验证码"
            />
            <a class="code_img" href="javascript:void(0);" @click="imgClick">
              <img :src="imgSrc" />
            </a>
          </div>
        </div>
      </div>
      <div class="ce_btn mt20">
        <a class="p_button" @click="toNext">下一步</a>
      </div>
    </article>
  </section>
</template>

<script>
import {
  matchClientInfo,
  getImgCode,
  businessStrategyCheck
} from '@/service/service.js';
import { exitApp } from '@/common/util';
export default {
  data() {
    return {
      clientId: '',
      clientName: '',
      idNo: '',
      imgCode: '',
      imgSrc: '',
      captchaToken: ''
    };
  },
  computed: {
    isApp() {
      return $hvue.platform !== '0';
    }
  },
  created() {
    $h.setSession('bizType', '010200');
    this.checkTime();
    this.imgClick();
  },
  destroyed() {
    $h.clearSession('bizType');
  },
  methods: {
    // back() {
    //   this.$router.replace({ name: 'login' });
    // },

    back() {
      this.$store.commit('user/setUserInfo', null);
      localStorage.removeItem('vuex');
      sessionStorage.clear();
      if (this.isApp) {
        exitApp();
      } else {
        this.$router.replace({
          path: '/login'
        });
      }
    },

    checkTime() {
      businessStrategyCheck({
        strategyNo: 'wx_business_handle_time_check'
      }).then((res) => {
        if (res.data.strategyResult === '1') {
          return;
        } else {
          let msg = JSON.parse(res.data.strategyResultMsg);
          this.$TAlert({
            title: msg.title,
            tips: msg.tips,
            confirmBtn: '我知道了',
            confirm: () => {
              this.back();
            }
          });
        }
      });
    },

    imgClick() {
      getImgCode({}, { loading: false }).then((data) => {
        if (data.code === 0) {
          let results = data.data;
          this.imgSrc = results.image;
          this.captchaToken = results.token;
          this.imgCode = '';
        }
      });
    },

    toNext() {
      if (!this.clientId) {
        _hvueToast({
          mes: '请输入客户号'
        });
        return false;
      }
      if (!this.clientName) {
        _hvueToast({
          mes: '请输入姓名'
        });
        return false;
      }
      if (!this.idNo) {
        _hvueToast({
          mes: '请输入身份证号'
        });
        return false;
      }
      if (!this.imgCode) {
        _hvueToast({
          mes: '请输入验证码'
        });
        return false;
      }
      let pattern = /\d{17}[\d|x]|\d{15}/;
      if (!pattern.test(this.idNo)) {
        _hvueToast({
          mes: '身份证号输入格式有误'
        });
        return false;
      } else {
        if (this.idNo.length < 18) {
          this.$TAlert({
            title: '请确认',
            tips: '您的身份证属于第一代身份证，请临柜办理。'
          });
          return false;
        }
      }
      matchClientInfo({
        clientId: this.clientId,
        clientName: this.clientName,
        idNo: this.idNo,
        imgCode: this.imgCode,
        captcha: this.imgCode,
        captchaToken: this.captchaToken
      })
        .then((res) => {
          if (res.code === 0) {
            let authorization = res.responseHeaders['tk-token-authorization'];
            // $h.setSession('bizType', bizType);
            $h.setSession('authorization', authorization);
            let userInfo = Object.assign({}, res.data);
            this.$store.commit('user/setUserInfo', userInfo);
            this.$router.push({
              name: 'phoneConfirm',
              query: { phone: res.data.mobileTel }
            });
          }
        })
        .catch((err) => {
          this.imgClick();
          this.$TAlert({ title: '请确认', tips: err });
          // _hvueToast({
          //   mes: err
          // });
        });
    }
  }
};
</script>
