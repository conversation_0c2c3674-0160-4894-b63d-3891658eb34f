<template>
  <section class="main fixed" data-page="home">
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click="back"></a>
        <h1 class="title">场内基金开户</h1>
        <a class="icon_cs" v-if="isAPP" @click="accountHref"></a>
      </div>
    </header>
    <article class="content">
      <ul class="com_infolist spel">
        <li>
          <span class="tit">普通资金账户</span>
          <p v-if="loadingEd">{{ $store.state.user.userInfo.fundAccount }}</p>
        </li>
        <li>
          <span class="tit">一码通账户</span>
          <p v-if="loadingEd">{{ ymtAccount || '无' }}</p>
        </li>
      </ul>
      <ul class="market_ctlist">
        <li>
          <div class="base">
            <div class="bg"><img src="@/assets/images/bg_shanghai.png" /></div>
            <h5>上海市场</h5>
            <p
              v-if="
                stockAccountInfoSh.stockAccountList.length === 0 && loadingEd
              "
            >
              <span>未开通</span>
            </p>
            <div
              v-for="(item, i) in stockAccountInfoSh.stockAccountList"
              :key="i"
            >
              <p
                style="color: #fa443a"
                v-if="!item.stockAccount && item.accountStatusDesc"
              >
                <span>{{ item.accountStatusDesc }}</span>
              </p>
              <p>
                <span v-if="item.stockAccount" class="num">
                  {{ item.stockAccount }}</span
                >
                <i class="tag_zhu_span" v-show="item.mainFlag === '1'">主</i>
                <em v-if="item.accountStatusDesc" class="acct_s_tag">{{
                  item.accountStatusDesc
                }}</em>
              </p>
            </div>
            <span
              v-if="
                stockAccountInfoSh.stockAccountList.length !== 0 &&
                stockAccountInfoSh.stockAccountList[0].seatNo
              "
              class="state"
              >席位号：{{ stockAccountInfoSh.stockAccountList[0].seatNo }}</span
            >
          </div>
          <div class="opea" v-show="shButtonDiv">
            <a
              v-if="stockAccountInfoSh.buttonStyle.openAccountShow"
              class="com_btn"
              @click="
                toBizType(
                  '010252',
                  '0-3527',
                  '1',
                  stockAccountInfoSh.buttonStyle,
                  stockAccountInfoSz.buttonStyle
                )
              "
              >开通账户</a
            >
            <a
              v-if="stockAccountInfoSh.buttonStyle.appointTradeShow"
              class="com_btn"
              @click="toGJ(stockAccountInfoSh)"
              >指定交易</a
            >
            <a
              v-if="stockAccountInfoSh.buttonStyle.servicePwdShow"
              class="com_btn"
              @click="toBizSjsType('010728', '0-3508')"
              >深交所服务密码</a
            >
            <a
              v-if="stockAccountInfoSh.buttonStyle.changeAccountShow"
              class="com_btn"
              @click="
                toBizType(
                  '010729',
                  '0-3507',
                  '1',
                  stockAccountInfoSh.buttonStyle,
                  stockAccountInfoSz.buttonStyle
                )
              "
              >变更股东账户</a
            >
            <a
              v-if="stockAccountInfoSh.buttonStyle.cancelAccountShow"
              class="com_btn"
              @click="toCancel"
              >注销账户</a
            >
            <a
              v-if="stockAccountInfoSh.buttonStyle.serviceCallShow"
              class="com_btn"
              @click="toOther"
              >联系客服</a
            >
          </div>
        </li>
        <li>
          <div class="base">
            <div class="bg"><img src="@/assets/images/bg_shenzhen.png" /></div>
            <h5>深圳市场</h5>
            <p
              v-if="
                stockAccountInfoSz.stockAccountList.length === 0 && loadingEd
              "
            >
              <span>未开通</span>
            </p>
            <div
              v-for="(item, i) in stockAccountInfoSz.stockAccountList"
              :key="i"
            >
              <p v-if="!item.stockAccount && item.accountStatusDesc">
                <span>{{ item.accountStatusDesc }}</span>
              </p>
              <p>
                <span v-if="item.stockAccount" class="num">{{
                  item.stockAccount
                }}</span>
                <i class="tag_zhu_span" v-show="item.mainFlag === '1'">主</i>
                <em v-if="item.accountStatusDesc" class="acct_s_tag">{{
                  item.accountStatusDesc
                }}</em>
              </p>
            </div>

            <span
              v-if="
                stockAccountInfoSz.stockAccountList.length !== 0 &&
                stockAccountInfoSz.stockAccountList[0].seatNo
              "
              class="state"
              >席位号：{{ stockAccountInfoSz.stockAccountList[0].seatNo }}</span
            >
          </div>
          <div class="opea" v-show="szButtonDiv">
            <a
              v-if="stockAccountInfoSz.buttonStyle.openAccountShow"
              class="com_btn"
              @click="
                toBizType(
                  '010252',
                  '0-3527',
                  '2',
                  stockAccountInfoSh.buttonStyle,
                  stockAccountInfoSz.buttonStyle
                )
              "
              >开通账户</a
            >
            <a
              v-if="stockAccountInfoSz.buttonStyle.appointTradeShow"
              class="com_btn"
              @click="toGJ(stockAccountInfoSh)"
              >指定交易</a
            >
            <a
              v-if="stockAccountInfoSz.buttonStyle.servicePwdShow"
              class="com_btn"
              @click="toBizSjsType('010728', '0-3508')"
              >深交所服务密码</a
            >
            <a
              v-if="stockAccountInfoSz.buttonStyle.changeAccountShow"
              class="com_btn"
              @click="toBGTips"
              >变更股东账户</a
            >
            <a
              v-if="stockAccountInfoSz.buttonStyle.cancelAccountShow"
              class="com_btn"
              @click="toCancel"
              >注销账户</a
            >
            <a
              v-if="stockAccountInfoSz.buttonStyle.serviceCallShow"
              class="com_btn"
              @click="toOther"
              >联系客服</a
            >
          </div>
        </li>
      </ul>
    </article>
    <footer class="footer">
      <!--  <div class="ce_btn">
        <a class="p_button border-2" @click="toBiztypeKHQRD">查看开户确认单</a>
      </div>
      <div class="foot_tipbox">交易日9: 00-16: 00期间支持查看和下载</div> -->
      <div class="ce_btn" v-show="openFundsShow === true">
        <a class="p_button border-2" @click="toBizType('010252')"
          >多开场内基金账户</a
        >
      </div>
    </footer>
  </section>
</template>

<script>
import { exchangeTradedFundAccListQry } from '@/service/inExchangeFoundAccountService';
import { businessStrategyCheck } from '@/service/service.js';
import { exitApp, getInstantToken, jumpThirdPartyUrl } from '@/common/util';

export default {
  data() {
    return {
      loadingEd: false,
      ymtAccount: '',
      openFundsShow: '', //新增多开场内基金账户按钮
      stockAccountInfoSh: {
        buttonStyle: {
          appointTradeShow: false,
          changeAccountShow: false,
          openAccountShow: false,
          serviceCallShow: false,
          servicePwdShow: false,
          cancelAccountShow: false
        },
        stockAccountList: []
      },
      stockAccountInfoSz: {
        buttonStyle: {
          appointTradeShow: false,
          changeAccountShow: false,
          openAccountShow: false,
          serviceCallShow: false,
          servicePwdShow: false,
          cancelAccountShow: false
        },
        stockAccountList: []
      }
    };
  },
  computed: {
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo?.clientId;
    },
    isAPP() {
      return $hvue.platform !== '0';
    },
    szButtonDiv() {
      return (
        this.stockAccountInfoSz.buttonStyle.openAccountShow ||
        this.stockAccountInfoSz.buttonStyle.appointTradeShow ||
        this.stockAccountInfoSz.buttonStyle.servicePwdShow ||
        this.stockAccountInfoSz.buttonStyle.changeAccountShow ||
        this.stockAccountInfoSz.buttonStyle.cancelAccountShow ||
        this.stockAccountInfoSz.buttonStyle.serviceCallShow
      );
    },
    shButtonDiv() {
      return (
        this.stockAccountInfoSh.buttonStyle.openAccountShow ||
        this.stockAccountInfoSh.buttonStyle.appointTradeShow ||
        this.stockAccountInfoSh.buttonStyle.servicePwdShow ||
        this.stockAccountInfoSh.buttonStyle.changeAccountShow ||
        this.stockAccountInfoSh.buttonStyle.cancelAccountShow ||
        this.stockAccountInfoSh.buttonStyle.serviceCallShow
      );
    }
  },
  watch: {
    ssoLoginFlag: {
      handler: function (clientId) {
        console.log(clientId);
        if (clientId) {
          this.renderingView();
        }
      },
      immediate: true
    }
  },
  created() {
    $h.setSession('showRejectResult', null);
    $h.setSession('inExchangFoundAccount', true);
    window.viewShowCallBack = this.viewShowCallBack;
  },
  mounted() {
    // this.renderingView();
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  methods: {
    accountHref() {
      getInstantToken()
        .then(({ instantToken, opStation, appId }) => {
          jumpThirdPartyUrl({
            url: `${window.$hvue.customConfig.targetUrl}/yjbwebonlineservice/onlineservice/web/onlineservice/html/index.html?instant_token=${instantToken}&op_station=${opStation}&app_id=${appId}`
          });
        })
        .catch((err) => {
          _hvueToast({ mes: err });
        });
    },

    viewShowCallBack() {
      this.renderingView();
    },

    back() {
      $h.clearSession('inExchangFoundAccount');
      if ($hvue.platform === '0') {
        this.$router.back();
      } else {
        if ($h.getSession('history_list').index === 0) {
          exitApp();
        } else {
          this.$router.back();
        }
      }
    },

    renderingView() {
      businessStrategyCheck({
        strategyNo: 'wt_klcnjjzh_init_no_parameter_check'
      }).then((res) => {
        if (res.data.strategyResult === '1') {
          exchangeTradedFundAccListQry().then((res) => {
            this.ymtAccount = res.data.acodeAccount;
            this.openFundsShow = res.data.openFundsShow;
            if (res.data.stockAccountInfoSh) {
              const { buttonStyle = {}, stockAccountList = [] } =
                res.data.stockAccountInfoSh;
              this.stockAccountInfoSh = { buttonStyle, stockAccountList };
            }
            if (res.data.stockAccountInfoSz) {
              const { buttonStyle = {}, stockAccountList = [] } =
                res.data.stockAccountInfoSz;
              this.stockAccountInfoSz = { buttonStyle, stockAccountList };
            }
            this.loadingEd = true;
          });
        } else {
          let tip = JSON.parse(res.data.result[0].ruleResultDesc);
          this.$TAlert({
            title: tip.title,
            tips: tip.tips,
            confirm: () => {
              this.back();
            }
          });
          return;
        }
      });
    },

    toBizSjsType(bizType, flowNo) {
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType, flowNo, initJumpMode: '0' });
      });
    },

    toBizType(
      bizType = '',
      flowNo = '',
      exchangeType = '',
      shbuttonStyle = '',
      szbuttonStyle = ''
    ) {
      import('@/common/flowMixinV2.js').then((a) => {
        let extInitParams = {
          isChangeAccountShowSz: false,
          isChangeAccountShowSh: false,
          isOpenAccountShowSz: true,
          isOpenAccountShowSh: true
        };
        if (exchangeType !== '') {
          extInitParams = {
            exchangeType,
            isChangeAccountShowSz: szbuttonStyle.changeAccountShow,
            isChangeAccountShowSh: shbuttonStyle.changeAccountShow,
            isOpenAccountShowSz: szbuttonStyle.openAccountShow,
            isOpenAccountShowSh: shbuttonStyle.openAccountShow
          };
        }
        a.initFlow.call(this, {
          bizType,
          flowNo,
          contextParam: JSON.stringify({
            extInitParams: JSON.stringify(extInitParams)
          }),
          initJumpMode: '0'
        });
      });
    },

    toBGTips() {
      // 深市变更提示页
      this.$router.push({ name: 'changeShareAccountTips' });
    },

    toGJ(stockAccountInfoSh) {
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010053', initJumpMode: '0' });
      });
      return;
      // let targetUrl =
      //   $hvue.customConfig.targetUrl +
      //   '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=specifiedForWeb';
      // if ($hvue.platform == 0) {
      //   window.location.href = targetUrl;
      // } else {
      //   let reqParams = {
      //     funcNo: '60099',
      //     moduleName: 'open',
      //     actionType: '6',
      //     // targetModule: 'open',
      //     params: {
      //       url: targetUrl,
      //       leftType: 1,
      //       rightType: 99,
      //       rightText: ''
      //     }
      //   };
      //   console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
      //   const res = $h.callMessageNative(reqParams);
      //   console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      // }
    },

    toCancel() {
      // 注销账户
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010731', initJumpMode: '0' });
      });
      return;
      // let targetUrl =
      //   $hvue.customConfig.targetUrl +
      //   '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=reservationListForWeb';
      // if ($hvue.platform == 0) {
      //   window.location.href = targetUrl;
      // } else {
      //   let reqParams = {
      //     funcNo: '60099',
      //     moduleName: 'open',
      //     actionType: '6',
      //     // targetModule: 'open',
      //     params: {
      //       url: targetUrl,
      //       leftType: 1,
      //       rightType: 99,
      //       rightText: ''
      //     }
      //   };
      //   console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
      //   const res = $h.callMessageNative(reqParams);
      //   console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      // }
    },

    toOther() {
      // 联系客服
      // this.$TAlert({
      //   title:'温馨提示',
      //   tips:''
      // })
      let targetUrl =
        $hvue.customConfig.targetUrl +
        '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=onlineServiceForWeb';
      if ($hvue.platform == 0) {
        window.location.href = targetUrl;
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          // targetModule: 'open',
          params: {
            url: targetUrl,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      }
    },

    toBiztypeKHQRD() {
      import('@/common/flowMixin.js').then((a) => {
        a.initFlow.call(this, '010003', '0-3057');
      });
    }
  }
};
</script>
