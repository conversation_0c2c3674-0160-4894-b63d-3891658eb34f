<template>
  <section class="main fixed" data-page="home">
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click="back"></a>
        <h1 class="title">启用独立密码</h1>
      </div>
    </header>
    <article class="content">
      <div class="com_title">
        <h5>您有如下资金账号，可单独启用或取消独立密码功能</h5>
      </div>
      <div>
        <div class="pro_sl_item" v-for="(it, i) in fundAccountList" :key="i">
          <div class="tit">
            <h5>
              {{ it.label }}:{{ it.fundAccount
              }}<em
                class="acct_s_tag abnormal"
                v-show="it.fundAccountStatus !== '0'"
                >异常</em
              >
            </h5>
          </div>
          <div class="info">
            <div class="row">
              <span class="state finish">{{
                it.specialFlag === '1' ? '已启用' : '未启用'
              }}</span>
              <span class="time" v-show="it.fundAccountStatus !== '0'"
                >账户状态异常不支持{{
                  it.specialFlag === '1' ? '取消' : '启用'
                }}独立密码</span
              >
            </div>
            <a
              v-if="it.fundAccountStatus === '0'"
              class="com_btn border"
              @click="
                toBizType({
                  enable: it.specialFlag !== '1',
                  fundAccountInfo: it
                })
              "
              >{{ it.specialFlag === '1' ? '去取消' : '去启用' }}</a
            >
          </div>
        </div>
      </div>
    </article>
  </section>
</template>

<script>
import { fundAccountListQryV1 } from '@/service/service.js';
import { ASSET_PROP } from '@/common/enumeration';

export default {
  data() {
    return {
      fundAccountList: []
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    back() {
      this.$router.go(-1);
    },
    init() {
      fundAccountListQryV1({})
        .then(({ data }) => {
          this.fundAccountList = data.fundAccountList
            .filter(({ assetProp }) => {
              return [
                ASSET_PROP.DERIVATIVES_ACCOUNT,
                ASSET_PROP.CREDIT_ACCOUNT
              ].includes(assetProp);
            })
            .map((it) => {
              return {
                label: this.getAssetPropMap(it.assetProp),
                ...it
              };
            });
        })
        .catch((err) => {
          this.$TAlert({
            title: '温馨提示',
            tips: err
          });
        });
    },
    getAssetPropMap(a) {
      let getMap = new Map();
      getMap.set(ASSET_PROP.DERIVATIVES_ACCOUNT, '衍生品资金账户');
      getMap.set(ASSET_PROP.ORDINARY_ACCOUNT, '普通资金账户');
      getMap.set(ASSET_PROP.CREDIT_ACCOUNT, '信用资金账户');
      getMap.set(ASSET_PROP.OPTIONS_ACCOUNT, '期权资金账户');
      getMap.set(ASSET_PROP.FUND_ACCOUNT, '基金资金账户');
      return getMap.get(a) || '';
    },
    toBizType({ enable, fundAccountInfo }) {
      if (enable) {
        import('@/common/flowMixinV2.js').then((a) => {
          a.initFlow.call(this, {
            bizType: '010303',
            initJumpMode: '0',
            contextParam: JSON.stringify({
              extInitParams: JSON.stringify({
                fundAccountInfo
              })
            })
          });
        });
      } else {
        import('@/common/flowMixinV2.js').then((a) => {
          a.initFlow.call(this, {
            bizType: '010305',
            initJumpMode: '0',
            contextParam: JSON.stringify({
              extInitParams: JSON.stringify({
                fundAccountInfo
              })
            })
          });
        });
      }
    }
  }
};
</script>

<style></style>
