<template>
  <section class="main fixed" style="position: fixed">
    <t-header @back="back"></t-header>
    <article class="content">
      <div
        class="visit_main"
        v-for="(item, index) in questionList"
        :ref="`question${index}`"
        :key="index"
      >
        <div class="visit_box">
          <h5>{{ item[0].questionContent }}</h5>
          <ul>
            <li
              :class="{
                checked: m.checked,
                disabled: m.degreeCode && m.degreeCode !== ''
              }"
              @click="selectItem(m, i, index)"
              v-for="(m, i) in item"
              :key="'m' + i"
            >
              <span>{{ m.answerContent }}</span>
            </li>
          </ul>
        </div>
      </div>
      <div class="wx_tips">
        <p>
          温馨提示您：请您妥善好保管账号和密码，定期修改密码。账户需本人操作，不得将本人的证券账户提供给他人使用，除本人外，禁止任何人代为操作，包括您的亲友及我司工作人员等，如您信息发生重要变化（如姓名、身份证信息、手机号码
          ）请您及时变更,因提供信息不真实、不准确、不完整可能会影响账户的使用，请您知悉。
        </p>
      </div>
    </article>

    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="toNext">提交</a>
      </div>
    </footer>
  </section>
</template>

<script>
import { qusetionVisitQuery, qusetionVisitSubmit } from '@/service/service';
import { getJwtToken } from '@/service/service';
import { QUESTION_KIND } from '@/common/enumeration';

export default {
  data() {
    return {
      QUESTION_KIND,
      questionList: [],
      answerList: []
    };
  },
  mounted() {
    this.renderingView();
  },
  methods: {
    back() {
      this.$router.back();
    },

    async renderingView() {
      console.log(this.$route.query);
      let { paperType } = this;
      // const tokenRes = await getJwtToken({
      //   flowNo: flowNodeNo,
      //   businessType: inProperty.bizType
      // });
      $h.setSession('jwtToken', '');
      qusetionVisitQuery({
        subjectNo: this.$route.query.subjectNo,
        bizType: this.$route.query.biz,
        formId: this.$route.query.formId
      }).then((data) => {
        let _resArr = data.data;
        let _queId = '';
        let _queNum = -1;
        let i = 0;
        _resArr.forEach((item) => {
          if (item.questionNo !== _queId) {
            _queId = item.questionNo;
            _queNum++;
            i = 0;
            item.extName =
              item.questionKind === QUESTION_KIND.MULTI ? '（多选）' : '';
            this.$set(this.questionList, _queNum, []);
          }
          // 增加checked属性来判定是否选中当前选项
          if (item.degreeCode !== '' && item.isAlter) {
            item.checked = true;
            this.$set(
              this.answerList,
              _queNum,
              `${item.questionNo}_${item.answerNo}`
            );
          } else {
            item.checked = false;
          }
          this.$set(this.questionList[_queNum], i++, item);
        });
      });
    },

    selectItem(item, aIndex, quesIndex) {
      const _this = this;
      let ansAtr = [];
      let quesNo = '';
      if (this.questionList[quesIndex].find((item) => item.degreeCode)) {
        return;
      }
      _this.questionList[quesIndex].forEach((a) => {
        if (a.questionKind === QUESTION_KIND.MULTI) {
          if (item.answerNo === a.answerNo) {
            a.checked = !a.checked;
          }
          if (a.checked) {
            quesNo = a.questionNo;
            ansAtr.push(a.answerNo);
          }
        } else {
          if (a.answerNo === item.answerNo) {
            a.checked = true;
            quesNo = a.questionNo;
            ansAtr.push(a.answerNo);
          } else {
            a.checked = false;
          }
        }
      });
      // 选择后更改答案列表
      let a = '';
      if (ansAtr.length > 0) {
        a = `${quesNo}_${ansAtr.join('&')}`;
      }
      this.$set(this.answerList, quesIndex, a);
    },

    toNext() {
      console.log(this.answerList.join('|'));
      qusetionVisitSubmit({
        subjectNo: this.$route.query.subjectNo,
        // isObtainLoc:'',
        bizType: this.$route.query.biz,
        formId: this.$route.query.formId,
        paperAnswer: this.answerList.join('|')
      })
        .then((res) => {
          // this.eventMessage(this, EVENT_NAME.NEXT_STEP, {});
          this.$router.replace({ name: 'home' });
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    }
  }
};
</script>
