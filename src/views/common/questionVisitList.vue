<template>
  <section class="main fixed" data-page="home">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="tab_nav">
        <ul>
          <li :class="nav === 0 ? 'active' : ''" @click="nav = 0">
            <span>待完成({{ unList.length }})</span>
          </li>
          <li :class="nav === 1 ? 'active' : ''" @click="nav = 1">
            <span>已完成</span>
          </li>
        </ul>
      </div>
      <div class="tab_content" v-if="nav === 0">
        <ul class="visit_navlist">
          <li
            v-for="(item, index) in unList"
            :key="index"
            @click="gotoDetail(item)"
          >
            <p>{{ item.subjectName }}</p>
            <i class="arrow"></i>
          </li>
        </ul>
      </div>
      <div class="tab_content" v-if="nav === 1">
        <ul class="visit_navlist">
          <li v-for="(item, index) in list" :key="index">
            <p>{{ item.subjectName }}</p>
            <i class="arrow"></i>
          </li>
        </ul>
      </div>
    </article>
  </section>
</template>

<script>
import { returnVisitList } from '@/service/service';

export default {
  data() {
    return {
      nav: 0,
      unList: [],
      list: []
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    back() {
      this.$router.back();
    },

    getList() {
      returnVisitList().then((res) => {
        this.unList = res.data.records.filter(
          (item) => item.visitStatus === '0'
        );
        this.list = res.data.records.filter((item) => item.visitStatus === '1');
      });
    },

    gotoDetail(item) {
      this.$router.push({
        name: 'questionVisitDetail',
        query: {
          subjectNo: item.subjectNo,
          biz: item.bizType,
          formId: item.formId
        }
      });
    }
  }
};
</script>
