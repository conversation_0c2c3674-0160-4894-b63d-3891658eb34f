<template>
  <section class="main fixed white_bg" data-page="home">
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click="back"></a>
        <h1 class="title">风险测评</h1>
      </div>
    </header>
    <article class="content top_border">
      <div class="com_title">
        <h5>尊敬的投资者，您的风险承受能力等级为</h5>
      </div>
      <div class="test_rsult">
        <div class="test_infobox">
          <div class="test_inner">
            <div class="info">
              <h5>{{ testResult.riskLevelName }}</h5>
              <p>{{ testResult.investAdvice }}</p>
            </div>
            <div class="test_level">
              <div class="txt">
                <h5>
                  <strong id="testData" style="font-size: 0.32rem">{{
                    testResult.paperScore
                  }}</strong
                  ><span>分</span>
                </h5>
                <p>测评得分</p>
              </div>
              <div class="test_canvas">
                <canvas id="testCanvas" width="320" height="320"></canvas>
              </div>
            </div>
          </div>
        </div>
        <div class="appro_info">
          <ul>
            <li>
              <span class="tit">拟投资期限为</span>
              <p>根据评估结果，您的投资期限为{{ testResult.enInvestTerm }}</p>
            </li>
            <li>
              <span class="tit">拟投资品种为</span>
              <p>{{ testResult.enInvestKind }}</p>
            </li>
          </ul>
        </div>
        <div v-if="riskMatchRightInfoList.length > 0" class="pro_appro_wrap">
          <p>
            根据上述测评情况，您已参与或已开通的产品或服务的适当性匹配结果如下
          </p>
          <div class="pro_appro_item">
            <h3 class="title on">适当性匹配情况说明</h3>
            <div class="cont">
              <div class="pro_appro_tbbox">
                <table
                  class="pro_appro_table"
                  width="100%"
                  cellpadding="0"
                  cellspacing="0"
                >
                  <tr>
                    <th width="50%">业务名称</th>
                    <th width="25%">业务等级</th>
                    <th width="25%">是否匹配</th>
                  </tr>
                  <tr
                    v-for="(item, index) in riskMatchRightInfoList"
                    :key="index"
                  >
                    <td>{{ item.bizName }}</td>
                    <td>{{ item.bizRiskLevelName }}</td>
                    <td>{{ item.isMatch === '0' ? '否' : '是' }}</td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
          <div class="com_imp_tips">
            建议您审慎评判自身风险承受能力、结合自身投资行为，审慎参与以上产品或服务。
          </div>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button border" @click="back">返回</a>
        <a class="p_button" @click="toRisk">重新测评</a>
      </div>
    </footer>
  </section>
</template>

<script>
import { riskQuery, riskMatchRight } from '@/service/service';

export default {
  data() {
    return {
      testResult: {},
      riskMatchRightInfoList: []
    };
  },
  mounted() {
    this.initData();
    this.$nextTick(() => {
      this.initCanvans();
    });
  },
  methods: {
    initCanvans() {
      var canvas1 = document.getElementById('testCanvas');
      var ctx1 = canvas1.getContext('2d');
      var W1 = canvas1.width;
      var H1 = canvas1.height;
      var deg1 = 0,
        new_deg1 = 0,
        dif1 = 0;
      var loop1;
      var t_data1 = document.getElementById('testData').innerHTML;
      var deColor1 = '#f0f0f0',
        dotColor1 = '#FA443A';

      function init1() {
        ctx1.clearRect(0, 0, W1, H1);
        ctx1.beginPath();
        ctx1.strokeStyle = deColor1;
        ctx1.lineWidth = 32;
        ctx1.arc(
          W1 / 2,
          H1 / 2 + 30,
          130,
          (Math.PI * 5) / 6,
          (Math.PI * 39) / 18,
          false
        );
        ctx1.stroke();

        var r2 = (2.4 * 1 * Math.PI) / 180 + (Math.PI * 1) / 3;
        ctx1.beginPath();
        ctx1.fillStyle = dotColor1;
        ctx1.arc(
          W1 / 2 - 130 * Math.sin(r2),
          H1 / 2 + 30 + 130 * Math.cos(r2),
          16,
          -180,
          true
        );
        ctx1.fill();

        var r3 = (2.4 * 100 * Math.PI) / 180 + (Math.PI * 1) / 3;
        ctx1.beginPath();
        ctx1.fillStyle = deColor1;
        ctx1.arc(
          W1 / 2 - 130 * Math.sin(r3),
          H1 / 2 + 30 + 130 * Math.cos(r3),
          16,
          -180,
          true
        );
        ctx1.fill();

        var r4 = (2.4 * deg1 * Math.PI) / 180 + (Math.PI * 1) / 3;
        ctx1.beginPath();
        ctx1.fillStyle = dotColor1;
        ctx1.arc(
          W1 / 2 - 130 * Math.sin(r4),
          H1 / 2 + 30 + 130 * Math.cos(r4),
          16,
          -180,
          true
        );
        ctx1.fill();

        var r1 = (2.4 * deg1 * Math.PI) / 180;
        ctx1.beginPath();
        ctx1.strokeStyle = dotColor1;
        ctx1.lineWidth = 32;
        ctx1.arc(
          W1 / 2,
          H1 / 2 + 30,
          130,
          (Math.PI * 5) / 6,
          r1 + (Math.PI * 5) / 6,
          false
        );
        ctx1.stroke();

        var r5 = (2.4 * deg1 * Math.PI) / 180 + (Math.PI * 1) / 3;
        ctx1.beginPath();
        ctx1.fillStyle = '#fff';
        ctx1.arc(
          W1 / 2 - 130 * Math.sin(r5),
          H1 / 2 + 30 + 130 * Math.cos(r5),
          7,
          -180,
          true
        );
        ctx1.fill();
      }

      function draw1() {
        new_deg1 = t_data1;
        dif1 = new_deg1 - deg1;
        loop1 = setInterval(to1, 500 / dif1);
      }
      function to1() {
        if (deg1 == new_deg1) {
          clearInterval(loop1);
        }
        if (deg1 < new_deg1) {
          deg1++;
        }
        init1();
      }
      draw1();
    },

    initData() {
      riskQuery().then((data) => {
        // Object.assign(this.testResult, data.data);
        // console.log(this.testResult);
        this.testResult = data.data;
        this.$nextTick(() => {
          this.initCanvans();
        });
        riskMatchRight().then((res) => {
          this.riskMatchRightInfoList = res.data.riskMatchRightInfoList;
        });
      });
    },

    back() {
      this.$router.back();
    },

    toRisk() {
      import('@/common/flowMixin.js').then((a) => {
        a.initFlow.call(this, '010013', '0-3011');
      });
    }
  }
};
</script>

<style></style>
