<template>
  <section
    v-show="showHtmlPage"
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <t-header title="电子协议查询" @back="back" />
    <article ref="protocolCont" class="content" style="background: #ffffff">
      <div
        v-if="contractInfo.contractChannel === 'middleware'"
        class="title"
        v-html="contractInfo.agreementName"
      />
      <div
        v-if="contractInfo.contractChannel === 'middleware'"
        class="protocol_cont"
        ref="middlewareProtocolContent"
      />
      <div
        v-else-if="contractInfo.contractChannel === 'vertex'"
        class="protocol_cont"
        ref="vertexProtocolContent"
      />
      <div
        v-else
        class="protocol_cont"
        id="protocol_cont"
        ref="protocolContent"
      />
    </article>
    <footer class="footer" v-show="!isLastContract" v-if="contractInfo.showNewVersionBtn">
      <div class="ce_btn">
        <a class="p_button" @click="showLastContract">查看新版协议</a>
      </div>
    </footer>
  </section>
</template>

<script>
import { contractQry, infoQry, agreementQry } from '@/service/service';

export default {
  props: {
    contractInfo: {
      type: Object,
      default: () => {}
    },
    showHtmlPage: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      currentContractContent: '',
      lastContractVersion: '',
      lastContractContent: '',
      currentContractContentForMiddleWare: '',
      lastContractContentForMiddleWare: '',
      isLastContract: false,
      queryLastVersion: false
    };
  },
  mounted() {
    console.log('contractInfo', this.contractInfo);
    if (this.contractInfo.contractChannel === 'middleware') {
      // 中台渠道
      this.getMiddleWareContractVersion();
      this.currentContractContentForMiddleWare = this.filterMetaTags(this.contractInfo.agreement_content_text);
      this.$refs['middlewareProtocolContent'].innerHTML = this.filterMetaTags(this.contractInfo.agreement_content_text);
    } else if(this.contractInfo.contractChannel === 'vertex') {
      this.$refs['vertexProtocolContent'].innerHTML = this.contractInfo.agreement_content_text;
    } else {
      // 非中台，自研渠道
      this.getContractText();
      this.getContractVersion();
    }
  },
  methods: {
    back() {
      if (this.isLastContract && this.queryLastVersion) {
        if(this.contractInfo.contractChannel === 'middleware'){
        this.$refs['middlewareProtocolContent'].innerHTML = this.filterMetaTags(this.currentContractContentForMiddleWare);
          return
        }
        this.isLastContract = false;
        this.$refs['protocolContent'].innerHTML = this.filterMetaTags(this.currentContractContent);
        return;
      }
      this.$emit('contractBack', false);
    },
    // 获取当前协议的文本内容--自研渠道
    getContractText() {
      const { econtract_no, econtract_version } = this.contractInfo;
      let params = {
        contract_no: econtract_no,
        version: econtract_version,
        data_channel: 1
      };
      contractQry(params).then((res) => {
        console.log('res', res);
        if (res.code == 0) {;
          this.currentContractContent = this.filterMetaTags(res.data.econtract_content);
          this.$refs['protocolContent'].innerHTML = this.filterMetaTags(res.data.econtract_content);
        }
      });
    },
    // 处理协议内容 去掉meta标签避免影响页面展示
    filterMetaTags(str){
      let regex = /<\/?meta[^>]*>/gi;
      return str.replace(regex, '');

    },
    // 获取有没有最新协议--自研渠道
    getContractVersion() {
      const { econtract_no, econtract_version } = this.contractInfo;
      let params = {
        econtract_id: econtract_no
      };
      infoQry(params).then((res) => {
        if (res.code == 0) {
          this.lastContractVersion = res.data.version;
          if (res.data.version > econtract_version) {
            this.isLastContract = false;
          } else {
            this.isLastContract = true;
          }
        }
      });
    },
    // 查看最新版协议--自研渠道
    showLastContract() {
      const { econtract_no, contractChannel } = this.contractInfo;
      if (contractChannel === 'middleware') {
        this.isLastContract = true;
        this.queryLastVersion = true;
      } else {
        let params = {
          econtract_id: econtract_no,
          include_content: '1' // 传入该参数时，接口返回最新版协议文本内容，不传入该参数时，接口返回最新版协议版本号
        };
        infoQry(params).then((res) => {
          if (res.code == 0) {
            this.isLastContract = true;
            this.queryLastVersion = true;
            this.$refs['protocolContent'].innerHTML =
            this.filterMetaTags(res.data.econtract_content);
          }
        });
      }
    },
    // 获取有没有最新协议 --中台渠道
    getMiddleWareContractVersion() {
      console.log('获取中台的最协议版本号');
      const { agreement_no, econtract_version } = this.contractInfo;
      let params = {
        agreementNo: agreement_no,
        channel: '3'
      };
      agreementQry(params).then((res) => {
        console.log('res ===== >', res);
        if (res.code == 0) {
          this.lastContractVersion = res.data.agreementVersion;
          if (res.data.agreementVersion !== econtract_version) {
            this.isLastContract = false;
            this.lastContractContentForMiddleWare =
            this.filterMetaTags(res.data.dynamicAgreementContent.agreementContentTxt);
          } else {
            this.isLastContract = true;
          }
        }
      });
    }
  }
};
</script>

<style scoped>
.protocol_cont {
  padding: 0.16rem;
}
.title {
  text-align: center;
}
</style>
