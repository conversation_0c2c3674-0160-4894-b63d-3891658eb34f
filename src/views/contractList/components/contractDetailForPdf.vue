<template>
  <section
    v-show="showPdfPage"
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <t-header title="电子协议查询" @back="back" />
    <article ref="protocolCont" class="content" style="background: #ffffff">
      <pdf
        v-for="i in numPages"
        :key="i"
        class="protocol_pdf"
        :src="pdfSrc"
        :page="i"
      />
    </article>
  </section>
</template>

<script>
import pdf from 'vue-pdf';

export default {
  components: {
    pdf
  },
  props: {
    contractInfo: {
      type: Object,
      default: () => {}
    },
    showPdfPage: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      numPages: null,
      pdfSrc: ''
    };
  },
  mounted() {
    console.log('contractInfo', this.contractInfo);
    this.updateData();
  },
  methods: {
    back() {
      this.$emit('contractBack', false);
    },
    updateData() {
      let { content_text } = this.contractInfo;
      if (content_text) {
        const loadingTask = pdf.createLoadingTask(
          content_text,
          // $hvue.customConfig.pdfFileUrl + content_text,
          { withCredentials: false }
        );
        this.pdfSrc = loadingTask;
        loadingTask.promise.then((pdf) => {
          this.numPages = pdf.numPages;
        });
      }
    }
  }
};
</script>
