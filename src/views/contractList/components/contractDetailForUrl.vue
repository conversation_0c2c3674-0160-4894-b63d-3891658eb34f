<template>
  <section
    v-show="showUrlPage"
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <t-header title="电子协议查询" @back="back" />
    <article ref="protocolCont" class="content" style="background: #ffffff">
      <img :src="contractInfo.content_text" />
    </article>
  </section>
</template>

<script>
export default {
  components: {},
  props: {
    contractInfo: {
      type: Object,
      default: () => {}
    },
    showUrlPage: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      numPages: null,
      pdfSrc: ''
    };
  },
  mounted() {
    console.log('contractInfo', this.contractInfo);
  },
  methods: {
    back() {
      this.$emit('contractBack', false);
    }
  }
};
</script>

<style></style>
