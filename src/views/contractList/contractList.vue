<template>
  <fragment>
    <section class="main fixed backWhite_box" data-page="home">
      <header class="header">
        <div class="header_inner">
          <a class="icon_back" @click.stop="back" />
          <h1 class="title">电子协议查询</h1>
        </div>
      </header>
      <article class="content">
        <div class="white_bg">
          <div class="top_searchbox">
            <i class="icon"></i>
            <input
              v-model="searchKey"
              class="t1"
              type="text"
              maxlength="25"
              style="padding: 0.08rem 0.15rem 0.08rem 0.4rem"
              placeholder="请输入协议名称"
              @keyup.enter="toSearch"
            />
            <a class="btn" @click="toSearch">搜索</a>
          </div>
        </div>
        <div v-if="contractList.length > 0">
          <van-list
            v-model="loading"
            :finished="finished"
            finished-text="温馨提示：更多电子协议，请查询国金证券官网。"
            offset="300"
            @load="onLoad"
            :immediate-check="false"
          >
            <van-cell
              v-for="(item, idx) in contractList"
              :key="idx"
              @click="openContract(item)"
              class="contract-title"
            >
              <div class="contract_name_container">
                <div class="contract_name_left">
                  <div class="contract_name">{{ item.econtract_name }}</div>
                  <div class="contract_remark">{{ item.remark }}</div>
                </div>
                <img class="contract_icon" src="@/assets/images/arrow01.png" />
              </div>
            </van-cell>
          </van-list>
        </div>
        <div class="content" v-else>
          <div class="empty">
            <img class="empty-img" src="@/assets/images/noData2.svg" />
            <div class="empty-text">没有查询到您签署过的协议</div>
          </div>
        </div>
      </article>
    </section>
    <contractDetailForPdf
      v-if="showPdfPage"
      :showPdfPage="showPdfPage"
      ref="contractDetailForPdf"
      :contractInfo="contractInfo"
      @contractBack="contractBack"
    />
    <contractDetailForHtml
      v-if="showHtmlPage"
      :showHtmlPage="showHtmlPage"
      ref="contractDetailForHtml"
      :contractInfo="contractInfo"
      @contractBack="contractBack"
    />
    <contrsctDetailForUrl
      v-if="showUrlPage"
      :showUrlPage="showUrlPage"
      ref="contrsctDetailForUrl"
      :contractInfo="contractInfo"
      @contractBack="contractBack"
    />
  </fragment>
</template>
<script>
import { exitApp, locationReplace } from '@/common/util';
import {
  clientContractSignedQry,
  agreementQryBySignId,
  queryVertexAgreementPath,
  queryVertexAgreementHTML
} from '@/service/service';
import contractDetailForPdf from './components/contractDetailForPdf.vue';
import contractDetailForHtml from './components/contractDetailForHtml.vue';
import contrsctDetailForUrl from './components/contractDetailForUrl.vue';

export default {
  components: {
    contractDetailForPdf,
    contractDetailForHtml,
    contrsctDetailForUrl
  },
  data() {
    return {
      page_row: 1,
      page_size: 50,
      searchKey: '',
      contractList: [],
      loading: false,
      finished: false,
      openStockOrder: {
        econtract_name: '开户确认单',
        remark: '(交易日9:00-16:00期间支持查看和下载)',
        key: 'goToKHQRD'
      },
      showAgreeDetail: false,
      isCount: false,
      contractInfo: {},
      showPdfPage: false,
      showHtmlPage: false,
      showUrlPage: false
    };
  },
  computed: {
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo?.clientId;
    }
  },
  watch: {
    ssoLoginFlag: {
      handler: function (clientId) {
        console.log(clientId);
        if (clientId) {
          this.renderingView();
        }
      },
      immediate: true
    }
  },
  created() {
    window.viewShowCallBack = this.viewShowCallBack;
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  mounted() {},
  methods: {
    back() {
      if ($hvue.platform === '0') {
        this.$router.back();
      } else {
        exitApp();
      }
    },
    viewShowCallBack() {
      this.renderingView();
    },
    // 渲染页面
    renderingView() {
      this.page_row = 1;
      let params = {
        account_id: this.ssoLoginFlag,
        account_type: '5',
        page_row: this.page_row,
        page_size: this.page_size
      };
      clientContractSignedQry(params).then((res) => {
        console.log('res======', res);
        if (res.code == 0) {
          this.contractList = [this.openStockOrder, ...res.data];
          if (this.contractList.length < this.page_size) {
            this.finished = true;
          } else {
            this.finished = false;
            this.loading = false;
          }
        }
      });
    },
    // 加载功能
    onLoad() {
      this.page_row = this.page_row + 1;
      console.log('this.page_row', this.page_row);
      let params = {
        account_id: this.$store.state.user?.userInfo?.clientId,
        account_type: '5',
        page_row: this.page_row,
        page_size: this.page_size
      };
      if (this.searchKey.toLowerCase()) {
        params.econtract_name = encodeURIComponent(
          this.searchKey.toLowerCase()
        );
      }
      clientContractSignedQry(params).then((res) => {
        console.log('res======', res);
        if (res.code == 0) {
          this.contractList = this.contractList.concat(res.data);
          if (res.data.length < this.page_size) {
            this.finished = true;
          } else {
            this.loading = false;
          }
        }
      });
    },
    // 搜索功能
    toSearch() {
      console.log('this.searchKey', this.searchKey);
      this.page_row = 1;
      let params = {
        account_id: this.ssoLoginFlag,
        account_type: '5',
        page_row: this.page_row,
        page_size: this.page_size
      };
      if (this.searchKey) {
        params.econtract_name = encodeURIComponent(
          this.searchKey.toLowerCase()
        );
      }
      clientContractSignedQry(params).then((res) => {
        console.log('res======', res);
        if (res.code == 0) {
          if ('开户确认单'.indexOf(this.searchKey) != -1) {
            this.contractList = [this.openStockOrder, ...res.data];
          } else {
            this.contractList = res.data;
          }
          if (this.contractList.length < this.page_size) {
            this.finished = true;
          } else {
            this.finished = false;
            this.loading = false;
          }
        }
      });
    },

    // 打开协议
    openContract(contractItem) {
      if (contractItem.key && contractItem.key === 'goToKHQRD') {
        console.log('去开户确认单业务');
        import('@/common/flowMixinV2.js').then((a) => {
          a.initFlow.call(this, {
            bizType: '010003',
            flowNo: '0-3057',
            initJumpMode: '0'
          });
        });
      } else {
        const {
          data_channel,
          content_type,
          econtract_id,
          econtract_no,
          econtract_version,
          content_text
        } = contractItem;
        console.log('contractItem', contractItem);
        if (data_channel === '4') {
          // 思迪渠道
          this.contractInfo = contractItem;
          this.showPdfPage = true;
        } else if (data_channel === '5') {
          //顶点渠道
          // content_type 1 表示url, 用于PDF渲染；2 表示html文本内容；
          if(content_type === '1') {
            this.getVertexAgreementPDF(contractItem);
          }else{
            this.getVertexAgreementHTML(contractItem);
          }
        } else if (data_channel === '3') {
          // 协议中台渠道
          this.gomIsMiddlewareAgreement(contractItem);
        } else {
          // 自研渠道
          this.contractInfo = { ...contractItem, showNewVersionBtn: true };
          if (content_type === '1') {
            // 自研URL
            let arr = content_text.split('.');
            let len = arr.length;
            let fmt = arr[len - 1];
            fmt = fmt.toLowerCase();
            if (fmt === 'pdf') {
              this.showPdfPage = true;
            } else if (fmt == 'doc' || fmt == 'docx') {
              console.log('fmt', fmt);
              if ($hvue.platform !== '0') {
                let callMessageNativeParams = {
                  funcNo: '50109',
                  moduleName: $hvue.customConfig.moduleName,
                  url: content_text
                };
                let result = $h.callMessageNative(callMessageNativeParams);
                console.log('返回结果', result);
              } else {
                locationReplace(content_text);
              }
            } else {
              this.showUrlPage = true;
            }
          }
          if (content_type === '2') {
            // 自研富文本
            this.showHtmlPage = true;
          }
        }
      }
    },
    getVertexAgreementHTML ({ content_text = "", econtract_no = "", econtract_name = "" }) {
      const _this = this;
      queryVertexAgreementHTML({ xyqsId: content_text }, { responseType: 'text' })
        .then(({ data }) => {
          _this.contractInfo = {
            contractChannel: "vertex",
            agreement_content_text: data,
            agreement_no: econtract_no,
            agreementName: econtract_name
          };
          _this.showHtmlPage = true;
        })
        .catch((e) => {
          this.$TAlert({
            title: "温馨提示",
            tips: "协议查询失败"
          });
        });
    },
    getVertexAgreementPDF(contractItem) {
      const _this = this;
      queryVertexAgreementPath(
        {
          xyqsId: contractItem.content_text
        },
        {
          responseType: 'blob',
          filter: true,
          headers: {
            'tk-encrypt-response': false,
            Accept:
              'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Content-Type': 'application/json',
            Authorization: $h.getSession('authorization'),
            'tk-token-authorization': $h.getSession('authorization'),
            'tk-jwt-authorization': $h.getSession('jwtToken'),
            'tk-flow-token': sessionStorage.getItem('TKFlowToken'),
            'x-bus-id': $h.getSession('bizType'),
            'tk-two-factor-token': $h.getSession('tkTwoFactorToken'),
            merchantId: $hvue.customConfig.merchantId,
            isLoading: true
          }
        }
      )
        .then(({ data }) => {
          const fileReader = new FileReader();
          fileReader.readAsText(data);
          fileReader.onload = function () {
            const url = window.URL.createObjectURL(new Blob([data]));
              _this.contractInfo = { ...contractItem, content_text: url };
              _this.showPdfPage = true;
          };
        })
        .catch((e) => {
          this.$TAlert({
            title: '温馨提示',
            tips: '协议查询失败'
          });
        });
    },
    // 去协议中台的协议详情页
    gomIsMiddlewareAgreement(contractItem) {
      const { sign_id, econtract_no, econtract_version } = contractItem;
      let params = {
        signId: sign_id,
        agreementNo: econtract_no,
        agreementVersion: econtract_version
      };
      agreementQryBySignId(params).then((res) => {
        console.log('res', res);
        const { code, data } = res;
        if (code == 0) {
          switch (data.agreementType) {
            case '1':
              let agreement = data.staticAgreementContent[0];
              if (agreement.agreementContentType === '1') {
                //中台静态协议pdf
                this.contractInfo = {
                  content_text: agreement.agreementContentUrl
                };
                this.showPdfPage = true;
                return;
              }
              if (agreement.agreementContentType === '2') {
                // 中台静态协议html网址
                window.location.href = agreement.agreementContentUrl;
                if ($h.getSession('appId') !== 'yjbwx') {
                  document.title = '电子协议查询';
                }
                return;
              }
              if (agreement.agreementContentType === '3') {
                // 中台静态协议图片
                this.contractInfo = {
                  content_text: agreement.agreementContentUrl
                };
                this.showUrlPage = true;
                return;
              }
              break;
            case '2':
              // 中台静态协议富文本
              this.contractInfo = {
                contractChannel: 'middleware',
                agreement_content_text:
                  data.dynamicAgreementContent.agreementContentTxt,
                agreement_no: econtract_no,
                econtract_version: econtract_version,
                agreementName: data.agreementName,
                showNewVersionBtn: true
              };
              this.showHtmlPage = true;
              break;
            case '3':
              // 动态协议，富文本html
              this.contractInfo = {
                agreement_content_text:
                  data.dynamicAgreementContent.agreementContentTxt,
                showNewVersionBtn: false,
                agreementName: data.agreementName,
                contractChannel: 'middleware'
              };
              this.showHtmlPage = true;
              break;
          }
        }
      });
    },
    // 返回到列表首页
    contractBack(boo) {
      this.showPdfPage = boo;
      this.showHtmlPage = boo;
      this.showUrlPage = boo;
    }
  }
};
</script>
<style scoped>
.empty {
  margin-top: 0.77rem;
  text-align: center;
}
.empty-img {
  margin-bottom: 0.16rem;
}

.empty-text {
  font-size: 0.15rem;
  color: #666666;
}
.contract_name_container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.contract_name {
  width: 100%;
}
.contract_icon {
  width: 0.16rem;
  height: 0.16rem;
}
.contract_remark {
  color: #87878d;
}
.contract_name_left {
  margin-right: 0.27rem;
}
</style>
