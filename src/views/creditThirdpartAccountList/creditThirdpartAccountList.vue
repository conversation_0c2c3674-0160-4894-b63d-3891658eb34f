<template>
  <section class="main fixed backWhite_box" data-page="home" v-if="!isLoading">
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click.stop="back" />
        <h1 class="title">信用三方存管</h1>
      </div>
    </header>
    <template v-if="creditFundAccount">
      <article>
        <div
          v-if="status === 'thirdpartList' && thirdpartList.length > 0"
          class="account_top"
        >
          <div>信用账户</div>
          <div>{{ creditFundAccount }}</div>
        </div>
        <div
          v-if="status === 'thirdpartList' && thirdpartList.length > 0"
          class="thirdpart"
        >
          <div class="bank_card">
            <bankCard
              v-for="(item, idx) in thirdpartList"
              :key="idx"
              :bankItem="item"
              :bindBank="bindBank"
              :changeBank="changeBank"
              :isCredit="true"
            />
          </div>
          <div class="warm_tips">
            <p>温馨提示：</p>
            <p>
              因办理三方存管银行变更需满足“当日无资金流水”、“当日无可取资金”的条件，在此提示您：
            </p>
            <p>
              1、若申请当日账户发生了资金流水，需等待至无资金流水的交易日方可发起三方存管银行变更；
            </p>
            <p>
              2、若申请当日账户有可取资金，您可以自助将资金转出，如果您开通了现金理财功能，也可以将保留金额设置为0；对未自助转出的，我司会自动将资金转出到您原三方存管银行。资金转出后的第二个交易日方可发起三方存管银行变更；
            </p>
            <p>3、线上仅支持异行换卡，同行换卡请到银行柜台办理。</p>
          </div>
        </div>
        <div v-if="status === 'noThirdpart'" class="no_data_content">
          <img src="@/assets/images/no_data.png" class="no_data_icon" />
          <div class="tips_bottom">
            您的账户目前无法进行资金存取，请尽快开通三方存管账户，如有疑问，请联系在线客服或拨打热线：<span
              class="phone_num"
              >95310</span
            >
          </div>
        </div>
      </article>
      <footer class="footer" v-if="status === 'noThirdpart'">
        <div class="ce_btn">
          <a class="p_button" @click="toNext(status)">马上开通</a>
        </div>
      </footer>
    </template>
    <div
      v-else-if="creditFundAccountExist === '0'"
      style="height: 100vh; background: #ffffff; text-align: center"
    >
      <div class="acct_nodata">
        <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
        <h5>
          您当前暂未开通信用账户，请前往开通信用账户，如有疑问可咨询客服95310。
        </h5>
      </div>
      <div class="bus_txtbox">
        <p>
          如何开通信用账户?
          <a class="link_right_arrow" @click="jumpBusiness('010174')"
            >前往开通</a
          >
        </p>
      </div>
    </div>
    <div
      v-else-if="creditBankAccountExist === '0'"
      style="height: 100vh; background: #ffffff; text-align: center"
    >
      <div class="acct_nodata">
        <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
        <h5>
          您当前暂未开通信用三方存管，请先开通信用三方存管后再进行业务办理，如有疑问可咨询客服95310。
        </h5>
      </div>
      <div class="bus_txtbox">
        <p>
          <a class="link_right_arrow" @click="jumpBusiness('010294')"
            >前往开通信用三方存管</a
          >
        </p>
      </div>
    </div>
    <div v-else style="height: 100vh; background: #ffffff; text-align: center">
      <div class="acct_nodata">
        <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
        <h5>
          您当前信用账户异常，暂无法进行业务办理，如有疑问可咨询客服95310。
        </h5>
      </div>
    </div>
  </section>
</template>
<script>
import {
  creditIndexquery,
  getBank,
  queryCreditFundAccount,
  businessStrategyCheck
} from '@/service/service';
import { exitApp, getOpStation } from '@/common/util';
import bankCard from '../../components/bankCard.vue';
import { creditAccountAndBankQueryV2 } from '@/service/lrService';

export default {
  components: {
    bankCard
  },
  data() {
    return {
      fundAccount: '',
      creditFundAccount: '',
      status: '',
      creditTPwdSameFlag: '1',
      thirdpartList: [],
      isLoading: true,
      creditFundAccountExist: '', //信用资金账户是否存在：1 存在；0 不存在
      creditFundAccountStatus: '', //信用资金账户状态：1 正常 0 异常
      creditBankAccountExist: '' //信用存管账户是否存在：1 存在；0 不存在
    };
  },
  computed: {
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo;
    }
  },
  watch: {
    ssoLoginFlag: {
      handler: function (userInfo) {
        console.log('userInfo', userInfo);
        if (userInfo) {
          this.initCheck();
        }
      },
      immediate: true
    }
  },
  created() {
    $h.setSession('introduceNeedBack', true);
    window.viewShowCallBack = this.viewShowCallBack;
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  mounted() {},
  methods: {
    initCheck() {
      businessStrategyCheck({
        strategyNo: 'wt_xysfcgbg_init_check',
        bizType: '010259'
      })
        .then((res) => {
          if (res.data.strategyResult === '1') {
            this.xyOpenCheck();
          } else {
            let tip = JSON.parse(res.data.result[0].ruleResultDesc);
            this.$TAlert({
              title: tip.title,
              tips: tip.tips,
              confirm: () => {
                this.back();
              }
            });
          }
        })
        .catch((e) => {
          this.$TAlert({
            tips: e
          });
        });
    },
    xyOpenCheck() {
      creditAccountAndBankQueryV2({})
        .then(({ data = {}, code, msg }) => {
          if (code === 0) {
            const {
              creditFundAccountExist = '0', //信用资金账户是否存在：1 存在；0 不存在
              creditFundAccountStatus = '0', //信用资金账户状态：1 正常 0 异常
              // creditBankAccountExist = '0' //信用存管账户是否存在：1 存在；0 不存在
            } = data;
            const checkArray = [
              creditFundAccountExist,
              creditFundAccountStatus,
              // creditBankAccountExist
            ].some((a) => a !== '1');
            if (checkArray) {
              this.creditFundAccountExist = creditFundAccountExist;
              this.creditFundAccountStatus = creditFundAccountStatus;
              this.creditBankAccountExist = '1';
              this.isLoading = false;
            } else {
              this.renderingView();
            }
          } else {
            return Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },
    toBizType(bizType) {
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType, initJumpMode: '0' });
      });
    },
    jumpBusiness(bizType = '') {
      if (bizType === '') throw new Error('bizType 不能为空');
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, {
          bizType,
          initJumpMode: '0'
        });
      });
    },
    back() {
      $h.clearSession('introduceNeedBack');
      if ($hvue.platform === '0') {
        this.$router.back();
      } else {
        exitApp();
      }
    },
    viewShowCallBack() {
      this.initCheck();
      // this.renderingView();
    },
    renderingView() {
      queryCreditFundAccount()
        .then((res) => {
          this.isLoading = false;
          if (res.code == 0) {
            if (res.data?.fundAccount) {
              this.creditFundAccount = res.data?.fundAccount;
              this.creditTPwdSameFlag = res.data?.specialFlag || '1';
              this.getBankList();
            }
          } else {
            this.$TAlert({
              title: '温馨提示',
              tips: res.msg
            });
          }
        })
        .catch((err) => {
          this.isLoading = false;
          this.$TAlert({
            title: '温馨提示',
            tips: err
          });
        });
    },
    getBankList() {
      getBank().then((res) => {
        const { code, data } = res;
        if (code == 0) {
          const { clientId, fundAccount, branchNo } =
            this.$store.state.user?.userInfo;
          this.fundAccount = fundAccount;
          let opStation = getOpStation();
          creditIndexquery({
            clientId: clientId,
            fundAccount: fundAccount,
            opStation: opStation,
            opEntrustWay: '7',
            branchNo: branchNo
          })
            .then((res) => {
              if (res.code == 0) {
                this.status = 'thirdpartList';
                this.thirdpartList = res.data.diList.map((item) => {
                  data.forEach((element) => {
                    if (element.bankNo === item.bankNo) {
                      item.bankLogo = element.bankLogo;
                    }
                  });
                  return item;
                });
                let mainAccountItem = this.thirdpartList.filter((item) => {
                  if (item.mainFlag == '1') {
                    return item;
                  }
                });
                console.log('mainAccountItem', mainAccountItem);
                if (!mainAccountItem[0]?.bankNo) {
                  // 主资金账号无三方存管场景
                  this.status = 'noThirdpart';
                }
              }
            })
            .catch((err) => {
              // 客户当前三方存管状态异常
              this.$TAlert({
                tips: '您的三方存管账户状态异常，请联系客服95310',
                confirm: () => {
                  this.back();
                }
              });
            });
        }
      });
    },
    toNext(type) {
      if (type === 'noThirdpart') {
        console.log('走新开流程');
        $h.setSession('bizType', '010294');
        import('@/common/flowMixinV2.js').then((a) => {
          a.initFlow.call(this, {
            bizType: '010294',
            flowNo: '0-010294',
            isReset: 0,
            contextParam: JSON.stringify({
              extInitParams: {
                bindStatus: 1
              },
              creditTPwdSameFlag: this.creditTPwdSameFlag,
              creditFundAccount: this.creditFundAccount
            }),
            initJumpMode: '0'
          });
        });
      }
    },
    bindBank({ bankLogo, ...bankItem }) {
      console.log('绑定银行卡======', bankItem);
      $h.setSession('bizType', '010294');
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, {
          bizType: '010294',
          flowNo: '0-010294',
          isReset: 0,
          contextParam: JSON.stringify({
            extInitParams: {
              oldBankInfo: bankItem,
              bindStatus: 3
            },
            creditTPwdSameFlag: this.creditTPwdSameFlag,
            creditFundAccount: this.creditFundAccount
          }),
          initJumpMode: '0'
        });
      });
    },
    showGuide() {
      this.$router.push({
        name: 'creditAccountGuide'
      });
    },
    changeBank({ bankLogo, ...bankItem }) {
      console.log('变更银行卡=====', bankItem);
      $h.setSession('bizType', '010259');
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, {
          bizType: '010259',
          flowNo: '0-010259',
          isReset: 0,
          contextParam: JSON.stringify({
            extInitParams: {
              oldBankInfo: bankItem,
              bindStatus: 2
            },
            bankNo: bankItem.bankNo,
            creditTPwdSameFlag: this.creditTPwdSameFlag,
            creditFundAccount: this.creditFundAccount
          }),
          initJumpMode: '0'
        });
      });
    }
  }
};
</script>
<style scoped lang="less">
.account_top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  padding: 0 16px;
  background-color: #fff;
}
.thirdpart {
  padding: 16px 16px 0;
  background: var(--bg-line-backgroud-f-5, #f2f4f8);
  min-height: 100vh;
  .warm_tips {
    color: var(--typography-666, #55555e);
    font-family: PingFang SC;
    font-size: 14px;
  }
}
.tips_top {
  color: var(--typography-333, #0f0f1b);
  text-align: center;
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  margin-bottom: 20px;
}
.tips_bottom {
  color: var(--typography-666, #55555e);
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
}
.phone_num {
  color: var(--fixed-link, #338aff);
}
.footer {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
}

.no_data_content {
  text-align: center;
  color: var(--typography-333, #333);
  font-size: 0.16rem;
  line-height: 0.24rem;
  padding: 0 0.1rem;
  .no_data_icon {
    width: 1.8rem;
    height: 1.54rem;
    margin-top: 0.5rem;
    margin-bottom: 0.2rem;
  }
}
.acct_nodata h5 {
  text-align: left;
  font-size: 0.15rem;
  color: #333333;
}
</style>
