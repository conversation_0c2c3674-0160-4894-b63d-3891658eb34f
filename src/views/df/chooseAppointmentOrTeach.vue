<template>
  <section class="main fixed white_bg" data-page="home">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="com_title">
        <h5>您需完成以下两项操作，方可预约开户</h5>
      </div>
      <ul class="yy_must_list">
        <li :class="dataCompleteFlag ? 'ok' : ''" @click="toAppointment">
          <div class="icon"><img src="@/assets/images/yy_nav_01.png" /></div>
          <div class="cont">
            <h5>填写开户信息</h5>
            <p>{{ time }}</p>
          </div>
        </li>
        <!-- 不存在过期 -->
        <li
          v-if="!hasExpire"
          :class="videoCompleteFlag ? 'ok' : ''"
          @click="toVideoWatch"
        >
          <div class="icon"><img src="@/assets/images/yy_nav_02.png" /></div>
          <div class="cont">
            <h5>观看投教视频</h5>
            <p>可观看时间：7*24小时</p>
          </div>
        </li>
        <!-- 存在过期视频 -->
        <li v-if="hasExpire" class="error" @click="toVideoWatch">
          <div class="icon"><img src="@/assets/images/yy_nav_02.png" /></div>
          <div class="cont">
            <h5>观看投教视频</h5>
            <p>可观看时间：7*24小时</p>
            <p class="tips">存在观看失效的视频，需重新观看</p>
          </div>
        </li>
      </ul>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" :class="{ disabled: isComplete }" @click="toNext"
          >去预约</a
        >
        <!-- <a class="p_button" @click="toNext">去预约</a> -->
      </div>
    </footer>
  </section>
</template>

<script>
import { exitApp } from '@/common/util';
import {
  dfFlowQry,
  videoWatchList,
  flowInsDetail,
  businessStrategyCheck,
  getClientArchiveFileExt,
  dfFlowDataQry,
  acceptancFormListQuery
} from '@/service/service';

export default {
  data() {
    return {
      dataCompleteFlag: false,
      videoCompleteFlag: false,
      clientPhoto: '',
      idCardPortrait: '',
      time: '',
      hasExpire: false,
      isOutOfDate: -1
    };
  },
  computed: {
    isComplete() {
      return !(
        this.dataCompleteFlag &&
        this.videoCompleteFlag &&
        !this.hasExpire
      );
    }
  },
  mounted() {
    this.init();
  },
  methods: {
    back() {
      this.$router.back();
    },

    async init() {
      const { clientId } = this.$store.state.user.userInfo;
      await businessStrategyCheck({
        strategyNo: 'df_business_handle_time_check'
      }).then((res) => {
        this.time = JSON.parse(res.data.result[0].ruleResultDesc).subTitle;
      });
      await videoWatchList({ bizType: '010174', clientId }).then((res) => {
        // if (res.data.recordList.length === 0) {
        //   this.hasExpire = false;
        //   return;
        // }
        // let expireArr = res.data.recordList.filter(
        //   (item) => item.isExpire === '1' && item.isComplete === '1'
        // );
        // if (expireArr.length > 0) {
        //   this.hasExpire = true;
        // }
        // let lateTimeArr = res.data.recordList.map((item) => {
        //   return item.expireTime ? item.expireTime.split('天')[0] : '';
        // });
        // // todo 获取过期最小时间
        // this.isOutOfDate = Math.min.apply(Math, lateTimeArr);
        this.hasExpire = res.data.hasExpire === '0' ? false : true;
      });
      acceptancFormListQuery().then((data) => {
        dfFlowQry({
          clientId
        }).then((res) => {
          this.dataCompleteFlag = res.data.dataCompleteFlag
            ? res.data.dataCompleteFlag === '0'
              ? false
              : true
            : false;
          this.videoCompleteFlag = res.data.videoCompleteFlag
            ? res.data.videoCompleteFlag === '0'
              ? false
              : true
            : false;
          if (res.data.flowCompleteFlag === '1') {
            this.$router.replace({ name: 'preResult' });
          }
          // if (res.data.flowToken) {
          //   flowInsDetail({ flowToken: res.data.flowToken }).then((res) => {
          //     let passNode = res.data.passedNodes.split('|');

          //   });
          // }
        });
      });
    },

    toAppointment() {
      let isAutoJumpStartForEnd = '0';
      if (this.dataCompleteFlag) {
        isAutoJumpStartForEnd = '1';
        this.$TAlert({
          title: '温馨提示',
          tips: '您的开户信息已填写完成，是否需要修改',
          hasCancel: true,
          confirmBtn: '确定',
          confirm: () => {
            import('@/common/flowMixin.js').then((a) => {
              a.initFlow.call(this, '010174', '100017-3051', '1');
            });
          }
        });
      } else {
        import('@/common/flowMixin.js').then((a) => {
          a.initFlow.call(this, '010174', '100017-3051', '0');
        });
      }
    },

    async toVideoWatch() {
      const { clientId } = this.$store.state.user.userInfo;
      // 跳转到活体识别
      // await getClientArchiveFileExt().then((res) => {
      //   this.clientPhoto = res.data.imageUrl;
      // });
      await dfFlowDataQry({
        clientId,
        searchKey: 'idCardPortrait'
      }).then((res) => {
        this.idCardPortrait = res.data.searchValue ? res.data.searchValue : '';
      });
      console.log(this.idCardPortrait);
      if (!this.clientPhoto) {
        if (!this.idCardPortrait) {
          this.$TAlert({
            tips: '请先填写开户信息，再观看投教视频',
            confirm: () => {}
          });
          return;
        }
      }
      this.$router.push({
        name: 'faceRecognition',
        query: {
          faceImage: this.idCardPortrait
            ? this.idCardPortrait
            : this.clientPhoto
        }
      });
    },

    toNext() {
      if (this.isComplete) {
        return;
      }
      // 下一步，选择营业部
      this.$router.push({ name: 'selectBusinessDepartment' });
    }
  }
};
</script>
