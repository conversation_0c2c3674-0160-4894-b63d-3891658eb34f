<template>
  <section class="main fixed white_bg" data-page="home" style="position: fixed">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="result_page">
        <div class="result_tips">
          <div v-if="preSuccess" class="icon ok"></div>
          <h5 v-if="preSuccess">恭喜您预约成功</h5>
          <h5 v-if="!preSuccess">预约失败</h5>
        </div>
        <div class="result_info">
          <ul>
            <li>
              <span class="tit">营业部</span>
              <p>
                {{ branchName }}
                <!-- <i class="icon_local"></i> -->
              </p>
            </li>
            <li>
              <span class="tit">预约日期</span>
              <p>{{ preTime }}</p>
            </li>
          </ul>
        </div>
      </div>
      <div class="tips_box">
        <p>温馨提示:</p>
        <p>1、请您携带本人的身份证、手机、银行卡，于约定时间前往指定营业部。</p>
        <p>2、抵达营业部后，请在工作人员指引下进行办理。</p>
        <!-- <p>3、如需变更预约信息，请点击下方"修改预约信息"进行变更。</p> -->
      </div>
      <div class="ce_btn black mt30">
        <!-- <a v-if="isOutOfDate" class="p_button" @click="resetToDo">重新办理</a> -->
        <!-- <a class="p_button" @click="changePreInfo">修改预约信息</a> -->
        <a class="p_button border" @click="backToIndex">返回首页</a>
      </div>
    </article>
  </section>
</template>

<script>
import {
  BANDLE_STATE,
  PROCESS_STATUS,
  TASK_TYPE,
  TASK_STATUS
} from '@/common/enumeration';
import {
  flowSubmit,
  flowQueryIns,
  flowEnd,
  videoWatchList,
  dfFlowQry,
  dfFlowSubmit,
  dfFlowSave,
  acceptancFormListQuery,
  dfFlowReprocess
} from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import { exitApp } from '@/common/util';

export default {
  name: 'PreResult',
  // inject: ['eventMessage', 'tkFlowInfo'],
  data() {
    return {
      overDay: -1,
      branchNo: '',
      branchName: '',
      preTime: '',
      hasExpire: false,
      preSuccess: ''
    };
  },
  computed: {
    isOutOfDate() {
      // 过期;
      if (this.hasExpire) {
        return true;
      }
      if (this.overDay >= 0) {
        return false;
      } else {
        return true;
      }
    }
  },
  mounted() {
    this.renderingView();
  },
  methods: {
    back() {
      exitApp();
      // this.$router.back();
    },

    renderingView() {
      const { clientId } = this.$store.state.user.userInfo;
      videoWatchList({ bizType: '010174', clientId }).then((res) => {
        if (res.data.recordList.length === 0) {
          this.hasExpire = false;
          return;
        }
        let expireArr = res.data.recordList.filter(
          (item) => item.isExpire === '1' && item.isComplete === '1'
        );
        if (expireArr.length > 0) {
          this.hasExpire = true;
        }
        let lateTimeArr = res.data.recordList.map((item) => {
          return item.expireTime ? item.expireTime.split('天')[0] : '';
        });
        // todo 获取过期最小时间
        this.overDay = Math.min.apply(Math, lateTimeArr);
      });

      acceptancFormListQuery()
        .then((res) => {
          dfFlowQry({
            clientId
          }).then((data) => {
            this.branchName = data.data.preBranchName;
            this.preTime = data.data.preTime;
            if (res.data.isAccepted === '1') {
              this.preSuccess = true;
              return;
            }
            if (data) {
              dfFlowSubmit({
                clientId
                // flowToken: data.data.flowToken
              })
                .then(() => {
                  this.preSuccess = true;
                  // dfFlowSave({
                  //   clientId,
                  //   flowCompleteFlag: '1'
                  // });
                })
                .catch((err) => {
                  this.preSuccess = false;
                  _hvueToast({
                    mes: err
                  });
                });
            }
          });
        })
        .catch();

      // const { inProperty } = this.tkFlowInfo();
      // this.branchNo = inProperty.preBranchNo;
      // this.branchName = inProperty.preBranchName;
      // this.preTime = inProperty.preTime;
      // const flowToken = sessionStorage.getItem('TKFlowToken');
      // const accArr = [PROCESS_STATUS.ACCEPT_COMPLETED]; // 配置需要提交受理结果的状态
      // flowQueryIns({ flowToken }).then((res) => {
      //   if (accArr.includes(res.data.status)) {
      //     flowSubmit({ flowToken }, { loading: false }).then((data) => {
      //       // this.dataResult = data.data[0];
      //       // this.nowDate = new Date().format('yyyy-MM-dd hh:mm:ss');
      //     });
      //   } else {
      //     this.eventMessage(this, EVENT_NAME.NEXT_STEP);
      //     flowSubmit({ flowToken }, { loading: false }).then((data) => {
      //       // this.dataResult = data.data[0];
      //       // this.nowDate = new Date().format('yyyy-MM-dd hh:mm:ss');
      //     });
      //   }
      // });
    },

    resetToDo() {
      const { clientId } = this.$store.state.user.userInfo;
      dfFlowReprocess({
        clientId
      }).then((res) => {
        this.$router.replace({ name: 'chooseAppointmentOrTeach' });
      });
    },

    changePreInfo() {},

    backToIndex() {
      exitApp();
    }
  }
};
</script>
