<template>
  <section class="main fixed" data-page="home" style="position: fixed">
    <t-header @back="back"></t-header>
    <article class="content">
      <h5 class="com_title">请选择预约时间</h5>
      <div class="com_box">
        <div class="input_form">
          <div class="input_text text">
            <!-- @click="showOpenSelect = !showOpenSelect" -->
            <span class="tit">营业部</span>
            <input
              class="t1"
              type="textarea"
              readonly="readonly"
              placeholder="请选择营业部"
              :value="branchName"
            />
            <!-- <a class="icon_location" href="javascript:void(0);">附近</a> -->
            <!-- <div class="yyb_info_text">
              <p>
                <i class="addr"></i
                >江西省南昌市西湖区广场南路205号恒茂国际华城16栋A座501室
              </p>
              <p><i class="tel"></i>(0755）955337</p>
            </div> -->
          </div>
        </div>
      </div>
      <div class="com_box mt10">
        <div class="input_form">
          <div class="input_text text">
            <span class="tit">预约日期</span>
            <div
              class="dropdown"
              placeholder="请选择预约时间"
              @click="showPicker = true"
            >
              <span v-if="advanceTime">{{ advanceTime }}</span>
            </div>
            <div class="imp_c_tips">
              <p>
                <span class="imp"
                  >请于预约时间至开户营业部办理，如有疑问，请联系营业部</span
                >
              </p>
            </div>
            <!-- <h-picker
                slot="right"
                v-model="advanceTime"
                :columns="columns"
                separator=" "
              /> -->
          </div>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" :class="{ disabled: isCheck }" @click="toNext"
          >下一步</a
        >
      </div>
    </footer>

    <van-popup v-model="showPicker" round position="bottom">
      <van-picker
        show-toolbar
        :columns="columns"
        @cancel="showPicker = false"
        @confirm="onConfirm"
      />
    </van-popup>

    <openDressSelect
      v-model="showOpenSelect"
      :default="branchInfo"
      :is-open-dress="true"
      @querySuccess="querySuccess"
      @selCallBack="openCallBack"
    ></openDressSelect>
  </section>
</template>

<script>
import {
  dfBranchInfoQry,
  getSysBranchInfo,
  videoWatchList,
  dfFlowQry,
  dfFlowSave
} from '@/service/service';
import openDressSelect from '@/components/openDressSelect';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'SelectBusinessDepartment',
  components: {
    openDressSelect
  },
  data() {
    return {
      branchName: '',
      branchNo: '',
      advanceTime: '',
      showPicker: false,
      showOpenSelect: false,
      branchInfo: {},
      columns: [
        [
          { label: '', id: '' },
          { label: '', id: '' },
          { label: '', id: '' }
        ],
        [
          { label: '', id: '12:00' },
          { label: '', id: '17:00' }
        ]
      ]
    };
  },
  computed: {
    isCheck() {
      return !this.advanceTime || !this.branchName;
    }
  },
  mounted() {
    // TODO 根据ip获取附近营业部
    // getSysBranchInfo({ branchNo }).then((res) => {
    //   this.branchInfo = res.data[0];
    // });
    this.getDateWeek();
  },
  methods: {
    back() {
      this.$router.back();
    },

    toNext() {
      if (this.isCheck) return;
      const { clientId } = this.$store.state.user.userInfo;
      dfFlowSave({
        clientId,
        preTime: this.advanceTime,
        preBranchNo: this.branchNo,
        preBranchName: this.branchName
      }).then((res) => {
        this.$router.push({ name: 'preResult' });
      });
    },

    openCallBack(item) {
      this.branchName = item.branchName;
      this.branchNo = item.branchNo;
    },

    onConfirm(val) {
      this.advanceTime = val[0];
      this.showPicker = false;
    },

    querySuccess(list) {
      let userInfo = list.filter((item) => item.isBelong === '1')[0];
      console.log(userInfo);
      if (userInfo) {
        this.branchName = this.branchName
          ? this.branchName
          : userInfo.branchName;
        this.branchNo = this.branchNo ? this.branchNo : userInfo.branchNo;
      }
    },

    /* 获取日期和周 */
    getDateWeek() {
      const { clientId } = this.$store.state.user.userInfo;
      videoWatchList({ bizType: '010174', clientId }).then((res) => {
        // if (res.data.recordList.length === 0) {
        //   this.hasExpire = false;
        //   return;
        // }
        // let expireArr = res.data.recordList.filter(
        //   (item) => item.isExpire === '1' && item.isComplete === '1'
        // );
        // if (expireArr.length > 0) {
        //   this.hasExpire = true;
        // }
        this.hasExpire = res.data.hasExpire === '0' ? false : true;
        // let lateTimeArr = res.data.recordList.map((item) => {
        //   return item.expireTime ? item.expireTime.split('天')[0] : '';
        // });
        // // todo 获取过期最小时间
        // let overDay = Math.min.apply(Math, lateTimeArr);
        // let maxDay = res.data.recordList[0].expireDateTime.split(' ')[0];

        // /* 得到当前日期的时间戳 */
        // const timestamp = Date.now();
        // // overDay
        // let dateWeek = Array.from(new Array(2)).map((_, i) => {
        //   /* 得到当前周每一天的时间戳 */
        //   const weekTimestamp = new Date(timestamp + i * 24 * 60 * 60 * 1000);
        //   const date =
        //     String(weekTimestamp.getFullYear()) +
        //     '-' +
        //     String(weekTimestamp.getMonth() + 1).padStart(2, '0') +
        //     '-' +
        //     String(new Date(weekTimestamp).getDate()).padStart(2, '0');
        //   let week = weekTimestamp.getDay();
        //   if (
        //     parseInt(date.replace(/-/g, '')) >
        //     parseInt(maxDay.replace(/-/g, ''))
        //   ) {
        //     return '';
        //   } else {
        //     return {
        //       label: date,
        //       week,
        //       id: date
        //     };
        //   }
        // });
        let dateWeek = res.data.sysTradeDateList;
        // dateWeek = dateWeek.filter((item) => item !== '');
        // dateWeek = dateWeek.filter((item) => item.week != 0 && item.week != 6);
        this.columns = [
          // dateWeek,
          {
            values: dateWeek.map((item) => item)
          }
          // {
          //   values: [
          //     '09:00~12:00',
          //     '13:00~17:00'
          //     // { label: '09:00~12:00', id: '09:00~12:00' },
          //     // { label: '13:00~17:00', id: '13:00~17:00' }
          //   ]
          // }
        ];
        const { clientId } = this.$store.state.user.userInfo;
        dfFlowQry({
          clientId
        }).then((data) => {});
      });
    }
  }
};
</script>
