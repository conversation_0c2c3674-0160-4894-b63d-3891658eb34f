<template>
  <section class="main fixed" data-page="home">
    <div class="header">
      <div class="header_inner">
        <a class="icon_back" @click.stop="back" />
        <h1 class="title">产品购买双录</h1>
        <div @click="goToHistoryDoubleRecord" class="header_right">历史记录</div>
      </div>
    </div>
    <div class="padding_content" v-show="model === 'doubleRecordListModel'">
      <div class="search" @click="changeModel('searchModel')"  v-show="model === 'doubleRecordListModel'">
        <div class="search_left">
          <img src="@/assets/images/search_icon.png" />
          <span class="search_text">请输入您想要查找的双录产品/产品代码</span>
        </div>
        <div class="search_right">搜索</div>
      </div>
      <article class="content" v-if="doubleRecordList.length > 0">
        <div class="title_tip">当前开放以下类型的产品购买双录</div>
        <div v-for="(item, idx) in doubleRecordList" :key="idx">
          <doubleRecordItem
            :doubleRecordItem="item"
            doubleRecordType="doubleRecordListModel"
          />
        </div>
        <div class="bottom_tip">
          <div>投资小贴士：</div>
          <div>
            根据监管要求，购买私募、资管、信托等特殊理财产品时，需要进行产品购买双录。
          </div>
        </div>
      </article>
      <article class="no_data_content" v-else>
        <img src="@/assets/images/result_none.png" class="no_data_icon" />
        <div class="none">未查询到理财产品双录</div>
      </article>
    </div>
    <doubleRecordSearch
      v-show="model === 'searchModel'"
      :changeModel="changeModel"
      doubleRecordType="doubleRecordListModel"
      :renderList="doubleRecordList"
    />
  </section>
</template>

<script>
import doubleRecordItem from '@/components/doubleRecordItem';
import doubleRecordSearch from '@/components/doubleRecordSearch';
import { doublerecordQuerylist } from '@/service/service';
import { exitApp } from '@/common/util';

export default {
  components: {
    doubleRecordItem,
    doubleRecordSearch
  },
  data() {
    return {
      model: 'doubleRecordListModel',
      doubleRecordList: []
    };
  },
  computed: {
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo?.fundAccount;
    }
  },
  watch: {
    ssoLoginFlag: {
      handler: function (fundAccount) {
        console.log(fundAccount);
        if (fundAccount) {
          this.pageInit();
        }
      },
      immediate: true
    }
  },
  created() {
    $h.setSession('showRejectResult', null);
    window.viewShowCallBack = this.viewShowCallBack;
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  methods: {
    back() {
      if ($hvue.platform === '0') {
        this.$router.back();
      } else {
        exitApp();
      }
    },
    changeModel(str) {
      this.model = str;
    },
    goToHistoryDoubleRecord() {
      this.$router.replace({ name: 'doubleRecordHistory'});
    },
    viewShowCallBack() {
      this.pageInit();
    },
    pageInit(){
      let params = {};
      let doubleRecordId = this.$route.params.id
      if(doubleRecordId){
        // 直接从连接进入双录流程
        params.doubleRecordId = doubleRecordId
        doublerecordQuerylist(params).then((res) => {
          console.log('=======================', res);
          let isReset = 1
          let flowInsId =''
          if(['2','5','7'].includes(res.data.doublerecordList[0].dualVideoState)){
            isReset = 0
            flowInsId = res.data.doublerecordList[0].flowInsId
          }
          let contextParam = JSON.stringify({
            doubleRecordId: doubleRecordId,
            doubleRecordName: res.data.doublerecordList[0].dualVideoProductName,
            doubleRecordRiskLevel: res.data.doublerecordList[0].doubleRecordRiskLevel,
            prodList: res.data.doublerecordList[0].prodList
          });
          $h.setSession('bizType', '010170');
          import('@/common/flowMixinV2.js').then((a) => {
            // a.initFlow.call(this, '010170', '0-010170', isReset, contextParam, flowInsId);
          a.initFlow.call(this, {bizType: '010170', flowNo: '0-010170', isReset: '1', contextParam, flowInsId, initJumpMode: '1'});
          });
        });
      }else{
        doublerecordQuerylist(params).then((res) => {
          console.log('=======================', res);
          if (res.code == '0' && res.data.doublerecordList) {
            this.doubleRecordList = res.data.doublerecordList;
          }
        });
      }
    }
  }
};
</script>

<style scoped lang="less">
.header_right {
  margin-right: 0.12rem;
}
.title_tip {
  color: var(--typography-333, #333);
  font-size: 0.14rem;
  font-weight: 500;
  line-height: 0.18rem;
  padding: 0.15rem 0.16rem;
  background: var(--bg-line-backgroud-f-5, #f4f4f4);
}
.main {
  background: var(--bg-line-backgroud-f-5, #f4f4f4);
}
.header_inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .icon_back {
    position: static;
  }
}
.search {
  background: #fff;
  padding: 0.06rem 0.16rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.14rem;
  line-height: 0.16rem;
  white-space: nowrap;
  .search_left {
    background: var(--bg-line-backgroud-f-5, #f7f8fa);
    padding: 0.1rem 0 0.1rem 0.16rem;
    border-radius: 0.47rem;
    margin-right: 0.1rem;
    flex: 1;
    img {
      margin-right: 0.08rem;
      width: 0.16rem;
      height: 0.16rem;
    }
    .search_text {
      color: var(--typography-bbb, #ccc);
    }
  }
  .search_right {
    color: var(--primary-b-500, #ff2840);
    font-weight: 500;
  }
}
.bottom_tip {
  padding: 0.06rem 0.16rem;
  color: var(--typography-666, #666);
  font-size: 0.14rem;
  line-height: 0.2rem;
}
.no_data_content {
  text-align: center;
  color: var(--typography-333, #333);
  font-size: 0.16rem;
  line-height: 0.24rem;
  .no_data_icon {
    width: 1.8rem;
    height: 1.54rem;
    margin-top: 0.5rem;
    margin-bottom: 0.2rem;
  }
}
</style>
