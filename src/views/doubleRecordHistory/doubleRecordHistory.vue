<template>
  <section class="main fixed" data-page="home">
    <div class="header">
      <div class="header_inner">
        <a class="icon_back" @click.stop="back" />
        <h1 class="title">产品购买双录</h1>
      </div>
    </div>
    <div  class="padding_content" v-show="model === 'doubleRecordHistoryListModel'">
      <div class="search" @click="changeModel('searchModel')"  v-show="model === 'doubleRecordHistoryListModel'">
        <div class="search_left">
          <img src="@/assets/images/search_icon.png" />
          <span class="search_text">请输入您想要查找的双录产品/产品代码</span>
        </div>
        <div class="search_right">搜索</div>
      </div>
      <article class="content"  v-if="historysuccessDoublerecordlist.length > 0">
        <div class="title_tip">当前开放以下类型的产品购买双录</div>
        <div v-for="(item, idx) in historysuccessDoublerecordlist" :key="idx">
          <doubleRecordItem
            :doubleRecordItem="item"
            doubleRecordType="doubleRecordHistoryListModel"
          />
        </div>
      </article>
      <article class="no_data_content" v-else>
        <img src="@/assets/images/result_none.png" class="no_data_icon" />
        <div class="none">未查询到理财产品双录</div>
      </article>
    </div>
    <doubleRecordSearch
      v-show="model === 'searchModel'"
      :changeModel="changeModel"
      doubleRecordType="doubleRecordHistoryListModel"
      :renderList="historysuccessDoublerecordlist"
    />
  </section>
</template>

<script>
import doubleRecordItem from '@/components/doubleRecordItem';
import doubleRecordSearch from '@/components/doubleRecordSearch';
import { historysuccessDoublerecordQuerylist } from '@/service/service';

export default {
  components: {
    doubleRecordItem,
    doubleRecordSearch
  },
  data() {
    return {
      model: 'doubleRecordHistoryListModel',
      historysuccessDoublerecordlist: []
    };
  },
  created() {
    window.viewShowCallBack = this.viewShowCallBack;
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  mounted() {
    this.pageInit()
  },
  methods: {
    back() {
      this.$router.replace({name: 'doubleRecord'});
    },
    changeModel(str) {
      this.model = str;
    },
    viewShowCallBack() {
      this.pageInit();
    },
    pageInit(){
      historysuccessDoublerecordQuerylist({}).then((res) => {
        console.log('=======================', res);
        if (res.code == '0' && res.data.result) {
          this.historysuccessDoublerecordlist = res.data.result;
        }
      });
    }
  }
};
</script>

<style scoped lang="less">
.main {
  background: var(--bg-line-backgroud-f-5, #f4f4f4);
}
.title_tip {
  color: var(--typography-333, #333);
  font-size: 0.14rem;
  font-weight: 500;
  line-height: 0.18rem;
  padding: 0.15rem 0.16rem;
  background: var(--bg-line-backgroud-f-5, #f4f4f4);
}
.search {
  background: #fff;
  padding: 0.06rem 0.16rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.14rem;
  line-height: 0.16rem;
  white-space: nowrap;
  .search_left {
    background: var(--bg-line-backgroud-f-5, #f7f8fa);
    padding: 0.1rem 0 0.1rem 0.16rem;
    border-radius: 0.47rem;
    margin-right: 0.1rem;
    flex: 1;
    img {
      margin-right: 0.08rem;
      width: 0.16rem;
      height: 0.16rem;
    }
    .search_text {
      color: var(--typography-bbb, #ccc);
    }
  }
  .search_right {
    color: var(--primary-b-500, #ff2840);
    font-weight: 500;
  }
}
.no_data_content {
  text-align: center;
  color: var(--typography-333, #333);
  font-size: 0.16rem;
  line-height: 0.24rem;
  .no_data_icon {
    width: 1.8rem;
    height: 1.54rem;
    margin-top: 0.5rem;
    margin-bottom: 0.2rem;
  }
}
</style>
