<template>
  <section
    v-show="showPage && sessingShowPage"
    class="main fixed white_bg"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <header class="header">
      <div class="header_inner">
        <!-- <a class="icon_back" @click="back"></a> -->
        <h1 class="title">结果页</h1>
      </div>
    </header>
    <article class="content">
      <div class="result_page">
        <div class="result_tips">
          <div class="icon fail"></div>
          <h5>审核未通过</h5>
          <p>提交时间：{{ acceptCompTime }}</p>
        </div>
        <div class="reject_txtinfo">
          <h5 class="title">原因</h5>
          <p v-for="(item, index) in rejectList" :key="index">
            {{ `${index + 1}. ${item.reason}` }}
          </p>
        </div>
        <div ref="signAgree" class="protocol_box box_bg">
          <h2 class="title">不符合专业投资者条件告知及确认书</h2>
          <div class="protocol_cont">
            <p>证券经营机构告知:</p>
            <p>
              尊敬的客户，根据您的财产状况、交易情况、工作经历等相关证明材料,经评估，您不具备具体为:
            </p>
            <p v-if="showZC" style="font-weight: bold; text-align: center">
              财产状况不符合专业投资者条件；
            </p>
            <p v-if="showJY" style="font-weight: bold; text-align: center">
              投资经历不符合专业投资者条件；
            </p>
            <p v-if="showSecJY" style="font-weight: bold; text-align: center">
              工作经历、职业资格等不符合专业投资者条件；
            </p>
            <p>
              据此，我公司将您划分为普通投资者，当您购买产品或服务时，我公司将根据您的风险承受能力出具匹配意见，您应当审慎评估购买产品或服务的风险，自行做出投资决定。
            </p>
            <p>投资者确认签署:</p>
            <p>
              本人/本机构确认已知晓贵公司将本人/本机构划分为普通投资者，知晓贵公司在信息告知、匹配方面对本人/本机构有特别保护，在购买产品或服务时，本人/本机构将审慎评估购买结合自身风险承受能力，自行做出投资决定。
            </p>
          </div>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn black">
        <a class="p_button" @click="close">确认并重新申请</a>
        <a class="p_button border" @click="toIndex">确认并放弃办理</a>
      </div>
    </footer>
  </section>
</template>

<script>
import {
  flowQueryIns,
  flowInsInvalid,
  addClientCritMark
} from '@/service/service';

export default {
  props: {
    rejectList: {
      type: Array,
      default: () => []
    },
    taskRemark: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showPage: false,
      sessingShowPage: false,
      acceptCompTime: ''
    };
  },
  computed: {
    showZC() {
      let arr = this.rejectList.map((item) => item.fieldname);
      if (arr.includes('金融资产证明') || arr.includes('近三年年均收入证明')) {
        return true;
      } else {
        return false;
      }
    },

    showJY() {
      let arr = this.rejectList.map((item) => item.fieldname);
      if (arr.includes('投资经历或工作经历')) {
        let onlySec =
          this.rejectList.filter(
            (item) => item.fieldname === '投资经历或工作经历'
          )[0].reason === '工作经历、职业资格等不符合专业投资者条件'
            ? true
            : false;
        if (onlySec) {
          return false;
        } else {
          return true;
        }
      } else {
        return false;
      }
    },

    showSecJY() {
      let arr = this.rejectList.map((item) => item.fieldname);
      if (arr.includes('投资经历或工作经历')) {
        let hasSec = this.rejectList
          .filter((item) => item.fieldname === '投资经历或工作经历')[0]
          .reason.includes('工作经历、职业资格等不符合专业投资者条件')
          ? true
          : false;
        if (hasSec) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    }
  },
  created() {
    if ($h.getSession('showProfRejectResult') !== null) {
      this.sessingShowPage = $h.getSession('showProfRejectResult');
    } else {
      this.sessingShowPage = false;
    }
    flowQueryIns({ flowToken: sessionStorage.getItem('TKFlowToken') }).then(
      (res) => {
        this.acceptCompTime = res.data.acceptCompTime;
      }
    );
  },
  methods: {
    show() {
      this.showPage = true;
    },

    close() {
      // console.log(this.$refs.signAgree.innerHTML);
      addClientCritMark({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        markType: '8',
        markContent: this.$refs.signAgree.innerHTML,
        confirmFlag: '1',
        auxiConfirmNo: this.taskRemark
      }).then((res) => {
        $h.setSession('showProfRejectResult', false);
        this.$emit('confirm');
        this.showPage = false;
      });
    },

    back() {
      this.$emit('back');
    },

    toIndex() {
      addClientCritMark({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        markType: '9',
        markContent: '放弃办理',
        confirmFlag: '1'
      }).then((res) => {
        flowInsInvalid({
          flowToken: sessionStorage.getItem('TKFlowToken')
        }).then((res) => {
          this.$emit('toIndex');
        });
      });
    }
  }
};
</script>

<style></style>
