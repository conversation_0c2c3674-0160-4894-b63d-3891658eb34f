<template>
  <section
    v-show="showPage && sessingShowPage"
    class="main fixed white_bg"
    data-page="home"
    style="position: fixed; z-index: 1400"
  >
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click="back"></a>
        <h1 class="title">结果页</h1>
      </div>
    </header>
    <article class="content">
      <div class="result_page">
        <div class="result_tips">
          <div class="icon fail"></div>
          <h5>审核未通过</h5>
          <p>提交时间：{{ acceptCompTime }}</p>
        </div>
        <div class="reject_txtinfo">
          <h5 class="title">原因</h5>
          <p v-for="(item, index) in rejectList" :key="index">
            {{ `${index + 1}. ${item.reason}` }}
          </p>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn black">
        <a class="p_button" @click="close">修改资料重新提交</a>
        <a v-show="showCancelBtn" class="p_button border" @click="toIndex">放弃办理</a>
      </div>
    </footer>
  </section>
</template>

<script>
import { flowQueryIns, flowInsInvalid } from '@/service/service';
export default {
  props: {
    rejectList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      showPage: false,
      sessingShowPage: false,
      acceptCompTime: ''
    };
  },
  computed:{
    showCancelBtn() {
      // 预约业务不支持放弃办理
      return !['010262', '010276', '010277'].includes($h.getSession('bizType'))
    }
  },
  created() {
    if ($h.getSession('showRejectResult') !== null) {
      this.sessingShowPage = $h.getSession('showRejectResult');
    } else {
      this.sessingShowPage = false;
    }
    flowQueryIns({ flowToken: sessionStorage.getItem('TKFlowToken') }).then(
      (res) => {
        this.acceptCompTime = res.data.acceptCompTime;
      }
    );
  },
  methods: {
    show() {
      this.showPage = true;
    },

    close() {
      $h.setSession('showRejectResult', false);
      this.$emit('confirm');
      this.showPage = false;
    },

    back() {
      this.$emit('back');
    },

    toIndex() {
      flowInsInvalid({
        flowToken: sessionStorage.getItem('TKFlowToken')
      }).then((res) => {
        this.$emit('toIndex');
      });
    }
  }
};
</script>

<style></style>
