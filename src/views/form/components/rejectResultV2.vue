<!--
 * @Author: wusm
 * @Date: 2024-10-30 14:02:27
 * @LastEditors: wusm
 * @LastEditTime: 2024-11-25 17:48:31
 * @Description:
 * @FilePath: \bc-h5-view\src\views\form\components\rejectResultV2.vue
-->
<template>
  <section v-show="showPage" class="main fixed white_bg" data-page="home" style="position: fixed; z-index: 1400">
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click="back"></a>
        <h1 class="title">结果页</h1>
      </div>
    </header>
    <article v-if="isCancelXH" class="content">
      <div class="result_page">
        <div class="result_tips">
          <div class="icon ok"></div>
          <h5>{{ title }}撤销成功</h5>
          <p>提交时间：{{ acceptCompTime }}</p>
        </div>
        <div class="result_info">
          <ul>
            <li>
              <span class="tit">{{ title }}</span>
              <p>办理成功</p>
            </li>
          </ul>
        </div>
      </div>
    </article>
    <article v-else class="content">
      <div class="result_page">
        <div class="result_tips">
          <div class="icon fail"></div>
          <h5>{{ title }}</h5>
          <p>提交时间：{{ acceptCompTime }}</p>
        </div>
        <div class="reject_txtinfo">
          <h5 class="title">原因</h5>
          <p v-for="(item, index) in rejectList" :key="index">
            {{ `${index + 1}. ${item.reason}` }}
          </p>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn black">
        <a class="p_button" @click="close">重新提交</a>
        <a class="p_button border" @click="toIndex">返回首页</a>
      </div>
    </footer>
  </section>
</template>

<script>
export default {
  props: {
    showRejectResult: {
      type: Boolean,
      default: false
    },
    isCancelXH: {
      type: Boolean,
      default: false
    },
    rejectList: {
      type: Array,
      default: () => []
    },
    acceptCompTime: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '审核不通过'
    }
  },
  data() {
    return {
      showPage: false,
    };
  },
  watch: {
    showRejectResult: {
      handler(val) {
        this.showPage = val;
      },
      immediate: true
    }
  },
  created() {
  },
  methods: {
    close() {
      this.$emit('confirm');
      this.showPage = false;
    },

    back() {
      this.$emit('back');
      this.showPage = false;
    },

    toIndex() {
      this.$emit('toIndex');
      this.showPage = false;
    }
  }
};
</script>

<style></style>
