<!--
 * @Author: chenjm
 * @Date: 2022-10-18 14:44:21
 * @LastEditors: chenjm
 * @LastEditTime: 2022-10-19 16:52:05
 * @Description: 中台预览容器
-->
<template>
  <section class="main fixed">
    <t-header :show-back="false" :title="pageConfig.title"></t-header>
    <FormilyParserH5
      ref="formParser"
      :schema="schema"
      :flow-data="flowOutputInfo"
      @onFormMount="onFormMount"
      @onFormDataInit="onFormDataInit"
      @onFieldValueChange="onFieldValueChange"
      @fieldCustomChange="fieldCustomChange"
    />
    <t-footer
      :button-txt="footerBtn.text"
      :disabled="footerBtn.status === 0"
      @triggerEvent="triggerEvent"
    ></t-footer>
  </section>
</template>

<script>
import { underlineToCamel } from '@/common/util';
import { formService } from '@/service/formService';
import { baseService } from '@/service/baseService';
import { ELEMENT_SOURCE_MODE, EVENT_NAME } from '@/common/formEnum';
export default {
  name: 'Preview',
  provide() {
    return {
      tkFlowInfo: this.getTKFlowInfo
    };
  },
  data() {
    return {
      flowOutputInfo: {},
      initData: {},
      baseService, // 提供给脚本运行使用
      pageConfig: {
        title: ''
      },
      schema: '',
      footerBtn: {
        text: '',
        isSetContext: true, //是否设置上下文
        status: 1, //0按钮不可用 1提交表单 2自定义按钮事件
        triggerEvent: null
      }
    };
  },
  mounted() {
    window.addEventListener('message', this.renderingView, false);
  },
  destroyed() {
    window.removeEventListener('message', this.renderingView, false);
  },
  methods: {
    renderingView({ data = '' }) {
      console.log('renderingView ********************************');
      console.log(data);
      if (data.constructor === Object) {
        try {
          this.flowOutputInfo = data;
          this.flowOutputInfo.stepContent = JSON.parse(
            this.flowOutputInfo.stepContent
          );
          this.pageConfig = this.flowOutputInfo.stepContent.pageConfig;
          this.schema = JSON.stringify({
            schema: { ...this.flowOutputInfo.stepContent.jsonSchema }
          });
          console.log(this.flowOutputInfo);
          this.$refs.formParser?.refresh(); // 刷新表单数据,如果表单不存在,默认不执行
        } catch (e) {
          console.error(e);
        }
      }
    },
    values(form) {
      const obj = {};
      for (const key in form.values) {
        const val = form.values[key];
        if (val.constructor === Array) {
          obj[key] = val.join('');
        } else {
          obj[key] = val;
        }
      }
      return obj;
    },
    onFormMount() {
      // this.interfaces();
      let { validation } = this.flowOutputInfo;
      if (validation?.length > 0) {
        this.footerBtn.triggerEvent = validation;
      }
    },
    /**
     * @description: 接口初始化,数据结构参考README.md  节点初始化参数
     */
    interfaces() {
      let serveList = [];
      const inProperty = this.flowOutputInfo.inProperty;
      let { interfaceConfig, validation } = this.flowOutputInfo.stepContent;
      for (let { src, inputs, method } of interfaceConfig) {
        let reqParma = {};
        for (let { name, sourceMode, sourceValue } of inputs) {
          // 入参模式 0 流程实例中获取出参(取上下文出参) 1 数据字典常量(取sourceValue字段值)
          let val = '';
          if (sourceMode === '0') {
            val = inProperty[name];
          } else {
            val = sourceValue;
          }
          reqParma[name] = val;
        }
        serveList.push(formService(src, reqParma, { method }));
      }
      let setItem = {};
      if (serveList.length > 0) {
        Promise.all(serveList).then((dataList) => {
          for (let data of dataList) {
            if (data.code === 0) {
              let results = data.data;
              setItem = this.getInitValue(results);
            }
          }
          this.setValues(setItem);
        });
      } else {
        setItem = this.getInitValue();
        this.setValues(setItem);
      }
      if (validation?.length > 0) {
        this.footerBtn.triggerEvent = validation;
      }
    },
    /**
     * @description: 获取初始化值
     * @param {Object} results
     * @return {Object} 需要设置表单的值
     */
    getInitValue(results = {}) {
      let setItem = {};
      const outProperty = this.flowOutputInfo.outProperty;
      let { elementConfig } = this.flowOutputInfo.stepContent;
      for (let {
        name,
        sourceMode,
        sourceName,
        sourceValue,
        elId,
        propConfig
      } of elementConfig) {
        //0:流程实例中上下文,1:服务接口，2：其他常量值
        if (
          sourceMode === ELEMENT_SOURCE_MODE.SERVER &&
          results[underlineToCamel(name)]
        ) {
          setItem[elId] = results[underlineToCamel(name)];
        } else if (
          sourceMode === ELEMENT_SOURCE_MODE.FLOW &&
          outProperty[underlineToCamel(sourceName)]
        ) {
          setItem[elId] = outProperty[underlineToCamel(sourceName)];
        } else if (sourceValue) {
          setItem[elId] = sourceValue;
        }
        const form = this.$refs.formParser?.form;
        //设置自定义表单属性
        for (let p of propConfig) {
          if (p.sourceMode === ELEMENT_SOURCE_MODE.SERVER) {
            form.query(elId).take().componentProps[p.propKey] =
              results[underlineToCamel(p.sourceName)];
          } else if (p.sourceMode === ELEMENT_SOURCE_MODE.FLOW) {
            form.query(elId).take().componentProps[p.sourceName] =
              outProperty[p.sourceName];
          } else {
            form.query(elId).take().componentProps[p.propKey] = p.sourceValue;
          }
        }
      }
      return setItem;
    },
    onFormDataInit(form) {
      console.log('onFormDataInit start ~');
      // 确保Object key为驼峰命名格式
      Object.keys(form.values).forEach((key) => {
        const isUnderline = key.match(/_/g); // 是否下划线
        if (isUnderline) {
          this.initData[underlineToCamel(key)] = form.values[key];
        } else {
          this.initData[key] = form.values[key];
        }
      });
    },
    onFieldValueChange(val) {
      console.log(val);
    },
    /**
     * @description: 自定义控件触发事件
     * @param {Object} eventName
     * @param {Object} eventData
     * @return {*}
     */
    fieldCustomChange({ eventName, eventData = {} }) {
      console.log('fieldCustomChange start ~');
      console.log(eventName);
      console.log(eventData);
    },
    triggerEvent() {
      const form = this.$refs.formParser?.form;
      if (form) {
        form
          .validate()
          .then(() => {
            const formValues = this.values(form);
            console.log(`submit: ${JSON.stringify(formValues)}`);
          })
          .catch((errList) => {
            console.log(errList);
          });
      } else {
        console.error('form不存在');
      }
    },
    getTKFlowInfo() {
      const { flowNodeNo, inProperty, outProperty } = this.flowOutputInfo;
      return {
        flowNodeNo,
        inProperty,
        outProperty
      };
    }
  }
};
</script>

<style lang="scss"></style>
