<!--
 * @Author: chenjm
 * @Date: 2022-10-19 16:48:02
 * @LastEditors: chenjm
 * @LastEditTime: 2022-10-19 16:52:51
 * @Description: 二级部件页面容器
-->
<template>
  <section class="main fixed">
    <t-header
      :title="targetRefPageTitle || ''"
      :show-back="headerBtn.display"
      @back="back"
    ></t-header>
    <article class="content">
      <div
        v-if="rejectList.length > 0"
        class="reject_box"
        @click="rejectListOn = !rejectListOn"
      >
        <h5 :class="{ on: rejectListOn }">
          <span>您提交的资料存在下列问题</span>
        </h5>
        <div v-show="rejectListOn" class="cont">
          <p v-for="(item, index) in rejectList" :key="index">
            {{ `${index + 1}. ${item.reason}` }}
          </p>
        </div>
      </div>
      <FormilyParserH5
        ref="formParser"
        :schema="schema"
        :flow-data="formData"
        @onFormMount="onFormMount"
        @onFormDataInit="onFormDataInit"
        @onFieldValueChange="onFieldValueChange"
        @fieldCustomChange="fieldCustomChange"
      />
    </article>
    <t-footer
      v-show="footerBtn.display"
      :button-txt="footerBtn.text"
      :disabled="footerBtn.status === 0"
      @triggerEvent="triggerEvent"
    ></t-footer>
  </section>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
import { mapGetters } from 'vuex';
import { mapState } from 'vuex';
import ChannelUtil from '@/common/ChannelUtil';

export default {
  name: 'Subpage',
  provide() {
    return {
      subpageFlowInfo: this.getSubpageFlowInfo
    };
  },
  data() {
    return {
      initData: {},
      flowOutputInfo: {},
      rejectList: [],
      rejectListOn: true, // 驳回列表信息展示
      footerBtn: {
        display: true,
        text: '',
        status: 1, //0按钮不可用 1提交表单 2自定义按钮事件
        triggerEvent: null
      },
      headerBtn: {
        display: true,
        event: () => {}
      },
      schema: ''
    };
  },
  computed: {
    ...mapState('pageContext', ['subpageData']),
    ...mapGetters('pageContext', ['formData', 'targetRefPageTitle'])
  },
  activated() {
    this.renderingView();
  },
  methods: {
    renderingView() {
      this.rejectList = this.formData?.rejectFields || [];
      try {
        const stepContent = JSON.parse(this.formData.stepContent);
        this.schema = JSON.stringify({
          schema: { ...stepContent.jsonSchema }
        });
        this.$refs.formParser?.refresh(); // 刷新表单数据,如果表单不存在,默认不执行
      } catch (e) {
        _hvueToast({ mes: e });
      }
    },
    onFormMount() {
      let { validation } = this.formData.stepContent;
      if (validation?.length > 0) {
        this.footerBtn.triggerEvent = validation;
      }
    },
    onFormDataInit() {
      console.log('onFormDataInit start ~');
    },
    triggerEvent() {
      const { status, triggerEvent } = this.footerBtn;
      if (status === 0) {
        return;
      } else if (status === 2) {
        try {
          triggerEvent();
        } catch (e) {
          _hvueToast({
            mes: e
          });
        }
        return;
      }
      const form = this.$refs.formParser?.form;
      const getFormValues = this.$refs.formParser?.getFormValues;
      let formValues = getFormValues();
      console.log(formValues);
      if (form) {
        // 判断是否存在必改项
        const formChangeValidate =
          this.$refs.formParser?.getFormChangeValidate();
        if (formChangeValidate.length > 0) {
          _hvueToast({
            mes: formChangeValidate[0].errorMessage
          });
          return;
        }

        // 表单格式校验
        form
          .validate()
          .then(() => {
            if (triggerEvent?.length > 0) {
              let a = eval(triggerEvent);
              a.call(this);
              return;
            }
            this.nextFlow(formValues);
          })
          .catch((errList) => {
            console.error(errList);
          });
      } else {
        console.error('form不存在');
      }
    },
    onFieldValueChange(val) {
      console.log(val);
    },
    /**
     * @description: 自定义控件触发事件
     * @param {Object} eventName
     * @param {Object} eventData
     * @return {*}
     */
    fieldCustomChange({ eventName, eventData = {} }) {
      console.log(eventName, '------fieldCustomChange------');
      switch (eventName) {
        case EVENT_NAME.CLOSE_REJECT:
          this.rejectList = [];
          break;
        case EVENT_NAME.NEXT_STEP:
          this.nextFlow(eventData);
          break;
        case EVENT_NAME.PREV_FLOW:
          this.back();
          break;
        case EVENT_NAME.NEXT_BTN:
          this.changeBtnState(eventData);
          break;
        case EVENT_NAME.BACK_BTN:
          this.headerBtn.display = eventData.display;
          break;
        case EVENT_NAME.INDEX_BTN:
          this.changeBtnState({
            text: eventData.text || '返回首页',
            data: () => this.toIndex()
          });
          break;
        case EVENT_NAME.TO_INDEX:
          this.toIndex();
          break;
        case EVENT_NAME.$JUMP_PAGE:
          this.toSubpage(eventData);
          break;
      }
    },
    changeBtnState({ text = '', display = true, data, btnStatus = 1 }) {
      this.footerBtn.status = btnStatus;
      this.footerBtn.display = display;
      this.footerBtn.text = text;
      if (btnStatus === 2) {
        this.footerBtn.triggerEvent = data;
      }
    },
    nextFlow(outProperty) {
      this.$store.commit('pageContext/setSubpageData', {
        ...this.subpageData,
        $formData: { ...this.subpageData.$formData, outProperty }
      });
      this.back();
    },
    back() {
      this.$router.go(-1);
    },
    toIndex() {
      console.log('--------toIndex---------');
      const appCode = $h.getSession('appCode');
      const channelUtil = new ChannelUtil({ vm: this, appCode });
      if (channelUtil.isChannel) {
        channelUtil.afterFlow();
      } else {
        window.$router.replace({
          path: '/home'
        });
      }
    },
    /**
     * @description: 跳转至二级部件
     * @arguments {
          "$compontId"  : xxx //部件id
          “$targetPage”: “risk”, //目标页面，业务负责添加
          "saveContextId": '', // 二级部件上下文标识,部件id+随机数
          “$saveContext”: “key”, // 存放的上下文，业务负责配置
          "$formData":{}//流程引擎返回的表单数据
          “$targetRefPageContent”: “大JSON”,// 关联目标页面JSON，容器负责添加
          "$targetRefPageTitle": '', //目标页面标题
          “$saveContextId”: “:xxxxxxxxxxx”,// 存放的上下文，容器负责添加
        }
     */
    toSubpage(data) {
      const saveContextId = `${data.$compontId}${Math.round(
        Math.random() * 100000
      )}`;
      this.subPageCompId = saveContextId;
      this.$store.commit('pageContext/setSubpageData', {
        ...data,
        saveContextId
      });
      let extendParam;
      if (data.extendParam) {
        extendParam = data.extendParam;
      }
      this.$router.push({
        path: `/form/subpage/${data.$targetPage}`,
        query: extendParam
      });
    },
    getSubpageFlowInfo() {
      const { inProperty, outProperty } = this.formData;
      return {
        inProperty,
        outProperty
      };
    }
  }
};
</script>
