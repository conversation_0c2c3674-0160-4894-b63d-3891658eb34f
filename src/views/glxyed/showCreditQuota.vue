<template>
  <section class="main fixed" data-page="home" style="position: fixed">
    <t-header
      :title="isGuide ? '信用账户' : '管理信用额度'"
      @back="back"
      v-if="!isLoading"
    />
    <article v-if="isLoading" class="content" />
    <article class="content" v-else-if="!isGuide">
      <ul class="com_infolist spel">
        <li>
          <span class="tit">信用资金账户</span>
          <p>{{ fundAccount }}</p>
        </li>
      </ul>
      <div class="limit_infobox">
        <p>当前信用额度(元)</p>
        <div class="num" v-if="currentCreditQuota !== '--'">
          {{ currentCreditQuota | formatMoney }}
        </div>
        <div class="num" v-else>{{ currentCreditQuota }}</div>
      </div>
      <div class="ce_btn block mt20">
        <a
          class="p_button"
          :class="errorStatus && 'disabled'"
          @click="toBiztype"
          >调整额度</a
        >
      </div>
    </article>
    <div
      v-else-if="creditFundAccountExist === '0'"
      style="height: 100vh; background: #ffffff; text-align: center"
    >
      <div class="acct_nodata">
        <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
        <h5>
          您当前暂未开通信用账户，请前往开通信用账户，如有疑问可咨询客服95310。
        </h5>
      </div>
      <div class="bus_txtbox">
        <p>
          如何开通信用账户?
          <a class="link_right_arrow" @click="jumpBusiness('010174')"
            >前往开通</a
          >
        </p>
      </div>
    </div>
    <div
      v-else-if="creditBankAccountExist === '0'"
      style="height: 100vh; background: #ffffff; text-align: center"
    >
      <div class="acct_nodata">
        <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
        <h5>
          您当前暂未开通信用三方存管，请先开通信用三方存管后再进行业务办理，如有疑问可咨询客服95310。
        </h5>
      </div>
      <div class="bus_txtbox">
        <p>
          <a class="link_right_arrow" @click="jumpBusiness('010294')"
            >前往开通信用三方存管</a
          >
        </p>
      </div>
    </div>
    <div v-else style="height: 100vh; background: #ffffff; text-align: center">
      <div class="acct_nodata">
        <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
        <h5>
          您当前信用账户异常，暂无法进行业务办理，如有疑问可咨询客服95310。
        </h5>
      </div>
    </div>
  </section>
</template>

<script>
import {
  queryCreditFundAccount,
  creditQuotaQuery,
  businessStrategyCheck
} from '@/service/service.js';
import { creditAccountAndBankQueryV2 } from '@/service/lrService';
import { exitApp } from '@/common/util';

export default {
  name: 'showCreditQuota',
  data() {
    return {
      // 加载页
      isLoading: true,
      // 是否展示引导页
      isGuide: false,
      // 信用账户
      fundAccount: '',
      // 当前信用额度
      currentCreditQuota: '',
      // 是否允许初始化流程（当前无信用额度字段返回）
      errorStatus: false,
      // 信用资金账户是否有独立密码
      creditTPwdSameFlag: '1',
      creditFundAccountExist: '', //信用资金账户是否存在：1 存在；0 不存在
      creditFundAccountStatus: '', //信用资金账户状态：1 正常 0 异常
      creditBankAccountExist: '' //信用存管账户是否存在：1 存在；0 不存在
    };
  },
  computed: {
    isApp() {
      return $hvue.platform !== '0';
    },
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo?.clientId;
    }
  },
  watch: {
    ssoLoginFlag: {
      handler: function (clientId) {
        console.log(clientId);
        if (clientId) {
          this.xyOpenCheck();
        }
      },
      immediate: true
    }
  },
  methods: {
    xyOpenCheck() {
      creditAccountAndBankQueryV2({})
        .then(({ data = {}, code, msg }) => {
          if (code === 0) {
            const {
              creditFundAccountExist = '0', //信用资金账户是否存在：1 存在；0 不存在
              creditFundAccountStatus = '0', //信用资金账户状态：1 正常 0 异常
              creditBankAccountExist = '0' //信用存管账户是否存在：1 存在；0 不存在
            } = data;
            const checkArray = [
              creditFundAccountExist,
              creditFundAccountStatus,
              creditBankAccountExist
            ].some((a) => a !== '1');
            if (checkArray) {
              this.creditFundAccountExist = creditFundAccountExist;
              this.creditFundAccountStatus = creditFundAccountStatus;
              this.creditBankAccountExist = creditBankAccountExist;
              this.isLoading = false;
              this.isGuide = true;
            } else {
              this.init();
            }
          } else {
            return Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },

    // 查询客户信用资金账号
    async init() {
      const { code, data, msg } = await this.fetchData(
        queryCreditFundAccount()
      );
      if (code != 0) return _hvueToast({ mes: msg });
      // 返回有值展示资金账户并且查询客户的当前信用额度，无值则展示引导开通信用账户
      this.isGuide = !data;
      this.fundAccount = data && data.fundAccount;
      this.creditTPwdSameFlag = (data && data.specialFlag) || '1';
      this.fundAccount && this.creditQuotaQuery(this.fundAccount);
      this.isLoading = false;
    },
    // 查询客户当前信用额度
    async creditQuotaQuery(fundAccount) {
      const { code, data, msg } = await this.fetchData(
        creditQuotaQuery({
          fundAccount
        })
      );
      if (code != 0) return _hvueToast({ mes: msg });
      if (!data.finContractQuota) {
        this.errorStatus = true;
        this.currentCreditQuota = '--';
        return;
      }
      this.errorStatus = false;
      this.currentCreditQuota = data.finContractQuota;
    },
    // 进入管理信用额度流程
    async toBiztype() {
      if (this.errorStatus) return;
      const { code, data, msg } = await this.fetchData(
        businessStrategyCheck({
          strategyNo: 'wt_glxyed_init_no_parameter_check'
        })
      );
      if (code != 0) return _hvueToast({ mes: msg });
      if (data.strategyResult !== '1') {
        let tip = JSON.parse(data.result[0].ruleResultDesc);
        return this.$TAlert({
          title: tip.title,
          tips: tip.tips,
          confirm: () => {}
        });
      }
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, {
          bizType: '010098',
          initJumpMode: '0',
          contextParam: JSON.stringify({
            creditFundAccount: this.fundAccount,
            creditTPwdSameFlag: this.creditTPwdSameFlag
          })
        });
      });
    },
    jumpBusiness(bizType = '') {
      if (bizType === '') throw new Error('bizType 不能为空');
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, {
          bizType,
          initJumpMode: '0'
        });
      });
    },
    back() {
      if (this.isApp) {
        exitApp();
      } else {
        this.$router.go(-1);
      }
    },
    showGuide() {
      this.$router.push({
        name: 'creditAccountGuide'
      });
    },
    async fetchData(promise) {
      try {
        const data = await promise;
        return data;
      } catch (error) {
        return {
          code: -1,
          msg: error
        };
      }
    }
  }
};
</script>
<style scoped>
.acct_nodata h5 {
  text-align: left;
  font-size: 0.15rem;
  color: #333333;
}
</style>
