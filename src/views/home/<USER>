<template>
  <section class="main fixed white_bg" data-page="home">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="result_page">
        <div class="result_tips">
          <div class="icon" :class="stateClass"></div>
          <h5>业务{{ statusDesc }}</h5>
          <p>提交时间：{{ time }}</p>
        </div>
        <div class="result_info" v-if="infos.length > 0">
          <ul>
            <li v-for="(item, index) in infos" :key="index">
              <span class="tit">{{ item.left }}</span>
              <p>
                <span class="state">{{ item.right }}</span>
              </p>
            </li>
          </ul>
        </div>
        <div class="reject_txtinfo" v-if="failReason">
          <h5 class="title">原因</h5>
          <p>{{ failReason }}</p>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn black">
        <a class="p_button" href="#">返回首页</a>
      </div>
    </footer>
  </section>
</template>

<script>
import { businessProcessingProgressDetailQry } from '@/service/service';

export default {
  data() {
    return {
      time: '',
      status: '',
      statusDesc: '',
      failReason: '',
      infos: []
    };
  },
  computed: {
    stateClass() {
      if (this.status === '4') {
        return 'ok';
      } else {
        return 'fail';
      }
    }
  },
  activated() {
    this.renderingView();
  },
  /**
   * 办理状态：0 受理中；1 办理中；2 已作废；3 被驳回；4 办理成功；5 办理失败；
   */
  methods: {
    back() {
      this.$router.back(-1);
    },

    renderingView() {
      businessProcessingProgressDetailQry({
        flowInsId: this.$route.query.flowInsId
      }).then((res) => {
        this.status = res.data.status;
        this.statusDesc = res.data.statusDesc;
        this.failReason = res.data.failReason;
        this.time = res.data.time;
        this.infos = res.data.data || [];
      });
    }
  }
};
</script>
