<template>
  <div>
    <div v-show="!showHome" class="bg_home">
      <t-header
        style="background: none"
        :show-title="false"
        @back="quitLogin"
      ></t-header>
    </div>
    <section v-show="showHome" class="main fixed white_bg" data-page="home">
      <t-header v-if="pageStep === 0" @back="quitLogin"></t-header>
      <article v-if="pageStep === 0" class="content">
        <div class="search_link">
          <a @click="toSearch">请输入需要办理的业务名称</a>
        </div>
        <!-- <div @click="gotoAnotherBiz">办理进度查询</div>
      <div @click="gotoAnotherStockAccountRightQry">已开通权限查询</div> -->
        <div
          v-if="businessList.hot && businessList.hot.length > 0"
          class="bus_tp_module hot"
        >
          <h3 class="bus_tp_title">
            <img src="@/assets/images/bus_tp_01.png" />热门业务
          </h3>
          <ul class="bus_hot_list">
            <li v-for="(item, index) in businessList.hot" :key="index">
              <a
                @click="
                  gotoBiz(
                    item.bizType,
                    item.flowNo,
                    item.isExistIntroduce,
                    item.bizNickName
                  )
                "
                ><img :src="fileUrl + item.bizLogoMobileImg" /><span>{{
                  item.bizNickName
                }}</span></a
              >
            </li>
          </ul>
        </div>
        <div
          v-for="(item, index) in businessList.labels"
          v-show="item.child"
          :key="index"
          class="bus_tp_module"
        >
          <h3 class="bus_tp_title">
            <!-- <img :src="fileUrl + item.labelPic" /> -->
            {{ item.labelName }}
          </h3>
          <ul class="bus_navlist">
            <li v-for="(it, idx) in item.child" :key="idx">
              <a
                @click="
                  gotoBiz(
                    it.bizType,
                    it.flowNo,
                    it.isExistIntroduce,
                    it.bizNickName,
                    it.bizUrl
                  )
                "
                ><span>{{ it.bizNickName }}</span></a
              >
            </li>
          </ul>
        </div>
      </article>

      <header v-show="pageStep === 1" class="header">
        <div class="header_inner">
          <a class="icon_back" @click="pageStep = 0"></a>
          <div class="top_searchbox">
            <i class="icon"></i>
            <input
              ref="searchInput"
              v-model="searchKey"
              class="t1"
              type="text"
              placeholder="请输入需要办理的业务名称"
              @keyup="onkeyup($event)"
              @keyup.enter="onenter"
            />
            <a class="btn" @click="search">搜索</a>
          </div>
        </div>
      </header>
      <article v-if="pageStep === 1 && searchList.length > 0" class="content">
        <div class="bus_tp_module">
          <ul class="bus_navlist">
            <li v-for="(it, idx) in searchList" :key="idx">
              <a
                @click="
                  gotoBiz(
                    it.bizType,
                    it.flowNo,
                    it.isExistIntroduce,
                    it.bizNickName,
                    it.bizUrl
                  )
                "
                ><span>{{ it.bizNickName }}</span></a
              >
            </li>
          </ul>
        </div>
      </article>
      <div @click="gotoTest">button</div>
      <!-- <footer class="footer" style="background: #ffffff">
      <div class="ce_btn">
        <a class="p_button border" @click="quitLogin">退出登录</a>
      </div>
    </footer> -->
    </section>
  </div>
</template>

<script>
import {
  queryPortalList,
  remind,
  riskQuery,
  riskMatchRight
} from '@/service/service';
import { exitApp } from '@/common/util';
import ChannelUtil from '@/common/ChannelUtil';

export default {
  data() {
    return {
      fileUrl: $hvue.customConfig.fileUrl,
      businessList: [],
      searchList: [],
      allList: [],
      hot: [],
      pageStep: 0,
      searchKey: '',
      lastTimeStamp: 0,
      showHome: false
    };
  },
  computed: {
    isApp() {
      return $hvue.platform !== '0';
    }
    // ssoLoginFlag() {
    //   return this.$store.state.user.ssoLoginFlag;
    // }
  },
  // watch: {
  //   ssoLoginFlag: {
  //     handler: function (flag) {
  //       if (flag) {
  //         this._queryPortalList();
  //         // this.getRemind();
  //         // this.getRiskInfo();
  //       }
  //     },
  //     immediate: true
  //   }
  // },
  created() {
    this.showHome = $hvue.customConfig.showHome;
    this.$store.commit('user/setSsoLoginFlag', false);
    // this._queryPortalList();
  },
  mounted() {
    $h.setSession('bizType', '');
    $h.setSession('showRejectResult', null);
  },
  methods: {
    gotoTest() {
      this.$router.push({ name: 'addShareHoldAccount' });
    },
    getRiskInfo() {
      if (!$h.getSession('homeIsRefresh')) {
        console.log('首次被加载');
        $h.setSession('homeIsRefresh', 'true');
        // 风险测评提示
        riskQuery().then(() => {
          riskMatchRight().then((res) => {
            if (
              res.data.riskMatchRightInfoList.filter(
                (item) => item.isMatch === '0'
              ).length > 0
            ) {
              this.$TAlert({
                title: '温馨提示',
                tips: '您的风险等级与您拟开通的服务不匹配，根据相关规定及证券交易委托代理协议的约定可能会限制、暂停、终止提供证券交易服务。',
                hasCancel: true,
                cancelBtn: '我知道了',
                confirmBtn: '查看详情',
                confirm: () => {
                  this.$router.push({ name: 'riskResult' });
                }
              });
            }
          });
        });
      } else if ($h.getSession('homeIsRefresh') == 'true') {
        console.log('页面被刷新');
      }
    },

    _queryPortalList() {
      console.log('queryPortalList');
      queryPortalList()
        .then((res) => {
          const appCode = $h.getSession('appCode');
          const channelUtil = new ChannelUtil({ appCode });
          let dataList = res.data;
          if (channelUtil.isChannel) {
            dataList.labels = dataList.labels.map(({ child, ...item }) => {
              return {
                ...item,
                child: child.filter(({ appType }) => appType.includes('0'))
              };
            });
          }
          this.businessList = dataList;
          if (dataList) {
            dataList.labels.forEach((item) => {
              item.child &&
                item.child.forEach((it) => {
                  this.allList.push(it);
                });
            });
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },

    quitLogin() {
      this.$store.commit('user/setUserInfo', null);
      localStorage.removeItem('vuex');
      sessionStorage.clear();
      if (this.isApp) {
        exitApp();
      } else {
        this.$router.replace({
          path: '/login'
        });
      }
    },

    getRemind() {
      remind().then((res) => {
        let { remindFlag, remindNum } = res.data;
        if (remindFlag === '1') {
          this.$TAlert({
            title: '温馨提示',
            tips: `尊敬的投资者您好，为加强适当性管理，保护投资者合法权益，邀请您参与《投资者适当性问卷回访》。感谢您的支持与信任。目前您有${remindNum}份问卷待完成。`,
            hasCancel: true,
            cancelBtn: '稍后处理',
            confirmBtn: '前往处理',
            confirm: () => {
              this.$router.push({ name: 'questionVisitList' });
            },
            cancel: () => {}
          });
        }
      });
    },

    onkeyup(event) {
      if (event.keyCode !== 13) {
        this.lastTimeStamp = event.timeStamp;
        setTimeout(() => {
          if (this.lastTimeStamp === event.timeStamp) {
            this.search();
          }
        }, 0);
      }
    },

    onenter() {
      this.search();
    },

    gotoAnotherStockAccountRightQry() {
      this.$router.push({ name: 'accountRight' });
    },

    gotoAnotherBiz() {
      this.$router.push({ name: 'bizProgress' });
    },

    toSearch() {
      this.pageStep = 1;
      this.searchKey = '';
      this.searchList = [];
      this.$nextTick(() => {
        this.$refs.searchInput.focus();
      });
    },

    search() {
      // queryPortalList({
      //   bizName: this.searchKey
      // }).then((res) => {
      //   this.searchList = res.data;
      // });
      if (this.searchKey) {
        this.searchList = this.allList.filter((item) => {
          return item.bizNickName.includes(this.searchKey);
        });
      } else {
        this.searchList = [];
      }
    },

    gotoBiz(bizType, flowNo, isExistIntroduce, bizName, bizUrl) {
      if(bizType === '010170'){
        // 产品购买双录
        this.$router.push({
          name: 'doubleRecord',
          // query: { bizType, flowNo }
        });
        return;
      }
      if (isExistIntroduce === '1') {
        this.$router.push({
          name: 'businessIntroduce',
          query: { bizType, flowNo }
        });
        return;
      }
      if (bizUrl) {
        window.location.href = bizUrl;
        return;
      }
      if (bizType) {
        $h.setSession('bizType', bizType);
        if (flowNo) {
          import('@/common/flowMixin.js').then((a) => {
            a.initFlow.call(this, bizType, flowNo);
          });
        } else {
          let bizType =
            (this.$route.query && this.$route.query.bizType) ||
            $hvue.customConfig.bizType;
          import('@/common/flowMixin.js').then((a) => {
            a.initFlow.call(this, bizType, flowNo);
          });
        }
      }
      // queryFlowStyleConfig({}, bizType, flowNo)
      //   .then((res) => {
      //     // if (res.data) {
      //     //   setTheme('custom', JSON.parse(res.data.styleConfigStr));
      //     //   $h.setSession('theme', res.data.styleConfigStr);
      //     // } else {
      //     //   setTheme('default');
      //     // }

      //   })
      //   .catch((err) => {
      //     console.log(err);
      //   });
    }
  }
};
</script>

<style lang="less">
.bg_home {
  width: 100%;
  height: 100vh;
  background: url('../../assets/images/bg.png');
}
</style>
