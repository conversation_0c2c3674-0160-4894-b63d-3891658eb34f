<!--
 * @Author: chenjm
 * @Date: 2023-05-23 15:22:49
 * @LastEditors: chenjm
 * @LastEditTime: 2023-05-30 16:13:24
 * @Description: demo
-->
<template>
  <van-form>
    <van-pull-refresh
      v-model="isLoading"
      style="min-height: 100vh"
      @refresh="onRefresh"
    >
      <article class="content">
        <div class="com_title" style="overflow: initial">
          <h5>原生接口请求DEMO</h5>
        </div>
        <van-cell-group>
          <van-field
            v-model="funNo"
            label="原生功能号"
            placeholder="请输入输入原生功能号"
            clearable
          />
        </van-cell-group>
        <div class="com_title" style="overflow: initial">
          <h5>原生参数</h5>
        </div>
        <van-row
          v-for="(item, index) in paramList"
          :key="index"
          gutter="14"
          style="margin-top: 10px"
        >
          <van-col span="10">
            <van-cell-group>
              <van-field
                v-model="item.key"
                placeholder="请输入参数key"
                clearable
              />
            </van-cell-group>
          </van-col>
          <van-col span="10">
            <van-cell-group>
              <van-field
                v-model="item.value"
                placeholder="请输入参数value"
                clearable
              />
            </van-cell-group>
          </van-col>
          <van-col span="4">
            <van-button icon="plus" type="info" @click="addParams" />
          </van-col>
        </van-row>
        <div style="margin: 16px">
          <van-button
            round
            block
            type="info"
            native-type="submit"
            @click="onSubmit"
          >
            提交
          </van-button>
        </div>
        <div class="com_title" style="overflow: initial">
          <h5>调用原生接口60099</h5>
        </div>
        <van-row
          v-for="(item, index) in paramList60099"
          :key="index + 100"
          gutter="14"
          style="margin-top: 10px"
        >
          <van-col span="10">
            <van-cell-group>
              <van-field
                v-model="item.key"
                placeholder="请输入参数key"
                clearable
              />
            </van-cell-group>
          </van-col>
          <van-col span="10">
            <van-cell-group>
              <van-field
                v-model="item.value"
                placeholder="请输入参数value"
                clearable
              />
            </van-cell-group>
          </van-col>
          <van-col span="4">
            <van-button icon="plus" type="info" @click="addParams60099" />
          </van-col>
        </van-row>
        <van-row gutter="5" style="margin-top: 10px">
          <van-col span="12">
            <van-button
              block
              type="primary"
              @click="toggleParams({ actionType: '1' })"
            >
              actionType : 1
            </van-button>
          </van-col>
          <van-col span="12">
            <van-button
              block
              type="primary"
              @click="toggleParams({ actionType: '2' })"
            >
              actionType : 2
            </van-button>
          </van-col>
        </van-row>
        <van-row gutter="5" style="margin-top: 10px">
          <van-col span="12">
            <van-button
              block
              type="primary"
              @click="toggleParams({ actionType: '5' })"
            >
              actionType : 5
            </van-button>
          </van-col>
          <van-col span="12">
            <van-button
              block
              type="primary"
              @click="
                toggleParams({
                  actionType: '6',
                  url: 'https://fzwebapps.yjbtest.com/yjbwebmoc/moc/web/moc-pro/build/indexView.html'
                })
              "
            >
              actionType : 6
            </van-button>
          </van-col>
        </van-row>
        <van-row gutter="5" style="margin-top: 10px">
          <van-col span="12">
            <van-button
              block
              type="primary"
              @click="
                toggleParams({
                  actionType: '11',
                  type: '8' //8：银证转账页面
                })
              "
            >
              actionType : 11
            </van-button>
          </van-col>
        </van-row>
        <van-cell-group>
          <van-field
            v-model="paramJSON60099"
            type="textarea"
            rows="2"
            autosize
            label="原生输入参数"
            placeholder="请输入原生输入参数"
            clearable
          />
        </van-cell-group>
        <div style="margin: 16px">
          <van-button
            round
            block
            type="info"
            native-type="submit"
            @click="onSubmit60099"
          >
            提交
          </van-button>
        </div>
        <div style="margin: 16px">
          <van-button
            round
            block
            type="info"
            native-type="submit"
            @click="closeSDK"
          >
            关闭
          </van-button>
        </div>
        <div style="margin: 16px">
          <van-button
            round
            block
            type="info"
            native-type="submit"
            @click="toRecording"
          >
            录制视频
          </van-button>
        </div>
      </article>
    </van-pull-refresh>
    <videorecord v-if="showRecord" context="" @callback="cb" />
    <video :src="videoSrc"></video>
  </van-form>
</template>
<script>
import videorecord from '@/components/media/VideoRecording.vue';
export default {
  components: { videorecord },
  data() {
    return {
      isLoading: false,
      funNo: '',
      paramList: [],
      paramList60099: [],
      paramJSON60099: '',
      showRecord: false,
      videoSrc: ''
    };
  },
  computed: {},
  watch: {},
  created() {
    window.nativeCallback = this.nativeCallback;
    this.addParams();
    this.addParams60099();
  },

  methods: {
    onRefresh() {
      Object.assign(this, this.$options.data());
      this.addParams();
      this.addParams60099();
    },
    onSubmit() {
      let reqParams = {
        funcNo: this.funNo
      };
      for (let { key, value } of this.paramList) {
        if (key === '' || value === '') continue;
        reqParams[key] = value;
      }
      console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
      const res = $h.callMessageNative(reqParams);
      console.log(`请求结果为: ~~${JSON.stringify(res)}`);
    },
    closeSDK() {
      let reqParams = {
        funcNo: '50114'
      };
      console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
      const res = $h.callMessageNative(reqParams);
      console.log(`请求结果为: ~~${JSON.stringify(res)}`);
    },
    toggleParams(params) {
      let actionType = params.actionType;
      delete params.actionType;
      let reqParams = {
        funcNo: '60099',
        // ...params
        actionType: actionType,
        params: { ...params }
      };
      for (let { key, value } of this.paramList60099) {
        if (key === '' || value === '') continue;
        reqParams.params[key] = value;
      }
      console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
      const res = $h.callMessageNative(reqParams);
      console.log(`请求结果为: ~~${JSON.stringify(res)}`);
    },
    nativeCallback(data) {
      console.log(`nativeCallback: ~~${JSON.stringify(data)}`);
    },
    addParams() {
      this.paramList.push({
        key: '',
        value: ''
      });
    },
    addParams60099() {
      this.paramList60099.push({
        key: '',
        value: ''
      });
    },
    onSubmit60099() {
      let param;
      try {
        param = JSON.parse(this.paramJSON60099);
      } catch (error) {
        this.$TAlert({
          tips: error
        });
        return;
      }
      let reqParams = {
        funcNo: '60099',
        ...param
      };
      console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
      const res = $h.callMessageNative(reqParams);
      console.log(`请求结果为: ~~${JSON.stringify(res)}`);
    },
    toRecording() {
      this.showRecord = true;
    },
    cb(b) {
      this.showRecord = false;
      this.videoSrc = b.base64;
    }
  }
};
</script>

<style lang="less"></style>
