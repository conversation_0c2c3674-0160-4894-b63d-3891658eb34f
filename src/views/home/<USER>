<template>
  <section class="main fixed white_bg" data-page="home">
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" href="javascript:void(0);" @click="back"></a>
        <h1 class="title">业务办理进度查询</h1>
        <a class="icon_text" href="javascript:void(0);" @click="accountHref">
          客服
        </a>
      </div>
    </header>
    <div class="com_title">
      <h5>仅展示近一年在业务办理已提交的业务流水数据。</h5>
    </div>
    <div class="tab_nav_wrap">
      <ul class="tab_navlist">
        <li :class="{ active: statusFlag == 0 }" @click="ChangeStatusFlag(0)">
          <span>办理中</span>
        </li>
        <li :class="{ active: statusFlag == 1 }" @click="ChangeStatusFlag(1)">
          <span>已完成</span>
        </li>
      </ul>
    </div>
    <div class="date_filter_wrap">
      <ul class="date_filter_nav">
        <li :class="{ active: timeIndex == 0 }" @click="ChangeTime(0)">
          <span>近一月</span>
        </li>
        <li :class="{ active: timeIndex == 1 }" @click="ChangeTime(1)">
          <span>近三月</span>
        </li>
        <li :class="{ active: timeIndex == 2 }" @click="ChangeTime(2)">
          <span>近六月</span>
        </li>
        <li :class="{ active: timeIndex == 3 }" @click="timeShow = true">
          <span>{{ TimeFormat }}</span>
        </li>
      </ul>
    </div>
    <article class="content" v-show="statusFlag == 0" v-if="isFilter">
      <div class="bus_rate_wrap">
        <AccountList
          :param="PageParam"
          :statusFlag="0"
          :filter="filter"
          :labelName="labelName"
        />
      </div>
    </article>
    <article class="content" v-show="statusFlag == 1" v-if="isFilter">
      <div class="bus_rate_wrap">
        <AccountList
          :param="PageParam"
          :statusFlag="1"
          :filter="filter"
          :labelName="labelName"
        />
      </div>
    </article>
    <van-calendar
      v-model="timeShow"
      type="range"
      :min-date="minDate"
      :max-date="maxDate"
      @confirm="Confirm"
    />
  </section>
</template>

<script>
import moment from 'moment';
import AccountList from './components/accountList';
import { exitApp, getInstantToken, jumpThirdPartyUrl } from '@/common/util';

export default {
  name: 'AccountPermissionAttempt',
  components: { AccountList },
  data() {
    return {
      // 状态标识
      statusFlag: 0,
      // 时间标识
      timeIndex: 0,
      // 开始时间
      startDate: '',
      // 结束时间
      endDate: '',
      // 自定日期选额框显示
      timeShow: false,
      // 最小日期
      minDate: moment().subtract(1, 'year').toDate(),
      // 最大日期
      maxDate: new Date(),
      // 过滤参数
      filter: [],
      // 名称聚合
      labelName: [],
      // 查询过滤
      isFilter: false
    };
  },
  computed: {
    StartDate() {
      if (this.timeIndex == 0) {
        // 近一月
        return moment().subtract(30, 'days').format('YYYY-MM-DD');
      } else if (this.timeIndex == 1) {
        // 近三月
        return moment().subtract(90, 'days').format('YYYY-MM-DD');
      } else if (this.timeIndex == 2) {
        // 近六月
        return moment().subtract(6, 'months').format('YYYY-MM-DD');
      } else if (this.timeIndex == 3) {
        // 自定义
        return this.startDate;
      }
    },
    EndDate() {
      if (this.timeIndex == 0) {
        // 近一月
        return moment().format('YYYY-MM-DD');
      } else if (this.timeIndex == 1) {
        // 近三月
        return moment().format('YYYY-MM-DD');
      } else if (this.timeIndex == 2) {
        // 近六月
        return moment().format('YYYY-MM-DD');
      } else if (this.timeIndex == 3) {
        // 自定义
        return this.endDate;
      }
    },
    // 自定义时间格式
    TimeFormat() {
      if (this.timeIndex != 3) return '年/月/日 - 年/月/日';
      const start = moment(this.startDate).format('YYYY/MM/DD');
      const end = moment(this.endDate).format('YYYY/MM/DD');
      return `${start} - ${end}`;
    },
    // 分页相关查询参数
    PageParam() {
      return {
        beginTime: this.StartDate,
        endTime: this.EndDate
      };
    },
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo?.clientId;
    }
  },
  watch: {
    ssoLoginFlag: {
      handler: function (clientId) {
        console.log(clientId);
        if (clientId) {
          this.loading();
        }
      },
      immediate: true
    }
  },
  created() {
    this.statusFlag = this.$route.query.statusFlag || 0;
  },
  methods: {
    accountHref() {
      getInstantToken()
        .then(({ instantToken, opStation, appId }) => {
          jumpThirdPartyUrl({
            url: `${window.$hvue.customConfig.targetUrl}/yjbwebonlineservice/onlineservice/web/onlineservice/html/index.html?instant_token=${instantToken}&op_station=${opStation}&app_id=${appId}`
          });
        })
        .catch((err) => {
          _hvueToast({ mes: err });
        });
    },
    loading() {
      this.isFilter = true;
    },
    back() {
      if ($hvue.platform === '0') {
        this.$router.back();
      } else {
        exitApp();
      }
    },
    ChangeStatusFlag(value) {
      this.$router.push({ query: { statusFlag: value } });
      this.statusFlag = value;
    },
    ChangeTime(value) {
      this.timeIndex = value;
    },
    Confirm(date) {
      const [start, end] = date;
      this.timeShow = false;
      this.startDate = moment(start).format('YYYY-MM-DD');
      this.endDate = moment(end).format('YYYY-MM-DD');
      this.ChangeTime(3);
    }
  }
};
</script>

<style scoped></style>
