<template>
  <section class="main fixed" data-page="home">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="com_title">
        <h5>已开通权限</h5>
      </div>
      <div class="bus_rate_wrap">
        <ul class="bus_ratelist" style="margin: 0">
          <li v-for="(item, index) in hasList" :key="index">
            <div class="row_1">
              <h5>{{ item.permissionTypeName }}</h5>
              <p>{{ item.permissionPromptDesc }}</p>
            </div>
            <span class="state">已开通</span>
          </li>
        </ul>
      </div>
      <div class="com_title">
        <h5>未开通权限</h5>
      </div>
      <div class="bus_rate_wrap">
        <ul class="bus_ratelist" style="margin: 0">
          <li v-for="(item, index) in noList" :key="index">
            <div class="row_1">
              <h5>{{ item.permissionTypeName }}</h5>
              <p>{{ item.permissionPromptDesc }}</p>
            </div>
            <a
              class="r_link_arrow"
              @click="gotobiz(item.permissionTypeName, item.bizType)"
              >前往开通</a
            >
          </li>
        </ul>
      </div>
    </article>
  </section>
</template>

<script>
import { stockAccountRightQry } from '@/service/service';

export default {
  data() {
    return {
      hasList: [], //已开通
      noList: [] //为开通
    };
  },
  mounted() {
    this.renderingView();
  },
  methods: {
    back() {
      this.$router.back();
    },

    renderingView() {
      stockAccountRightQry().then((res) => {
        this.hasList = res.data.stockAccountRightsList.filter(
          (item) => item.status === '1'
        );
        this.noList = res.data.stockAccountRightsList.filter(
          (item) => item.status === '0'
        );
      });
    },

    gotobiz(bizName, bizType) {
      this.$router.push({
        name: 'introduce',
        query: { bizType, bizName }
      });
    }
  }
};
</script>
