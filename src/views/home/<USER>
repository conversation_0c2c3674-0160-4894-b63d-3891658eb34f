<template>
  <section class="main fixed" data-page="home">
    <t-header @back="back"></t-header>
    <div class="tab_nav_wrap">
      <ul class="tab_navlist">
        <!-- <li :class="type === '1' ? 'active' : ''" @click="changeType('1')">
          <span>受理中</span>
        </li> -->
        <li :class="type === '2' ? 'active' : ''" @click="changeType('2')">
          <span>处理中</span>
        </li>
        <li :class="type === '3' ? 'active' : ''" @click="changeType('3')">
          <span>已结束</span>
        </li>
      </ul>
    </div>
    <article class="content" v-show="type === '1'">
      <div class="bus_rate_wrap">
        <van-list
          ref="type1Params"
          v-model="type1Params.loading"
          :finished="type1Params.finished"
          finished-text="没有更多了"
          class="bus_ratelist"
          @load="type1OnLoad"
        >
          <li
            v-for="(item, index) in type1List"
            :key="index"
            @click="togoDetail(item)"
          >
            <div class="row_1">
              <h5>{{ item.bizName }}</h5>
              <p>申请时间：{{ item.time }}</p>
            </div>
            <span class="state">{{ item.statusDesc }}</span>
          </li>
        </van-list>
      </div>
    </article>
    <article class="content" v-show="type === '2'">
      <div class="bus_rate_wrap">
        <van-list
          ref="type2Params"
          v-model="type2Params.loading"
          :finished="type2Params.finished"
          finished-text="没有更多了"
          class="bus_ratelist"
          @load="type2OnLoad"
        >
          <li
            v-for="(item, index) in type2List"
            :key="index"
            @click="togoDetail(item)"
          >
            <div class="row_1">
              <h5>{{ item.bizName }}</h5>
              <p>申请时间：{{ item.time }}</p>
            </div>
            <span class="state">{{ item.statusDesc }}</span>
          </li>
        </van-list>
      </div>
    </article>
    <article class="content" v-show="type === '3'">
      <div class="bus_rate_wrap">
        <van-list
          ref="type3Params"
          v-model="type3Params.loading"
          :finished="type3Params.finished"
          finished-text="没有更多了"
          class="bus_ratelist"
          @load="type3OnLoad"
        >
          <li
            v-for="(item, index) in type3List"
            :key="index"
            @click="togoDetail(item)"
          >
            <div class="row_1">
              <h5>{{ item.bizName }}</h5>
              <p>申请时间：{{ item.time }}</p>
            </div>
            <span class="state">{{ item.statusDesc }}</span>
          </li>
        </van-list>
      </div>
    </article>
  </section>
</template>

<script>
import { List } from 'vant';
import { businessProcessingProgressQry } from '@/service/service';

export default {
  data() {
    return {
      type: '2',
      type1List: [],
      type1Params: {
        pageNum: 1,
        pageSize: 20,
        loading: false,
        finished: false
      },
      type2List: [],
      type2Params: {
        pageNum: 1,
        pageSize: 20,
        loading: false,
        finished: false
      },
      type3List: [],
      type3Params: {
        pageNum: 1,
        pageSize: 20,
        loading: false,
        finished: false
      }
    };
  },
  mounted() {
    console.log('mounted');
  },
  destroyed() {
    console.log('destroyed');
  },
  methods: {
    togoDetail(item) {
      /**
       * 办理状态：0 受理中；1 办理中；2 已作废；3 被驳回；4 办理成功；5 办理失败；
       */
      // if (item.status === '0' || item.status === '1') {
      //   import('@/common/flowMixin.js').then((a) => {
      //     a.initFlow.call(this, item.bizType, item.flowNo);
      //   });
      // } else {
      //   this.$router.push({
      //     name: 'bizResult',
      //     query: { flowInsId: item.flowInsId }
      //   });
      // }
    },

    type1OnLoad() {
      businessProcessingProgressQry({
        pageNum: this.type1Params.pageNum,
        pageSize: this.type1Params.pageSize,
        queryType: this.type
      }).then((res) => {
        this.type1Params.loading = false;
        this.type1List = this.type1List.concat(res.data.flowInsList);
        this.type1Params.pageNum += 1;
        if (this.type1List.length >= parseInt(res.data.total)) {
          this.type1Params.finished = true;
        }
      });
    },

    type2OnLoad() {
      businessProcessingProgressQry({
        pageNum: this.type2Params.pageNum,
        pageSize: this.type2Params.pageSize,
        queryType: this.type
      }).then((res) => {
        this.type2Params.loading = false;
        this.type2List = this.type2List.concat(res.data.flowInsList);
        this.type2Params.pageNum += 1;
        if (this.type2List.length >= parseInt(res.data.total)) {
          this.type2Params.finished = true;
        }
      });
    },

    type3OnLoad() {
      businessProcessingProgressQry({
        pageNum: this.type3Params.pageNum,
        pageSize: this.type3Params.pageSize,
        queryType: this.type
      }).then((res) => {
        this.type3Params.loading = false;
        this.type3List = this.type3List.concat(res.data.flowInsList);
        this.type3Params.pageNum += 1;
        if (this.type3List.length >= parseInt(res.data.total)) {
          this.type3Params.finished = true;
        }
      });
    },

    back() {
      this.$router.back(-1);
    },

    changeType(val) {
      this.type = val;
      if (this.type === '1') {
        if (!this.type1Params.finished) {
          this.type1OnLoad();
        }
      } else if (this.type === '2') {
        if (!this.type2Params.finished) {
          this.type2OnLoad();
        }
      } else if (this.type === '3') {
        if (!this.type3Params.finished) {
          this.type3OnLoad();
        }
      }
    }
  }
};
</script>
