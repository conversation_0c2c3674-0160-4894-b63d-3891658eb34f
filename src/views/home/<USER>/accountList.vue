<template>
  <div class="bus_rate_wrap">
    <div class="acct_nodata" v-if="oneLoad && pagelist.length <= 0">
      <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
      <h5>很抱歉，当前暂未查询到数据</h5>
    </div>
    <van-list
      v-else
      ref="page"
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了"
      class="bus_ratelist"
      :immediate-check="false"
      @load="PageLoad"
      :key="Math.random()"
    >
      <li
        v-for="(item, index) in pagelist"
        :key="index"
        @click="DetailClick(item)"
      >
        <div class="row_1">
          <h5>{{ item.bizName }}</h5>
          <p>申请时间：{{ DateTime(item) }}</p>
        </div>
        <span class="state" :class="{ error: item.color }">
          {{ item.bizStatus }}
        </span>
        <i class="p_right_arrow"></i>
      </li>
    </van-list>

    <PermissionDetaile v-model="showDetail" :param="detailParam" />
  </div>
</template>

<script>
import PermissionDetaile from './permissionDetaile';
import { getFlowinsPage } from './service';
import { Dict, ErrorDict } from './dict';
export default {
  name: 'AccountList',
  components: { PermissionDetaile },
  // model: { prop: 'inputStr', event: 'change' },
  props: {
    param: { type: Object },
    statusFlag: { type: Number },
    filter: { type: Array, default: () => [] }
    // labelName: { type: Array, default: () => [] }
  },
  data() {
    return {
      // 是否加载
      loading: true,
      // 数据是否已全部加载完
      finished: false,
      // 分页数据
      pagelist: [],
      // 每页展示数据
      pageSize: 10,
      // 当前页码
      pageNumber: 1,
      // 初始化加载
      oneLoad: false,
      // 是否展示详情
      showDetail: false,
      // 详情页展示内容
      detailParam: {}
    };
  },
  watch: {
    param: {
      handler() {
        this.pageNumber = 1;
        this.pagelist = [];
        this.loading = true;
        this.PageLoad();
      },
      deep: true
    }
  },
  created() {
    this.oneLoad = false;
    this.PageLoad();
  },
  methods: {
    Dict,
    ErrorDict,
    PageLoad() {
      getFlowinsPage({
        ...this.param,
        status: this.statusFlag == 0 ? '1' : '2,3',
        pageSize: this.pageSize,
        pageNum: this.pageNumber
      })
        .then(({ code, data, msg }) => {
          if (code == 0) {
            this.oneLoad = true;
            this.loading = false;
            if (this.pageNumber <= 1) {
              this.pagelist = data.list;
            } else {
              this.pagelist = this.pagelist.concat(data.list);
            }
            const bizType = this.$props.filter.map((item) => item.bizType);
            this.pagelist = this.pagelist.filter((item) => {
              return !bizType.includes(item.bizType);
            });
            this.pageNumber += 1;
            this.finished = this.pageNumber > data.pages;
          } else {
            _hvueToast({ mes: msg });
          }
        })
        .catch((e) => {
          _hvueToast({ mes: e });
        });
    },
    LableName({ bizType }) {
      const data = this.labelName.find((item) => item.bizType == bizType);
      if (data) return data.bizNickName;
      return '--';
    },
    DateTime(item) {
      return item.updateTime || item.acceptCompTime || item.createTime || '--';
    },
    DetailClick(item) {
      this.showDetail = true;
      this.detailParam = item;
    }
  }
};
</script>

<style scoped></style>
