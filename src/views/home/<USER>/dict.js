export function Dict({ status, taskType, taskStatus }) {
  let value = '';
  status == 1 && taskType == '' && taskStatus == '' && (value = '受理完成');
  status == 1 && taskType == 2 && taskStatus == 0 && (value = '待审核');
  status == 1 && taskType == 2 && taskStatus == 1 && (value = '审核中');
  status == 1 && taskType == 2 && taskStatus == 2 && (value = '审核通过');
  status == 1 && taskType == 2 && taskStatus == 3 && (value = '审核驳回');
  status == 3 && taskType == 2 && taskStatus == 3 && (value = '审核驳回');
  status == 3 && taskType == 2 && taskStatus == 4 && (value = '审核作废');
  status == 1 && taskType == 3 && taskStatus == 0 && (value = '待办理');
  status == 1 && taskType == 3 && taskStatus == 1 && (value = '办理中');
  status == 2 && taskType == 3 && taskStatus == 4 && (value = '办理失败');
  status == 2 && taskType == '' && taskStatus == 2 && (value = '办理通过');
  status == 1 && taskType == 3 && taskStatus == 3 && (value = '办理驳回');
  status == 3 && taskType == 3 && taskStatus == 4 && (value = '办理作废');
  return value || '未知';
}

export function ErrorDict({ status, taskType, taskStatus }) {
  if (status == 1 || status == 3) {
    if (taskType == 2 || taskType == 3) {
      if (taskStatus == 3 || taskStatus == 4) {
        return true;
      }
    }
  }
  return false;
}

// status 流程状态
// taskType 任务类型
// taskStatus 任务状态

// 待审核 status == 1 && taskType == 2 && taskStatus == 0
// 审核中 status == 1 && taskType == 2 && taskStatus == 1
// 审核通过 status == 1 && taskType == 2 && taskStatus == 2

// 审核驳回 status == 1 && taskType == 2 && taskStatus == 3
// 审核作废 status == 3 && taskType == 2 && taskStatus == 4
// 待办理 status == 1 && taskType == 3 && taskStatus == 0
// 办理中 status == 1 && taskType == 3 && taskStatus == 1
// 办理通过 status == 2 && taskType == 3 && taskStatus == 2
// 办理驳回 status == 1 && taskType == 3 && taskStatus == 3
// 办理作废 status == 3 && taskType == 3 && taskStatus == 4
