<template>
  <div v-if="Value">
    <van-popup
      v-model="Value"
      :style="{ width: '100%', height: 'auto' }"
      round
      lazy-rebder
      position="bottom"
    >
      <div class="layer_box" style="position: relative">
        <div class="layer_tit">
          <h3>{{ BizeName }}</h3>
          <a class="close" href="javascript:void(0);" @click.stop="close"></a>
        </div>
        <div class="layer_cont">
          <div class="p_rate_wrap">
            <ul class="p_rate_progress">
              <li
                :class="item.taskStatus"
                v-for="(item, i) in NodeList"
                :key="i"
              >
                <i class="icon"></i>
                <h5>{{ item.nodeName }}</h5>
                <p>{{ item.nodeDate || item.nodeShow }}</p>
              </li>
            </ul>
            <div class="reject_txtinfo" v-if="FailReason.length > 0">
              <h5 class="title">失败原因</h5>
              <p v-for="item in FailReason" v-html="item" :key="item"></p>
            </div>
            <div v-if="!!TaskRemark" class="p_rate_tips">
              {{ TaskRemark }}
            </div>
          </div>
        </div>
        <div class="ce_btn" v-if="accountInfo.showButton == '1'">
          <a class="p_button" href="javascript:void(0)" @click.stop="pushGto"
            >重新申请</a
          >
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { getDetailQry } from './service';
import { ErrorDict } from './dict';
import { flattenDeep } from 'lodash';
export default {
  name: 'PermissionDetaile',
  model: { prop: 'value', event: 'change:value' },
  components: {},
  props: {
    value: {
      type: Boolean
    },
    param: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    Value: {
      get() {
        return this.value;
      },
      set(value) {
        if (!value) this.accountInfo = {};
        this.$emit('change:value', value);
      }
    },
    BizeName() {
      if (Object.keys(this.param).length <= 0) return '';
      return this.param.bizName;
    },
    NodeList() {
      if (Object.keys(this.accountInfo).length <= 0) return [];
      return this.accountInfo.nodeInfo.map((item) => {
        return {
          ...item,
          taskStatus: this.nodeState(item.taskStatus)
        };
      });
    },
    TaskRemark() {
      if (Object.keys(this.accountInfo).length <= 0) return '';
      return this.accountInfo.taskRemark;
    },
    FailReason() {
      if (Object.keys(this.accountInfo).length <= 0) return [];
      if (!this.accountInfo.failReason) return [];
      if (this.accountInfo.failReason.includes(']\r\n')) {
        return [this.accountInfo.failReason];
      }
      if (this.accountInfo.failReason[0].includes('[')) {
        // 为了处理后端无法转换数据结构
        const data = JSON.parse(`[${this.accountInfo.failReason}]`);
        return flattenDeep(data);
      } else {
        return [this.accountInfo.failReason];
      }
    }
  },
  watch: {
    Value() {
      this.Value && Object.keys(this.param).length > 0 && this.queryDetail();
    }
  },
  data() {
    return {
      accountInfo: {}
    };
  },
  created() {
    console.info('--进入');
  },
  methods: {
    ErrorDict,
    pushGto() {
      if (!this.accountInfo.url) {
        import('@/common/flowMixinV2.js').then((a) => {
          a.initFlow.call(this, {
            bizType: this.accountInfo.bizType,
            initJumpMode: '0'
          });
        });
      } else {
        if (this.accountInfo.url.includes('：//')) {
          window.location.href = this.accountInfo.url;
          return;
        }
        if (this.accountInfo.url.includes('&')) {
          window.location.href = window.location.origin + this.accountInfo.url;
          return;
        }
        this.$router.push({ name: this.accountInfo.url });
      }
    },
    queryDetail() {
      getDetailQry({
        bizType: this.param.bizType,
        formId: this.param.formId,
        acceptanceNo: this.param.acceptanceNo
      })
        .then(({ code, data, msg }) => {
          if (code == 0) {
            this.accountInfo = data;
          } else {
            _hvueToast({ mes: msg });
          }
        })
        .catch((e) => {
          _hvueToast({ mes: e });
        });
    },
    close() {
      this.Value = false;
    },
    nodeState(item) {
      if (item == 3) return 'error';
      if (item == 2) return 'ok';
      if (item == 1) return 'ing';
      if (item == 0) return '';
    }
  }
};
</script>

<style scoped></style>
