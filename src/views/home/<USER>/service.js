/**
 * @desc 接口请求定义
 * <AUTHOR>
 */

import { baseService } from '@/service/baseService';
import { trackEvent } from '@/common/util';
let requestURL = $hvue.customConfig.serverUrl;

/**
 * 所有请求的公用方法 可以添加公用参数 或者其他公用操作  
 * @param {String} funcNo 功能号
 * @param {Object} params 接口的业务参数
 * @param {Object} options 此次请求的相关设置 如 请求方法 是否加签， 具体定义见netIntercept.js
 */
function commService(funcNo, params = {}, options = {}) {
  let url = requestURL;
  if (funcNo) {
    url += `/${funcNo}`;
  }
  return baseService({
    url,
    params,
    options
  }).then(
    (res) => {
      return Promise.resolve(res);
    },
    (err) => {
      trackEvent({
        // event_name: 'ywbl_view',
        event_name: 'ywbl_show', // 曝光弹框
        page_name: '',
        module_name: '报错提示',
        element_name: 'error_popup',
        remarks: JSON.stringify(err)
      });
      return Promise.reject(err);
    }
  );
}

/**
 * @desc 获取业务列表
 * @param {Object} params 业务参数
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function getFlowinsPage(params) {
  return commService('business/accPermissionViewPageQry', params, {
    loading: false,
    method: 'GET',
    allowRepeat: true
  });
}

/**
 * @desc 获取业务详情
 * @param {Object} params 业务参数
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function getDetailQry(params) {
  return commService('business/accPermissionViewDetailQry', params, {
    loading: false,
    method: 'GET',
    allowRepeat: true
  });
}

/**
 * @desc 获取需要过滤的业务
 * @param {Object} params 业务参数
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function getFlowinsPageFilter(params) {
  return commService('business/accPermissionViewShowFlagQry', params, {
    loading: false,
    method: 'GET'
    // allowRepeat: true
  });
}
