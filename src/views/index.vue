<template>
  <transition :name="$store.state.router.direction">
    <!-- <keep-alive v-if="resetShow" :exclude="notKeepAliveArr">
      <router-view :key="key" @reload="clearKeepAlive" />
    </keep-alive> -->
    <router-view :key="key" @reload="clearKeepAlive" />
  </transition>
</template>

<script>
import ChannelUtil from '@/common/ChannelUtil';
import { trackUuid, isWeixin } from '@/common/util';

export default {
  name: 'Index',
  provide() {
    return {
      clearKeepAlive: this.setClearKAFlag
    };
  },
  data() {
    return {
      notKeepAliveArr: $hvue.config.notKeepAliveArr,
      resetShow: true,
      isClearKeepAlive: false // 判断路由跳转时是否清楚缓存
    };
  },
  computed: {
    key() {
      return this.$route.path;
    }
  },
  watch: {
    $route(to) {
      if ($hvue.config.noCachePage.includes(to.name) || this.isClearKeepAlive) {
        // 回到首页时  清掉keepAlive页面缓存
        this.clearKeepAlive();
      }
    }
  },
  created() {
    this.isClearKeepAlive = true;
    this.setChannel();
    // if (window.history && window.history.pushState) {
    //   window.addEventListener('popstate', this.goBack, false);
    // }
    if (isWeixin()) {
      // 微信环境下禁用分享
      document.addEventListener(
        'WeixinJSBridgeReady',
        function onBridgeReady() {
          WeixinJSBridge.call('hideOptionMenu');
        }
      );
    }
  },
  methods: {
    // goBack() {
    //   const PreNode = $h.getSession('PreNode');
    //   if (!PreNode?.stepName && TKFlowEngine.getFlowStack().length === 0) {
    //     // 没有上一个节点了
    //     if ($hvue.platform == 0) {
    //       // h5
    //     } else {
    //       // app
    //       if ($h.getSession('optType') === '1') {
    //         let reqParams = {
    //           funcNo: '60099',
    //           actionType: '5',
    //           params: {
    //             optType: '1',
    //             isSuccess: '0'
    //           }
    //         };
    //         const res = $h.callMessageNative(reqParams);
    //         console.log(`请求结果为: ~~${JSON.stringify(res)}`);
    //         $h.callMessageNative({
    //           funcNo: '50114',
    //           moduleName: 'open'
    //         });
    //       } else if ($h.getSession('optType') === 'loginSuccess') {
    //         let reqParams = {
    //           funcNo: '60099',
    //           actionType: '5',
    //           params: {
    //             optType: '1',
    //             isSuccess: '1'
    //           }
    //         };
    //         const res = $h.callMessageNative(reqParams);
    //         console.log(`请求结果为: ~~${JSON.stringify(res)}`);
    //         $h.callMessageNative({
    //           funcNo: '50114',
    //           moduleName: 'open'
    //         });
    //       } else {
    //         $h.callMessageNative({
    //           funcNo: '50114',
    //           moduleName: 'open'
    //         });
    //       }
    //     }
    //   } else {
    //     if (TKFlowEngine.getFlowStack().length > 0) {
    //       // isFlowBack用于判断是有没有走宝哥流程引擎返回还是手动左上角返回，true为手动流程引擎返回。false为浏览器返回
    //       // 走宝哥流程引擎返回时，宝哥自己会处理宝哥堆栈信息也会history.go(-n),此时什么都不要做。否则则是浏览器返回需要走宝哥的方法处理宝哥堆栈信息，浏览器自己go(-1)
    //       if (!$h.getSession('isFlowBack')) {
    //         TKFlowEngine.goback(PreNode, '0', '0');
    //       }
    //     }
    //     $h.clearSession('routerBack');
    //     console.log('监听返回');
    //   }
    // },

    clearKeepAlive() {
      console.log('********  reload ************');
      const _this = this;
      _this.resetShow = false;
      _this.isClearKeepAlive = false;
      _this.$nextTick(function () {
        _this.resetShow = true;
      });
    },
    setChannel() {
      const { appCode, bizType, flowNo, opStation } =
        this.$router.currentRoute.query;
      const channelUtil = new ChannelUtil({ appCode });
      if (channelUtil.isChannel) {
        $h.setSession('appCode', appCode);
        $h.setSession('bizType', bizType);
        $h.setSession('flowNo', flowNo);
        $h.setSession('opStation', opStation);
      }
    },
    setClearKAFlag() {
      this.isClearKeepAlive = true;
    }
  }
};
</script>
<style>
.left-enter-active,
.left-leave-active,
.right-enter-active,
.right-leave-active {
  transition: all 0.3s;
}
.left-enter {
  opacity: 0.5;
  transform: translate3d(100%, 0, 0);
}
.left-leave-active {
  display: none;
  transition: all 0s;
  opacity: 0.5;
  transform: translate3d(-100%, 0, 0);
}
.right-enter {
  opacity: 0.5;
  transform: translate3d(-100%, 0, 0);
}
.right-leave-active {
  opacity: 0.5;
  transform: translate3d(100%, 0, 0);
}
/* page transition css  end*/
</style>
