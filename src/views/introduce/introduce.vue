<template>
  <section class="main fixed white_bg" data-page="home">
    <div class="header">
      <div class="header_inner">
        <a class="icon_back" @click.stop="back" />
      </div>
    </div>
    <article v-if="bizType === '010039'" class="content">
      <div class="bus_banbox">
        <div class="txt">
          <h2>私募合格<br />投资者认定</h2>
          <div class="tips" />
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i />个人投资者申请条件</h5>
        <div class="cont">
          <p>金融资产不低于300万或近3年个人年均收入不低于50万元。</p>
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i />私募合格投资者</h5>
        <div class="cont">
          <p>该资格认证后，您可以在理财商城购买私募基金类型的产品。</p>
        </div>
      </div>
      <div class="txt_center">
        <a class="link_right_arrow" @click.stop="toHighFinancial"
          >前往高端理财</a
        >
      </div>
    </article>
    <!-- <article v-else-if="zqzytzzOpenBiz" class="content">
      <div class="bus_banbox">
        <div class="txt">
          <h2>开通债券专业投资者权限</h2>
          <div class="tips" />
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i />个人客户申请条件</h5>
        <div class="cont">
          <p>1、无不良诚信记录；</p>
          <p>2、风险测评等级需满足C4及以上；</p>
          <p>3、需存在状态正常的深A、沪A账户、特转A账户；</p>
          <p>4、近20个交易日日均资产不低于500万元；</p>
          <p>5、具有2年以上投资经验。</p>
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i />机构/产品客户申请条件</h5>
        <div class="cont">
          <p>1、风险测评等级需满足C4及以上；</p>
          <p>2、需存在状态正常的特转A账户；</p>
          <p>3、沪深任一市场已开通债券专业投资者权限。</p>
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i />温馨提示</h5>
        <div class="cont">
          <p>
            1、如您已开通沪市或深市的债券专业投资者权限，可豁免资产及交易年限校验直接申请开通特转A账户权限；
          </p>
          <p>2、债券专业投资者有效期为2年，如已过有效期，需重新进行开通。</p>
        </div>
      </div>
    </article> -->
    <article v-else-if="bizType === '010040'" class="content">
      <div class="bus_banbox">
        <div class="txt">
          <h2>资管/信托合格<br />投资者认定</h2>
          <div class="tips" />
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i />个人投资者申请条件</h5>
        <div class="cont">
          <p>1、具有2年以上投资经验。</p>
          <p>
            2、家庭金融净资产不低于300万元或家庭金融资产不低于500万元或近3年个人年均收入不低于40万元。
          </p>
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i />资管/信托合格投资者</h5>
        <div class="cont">
          <p>该资格认证后，您可以在理财商城购买如下类型的产品:</p>
          <p>国金金创鑫系列产品</p>
          <p>国金慧多利系列产品</p>
          <p>五矿信托/外贸信托慧丰利系列产品等</p>
        </div>
      </div>
      <div class="txt_center">
        <a class="link_right_arrow" @click.stop="toHighFinancial"
          >前往高端理财</a
        >
      </div>
    </article>
    <article v-else-if="bizType === '010213'" class="content">
      <div class="bus_banbox">
        <div class="txt">
          <h2>特转A<br />账户开通</h2>
          <div class="tips" />
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i />申请条件</h5>
        <div class="cont">
          <p>1、无不良诚信记录</p>
          <p>2、非重点监控客户名单</p>
          <p>3、存在状态正常的深A账户</p>
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i />温馨提示</h5>
        <div class="cont">
          <p>1、网上办理仅支持开立一个特转A账户</p>
          <p>
            2、开通后您持有的北交所和挂牌公司股票只能卖出持有的两网及退市公司股票可以买卖
          </p>
        </div>
      </div>
    </article>
    <article v-else-if="bizType === '010131'" class="content">
      <div class="header_inner">
        <a class="icon_back" @click.stop="back" />
      </div>
      <div class="bus_banbox">
        <div class="txt">
          <h2>期权买入<br />额度调整</h2>
          <div class="tips" />
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i />申请条件</h5>
        <div class="cont">
          <p>1、已开立股票期权资金账户，且状态正常；</p>
          <p>2、已开立上海或深圳股票期权合约账户且状态正常；</p>
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i />温馨提示</h5>
        <div class="cont">
          <p>
            买入额度是投资者用于买入期权合约的总成交金额。股票期权业务对个人投资者进行买入额度管理，将结合投资者的资产状况、适当性综合评估结果等因素，规定投资者的限购额度，即最大买入额度。
          </p>
        </div>
      </div>
    </article>
    <article v-else-if="bizType === '010203'" class="content">
      <div class="header_inner">
        <a class="icon_back" @click.stop="back" />
      </div>
      <div class="bus_banbox">
        <div class="txt">
          <h2>开通公司债/<br />企业债权限</h2>
          <div class="tips" />
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i />申请条件</h5>
        <div class="cont">
          <p>1、无不良诚信记录</p>
          <p>2、风险测评等级需满足C3及以上</p>
          <p>
            3、存在状态正常的深A、沪A账户、特转A账户、深市信用账户、沪市信用账户、信用特转A账户
          </p>
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i />温馨提示</h5>
        <div class="cont">
          <p>
            债券普通投资者开通公司债/企业债券权限后，仅可交易面向债券普通投资者公开发行的公司债或企业债。
          </p>
        </div>
      </div>
    </article>
    <!-- <article
      v-else-if="bizType === '010197'"
      class="content"
    >
      <div class="header_inner">
        <a
          class="icon_back"
          @click.stop="back"
        />
      </div>
      <div class="bus_banbox">
        <div class="txt">
          <h2>关联关系确认</h2>
          <div class="tips">
            办理时间：交易日9：00-16：00
          </div>
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title">
          <i />关联关系确认
        </h5>
        <div class="cont">
          <p>证券账户关联关系指投资者名下各子证券账户与投资者一码通账户的对应关系，如名下子证券账户未确认关联关系，将会影响投资者办理证券账户开立、关键信息(名称、证件类型、证件号码)变更、休眠账户激活业务。</p>
        </div>
      </div>
      <div class="bus_infobox" />
    </article> -->
    <article v-else-if="bizType === '010203'" class="content">
      <div class="header_inner">
        <a class="icon_back" @click.stop="back" />
      </div>
      <div class="bus_banbox">
        <div class="txt">
          <h2>开通公司债/<br />企业债权限</h2>
          <div class="tips" />
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i />申请条件</h5>
        <div class="cont">
          <p>1、无不良诚信记录</p>
          <p>2、风险测评等级需满足C3及以上</p>
          <p>3、存在状态正常的深A、沪A账户、深市信用账户、沪市信用账户</p>
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i />温馨提示</h5>
        <div class="cont">
          <p>
            债券普通投资者开通公司债/企业债券权限后，仅可交易面向债券普通投资者公开发行的公司债或企业债。
          </p>
        </div>
      </div>
    </article>
    <article v-else-if="['010731', '010046'].includes(bizType)" class="content">
      <div class="bus_banbox">
        <div class="txt">
          <h2>销户</h2>
          <div class="tips">办理时间：交易日9:00-17:00</div>
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i></i>销户须知</h5>
        <div class="cont">
          <p>1、需要使用本人在有效期内的身份证件，且需由本人办理；</p>
          <p>2、办理销户时需确保网络通畅；</p>
          <p>
            3、账户内未持有证券、无资金、无在途资金或不存在任何未了结的债权债务关系等。若您的账户内有资金，请先在交易日9:00-16:00自助转出；
          </p>
          <p>
            4、证券账户可能因在其他券商持有股票等原因导致注销失败，请以实际办理结果为准；
          </p>
          <p>
            5、申请注销证券账户时，应当确保该账户满足注销条件，并不得使用注销账户申报交易。若投资者违反账户注销规定而产生的相应经济损失和法律责任由投资者自行承担。
          </p>
        </div>
      </div>
    </article>
    <article v-else-if="bizType === '010177'" class="content">
      <div class="header_inner">
        <a class="icon_back" @click.stop="back" />
      </div>
      <div class="bus_banbox">
        <div class="txt">
          <h2>场内外账户对应<br />关系维护</h2>
          <div class="tips">办理时间：交易日9：00-16：00</div>
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title">
          <i />证券基金账户对应关系维护，是指添加或取消证券基金对应关系。
        </h5>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i />添加证券基金账户对应关系</h5>
        <div class="cont">
          <p>
            指场外沪深TA账户和场内证券账户/封闭式基金账户建立对应关系，以便客户持有的基金份额进行跨系统转托管操作。
          </p>
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i />取消证券基金账户对应关系</h5>
        <div class="cont">
          <p>
            指取消现存的场外沪深TA账户和场内证券账户/封闭式基金账户的对应关系，以便建立新的证券基金账户对应关系。
          </p>
        </div>
      </div>
    </article>
    <article v-else class="content">
      <div class="bus_banbox">
        <div class="txt">
          <h2>{{ bizName }}</h2>
          <div class="tips">{{ bizTime }}</div>
        </div>
      </div>
      <div v-for="(it, i) in introduce" class="bus_infobox" :key="i">
        <h5 class="title"><i></i>{{ it.title }}</h5>
        <div class="cont" v-html="it.content"></div>
      </div>
      <div class="txt_center" v-show="bottomLinkName !== ''">
        <a class="link_right_arrow" @click.stop="jumpPage">{{
          bottomLinkName
        }}</a>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="toNext">{{ buttonName }}</a>
      </div>
    </footer>
  </section>
</template>

<script>
import { queryBusinessIntroduce, xhFlowInfoQry, getConfigMap } from '@/service/service';
import { exitApp, trackEvent } from '@/common/util';

export default {
  data() {
    return {
      buttonName: '',
      bizTime: '',
      bizName: '',
      bizType: this.$route.query.bizType,
      flowNo: this.$route.query.flowNo ? this.$route.query.flowNo : '',
      introduce: [
        {
          title: '',
          content: '',
          viewSort: 0
        }
      ],
      bottomLinkType: '', //底部链接类型：1 业务办理链接；2 非业务办理链接；
      bottomLinkName: '', //底部链接名称
      bottomLinkUrl: '' // 底部链接地址
    };
  },
  computed: {
    zqzytzzOpenBiz() {
      //债券专业投资者权限开通标识
      return ['010066', '040066'].includes(this.bizType);
    },
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo?.clientId;
    }
  },
  watch: {
    ssoLoginFlag: {
      handler: function (clientId) {
        console.log(clientId);
        if (clientId) {
          this.renderingView();
        }
      },
      immediate: true
    }
  },
  created() {
    $h.setSession('showRejectResult', null);
    const { toPage } = this.$route.query;
    if (toPage === 'introduce' && $h.getSession('introduceNeedBack')) {
      // 处理可能重复路由栈的问题
      this.$router.back();
    }
    $h.setSession('fromIntroduce', true);
    if (['010039', '010040'].includes(this.bizType)) {
      this.buttonName = '申请认证';
    } else if (['010731', '010046'].includes(this.bizType)) {
      this.buttonName = '开始销户';
    } else {
      this.buttonName = '立即办理';
    }
  },
  methods: {
    renderingView() {
      const configKey = `bc.business.accept.time.${this.bizType}`;
      this.introduce = [];
      queryBusinessIntroduce({
        bizType: this.bizType,
        flowNo: this.flowNo
      }).then((res) => {
        const {
          businessIntroduceCardList = [],
          tips = '',
          conditions = '',
          enableBottomLink = '0',
          bottomLinkType = '',
          bottomLinkName = '',
          bottomLinkUrl = ''
        } = res.data;
        this.bizName = res.data.bizName;
        trackEvent({
          event_name: 'ywbl_view',
          page_name: res.data.bizName,
          module_name: '页面展示',
          element_name: 'init'
        });
        if (businessIntroduceCardList.length === 0) {
          if (conditions !== '') {
            this.introduce.push({
              title: '申请条件',
              content: conditions,
              viewSort: 0
            });
          }
          if (tips !== '') {
            this.introduce.push({
              title: '温馨提示',
              content: tips,
              viewSort: 1
            });
          }
        } else {
          this.introduce = businessIntroduceCardList.sort(
            (a, b) => a.viewSort - b.viewSort
          );
        }
        if (enableBottomLink === '1') {
          this.bottomLinkType = bottomLinkType;
          this.bottomLinkName = bottomLinkName;
          this.bottomLinkUrl = bottomLinkUrl;
        }
        this.loading = false;
        return getConfigMap({
          configKey
        })
          .then(({ data = {} }) => {
            const configInfo = data[configKey];
            if (configInfo) {
              const [start, end] = configInfo.configValue.split(',');
              this.bizTime = `办理时间：${start}-${end}`;
            }
          })
          .catch((err) => {
            _hvueToast({ mes: err });
          });
      });
    },
    toHighFinancial() {
      // 跳转至高端理财
      if ($hvue.platform === '0') {
        window.navigateLocationHref({
          url: $hvue.customConfig.thirdPartyUrl.highFinancial
        });
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          params: {
            url: $hvue.customConfig.thirdPartyUrl.highFinancial,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      }
    },
    back() {
      $h.clearSession('fromIntroduce');
      if ($hvue.platform === '0') {
        this.$router.back();
      } else {
        if ($h.getSession('introduceNeedBack')) {
          this.$router.back();
        } else {
          exitApp();
        }
      }
    },

    toNext() {
      const { userInfo } = this.$store.state.user;
      if (this.bizType && this.bizType === '010174') {
        this.$router.push({ name: 'chooseAppointmentOrTeach' });
        return;
      } else if (this.bizType && ['010731', '010046'].includes(this.bizType)) {
        xhFlowInfoQry({})
          .then(({ data, code, msg }) => {
            if (code === 0) {
              //type流程类型: 0预约 1挽留处理中 2销户；
              //conclusion 挽回状态: 0挽留失败，1挽留成功，2无需挽留 3挽留处理中
              //cancelStatus 销户主流程状态,0初始化预约 1已有挽留结果 2已做双向视频 3销户办结 4发送完
              const {
                type,
                conclusion = '',
                businessAccCancel = '',
                preFlowInsId = '',
                reasonAccCancel = '',
                reasonAccCancelOther = '',
                cancelStatus = '',
                reasonCancelEboss = '',
                reasonCancelOthEboss = ''
              } = data;
              if (['0'].includes(type) && conclusion === '') {
                $h.setSession('bizType', '010731');
                import('@/common/flowMixin.js').then((a) => {
                  a.initFlow.call(this, '010731', this.flowNo);
                });
              } else if (
                ['1'].includes(type) ||
                (['0', '2'].includes(type) &&
                  ['0', '1', '3'].includes(conclusion) &&
                  !['2'].includes(cancelStatus))
              ) {
                const { app_id = '' } = this.$route.query;
                this.$router.push({
                  name: 'yyxhResult',
                  query: { app_id }
                });
              } else if (preFlowInsId === '') {
                return Promise.reject('未能查询到预约销户信息');
              } else {
                let contextParam;
                let isReset = ['0', '1'].includes(cancelStatus);
                try {
                  contextParam = JSON.stringify({
                    conclusion,
                    businessAccCancel,
                    preFlowInsId,
                    reasonAccCancel,
                    reasonAccCancelOther,
                    reasonCancelEboss,
                    reasonCancelOthEboss
                  });
                } catch (err) {
                  return Promise.reject(err);
                }

                $h.setSession('bizType', '010046');
                import('@/common/flowMixin.js').then((a) => {
                  a.initFlow.call(this, '010046', null, isReset, contextParam);
                });
              }
            } else {
              return Promise.reject(msg);
            }
          })
          .catch((err) => {
            this.$TAlert({
              tips: err
            });
          });
        return;
      }
      if (this.zqzytzzOpenBiz) {
        if (userInfo.organName === '个人') {
          this.bizType = '010066';
        } else {
          this.bizType = '040066';
        }
      }
      if (this.bizType) {
        $h.setSession('bizType', this.bizType);
        if (this.flowNo) {
          import('@/common/flowMixin.js').then((a) => {
            a.initFlow.call(this, this.bizType, this.flowNo);
          });
        } else {
          let bizType =
            (this.$route.query && this.$route.query.bizType) ||
            $hvue.customConfig.bizType;
          import('@/common/flowMixin.js').then((a) => {
            a.initFlow.call(this, this.bizType, this.flowNo);
          });
        }
      }
    },

    jumpPage() {
      /* 跳转规则
       *  bottomLinkType = 1 内部跳转
       *  - bottomLinkUrl是6位数字，判断为业务编号，进行流程初始化
       *  - bottomLinkUrl是英文单词，判断为页面路由名称，使用$router路由跳转
       *  - bottomLinkUrl是URL链接，但是没有拼接证书域名端口，使用当前地址拼接跳转
       *  - bottomLinkUrl是完整URL链接，直接跳转
       */
      if (this.bottomLinkType === '2') {
        if ($hvue.platform === '0') {
          window.navigateLocationHref({ url: this.bottomLinkUrl });
        } else {
          let reqParams = {
            funcNo: '60099',
            moduleName: $hvue.customConfig.moduleName,
            actionType: '6',
            params: {
              url: this.bottomLinkUrl,
              leftType: 1,
              rightType: 99,
              rightText: ''
            }
          };
          console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
          const res = $h.callMessageNative(reqParams);
          console.log(`请求结果为: ~~${JSON.stringify(res)}`);
        }
      } else if (this.bottomLinkType === '1') {
        // 内部跳转处理
        if (/^\d{6}$/.test(this.bottomLinkUrl)) {
          $h.setSession('bizType', this.bottomLinkUrl);
          import('@/common/flowMixinV2.js').then((a) => {
            a.initFlow.call(this, {
              bizType: this.bottomLinkUrl,
              initJumpMode: '0'
            });
          });
        } else if (/^[a-zA-Z0-9_]+$/.test(this.bottomLinkUrl)) {
          // 英文单词，判断为页面路由名称
          this.$router.push({
            name: this.bottomLinkUrl
          });
        } else if (
          this.bottomLinkUrl.startsWith('/') ||
          !this.bottomLinkUrl.includes('://')
        ) {
          // URL链接，但没有域名，使用当前地址拼接
          const currentOrigin = window.location.origin;
          const fullUrl = this.bottomLinkUrl.startsWith('/')
            ? `${currentOrigin}${this.bottomLinkUrl}`
            : `${currentOrigin}/${this.bottomLinkUrl}`;
          window.navigateLocationHref({ url: fullUrl });
        } else {
          // 完整URL链接，直接跳转
          window.navigateLocationHref({ url: this.bottomLinkUrl });
        }
      }
    }
  }
};
</script>
<style scoped>
div.cont >>> ul {
  padding-left: 0.05rem;
  padding-bottom: 0rem;
}

div.cont >>> ul > li {
  list-style-type: disc;
  padding: 0.05rem 0 0.05rem 0.05rem;
}

div.cont >>> p {
  padding-left: 0.05rem;
}

div.cont >>> ul > li > strong {
  position: relative;
  font-size: 0.16rem;
  font-weight: 500;
  line-height: 1.375;
  color: #333333;
}
.header_inner {
  position: absolute;
}
.header_inner >>> .icon_back {
  color: #ffffff;
}
</style>
