<template>
  <section class="main fixed backWhite_box" data-page="home">
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click.stop="back" />
        <h1 class="title">选择认定类型</h1>
      </div>
    </header>
    <article class="content">
      <div class="com_title">
        <h5>请选择您想要开通的合格投资者类型</h5>
      </div>
      <div class="">
        <div class="pro_sl_item">
          <div class="tit">
            <h5>私募合格投资者</h5>
          </div>
          <div class="info">
            <div class="row">
              <span
                v-if="investorListInfo.privateEquityInvestorStatus === '1'"
                class="state error"
                >未认证</span
              >
              <span
                v-else-if="investorListInfo.privateEquityInvestorStatus === '2'"
              >
                <span class="state finish">已认证 </span>
                <span class="time"
                  >有效期至：{{
                    investorListInfo.privateEquityInvestorValidDate
                  }}</span
                >
              </span>
              <span
                v-else-if="investorListInfo.privateEquityInvestorStatus === '3'"
              >
                <span class="state finish">即将过期</span>
                <span class="time"
                  >有效期至：{{
                    investorListInfo.privateEquityInvestorValidDate
                  }}</span
                >
              </span>
              <span
                v-else-if="investorListInfo.privateEquityInvestorStatus === '4'"
              >
                <span class="state finish">已过期</span>
                <span class="time"
                  >有效期至：{{
                    investorListInfo.privateEquityInvestorValidDate
                  }}</span
                >
              </span>
            </div>
            <a
              v-if="investorListInfo.privateEquityInvestorStatus !== '2'"
              class="com_btn"
              @click.stop="toAuth('010039')"
              >去认证</a
            >
          </div>
        </div>
        <div class="pro_sl_item">
          <div class="tit">
            <h5>资管/信托合格投资者</h5>
          </div>
          <div class="info">
            <div class="row">
              <span
                v-if="investorListInfo.assetManageInvestorStatus === '1'"
                class="state error"
                >未认证</span
              >
              <span
                v-else-if="investorListInfo.assetManageInvestorStatus === '2'"
              >
                <span class="state finish">已认证 </span>
                <span class="time"
                  >有效期至：{{
                    investorListInfo.assetManageInvestorValidDate
                  }}</span
                >
              </span>
              <span
                v-else-if="investorListInfo.assetManageInvestorStatus === '3'"
              >
                <span class="state finish">即将过期</span>
                <span class="time"
                  >有效期至：{{
                    investorListInfo.assetManageInvestorValidDate
                  }}</span
                >
              </span>
              <span
                v-else-if="investorListInfo.assetManageInvestorStatus === '4'"
              >
                <span class="state finish">已过期</span>
                <span class="time"
                  >有效期至：{{
                    investorListInfo.assetManageInvestorValidDate
                  }}</span
                >
              </span>
            </div>
            <a
              v-if="investorListInfo.assetManageInvestorStatus !== '2'"
              class="com_btn"
              @click.stop="toAuth('010040')"
              >去认证</a
            >
          </div>
        </div>
      </div>
      <div class="tip_txtbox">
        <p>投资小贴士:</p>
        <p>
          根据监管要求，购买私募、资管、信托等高端理财产品前，需先进行合格投资者认证。
        </p>
      </div>
    </article>
  </section>
</template>
<script>
import { bussinesInvestorRightQry } from '@/service/service';
import { exitApp } from '@/common/util';

export default {
  data() {
    return {
      investorListInfo: {
        assetManageInvestorStatus: '',
        assetManageInvestorValidDate: '',
        privateEquityInvestorStatus: '',
        privateEquityInvestorValidDate: ''
      },
      profFlag: '' // 是否为专业投资者
    };
  },
  computed: {
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo?.clientId;
    }
  },
  watch: {
    ssoLoginFlag: {
      handler: function (clientId) {
        console.log(clientId);
        if (clientId) {
          this.renderingView();
        }
      },
      immediate: true
    }
  },
  created() {
    $h.setSession('introduceNeedBack', true);
    window.viewShowCallBack = this.viewShowCallBack;
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  mounted() {
    // setTimeout(
    //   () =>
    //     this.$nextTick(function () {
    //       this.renderingView();
    //     }),
    //   10
    // );
  },
  methods: {
    back() {
      $h.clearSession('introduceNeedBack');
      if ($hvue.platform === '0') {
        this.$router.back();
      } else {
        exitApp();
      }
    },
    viewShowCallBack() {
      this.renderingView();
    },
    renderingView() {
      bussinesInvestorRightQry({
        opEntrustWay: $hvue.customConfig.opEntrustWay
      })
        .then((res) => {
          this.investorListInfo = res.data;
        })
        .catch((err) => {
          _hvueLoading.close();
          _hvueToast({ mes: err });
        });
    },
    toAuth(bizType) {
      this.$router.push({ name: 'businessIntroduce', query: { bizType } });
    }
  }
};
</script>
<style scoped>
.com_btn {
  border-radius: 0.18rem;
  border: 1px solid #ff2840;
  background: var(--primary-b-500, #fff);
  color: var(--typography-fff, #ff2840);
}
</style>
