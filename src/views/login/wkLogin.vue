<template>
  <section v-if="showPage" class="main fixed">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="login_box">
        <div class="input_form">
          <div class="input_text">
            <!-- <span class="tit">客户号</span> -->
            <input v-model="account" class="t1" placeholder="请输入客户号" />
          </div>
          <div class="input_text">
            <!-- <span class="tit">密码</span> -->
            <input
              v-model="password"
              class="t1"
              type="password"
              placeholder="请输入密码"
            />
          </div>
          <div class="input_text code">
            <!-- <span class="tit">验证码</span> -->
            <input
              v-model="imgCode"
              class="t1"
              maxlength="4"
              type="tel"
              placeholder="请输入图形验证码"
            />
            <a class="code_img" href="javascript:void(0);" @click="imgClick">
              <img :src="imgSrc" />
            </a>
          </div>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a
          v-throttle
          class="p_button"
          href="javascript:void(0);"
          @click="nextClick"
          >登录</a
        >
      </div>
    </footer>
  </section>
</template>

<script>
import { login, getImgCode } from '@/service/service.js';
import { getPwdEncryption } from '@/common/util';

export default {
  name: 'Login',
  data() {
    return {
      account: '', // 账号
      password: '', // 密码
      imgCode: '', // 图片验证码
      imgSrc: '', // 图片验证码地址
      captchaToken: '',
      bizType: ''
    };
  },
  created() {
    this.showPage = true;
    this.imgClick();
  },
  methods: {
    back() {
      this.$router.replace({ name: 'home' });
    },

    nextClick() {
      if (!this.account) {
        _hvueToast({
          mes: '请输入客户号'
        });
        return false;
      }
      if (!this.password) {
        _hvueToast({
          mes: '请输入密码'
        });
        return false;
      }
      if (!this.imgCode) {
        _hvueToast({
          mes: '请输入验证码'
        });
        return false;
      }
      let loginParam = {
        clientId: this.account,
        tradePassword: 'encrypt:' + getPwdEncryption(this.password),
        captcha: this.imgCode,
        captchaToken: this.captchaToken
      };
      let bizType =
        (this.$route.query && this.$route.query.bizType) ||
        $hvue.customConfig.bizType;
      let userInfo;
      login(loginParam)
        .then((loginData) => {
          if (loginData.code === 0) {
            let res = loginData.data;
            let authorization =
              loginData.responseHeaders['tk-token-authorization'];
            $h.setSession('bizType', bizType);
            $h.setSession('authorization', authorization);
            userInfo = Object.assign({}, res);
            this.$store.commit('user/setUserInfo', userInfo);
            this.$router.replace({ name: 'home' });
          } else if (loginData.code === 9007) {
            this.imgClick();
            _hvueToast({
              mes: '账号密码错误，请重新输入'
            });
          } else {
            this.imgClick();
            _hvueToast({
              mes: loginData.msg
            });
          }
        })
        .catch((err) => {
          this.imgClick();
          _hvueToast({
            mes: err.msg
          });
        });
    },

    _getImgCode() {
      let _this = this;
      getImgCode({}, { loading: false }).then((data) => {
        if (data.code === 0) {
          let results = data.data;
          _this.imgSrc = results.image;
          _this.captchaToken = results.token;

          //清空验证码
          for (let ele of _this.inputViewList) {
            if (ele.name === 'imgCode') {
              ele.value = '';
              break;
            }
          }
        }
      });
    },

    imgClick() {
      getImgCode({}, { loading: false }).then((data) => {
        if (data.code === 0) {
          let results = data.data;
          this.imgSrc = results.image;
          this.captchaToken = results.token;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.login_box {
  background: #fff;
  border-radius: 0.12rem;
  margin: 0.15rem 0.12rem;
  .input_form {
    margin: 0 0.12rem;
    padding: 0.3rem 0;
    .input_text {
      // padding-left: 0.7rem;
      padding: 0.1rem 0;
      position: relative;
      box-sizing: content-box;
      min-height: 0.4rem;
      border-top: 0 none;
      border-bottom: 1px solid rgba(7, 3, 31, 0.1);
      .tit {
        font-size: 0.16rem;
        line-height: 0.4rem;
        color: rgba(2, 3, 36, 0.6);
        position: absolute;
        top: 0;
        left: 0;
      }
      .t1 {
        display: block;
        width: 100%;
        height: 0.4rem;
        padding: 0.12rem 0.12rem 0.12rem 0;
        line-height: 0.16rem;
        font-size: 0.16rem;
        border: 0 none;
        outline: none;
        background: none;
        color: rgba(7, 3, 31, 1);
        font-family: -apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI',
          Roboto, Ubuntu;
        font-weight: normal;
      }
      .code_img {
        width: 0.8rem;
        height: 0.38rem;
        position: absolute;
        top: 50%;
        margin-top: -0.19rem;
        right: 0;
        z-index: 50;
      }
    }
  }
}
</style>
