<template>
  <section class="main fixed white_bg" data-page="home" v-if="!loading">
    <div class="header">
      <div class="header_inner">
        <a class="icon_back" @click.stop="back" />
      </div>
    </div>
    <article class="content">
      <!-- <div class="bus_banbox">
        <div class="txt">
          <h2>融资融券</h2>
          <br />
          <h2>开户预约</h2>
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i />办理条件</h5>
        <div class="cont">
          <p>1、沪A、深A账户状态正常</p>
          <p>2、无不良诚信记录</p>
          <p>3、风险测评等级需满足C4及以上</p>
          <p>4、开户最近20个交易日日均证券类资产不低于50万</p>
          <p>5、参与证券交易的时间满6个月</p>
          <p>6、我司规定的其它条件</p>
        </div>
      </div>
      <div class="bus_infobox">
        <h5 class="title"><i />温馨提示</h5>
        <div class="cont">
          <p>
            您可先行判断是否已满足上述融资融券开户办理条件，如有需要请联系您的专属服务人员或95310获取支持。
          </p>
        </div>
      </div> -->
      <div class="bus_banbox">
        <div class="txt">
          <h2>{{ bizName }}</h2>
          <div class="tips">{{ bizTime }}</div>
        </div>
      </div>
      <div v-for="(it, i) in introduce" class="bus_infobox" :key="i">
        <h5 class="title"><i></i>{{ it.title }}</h5>
        <div class="cont" v-html="it.content"></div>
      </div>
      <div class="txt_center" v-show="bottomLinkName !== ''">
        <a class="link_right_arrow" @click.stop="jumpPage">{{
          bottomLinkName
        }}</a>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="toNext">立即办理</a>
      </div>
    </footer>
  </section>
</template>

<script>
import {
  CreditAccount,
  FlowinsCancel,
  OrderOnTheWay
} from '@/service/lrService';
import { queryBusinessIntroduce, getConfigMap } from '@/service/service';
import { exitApp, trackEvent } from '@/common/util';

export default {
  data() {
    return {
      creditAccount: {},
      loading: true,
      bizType: '010174',
      flowNo: this.$route.query.flowNo ? this.$route.query.flowNo : '',
      bizName: '融资融券开户预约',
      bizTime: '',
      introduce: [
        {
          title: '',
          content: '',
          viewSort: 0
        }
      ],
      bottomLinkType: '', //底部链接类型：1 业务办理链接；2 非业务办理链接；
      bottomLinkName: '', //底部链接名称
      bottomLinkUrl: '' // 底部链接地址
    };
  },
  computed: {
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo?.clientId;
    }
  },
  watch: {
    ssoLoginFlag: {
      handler: function (clientId) {
        console.log(clientId);
        if (clientId) {
          this.renderingView();
        }
      },
      immediate: true
    }
  },
  mounted() {},
  methods: {
    back() {
      if ($hvue.platform === '0') {
        this.$router.back();
      } else {
        if ($h.getSession('history_list').index === 0) {
          exitApp();
        } else {
          this.$router.back();
        }
      }
    },

    renderingView() {
      const configKey = `bc.business.accept.time.${this.bizType}`;
      this.introduce = [];
      CreditAccount({})
        .then((res) => {
          if (res.data && res.data) {
            this.creditAccount = res.data;
            return queryBusinessIntroduce({
              bizType: this.bizType,
              flowNo: this.flowNo
            });
          }
        })
        .then((res) => {
          const {
            businessIntroduceCardList = [],
            tips = '',
            conditions = '',
            enableBottomLink = '0',
            bottomLinkType = '',
            bottomLinkName = '',
            bottomLinkUrl = ''
          } = res.data;
          this.bizName = res.data.bizName;
          trackEvent({
            event_name: 'ywbl_view',
            page_name: res.data.bizName,
            module_name: '页面展示',
            element_name: 'init'
          });
          if (businessIntroduceCardList.length === 0) {
            if (conditions !== '') {
              this.introduce.push({
                title: '申请条件',
                content: conditions,
                viewSort: 0
              });
            }
            if (tips !== '') {
              this.introduce.push({
                title: '温馨提示',
                content: tips,
                viewSort: 1
              });
            }
          } else {
            this.introduce = businessIntroduceCardList.sort(
              (a, b) => a.viewSort - b.viewSort
            );
          }
          if (enableBottomLink === '1') {
            this.bottomLinkType = bottomLinkType;
            this.bottomLinkName = bottomLinkName;
            this.bottomLinkUrl = bottomLinkUrl;
          }
          this.loading = false;
          return getConfigMap({
            configKey
          });
        })
        .then(({ data = {} }) => {
          const configInfo = data[configKey];
          if (configInfo) {
            const [start, end] = configInfo.configValue.split(',');
            this.bizTime = `办理时间：${start}-${end}`;
          }
        })
        .catch((err) => {
          _hvueToast({ mes: err });
        });
    },

    jumpPage() {
      /* 跳转规则
       *  bottomLinkType = 1 内部跳转
       *  - bottomLinkUrl是6位数字，判断为业务编号，进行流程初始化
       *  - bottomLinkUrl是英文单词，判断为页面路由名称，使用$router路由跳转
       *  - bottomLinkUrl是URL链接，但是没有拼接证书域名端口，使用当前地址拼接跳转
       *  - bottomLinkUrl是完整URL链接，直接跳转
       */
      if (this.bottomLinkType === '2') {
        if ($hvue.platform === '0') {
          window.navigateLocationHref({ url: this.bottomLinkUrl });
        } else {
          let reqParams = {
            funcNo: '60099',
            moduleName: $hvue.customConfig.moduleName,
            actionType: '6',
            params: {
              url: this.bottomLinkUrl,
              leftType: 1,
              rightType: 99,
              rightText: ''
            }
          };
          console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
          const res = $h.callMessageNative(reqParams);
          console.log(`请求结果为: ~~${JSON.stringify(res)}`);
        }
      } else if (this.bottomLinkType === '1') {
        // 内部跳转处理
        if (/^\d{6}$/.test(this.bottomLinkUrl)) {
          $h.setSession('bizType', this.bottomLinkUrl);
          import('@/common/flowMixinV2.js').then((a) => {
            a.initFlow.call(this, {
              bizType: this.bottomLinkUrl,
              initJumpMode: '0'
            });
          });
        } else if (/^[a-zA-Z0-9_]+$/.test(this.bottomLinkUrl)) {
          // 英文单词，判断为页面路由名称
          this.$router.push({
            name: this.bottomLinkUrl
          });
        } else if (
          this.bottomLinkUrl.startsWith('/') ||
          !this.bottomLinkUrl.includes('://')
        ) {
          // URL链接，但没有域名，使用当前地址拼接
          const currentOrigin = window.location.origin;
          const fullUrl = this.bottomLinkUrl.startsWith('/')
            ? `${currentOrigin}${this.bottomLinkUrl}`
            : `${currentOrigin}/${this.bottomLinkUrl}`;
          window.navigateLocationHref({ url: fullUrl });
        } else {
          window.navigateLocationHref({ url: this.bottomLinkUrl });
        }
      }
    },

    toNext() {
      trackEvent({
        event_name: 'ywbl_click',
        page_name: this.bizName,
        module_name: 'next',
        element_name: '立即办理'
      });

      // 看有无信用资金账户
      if (this.creditAccount.creditFundAccountStatus == 1) {
        let bizType = '010099';
        import('@/common/flowMixinV2.js').then((a) => {
          a.initFlow.call(this, { bizType, initJumpMode: '0' });
        });
      } else {
        let bizType = '010174';
        import('@/common/flowMixinV2.js').then((a) => {
          a.initFlow.call(this, { bizType, initJumpMode: '0' });
        });
        return;
        /* OrderOnTheWay({ bizType }).then(({ code, data }) => {
          if (code != 0) return _hvueToast({ mes: '流程单状态获取异常' });
          $h.setSession('showRejectResult', null);
          if (data && data.status == 0) {
            this.$TAlert({
              title: '温馨提示',
              tips: '您已有办理记录，是否继续上次办理流程？',
              confirmBtn: '继续办理',
              cancelBtn: '重新申请',
              hasCancel: true,
              cancel: () => {
                FlowinsCancel({ flowInsId: data.id }).then((res) => {
                  import('@/common/flowMixinV2.js').then((a) => {
                    a.initFlow.call(this, { bizType, initJumpMode: '0' });
                  });
                });
              },
              confirm: () => {
                import('@/common/flowMixinV2.js').then((a) => {
                  a.initFlow.call(this, { bizType, initJumpMode: '0' });
                });
              }
            });
          } else {
            import('@/common/flowMixinV2.js').then((a) => {
              a.initFlow.call(this, { bizType, initJumpMode: '0' });
            });
          }
          console.info(data);
        }); */
      }
    }
  }
};
</script>

<style scoped>
div.cont >>> ul {
  padding-left: 0.05rem;
  padding-bottom: 0rem;
}

div.cont >>> ul > li {
  list-style-type: disc;
  padding: 0.05rem 0 0.05rem 0.05rem;
}

div.cont >>> p {
  padding-left: 0.05rem;
}

div.cont >>> ul > li > strong {
  position: relative;
  font-size: 0.16rem;
  font-weight: 500;
  line-height: 1.375;
  color: #333333;
}

.header_inner {
  position: absolute;
}

.header_inner >>> .icon_back {
  color: #ffffff;
}
</style>
