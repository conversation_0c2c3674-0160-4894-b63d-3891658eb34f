.survey_wrapper {
  background: #f4f4f4;
  padding: 0 15px 44px;
  height: 100vh;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}
.surveyTitle {
  width: 100%;
  height: 30px;
  font-size: 17px;
  color: #030303;
  line-height: 30px;
  text-align: center;
  margin: 16px 0;
}
.disabledTitle {
  color: #888888 !important;
}
.surveyHint {
  font-size: 15px;
  color: #333333;
  line-height: 21px;
  margin-bottom: 14px;
  margin-top: 18px;
}
.question_wrapper {
  width: 100%;
  height: auto;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #ededed;
  padding: 15px;
  margin-bottom: 10px;
  .questionTitle {
    font-size: 15px;
    color: #333333;
    line-height: 21px;
    margin-bottom: 14px;
    i {
      color: red;
    }
  }
  .van-field {
    background: #f6f6f6;
    border-radius: 8px;
    border: 1px solid #ededed;
  }
  .van-radio {
    min-height: 32px;
    border-radius: 8px;
    margin-bottom: 8px;
    border: 1px solid #d7d7d7;
    padding: 6px 8px;
    background: #ffffff;
  }
  .van-checkbox {
    min-height: 32px;
    border-radius: 8px;
    margin-bottom: 8px;
    border: 1px solid #d7d7d7;
    padding: 6px 8px;
    background: #ffffff;
  }
  .checked {
    background: #e2eeff;
    border: 1px solid #e2eeff;
  }
}
.error {
  background-color: #fff0f0 !important;
  .van-field {
    background: #ffffff;
  }
}
.button {
  width: 170px;
  height: 40px;
  line-height: 40px;
  background: #ff2840;
  border-radius: 8px;
  text-align: center;
  font-size: 15px;
  color: #ffffff;
  margin: 30px auto 60px;
}
.result {
  text-align: center;
  padding-top: 42px;
  .main {
    font-size: 15px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #333333;
    margin-top: 14px;
  }
  .hint {
    font-size: 13px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #999999;
    margin-top: 8px;
  }
}
