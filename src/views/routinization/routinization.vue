<template>
  <section class="main fixed white_bg" data-page="home">
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click="back"></a>
        <h1 class="title">程序化交易信息报备</h1>
      </div>
    </header>
    <article class="content" v-if="loaded">
      <PTQuestionnaire v-if="!isSuccess" @success="success" />
      <!-- <div v-if="!isSuccess">
        <div class="survey_wrapper" ref="surveyList">
          <div class="surveyHint" v-show="this.surveyData.surveyTitle">
            1-4题填写说明：账户资金规模填写截至报告日期账户中用于股票（基金、存托凭证）交易的资产总额，包括自有资金和杠杆资金，以万元为单位。对于产品账户，资金规模、来源、占比均按产品维度进行填报。
            <br />资金来源从下列选项中选择填写，可以选择多项：（1）自有资金;（2）募集资金;（3）杠杆资金;（4）其他。产品管理人跟投产品的资金视作募集资金。
          </div>
          <template v-for="(item, i) in questionArr">
            <div
              :key="item.questionId"
              class="question_wrapper"
              :class="{ error: item.questionId == errInfo.id }"
              :ref="`question${item.questionId}`"
            >
              <div
                class="questionTitle"
                :class="{
                  disabledTitle:
                    disabledIds.includes(item.canDisabledId) || item.disabled
                }"
              >
                <i>*</i>{{ i }}、<span :ref="`title${item.questionId}`"></span>
              </div>
              <template v-if="item.type == 'input'">
                <van-field
                  :disabled="
                    disabledIds.includes(item.canDisabledId) || item.disabled
                  "
                  :value="item.data"
                  @input="changeVal($event, item, i)"
                  label=""
                  :type="item.inputType"
                  placeholder="请输入"
                  autosizeId
                  :maxlength="item.maxLength"
                  :border="false"
                />
              </template>
              <template v-else-if="item.type == 'radio'">
                <van-radio-group
                  :disabled="disabledIds.includes(item.canDisabledId)"
                  v-model="item.data"
                  @change="changeRadio($event)"
                >
                  <van-radio
                    v-for="(option, o) in item.options"
                    :disabled="
                      isquan &&
                      item.questionTitle.includes(
                        '主策略类型：当选择“其他”时'
                      ) &&
                      o === 0
                    "
                    :key="item.questionId + (o + 1)"
                    :name="item.questionId + (o + 1)"
                    :class="{
                      checked: item.data === item.questionId + (o + 1)
                    }"
                    checked-color="#FF2840"
                    >{{ option }}</van-radio
                  >
                </van-radio-group>
                <div
                  v-if="
                    item.options[Number(String(item.data).substr(3)) - 1] ===
                    '其他'
                  "
                >
                  <van-field
                    :value="item.other"
                    @input="changeVal($event, item, i)"
                    label=""
                    :type="item.inputType"
                    placeholder="请输入"
                    autosize
                    :maxlength="item.maxLength"
                    :border="false"
                  />
                </div>
              </template>
              <template v-else-if="item.type == 'checkbox'">
                <van-checkbox-group
                  v-model="item.data"
                  :disabled="disabledIds.includes(item.canDisabledId)"
                  :max="item.max || 0"
                >
                  <van-checkbox
                    v-for="(option, o) in item.options"
                    :key="item.questionId + (o + 1)"
                    :name="item.questionId + (o + 1)"
                    :class="{
                      checked: item.data.includes(item.questionId + (o + 1))
                    }"
                    shape="square"
                    checked-color="#FF2840"
                    >{{ option }}</van-checkbox
                  >
                </van-checkbox-group>
                <div
                  v-if="
                    item.data.filter(
                      (v) => item.options[Number(v.substr(3)) - 1] === '其他'
                    ).length
                  "
                >
                  <van-field
                    :value="item.other"
                    @input="changeVal($event, item, i)"
                    label=""
                    :type="item.inputType"
                    placeholder="请输入"
                    autosize
                    :maxlength="item.maxLength"
                    :border="false"
                  />
                </div>
              </template>
            </div>
          </template>
          <div
            class="button"
            @click="commit"
            v-if="this.surveyData.surveyTitle"
          >
            提交
          </div>
        </div>
      </div> -->
      <div class="result" v-else>
        <van-icon name="checked" color="#FF2840" size="55px" />
        <div class="main">问卷提交成功</div>
        <div class="hint">国金证券再次感谢您的参与</div>
      </div>
    </article>
  </section>
</template>

<script>
/* import survey1 from './components/survey1';
import survey2 from './components/survey2';
import survey3 from './components/survey3';
import { jumpThirdPartyUrl, getInstantToken, exitApp } from '@/common/util'; */
/* import {
  clientInfoQryV2,
  surveySubmit,
  stockAccountList,
  surveyJourQuery,
  surveyJourSubmit,
  agreementSignStatus
} from '@/service/service'; */
import { exitApp } from '@/common/util';
import PTQuestionnaire from '@/components/routinization/PTQuestionnaire.vue';

export default {
  name: 'Routinization',
  components: {
    PTQuestionnaire
  },
  data() {
    return {
      /*  disabledIds: [],
      surveyData: {},
      questionArr: [],
      submitData: {},
      errInfo: {}, */
      isSuccess: false,
      loaded: false
      /*  isFastRender: false, // 是否是一键抄写首次渲染
      isquan: true // 是否是量化交易是否选择了是 */
    };
  },
  computed: {
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo?.clientId;
    }
  },
  watch: {
    /*  questionArr: {
      handler(newval) {
        // 以下为出发部分题无法操作的条件
        let disItem1 = newval?.filter(
          (v) => v.questionTitle === '是否量化交易'
        )[0];
        let arr = [...this.disabledIds];
        if (disItem1.data?.slice(-1) === '2') {
          arr.push('是否量化交易');
          arr = [...new Set(arr)];
        } else {
          arr = arr.filter((v) => v !== '是否量化交易');
        }

        // 仅当第4题其他资金规模大于0时才需要输入第5题
        let disItem2 = newval?.filter((v) =>
          v.questionTitle.includes('其他资金规模')
        )[0];
        if ((disItem2.data || 0) == 0) {
          arr.push('其他资金规模');
          arr = [...new Set(arr)];
        } else {
          arr = arr.filter((v) => v !== '其他资金规模');
        }

        // 仅当第3题杠杆资金规模大于0时才需要输入第6题
        let disItem3 = newval?.filter((v) =>
          v.questionTitle.includes('杠杆资金规模')
        )[0];
        if ((disItem3.data || 0) == 0) {
          arr.push('杠杆资金规模');
          arr = [...new Set(arr)];
        } else {
          arr = arr.filter((v) => v !== '杠杆资金规模');
        }
        this.disabledIds = arr;
      },
      deep: true
    }, */
    ssoLoginFlag: {
      handler: function (clientId) {
        if (clientId) {
          this.loaded = true;
        }
      },
      immediate: true
    }
  },
  created() {},
  methods: {
    back() {
      if ($hvue.platform === '0') {
        this.$router.back();
      } else {
        exitApp();
      }
    },
    success() {
      this.isSuccess = true;
    }
  }
};
</script>
<style lang="less" scoped>
@import './routinization.less';
</style>
