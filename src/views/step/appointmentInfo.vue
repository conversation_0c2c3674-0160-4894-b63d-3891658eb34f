<!--  -->
<template>
  <section class="main fixed">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="com_title">请选择营业部</div>
      <!-- <div class="com_box">
				<div class="input_form">
					<div class="input_text text" @click="showOpenSelect = !showOpenSelect">
						<span :class="branchName ? 'tit active' : 'tit'">营业部</span>
						<div class="dropdown" placeholder="请选择">{{ branchName }}</div>
					</div>
					<div class="input_text text">
						<span :class="advanceTime ? 'tit active' : 'tit'">预约时间</span>
						<div class="dropdown" placeholder="请选择">
							<h-picker slot="right" :columns="columns" v-model="advanceTime" separator=" " />
						</div>
					</div>
				</div>
			</div> -->
      <div class="com_box">
        <div class="input_form">
          <div
            class="input_text text"
            @click="showOpenSelect = !showOpenSelect"
          >
            <span class="tit">营业部</span>
            <input
              class="t1 p_r70"
              type="text"
              readonly="readonly"
              placeholder="请选择营业部"
              :value="branchName"
            />
            <!-- <a class="icon_location" href="javascript:void(0);">附近</a> -->
          </div>
          <!-- <div class="imp_c_tips">
						<p><span class="imp">选择任何营业部开户都可在全国任意网点享受相同的服务</span></p>
					</div> -->
        </div>
      </div>
      <div class="com_box mt10">
        <div class="input_form form_tit_right">
          <div class="input_text text">
            <span class="tit">预约日期</span>
            <div
              class="dropdown"
              style="
                padding-top: 0;
                padding-bottom: 0;
                display: flex;
                align-items: center;
              "
              placeholder="请选择"
            >
              <h-picker
                slot="right"
                v-model="advanceTime"
                :columns="columns"
                separator=" "
              />
            </div>
          </div>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" :class="{ disabled: isCheck }" @click="toNext"
          >下一步</a
        >
      </div>
    </footer>
    <openDressSelect
      v-model="showOpenSelect"
      :default="branchInfo"
      :is-open-dress="true"
      @selCallBack="openCallBack"
    ></openDressSelect>
  </section>
</template>

<script>
import { getSysBranchInfo } from '@/service/service';
import openDressSelect from '@/components/openDressSelect';
import flowMixin from '@/common/flowMixin';
export default {
  components: {
    openDressSelect
  },

  mixins: [flowMixin],
  data() {
    return {
      showOpenSelect: false,
      branchInfo: {},
      branchName: '',
      branchNo: '',
      advanceTime: '',
      columns: [
        [
          { label: '', id: '' },
          { label: '', id: '' },
          { label: '', id: '' }
        ],
        [
          { label: '', id: '12:00' },
          { label: '', id: '17:00' }
        ]
      ]
    };
  },

  computed: {
    isCheck() {
      return !this.advanceTime || !this.branchName;
    }
  },

  mounted() {
    // TODO 根据ip获取附近营业部
    this.getDateWeek();
  },

  methods: {
    renderingView() {
      let { branchName, branchNo } = this.flowOutputInfo.inProperty;
      this.branchName = branchName;
      this.branchNo = branchNo;
      getSysBranchInfo({ branchNo }).then((res) => {
        this.branchInfo = res.data[0];
        _hvueLoading.close();
      });
    },
    back() {
      this.prevFlow();
    },
    openCallBack(item) {
      this.branchName = item.branchName;
      this.branchNo = item.branchNo;
    },
    /* 获取日期和周 */
    getDateWeek() {
      /* 得到当前日期的时间戳 */
      const timestamp = Date.now();
      let dateWeek = Array.from(new Array(9)).map((_, i) => {
        /* 得到当前周每一天的时间戳 */
        const weekTimestamp = new Date(timestamp + i * 24 * 60 * 60 * 1000);
        const date =
          String(weekTimestamp.getFullYear()) +
          '-' +
          String(weekTimestamp.getMonth() + 1).padStart(2, '0') +
          '-' +
          String(new Date(weekTimestamp).getDate()).padStart(2, '0');
        let week = weekTimestamp.getDay();
        return {
          label: date,
          week,
          id: date
        };
      });
      dateWeek = dateWeek.filter((item) => item.week != 0 && item.week != 6);
      this.columns = [
        dateWeek,
        [
          { label: '09:00~12:00', id: '09:00~12:00' },
          { label: '13:00~17:00', id: '13:00~17:00' }
        ]
      ];
    },
    toNext() {
      if (this.isCheck) return;
      this.nextFlow({
        advance_time: this.advanceTime,
        pre_branch_no: this.branchNo,
        audit_biz_type: this.flowOutputInfo.privProperty.auditBizType,
        pre_branch_name: this.branchName
      });
    }
  }
};
</script>
<style lang="scss" scoped></style>
