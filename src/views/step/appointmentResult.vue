<!--  -->
<template>
  <div>
    <section class="main fixed">
      <t-header :show-back="false"></t-header>
      <article class="content">
        <div class="result_page">
          <div v-if="showStep2">
            <div class="result_tips">
              <div class="icon ing"></div>
              <h5>两融开户预约申请提交成功！</h5>
              <p>
                预计<span class="ared">1</span>
                个工作日通过审核，请注意查收手机短信
              </p>
            </div>

            <div class="result_step">
              <ul>
                <li class="s1 off">
                  <i></i>
                  <span>申请提交</span>
                </li>
                <li class="s2" :class="{ on: showStep2, off: showStep3 }">
                  <i></i>
                  <span>审核中</span>
                </li>
                <li class="s3" :class="{ on: showStep3 }">
                  <i></i>
                  <span>预约成功</span>
                </li>
              </ul>
            </div>
          </div>

          <div v-if="showStep3">
            <div class="result_tips">
              <div class="icon ok"></div>
              <h5>恭喜您预约成功！</h5>
            </div>
          </div>

          <div class="result_info">
            <ul>
              <li>
                <span class="tit">营业部</span>
                <p>
                  {{ branchName
                  }}<i
                    v-if="showStep3 && ipIsMatch"
                    class="icon_local"
                    @click="toShowMap"
                  ></i>
                </p>
              </li>
              <li>
                <span class="tit">预约时间</span>
                <p :class="canChangeTime ? '' : 'error'">{{ advanceTime }}</p>
              </li>
            </ul>
          </div>
        </div>
        <div v-if="showStep3 && ipIsMatch" class="tips_box">
          <p>温馨提示:</p>
          <p>1、请您携带本人的身份证、手机、银行卡，于约定时间前往指定营业部</p>
          <p>2、抵达营业部后，请在工作人员指引下点击“临柜办理”按钮</p>
          <p>3、如需变更预约信息，请点击下方“修改预约信息"进行变更</p>
        </div>
      </article>
      <footer class="footer">
        <div class="ce_btn">
          <a
            v-if="!canChangeTime && !showStep3"
            class="p_button"
            @click="reStart"
            >重新预约</a
          >
          <a
            v-if="!showStep3"
            class="p_button"
            :class="canChangeTime ? '' : 'border'"
            @click="toNext"
            >返回首页</a
          >
          <a v-if="showStep3 && ipIsMatch" class="p_button" @click="toNext"
            >临柜办理</a
          >
          <a v-if="canChangeTime" class="p_button border" @click="change"
            >修改预约信息</a
          >
          <!-- <a class="p_button border" @click="flowReset">重新办理</a> -->
          <a v-if="showStep3" class="p_button border" @click="toIndex"
            >返回首页</a
          >
        </div>
      </footer>
    </section>
    <showMap ref="showMap"></showMap>

    <section v-if="showChange" class="main fixed" style="z-index: 1499">
      <t-header title="修改预约信息" @back="back"></t-header>
      <article class="content" style="background: #f2f2f4">
        <div class="com_title">请选择营业部</div>
        <div class="com_box">
          <div class="input_form">
            <div
              class="input_text text"
              @click="showOpenSelect = !showOpenSelect"
            >
              <span :class="changeBranchName ? 'tit active' : 'tit'"
                >营业部</span
              >
              <div class="dropdown" placeholder="请选择">
                {{ changeBranchName }}
              </div>
            </div>
            <div class="input_text text">
              <span :class="changeAdvanceTime ? 'tit active' : 'tit'"
                >预约时间</span
              >
              <div class="dropdown" placeholder="请选择">
                <h-picker
                  slot="right"
                  v-model="changeAdvanceTime"
                  :columns="columns"
                  separator=" "
                />
              </div>
            </div>
          </div>
        </div>
      </article>
      <footer class="footer">
        <div class="ce_btn">
          <a class="p_button" @click="changeBranch">确认修改</a>
        </div>
      </footer>
      <openDressSelect
        v-model="showOpenSelect"
        :default="branchInfo"
        :is-open-dress="true"
        @selCallBack="openCallBack"
      ></openDressSelect>
    </section>

    <div v-if="showConfirm">
      <div class="dialog_overlay"></div>
      <div class="layer_box spel">
        <div class="layer_tit">
          <a class="ly_opea_cancel" @click="showConfirm = false">取消</a>
          <h3>面见人身份验证</h3>
          <a class="ly_opea_sure" @click="confirm">确认</a>
        </div>
        <div class="layer_cont">
          <div class="com_box">
            <div class="input_form am_loginform">
              <div class="input_text">
                <multLineInput
                  id="appendPhone"
                  v-model="staff_no"
                  class="t1"
                  type="text"
                  :maxlength="11"
                  placeholder="请输入面见人员工编号"
                  autocomplete="off"
                />
              </div>
              <div class="input_text">
                <multLineInput
                  id="appendPhone"
                  v-model="staff_name"
                  class="t1"
                  type="text"
                  :maxlength="11"
                  placeholder="请输入面见人姓名"
                  autocomplete="off"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import flowMixin from '@/common/flowMixin';
import {
  getSysBranchInfo,
  queryIns,
  updateFlowForm,
  getImgCode,
  getUserIp,
  branchIpMatch
} from '@/service/service';
import openDressSelect from '@/components/openDressSelect';
import showMap from '@/components/showMap';
import multLineInput from '@/components/multLineInput';

export default {
  components: {
    openDressSelect,
    multLineInput,
    showMap
  },

  mixins: [flowMixin],
  data() {
    return {
      ipIsMatch: true,
      branchInfo: {},
      orderTime: '',

      staff_no: '',
      staff_name: '',
      imgCode: '',
      imgSrc: null,

      showChange: false,
      showConfirm: false,

      advanceTime: '',
      branchName: '',
      status: '0', // 任务状态 0：待办，1：办理中，2：通过，3：驳回，4：取消
      pageShow: false,

      showOpenSelect: false,
      branchNo: '',

      changeBranchName: '',
      changeBranchNo: '',
      changeAdvanceTime: '',
      columns: [
        [
          { label: '', id: '' },
          { label: '', id: '' },
          { label: '', id: '' }
        ],
        [
          { label: '', id: '12:00' },
          { label: '', id: '17:00' }
        ]
      ]
    };
  },

  computed: {
    showStep2() {
      console.log('=====>' + this.status);
      if (this.status === '0' || this.status === '1') {
        return true;
      } else {
        return false;
      }
    },

    showStep3() {
      if (this.status === '2') {
        return true;
      } else {
        return false;
      }
    },

    canChangeTime() {
      let nowTime = new Date().getTime();
      if (nowTime > this.orderTime) {
        return false;
      } else {
        return true;
      }
    }
  },

  methods: {
    back() {
      this.showChange = false;
    },
    change() {
      this.showChange = true;
    },
    imgClick() {
      getImgCode({}, { loading: false }).then((data) => {
        if (data.code === 0) {
          let results = data.data;
          this.imgSrc = results.image;
          this.captchaToken = results.token;
        }
      });
    },
    formatAdvanceTime(time) {
      let date = time.split(' ')[0];
      let lastHours = time.split('~')[1];
      let newTime = date + ' ' + lastHours;
      (newTime = newTime.substring(0, 16)),
        (newTime = newTime.replace(/-/g, '/'));
      let timeTep = new Date(newTime).getTime();
      return timeTep;
    },
    initData() {
      let _this = this;
      let { advanceTime, preBranchNo } = _this.flowOutputInfo.inProperty;
      this.advanceTime = advanceTime;
      this.orderTime = this.formatAdvanceTime(this.advanceTime);
      this.changeBranchNo = preBranchNo;
      this.changeAdvanceTime = advanceTime;
      getSysBranchInfo({ branchNo: preBranchNo }).then((res) => {
        if (res.code == 0) {
          this.branchName = res.data[0] && res.data[0].branchName;
          this.pageShow = true;
          this.changeBranchName = this.branchName;
          this.branchInfo = res.data[0];
        } else {
          this.pageShow = true;
          _hvueAlert({ mes: data.msg });
        }
      });
    },
    renderingView() {
      this.getDateWeek();
      this.initData();
      this.status = this.flowOutputInfo.taskStatus;
      _hvueLoading.close();
      branchIpMatch({ flowToken: sessionStorage.getItem('TKFlowToken') }).then(
        (res) => {
          // this.ipIsMatch = res.data;
        }
      );
      // this._getImgCode();
    },
    reStart() {
      this.prevFlow();
      // import('@/common/flowMixin.js').then(a => {
      // 	a.initFlow.call(this, this.flowOutputInfo.inProperty.bizType, true);
      // });
    },
    toShowMap() {
      console.log(this);
      this.$refs.showMap.showSelect();
    },
    toNext() {
      if (this.showStep3) {
        this.showConfirm = true;
        this.imgClick();
        // this.nextFlow();
      } else {
        // 返回首页
        this.toIndex();
      }
    },
    confirm() {
      getUserIp().then();
      if (this.staff_name && this.staff_no) {
        this.nextFlow({
          staff_name: this.staff_name,
          staff_no: this.staff_no
        });
      } else {
        if (!this.staff_name) {
          _hvueToast({
            mes: '请输入面见人姓名'
          });
          return;
        } else {
          _hvueToast({
            mes: '请输入面见人编号'
          });
          return;
        }
      }
    },
    openCallBack(item) {
      this.changeBranchName = item.branchName;
      this.changeBranchNo = item.branchNo;
    },
    changeBranch() {
      updateFlowForm({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        bizType: this.flowOutputInfo.inProperty.bizType,
        formParam: {
          preBranchNo: this.changeBranchNo,
          advanceTime: this.changeAdvanceTime,
          preBranchName: this.changeBranchName
        }
      }).then((res) => {
        if (res.code === 0) {
          this.showChange = false;
          this.getFlowInfo();
        }
      });
    },
    /* 获取日期和周 */
    getDateWeek() {
      /* 得到当前日期的时间戳 */
      const timestamp = Date.now();
      let dateWeek = Array.from(new Array(9)).map((_, i) => {
        /* 得到当前周每一天的时间戳 */
        const weekTimestamp = new Date(timestamp + i * 24 * 60 * 60 * 1000);
        const date =
          String(weekTimestamp.getFullYear()) +
          '-' +
          String(weekTimestamp.getMonth() + 1).padStart(2, '0') +
          '-' +
          String(new Date(weekTimestamp).getDate()).padStart(2, '0');
        let week = weekTimestamp.getDay();
        return {
          label: date,
          week,
          id: date
        };
      });
      dateWeek = dateWeek.filter((item) => item.week != 0 && item.week != 6);
      this.columns = [
        dateWeek,
        [
          { label: '09:00~12:00', id: '09:00~12:00' },
          { label: '13:00~17:00', id: '13:00~17:00' }
        ]
      ];
    }
  }
};
</script>
<style lang="scss" scoped></style>
