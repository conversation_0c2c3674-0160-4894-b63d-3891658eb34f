<template>
  <section class="main fixed" data-page="home">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="com_title">
        <h5>三方存管</h5>
      </div>
      <h5 class="mid_title">请绑定持卡人本人的银行借记卡(不能是信用卡)</h5>
      <div class="com_box">
        <div class="input_form">
          <div class="input_text text">
            <span :class="name ? 'tit active' : 'tit'">{{
              isBankCardNo ? '持卡人' : '机构名称'
            }}</span>
            <input
              v-model="name"
              class="t1 disabled"
              type="text"
              :disabled="!isBankCardNo"
              placeholder="请输入"
            />
          </div>
          <div v-if="upCardData.src" class="tpbank_img">
            <img :src="upCardData.src" />
          </div>
          <div v-if="isBankCardNo" class="input_text text">
            <span :class="cardNo ? 'tit active' : 'tit'">银行卡号</span>
            <input
              v-model="cardNo"
              class="t1"
              type="text"
              placeholder="请输入银行卡号"
            />
            <a class="icon_photo" @click="choseBank"></a>
          </div>
          <div class="input_text text" @click="showChoseBank = !showChoseBank">
            <span :class="bankName ? 'tit active' : 'tit'">所属银行</span>
            <div class="dropdown" placeholder="请选择">{{ bankName }}</div>
          </div>
          <!-- <div class="input_text text" v-if="bankName">
						<span :class="bkPassword ? 'tit active' : 'tit'">请输入6位资金数字密码</span>
						<input
							class="t1"
							type="password"
							v-model="bkPassword"
							placeholder="请输入6位资金数字密码"
						/>
					</div>
					<div class="input_text text" v-if="bankName">
						<span :class="conBkPassword ? 'tit active' : 'tit'">请重复输入6位资金数字密码</span>
						<input
							class="t1"
							type="password"
							v-model="conBkPassword"
							placeholder="请重复输入6位资金数字密码"
						/>
					</div> -->
        </div>
      </div>
      <div v-if="agreeDetail.agreementName" class="rule_check">
        <span
          class="icon_check"
          :class="{ checked: checkEpaper }"
          @click="signAgree"
        ></span
        >本人已详细阅读并同意签署以下协议<a @click="openAgree"
          >《{{ agreeDetail.agreementName }}》</a
        >
      </div>
      <div class="tpbank_zcbox">
        <h5 class="title">我们支持的银行借记卡如下：</h5>
        <ul class="available_bklist">
          <li>
            <img src="@/assets/images/bank/bk15.png" /><span>中信银行</span>
          </li>
          <li>
            <img src="@/assets/images/bank/bk08.png" /><span>平安银行</span>
          </li>
          <li>
            <img src="@/assets/images/bank/bk17.png" /><span>华夏银行</span>
          </li>
          <li>
            <img src="@/assets/images/bank/bk02.png" /><span>工商银行</span>
          </li>
          <li>
            <img src="@/assets/images/bank/bk01.png" /><span>中国银行</span>
          </li>
          <li>
            <img src="@/assets/images/bank/bk04.png" /><span>建设银行</span>
          </li>
        </ul>
      </div>
    </article>
    <!-- <article class="content" v-if="showConCard">
			<div class="com_title">
				<h5>三方存管</h5>
			</div>
			<h5 class="mid_title">请核对卡号信息，若有误可点击号码修改</h5>
			<div class="bank_cardbox">
				<div class="pic"><img :src="upCardData.src" /></div>
				<ul class="text_list">
					<li><input type="text" v-model="upCardData.cardList[0]" /></li>
					<li><input type="text" v-model="upCardData.cardList[1]" /></li>
					<li><input type="text" v-model="upCardData.cardList[2]" /></li>
					<li><input type="text" v-model="upCardData.cardList[3]" /></li>
				</ul>
			</div>
		</article> -->
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="toNext">下一步</a>
      </div>
    </footer>
    <agreementDetail
      :show="showAgreeDetail"
      :info="agreeDetail"
      :is-count="true"
      @callback="agreeCallBack"
    />
    <selBank
      v-model="showChoseBank"
      id-string="bankName"
      :default-str="bankName"
      :init-data="bankList"
      @selCallback="choseBankCallback"
    ></selBank>
    <getImgBoxBrowser
      ref="getImgBoxBrowser"
      :scan="true"
      @getImgCallBack="getImgCallBack"
    ></getImgBoxBrowser>
  </section>
</template>

<script>
import getImgBoxBrowser from '@/components/getImg_browser';
import selBank from '@/components/selBank';
import { uploadFile } from '@/common/util.js';
import { getBank, queryAgreement, getJwtToken } from '@/service/service';
import agreementDetail from '@/components/agreementDetail';
import flowMixin from '@/common/flowMixin';
export default {
  components: {
    getImgBoxBrowser,
    agreementDetail,
    selBank
  },
  mixins: [flowMixin],
  data() {
    return {
      isBankCardNo: false, //是否需要银行卡号
      upCardData: {},
      showAgreeDetail: false,
      agreeDetail: {},
      canCheckEpaper: false,
      showChoseBank: false,
      showConCard: false,
      bankList: [],
      name: '',
      cardNo: '',
      bankName: '',
      bankNo: '',
      bkPassword: '',
      conBkPassword: '',
      epaper_sign_json: '',
      checkEpaper: false
    };
  },
  methods: {
    back() {
      this.prevFlow();
      // if (this.showConCard) {
      // 	this.showConCard = !this.showConCard;
      // } else {
      // 	this.prevFlow();
      // }
    },

    openAgree() {
      this.showAgreeDetail = true;
    },

    signAgree() {
      if (!this.canCheckEpaper) {
        _hvueToast({
          mes: '请先阅读协议'
        });
        return;
      }
      this.checkEpaper = !this.checkEpaper;
    },

    agreeCallBack(flag) {
      this.showAgreeDetail = false;
      if (flag) {
        this.canCheckEpaper = true;
        this.checkEpaper = true;
      }
    },

    renderingView() {
      this.isBankCardNo =
        this.flowOutputInfo.privProperty.isBankCardNo === '1' ? true : false;
      this.name = this.flowOutputInfo.inProperty.organName;
      getBank().then((res) => {
        this.bankList = res.data.map((item) => {
          _hvueLoading.close();
          return Object.assign(item, {
            bankLogo: `${$hvue.customConfig.fileUrl}${item.bankLogo}`
          });
        });
      });
    },

    async toNext() {
      // if (this.showConCard) {
      // } else {

      // }
      if (!this.bankName) {
        _hvueToast({
          mes: '请先选择银行'
        });
        return;
      }
      if (!this.checkEpaper) {
        _hvueToast({
          mes: '请阅读并勾选协议'
        });
        return;
      }
      if (this.check()) {
        let agreePrintJson = JSON.parse(
          this.flowOutputInfo.outProperty.agreePrintJson
        );
        let hasIndex = agreePrintJson.findIndex(
          (item) =>
            item.groupId ===
            JSON.parse(this.flowOutputInfo.privProperty.epaperRegisterJson)[0]
              .groupId
        );
        if (hasIndex >= 0) {
          agreePrintJson[hasIndex] = {
            agreementId: this.agreeDetail.agreementId,
            groupId: this.agreeDetail.groupId,
            contractType: this.agreeDetail.contractType,
            agreementSubtype: this.agreeDetail.agreementSubtype,
            agreementName: this.agreeDetail.agreementName
          };
        } else {
          agreePrintJson.push({
            agreementId: this.agreeDetail.agreementId,
            groupId: this.agreeDetail.groupId,
            contractType: this.agreeDetail.contractType,
            agreementSubtype: this.agreeDetail.agreementSubtype,
            agreementName: this.agreeDetail.agreementName
          });
        }
        this.nextFlow({
          bank_no: this.cardNo,
          bank_name: this.bankName,
          bk_password: this.bkPassword,
          epaperSignJson: this.flowOutputInfo.inProperty.epaperSignJson,
          epaper_sign_json: this.flowOutputInfo.inProperty.epaperSignJson,
          agree_print_json: JSON.stringify(agreePrintJson)
        });
      }
    },

    check() {
      if (this.isBankCardNo) {
        if (!this.name || !this.cardNo || !this.bankName) {
          _hvueToast({
            mes: '请完成表单项的填写！'
          });
          return false;
        }
        if (this.cardNo.length < 12 || this.cardNo.length > 23) {
          _hvueToast({
            mes: '银行卡号格式不正确！'
          });
          return false;
        }
        return true;
      } else {
        return true;
      }
    },

    async choseBankCallback(data) {
      this.bankName = data.data.bankName;
      this.bankNo = data.data.bankNo;
      let bankId = this.bankList.filter(
        (item) => item.bankFullName === this.bankName
      )[0].bankId;
      const tokenRes = await getJwtToken({
        flowNo: this.flowOutputInfo.flowNodeNo,
        businessType: this.flowOutputInfo.inProperty.bizType
      });
      $h.setSession('jwtToken', tokenRes.data);
      queryAgreement({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        bizType: this.flowOutputInfo.inProperty.bizType,
        groupId: JSON.parse(
          this.flowOutputInfo.privProperty.epaperRegisterJson
        )[0].groupId,
        contractType: JSON.parse(
          this.flowOutputInfo.privProperty.epaperRegisterJson
        )[0].contractType,
        agreementSubtype: bankId
      }).then((res) => {
        this.agreeDetail = res.data[0];
        this.agreeDetail.bankId = bankId;
      });
    },

    choseBank() {
      this.$refs.getImgBoxBrowser.getImg();
    },

    getImgCallBack(imgInfo) {
      uploadFile(
        $hvue.customConfig.serverUrl + '/client/ocrParseBankCard',
        // this.dataURLtoFile('data:image/jpeg;base64,' + imgInfo.base64),
        imgInfo.base64,
        {
          success: async (data) => {
            _hvueLoading.close();
            if (data.code == 0) {
              this.upCardData = data.data;
              this.upCardData.src =
                'data:image/jpeg;base64,' + data.data.cropImage;
              // this.upCardData.cardList = data.data.cardNumber.split(' ');
              // this.showConCard = true;
              this.bankName = this.upCardData.issuer;
              this.bankList.forEach((item) => {
                if (item.bankName === this.bankName) {
                  this.bankNo = item.bankNo;
                }
              });
              if (!this.bankName) {
                _hvueToast({
                  mes: '请使用正确的银行卡识别'
                });
                return;
              }
              // 查询银行对应协议
              let bankIndex = this.bankList.filter(
                (item) => item.bankFullName === this.bankName
              )[0];
              let bankId = '';
              if (bankIndex) {
                this.cardNo = data.data.cardNumber.replace(/\s+/g, '');
                bankId = bankIndex.bankId;
              } else {
                this.bankName = '';
                this.upCardData.src = '';
                _hvueToast({
                  mes: '请使用我们支持的银行卡识别'
                });
                return;
              }
              const tokenRes = await getJwtToken({
                flowNo: this.flowOutputInfo.flowNodeNo,
                businessType: this.flowOutputInfo.inProperty.bizType
              });
              $h.setSession('jwtToken', tokenRes.data);
              queryAgreement({
                flowToken: sessionStorage.getItem('TKFlowToken'),
                bizType: this.flowOutputInfo.inProperty.bizType,
                groupId: JSON.parse(
                  this.flowOutputInfo.privProperty.epaperRegisterJson
                )[0].groupId,
                contractType: JSON.parse(
                  this.flowOutputInfo.privProperty.epaperRegisterJson
                )[0].contractType,
                agreementSubtype: bankId
              }).then((res) => {
                this.agreeDetail = res.data[0];
                this.agreeDetail.bankId = bankId;
                this.showConCard = false;
              });
            } else {
              _hvueAlert({ mes: data.msg });
            }
          },
          progress: (count) => {
            console.log(count);
          },
          error: (e) => {
            _hvueLoading.close();
            console.log(e);
          }
        },
        {}
      );
    }
  }
};
</script>
