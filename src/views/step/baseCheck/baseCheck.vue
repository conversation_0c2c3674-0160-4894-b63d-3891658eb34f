<template>
  <section class="main fixed">
    <t-header @back="back"></t-header>
    <div v-if="dataDetails.ifrCustomerinfoPerfect === '0'">
      <div class="dialog_overlay"></div>
      <div class="dialog_box">
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>基本信息未完整</h3>
          <div>
            <p>您的基本信息未完善，请您先去完善。</p>
          </div>
        </div>
        <div class="dialog_btn">
          <a href="#">前往完善</a>
        </div>
      </div>
    </div>
    <div v-else-if="dataDetails.ifrCustomercodeValidity === '0'">
      <div class="dialog_overlay"></div>
      <div class="dialog_box">
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>身份证有效期已过期</h3>
          <div>
            <p>您的身份证有效期已过期，请您先去更新。</p>
          </div>
        </div>
        <div class="dialog_btn">
          <a href="#">前往完善</a>
        </div>
      </div>
    </div>
    <div v-else-if="dataDetails.ifrCustomerriskValidity === '0'">
      <div class="dialog_overlay"></div>
      <div class="dialog_box">
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>风险测评已过期</h3>
          <div>
            <p>您的风险测评已过期，请您先重新测评。</p>
          </div>
        </div>
        <div class="dialog_btn">
          <a href="#">前往完善</a>
        </div>
      </div>
    </div>
    <div v-else-if="dataDetails.ifrCustomersignDzym === '0'">
      <div class="dialog_overlay"></div>
      <div class="dialog_box">
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>电子签名约定书未签订</h3>
          <div>
            <p>电子签名约定书未签订</p>
          </div>
        </div>
        <div class="dialog_btn">
          <a href="#">前往完善</a>
        </div>
      </div>
    </div>
    <!-- <article class="content">
			<div class="business_page">
				<div class="add-business_result" v-if="dataDetails.ifrCustomerinfoPerfect === '0'">
					<div class="add-result_box">
						<img src="@/assets/images/result_img03.png" alt="" />
						<h2>基本信息未完整</h2>
						<p class="txt">您的基本信息未完善，请您先去完善。</p>
					</div>
				</div>
				<div class="business_page" v-else-if="dataDetails.ifrCustomercodeValidity === '0'">
					<div class="add-business_result">
						<div class="add-result_box">
							<img src="@/assets/images/result_img04.png" alt="" />
							<h2>身份证有效期已过期</h2>
							<p class="txt">您的身份证有效期已过期，请您先去更新。</p>
						</div>
					</div>
				</div>
				<div class="business_page" v-else-if="dataDetails.ifrCustomerriskValidity === '0'">
					<div class="add-business_result">
						<div class="add-result_box">
							<img src="@/assets/images/result_img05.png" alt="" />
							<h2>风险测评已过期</h2>
							<p class="txt">您的风险测评已过期，请您先重新测评。</p>
						</div>
					</div>
				</div>
				<div class="business_page" v-else-if="dataDetails.ifrCustomersignDzym === '0'">
					<div class="add-business_result">
						<div class="add-result_box">
							<img src="@/assets/images/result_img06.png" alt="" />
							<h2>电子签名约定书未签订</h2>
							<p class="txt">电子签名约定书未签订</p>
						</div>
					</div>
				</div>
			</div>
		</article> -->
  </section>
</template>

<script>
import { baseInfoCheck } from '@/service/service.js';
import flowMixin from '@/common/flowMixin';

export default {
  mixins: [flowMixin],
  data() {
    return {
      dataDetails: {}
    };
  },
  computed: {
    isAllPassed() {
      return Object.values(this.dataDetails).every((a) => {
        return a !== '0';
      });
    }
  },
  methods: {
    back() {
      this.prevFlow();
    },

    renderingView() {
      this.enterCheck();
    },

    enterCheck() {
      let _this = this;
      console.log(_this.flowOutputInfo);
      let { clientId, fundAccount } = _this.flowOutputInfo.privProperty;
      let {
        isCustomerinfoPerfectCheck,
        isCustomercodeValidityCheck,
        isCustomerriskValidityCheck,
        isCustomersignDzymCheck
      } = _this.flowOutputInfo.privProperty;
      baseInfoCheck({
        clientId,
        fundAccount,
        isCustomerinfoPerfectCheck,
        isCustomercodeValidityCheck,
        isCustomerriskValidityCheck,
        isCustomersignDzymCheck
      })
        .then((data) => {
          _this.dataDetails = data.data;
          console.log(_this.dataDetails);
          if (_this.isAllPassed) {
            this.nextFlow();
          }
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    }
  }
};
</script>

<style></style>
