<template>
  <section class="main fixed">
    <t-header @back="back"></t-header>
    <div v-if="show">
      <div class="dialog_overlay"></div>
      <div class="dialog_box">
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>{{ tips.title }}</h3>
          <div>
            <p>{{ tips.tips }}</p>
          </div>
        </div>
        <div class="dialog_btn">
          <a href="">前往完善</a>
        </div>
      </div>
      <!-- <div
        v-if="dataDetails.ifrCustomercodeValidity === '0'"
        class="dialog_box"
      >
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>身份证有效期已过期</h3>
          <div>
            <p>抱歉，您的身份证有效期已过期，请前往更新。</p>
          </div>
        </div>
        <div class="dialog_btn">
          <a href="#">前往完善</a>
        </div>
      </div>
      <div
        v-if="dataDetails.ifrCustomerIdNoValidity === '0'"
        class="dialog_box"
      >
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>身份证未升位</h3>
          <div>
            <p>抱歉，您的身份证不足18位，请前往更新。</p>
          </div>
        </div>
        <div class="dialog_btn">
          <a href="#">前往完善</a>
        </div>
      </div>
      <div
        v-else-if="dataDetails.ifrCustomerinfoPerfect === '0'"
        class="dialog_box"
      >
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>基本信息未完善</h3>
          <div>
            <p>抱歉，您的基本信息未完善，请前往完善。</p>
          </div>
        </div>
        <div class="dialog_btn">
          <a href="#">前往完善</a>
        </div>
      </div>
      <div
        v-else-if="dataDetails.ifrCustomersignDzym === '0'"
        class="dialog_box"
      >
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>电子签名书未签订</h3>
          <div>
            <p>抱歉，您的电子签名书未签订，请前往签订。</p>
          </div>
        </div>
        <div class="dialog_btn">
          <a href="#">前往完善</a>
        </div>
      </div>
      <div
        v-else-if="dataDetails.ifrCustomerriskValidity === '0'"
        class="dialog_box"
      >
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>风险测评有效期已过期</h3>
          <div>
            <p>抱歉，您的风险测评有效期已过期，请前往重新测评。</p>
          </div>
        </div>
        <div class="dialog_btn">
          <a href="#">前往完善</a>
        </div>
      </div> -->
    </div>
  </section>
</template>

<script>
import { baseInfoCheck } from '@/service/service.js';
import flowMixin from '@/common/flowMixin';

export default {
  mixins: [flowMixin],
  data() {
    return {
      // dataDetails: {}
      show: false,
      tips: {}
    };
  },
  computed: {
    isAllPassed() {
      return Object.values(this.dataDetails).every((a) => {
        return a !== '0';
      });
    }
  },
  methods: {
    back() {
      this.prevFlow();
    },

    renderingView() {
      this.enterCheck();
    },

    enterCheck() {
      // console.log(_this.flowOutputInfo);
      // let { clientId, fundAccount } = _this.flowOutputInfo.privProperty;
      // let {
      //   isCustomerinfoPerfectCheck,
      //   isCustomercodeValidityCheck,
      //   isCustomerriskValidityCheck,
      //   isCustomersignDzymCheck
      // } = _this.flowOutputInfo.privProperty;
      baseInfoCheck({
        flowToken: sessionStorage.getItem('TKFlowToken')
      })
        .then((data) => {
          _hvueLoading.close();
          // _this.dataDetails = data.data;
          if (data.data.strategyResult !== '0') {
            this.nextFlow();
          } else {
            this.show = true;
            this.tips = JSON.parse(data.data.tips);
          }
        })
        .catch((err) => {
          _hvueLoading.close();
          _hvueToast({
            mes: err
          });
        });
    }
  }
};
</script>

<style></style>
