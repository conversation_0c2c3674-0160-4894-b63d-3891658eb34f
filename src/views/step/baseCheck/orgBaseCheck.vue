<template>
  <section class="main fixed">
    <t-header @back="back"></t-header>
    <div>
      <div class="dialog_overlay"></div>
      <div v-if="dataDetails.ifrBusinessValidity === '0'" class="dialog_box">
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>营业执照有效期已过期</h3>
          <div>
            <p>抱歉，您的营业执照有效期已过期，请前往更新。</p>
          </div>
        </div>
        <div class="dialog_btn">
          <a href="#">前往完善</a>
        </div>
      </div>
      <div
        v-else-if="dataDetails.ifrCustomerinfoPerfect === '0'"
        class="dialog_box"
      >
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>基本信息未完善</h3>
          <div>
            <p>抱歉，您的基本信息未完善，请前往完善。</p>
          </div>
        </div>
        <div class="dialog_btn">
          <a href="#">前往完善</a>
        </div>
      </div>
      <div
        v-else-if="dataDetails.ifrCustomerriskValidity === '0'"
        class="dialog_box"
      >
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>风险测评有效期已过期</h3>
          <div>
            <p>抱歉，您的风险测评有效期已过期，请前往重新测评。</p>
          </div>
        </div>
        <div class="dialog_btn">
          <a href="#">前往完善</a>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import { orgPreConditionCheck } from '@/service/service.js';
import flowMixin from '@/common/flowMixin';

export default {
  mixins: [flowMixin],
  data() {
    return {
      dataDetails: {}
    };
  },
  computed: {
    isAllPassed() {
      return Object.values(this.dataDetails).every((a) => {
        return a !== '0';
      });
    }
  },
  methods: {
    back() {
      this.prevFlow();
    },

    renderingView() {
      this.enterCheck();
    },

    enterCheck() {
      let _this = this;
      let { clientId, fundAccount } = _this.flowOutputInfo.privProperty;
      let {
        isBusinessValidityCheck,
        isCustomerinfoPerfectCheck,
        isCustomerriskValidityCheck
      } = _this.flowOutputInfo.privProperty;
      orgPreConditionCheck({
        clientId,
        fundAccount,
        isBusinessValidityCheck,
        isCustomerinfoPerfectCheck,
        isCustomerriskValidityCheck
      })
        .then((data) => {
          _hvueLoading.close();
          _this.dataDetails = data.data;
          if (_this.isAllPassed) {
            this.nextFlow();
          }
        })
        .catch((err) => {
          _hvueLoading.close();
          _hvueToast({
            mes: err
          });
        });
    }
  }
};
</script>

<style></style>
