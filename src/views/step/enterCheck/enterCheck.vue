<template>
  <section class="main fixed">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="business_page">
        <div class="add-condition_check">
          <h2 v-if="!allPass" class="error">
            抱歉，您不满足以下办理条件,无法办理此业务
          </h2>
          <h2 v-if="allPass" class="succ">恭喜，您满足以下办理条件</h2>
          <ul class="add-check_list border_top">
            <li
              v-for="(item, index) in viewList"
              :key="index"
              class="border_bottom"
              :class="item.ruleResult === '1' ? 'succ' : 'error'"
            >
              <p class="tit">{{ item.ruleName }}</p>
              <p v-if="item.ruleResult === '0'" class="p1">
                您不存在正常未开通权限的特转A账户，不满足办理条件
              </p>
            </li>
          </ul>
        </div>
      </div>
    </article>
    <footer>
      <div class="add-btn_box">
        <a v-if="allPass" @click="toNext">确认</a>
        <a v-if="!allPass" @click="toIndex">返回首页</a>
      </div>
    </footer>
  </section>
</template>

<script>
import { businessEgliCheck, getBizName } from '@/service/service.js';
import flowMixin from '@/common/flowMixin';
import { RULE_RESULT } from '@/common/enumeration';

export default {
  mixins: [flowMixin],
  data() {
    return {
      viewList: [],
      bizName: '',
      allPass: false,
      RULE_RESULT
    };
  },
  methods: {
    back() {
      this.prevFlow();
    },

    toNext() {
      this.nextFlow();
    },

    renderingView() {
      let _this = this;
      const { bizType } = _this.flowOutputInfo.inProperty;
      const { strategyNo } = _this.flowOutputInfo.privProperty;
      const flowToken = sessionStorage.getItem('TKFlowToken');
      getBizName({ bizType }).then((data) => {
        _this.bizName = data.data.bizName;
      });
      businessEgliCheck({
        bizType,
        flowToken,
        strategyNo
      })
        .then((data) => {
          _this.viewList = data.data.result;
          const strategyResult = data.data.strategyResult;
          if (strategyResult === RULE_RESULT.pass) {
            _this.allPass = true;
            // this.nextFlow();
          } else {
            _this.viewList = data.data.result;
          }
        })
        .catch((err) => {
          _hvueToast({ mes: err });
        });
    }
  }
};
</script>

<style></style>
