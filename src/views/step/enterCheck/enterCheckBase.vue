<template>
  <section class="main fixed white_bg">
    <t-header @back="back"></t-header>
    <article class="content">
      <div v-if="pageShow" class="com_box">
        <div class="com_title">
          <h5>办理此业务需要满足以下条件</h5>
        </div>
        <ul class="cond_list">
          <li
            v-for="(item, index) in viewList"
            :key="index"
            class="ok"
            :class="item.ruleResult === '1' ? 'ok' : 'error'"
          >
            <div class="tit">
              <h5>{{ JSON.parse(item.ruleResultDesc).title }}</h5>
              <p>{{ JSON.parse(item.ruleResultDesc).tips }}</p>
            </div>
          </li>
        </ul>
        <div v-if="!allPass" class="cond_tips">
          <p>抱歉，您不满足办理条件，无法办理此业务，详情可咨询客服人员。</p>
        </div>
      </div>
    </article>
    <!-- <article class="content">
			<div class="business_page">
				<div class="add-condition_check">
					<h2 class="error" v-if="!allPass">抱歉，您不满足以下办理条件,无法办理此业务</h2>
					<h2 class="succ" v-if="allPass">恭喜，您满足以下办理条件</h2>
					<ul class="add-check_list border_top">
						<li
							v-for="(item, index) in viewList"
							:key="index"
							class="border_bottom"
							:class="item.ruleResult === '1' ? 'succ' : 'error'"
						>
							<p class="tit">{{ item.ruleName }}</p>
							<p class="p1" v-if="item.ruleResult === '0'">
								您不存在正常未开通权限的特转A账户，不满足办理条件
							</p>
						</li>
					</ul>
				</div>
			</div>
		</article> -->
    <footer>
      <div class="ce_btn">
        <a v-if="allPass" class="p_button" @click="toNext">确认</a>
        <a v-if="!allPass" class="p_button" @click="toNext">返回首页</a>
      </div>
    </footer>
  </section>
</template>

<script>
import { businessEgliCheck, getBizName } from '@/service/service.js';
import flowMixin from '@/common/flowMixin';
import { RULE_RESULT } from '@/common/enumeration';

export default {
  mixins: [flowMixin],
  data() {
    return {
      viewList: [],
      bizName: '',
      allPass: false,
      RULE_RESULT,
      pageShow: false
    };
  },
  methods: {
    back() {
      this.prevFlow();
    },

    toNext() {
      this.nextFlow();
    },

    renderingView() {
      let _this = this;
      const { bizType } = _this.flowOutputInfo.inProperty;
      // const { strategyNo } = _this.flowOutputInfo.privProperty;
      let taskStatus = this.flowOutputInfo.taskStatus;
      const flowToken = sessionStorage.getItem('TKFlowToken');
      getBizName({ bizType }).then((data) => {
        _this.bizName = data.data.bizName;
      });
      businessEgliCheck({
        bizType,
        flowToken
        // strategyNo
      })
        .then((data) => {
          _hvueLoading.close();
          _this.pageShow = true;
          _this.viewList = data.data.result;
          const strategyResult = data.data.strategyResult;
          if (strategyResult === RULE_RESULT.pass) {
            _this.allPass = true;
            if (taskStatus) {
              this.nextFlow();
            }
            // this.nextFlow();
          } else {
            _this.viewList = data.data.result;
          }
        })
        .catch((err) => {
          _hvueLoading.close();
          _hvueToast({ mes: err });
        });
    }
  }
};
</script>

<style></style>
