<template>
  <section class="main fixed" data-page="home">
    <t-header @back="back"></t-header>
    <article v-show="faceRecognitionStat === 1" class="content">
      <div class="fc_basebox">
        <h5>请进行人脸识别来认证身份</h5>
        <p>为了确保本人操作我们将进行人脸识别</p>
        <div class="pic"><img src="@/assets/images/video_face2.png" /></div>
      </div>
      <div class="lz_tipbox">
        <h5 class="title">视频环节请注意以下事项：</h5>
        <ul>
          <li>
            <i><img src="@/assets/images/p_fc_tp01.png" /></i
            ><span>确保光线清晰</span>
          </li>
          <li>
            <i><img src="@/assets/images/p_fc_tp04.png" /></i
            ><span>远离嘈杂环境</span>
          </li>
          <li>
            <i><img src="@/assets/images/p_fc_tp03.png" /></i
            ><span>不能带帽子</span>
          </li>
        </ul>
      </div>
    </article>

    <article v-show="faceRecognitionStat === 2" class="content">
      <div class="fc_sbbox">
        <div class="fc_imgbox ing">
          <div class="pic"><img src="@/assets/images/fc_default.png" /></div>
        </div>
        <h5>识别中…</h5>
      </div>
    </article>

    <article v-show="faceRecognitionStat === 3" class="content">
      <div class="video_compage">
        <div class="review_video">
          <div class="pic">
            <img ref="faceImg" src="@/assets/images/fc_default.png" />
          </div>
          <div class="vd_result_box"><span class="ok">人脸识别通过</span></div>
        </div>
        <div class="video_info">
          <h5>系统将留存此照片，请确认后提交!</h5>
          <ul>
            <li><i></i>须为正面免冠照片</li>
            <li><i></i>须完整面部出镜，无反光、遮挡、五官不清晰等情形</li>
          </ul>
        </div>
      </div>
    </article>

    <article v-show="faceRecognitionStat === 4" class="content">
      <div class="notice_box spel">
        <div class="pic"><img src="@/assets/images/notice_error.png" /></div>
        <h5 class="error">人脸识别不通过</h5>
        <p>自拍照与证件照不是同一人</p>
      </div>
    </article>

    <livingRecognitionBrowser
      v-if="faceRecognitionStat === 11"
      ref="livingRecognitionBrowser"
      :user-info="userInfo"
      @cancel="livingCancel"
      @getImgCallBack="getImgCallBack"
    ></livingRecognitionBrowser>

    <footer
      v-show="
        faceRecognitionStat === 1 ||
        faceRecognitionStat === 4 ||
        faceRecognitionStat === 3
      "
      class="footer"
    >
      <div class="ce_btn">
        <a v-if="faceRecognitionStat === 1" class="p_button" @click="start"
          >开始认证</a
        >
      </div>
      <div class="ce_btn">
        <a v-if="faceRecognitionStat === 4" class="p_button" @click="start"
          >开始认证</a
        >
      </div>
      <div class="ce_btn">
        <a v-if="faceRecognitionStat === 3" class="p_button" @click="nextClick"
          >下一步</a
        >
      </div>
    </footer>
  </section>
</template>

<script>
import flowMixin from '@/common/flowMixin';
import livingRecognitionBrowser from '@/components/witnessAuth/one/livingRecognition_browser';
import { submitLivingLog, faceRecognition } from '@/service/videoOneService.js';
import {
  videoGetJwtToken,
  videoOneRegist
  // faceCompare
} from '@/service/service.js';
import { uploadFile } from '@/common/util.js';
import { filterBase64Pre } from '@/common/util.js';
export default {
  components: { livingRecognitionBrowser },
  mixins: [flowMixin],
  data() {
    return {
      faceRecognitionStat: 1, //人脸识别状态 1人脸识别准备 2识别中 3识别成功 4识别失败 5视频录制准备 6视频上传中
      livingLogParam: {},
      faceFailCount: 0,
      regResult: {},
      userInfo: {},
      faceImage: '',
      upfaceImage: ''
    };
  },
  deactivated() {
    this.faceRecognitionStat = 1;
  },
  activated() {
    this.faceFailCount = 0;
  },
  methods: {
    back() {
      if (
        this.faceRecognitionStat === 1 ||
        this.faceRecognitionStat === 4 ||
        this.faceRecognitionStat === 3
      ) {
        // 返回上一步
        this.prevFlow();
      } else {
        this.faceRecognitionStat = 1;
      }
    },

    renderingView() {
      _hvueLoading.close();
      this.faceImage = this.flowOutputInfo.inProperty.contactIdCardPortrait
        ? this.flowOutputInfo.inProperty.contactIdCardPortrait
        : this.flowOutputInfo.inProperty.instreprIdCardPortrait;
    },

    livingCancel() {
      this.faceRecognitionStat = 1;
    },

    getRandAction(num) {
      var actionArr = ['0', '1', '2', '3']; // 0:眨眼 1:上下点头 2:张嘴 3:左右转头
      var resultArr = [];
      if (!num) num = 1;
      if (num > actionArr.length) num = actionArr.length;
      for (var i = 0; i < num; i++) {
        var rand = Math.floor(Math.random() * actionArr.length);
        resultArr.push(actionArr.splice(rand, 1));
      }
      let nameArr = [];
      let nameMap = {
        0: 'blink',
        1: 'nod ',
        2: 'mouth',
        3: 'yaw'
      };
      resultArr.forEach((a) => {
        nameArr.push(nameMap[a]);
      });
      return {
        num: resultArr.join(','),
        name: nameArr.join('|')
      };
    },

    async start() {
      let jwt = await videoGetJwtToken({
        businessType: this.flowOutputInfo.inProperty.bizType,
        flowNo: this.flowOutputInfo.flowNodeNo
      });
      let jwtToken = jwt.data.jwtToken;
      $h.setSession('videoToken', jwtToken);
      let result = await videoOneRegist({
        jwtToken: jwtToken,
        imgUrl: this.faceImage
      });
      if (result.code == 0) {
        this.regResult = result.data;
        if ($hvue.platform == 0) {
          this.userInfo = Object.assign(this.userInfo, {
            flow_no: this.regResult.regFlowNo,
            authorization: this.regResult.jwtToken,
            confirm_tips: `我是${this.flowOutputInfo.inProperty.clientName}，已知晓两融开户风险，已阅读并充分理解网上开户协议条款，自愿选择在思迪开立账户，并承诺所提供的信息及证件真实、合法、有效。`,
            tips: [
              {
                tip_content:
                  '尊敬的客户，请您在“滴”声后大声使用“是”或“不是”回答以下问题。'
              },
              {
                tip_content:
                  '您是否为<em style="color:red;">' +
                  this.flowOutputInfo.inProperty.clientName +
                  '</em>本人，且已知两融开户风险，已阅读并充分理解网上开户协议条款，自愿在思迪开户？',
                standardans: '/^((?!不).)*[是对4事四思室市氏士似]((?!不).)*$/',
                wait_time: '5',
                prompt: '请清晰回答“<b>是或否</b>”',
                failans: '/[不否部布步]/' // 错误回答
              },
              {
                tip_content: '见证完毕，感谢您的配合。'
              }
            ]
          });
          this.faceRecognitionStat = 11;
        } else {
          this.regResult = result.data;
          let actionGroup = this.getRandAction(1);
          this.livingLogParam = {
            actionType: actionGroup.name
          };
          let param = {
            funcNo: '60007',
            moduleName: 'open',
            moreMaxFailNum: 3,
            actionGroup: actionGroup.num,
            mainColor: $hvue.customConfig.mainColor
          };
          window.livingRecognitionCallBack = this.getImgCallBack;
          $h.callMessageNative(param);
        }
      } else {
        _hvueAlert({ mes: result.msg });
      }
    },

    getImgCallBack(data) {
      if ($hvue.platform == 0) {
        this.faceRecognitionStat = 2;
        this.$refs.faceImg.src =
          'data:image/jpeg;base64,' + filterBase64Pre(data.base64);
        let param = {
          flowNo: this.userInfo.flow_no,
          faceImageData: filterBase64Pre(data.base64),
          livingImagePath: data.faceImage
        };
        return faceRecognition(param, { loading: false })
          .then(() => {})
          .catch((e) => {
            this.faceRecognitionStat = 4;
            _hvueAlert({ mes: e.message });
          });
      } else {
        if (data.error_no == '-1') {
          console.log('取消');
          return;
        } else if (data.error_no == '-3') {
          if (this.liveFailCount >= $hvue.customConfig.video.liveFailNum) {
            _hvueAlert({
              title: '温馨提示',
              mes: '由于长时间未完成人脸认证，我们为您切换到一对一人工见证服务。',
              callback: () => {
                this.$router.replace({ name: 'witnessTwo' });
              }
            });
          } else {
            this.liveFailCount++;
            this.faceRecognitionStat = 11;
          }
          return;
        } else if (data.error_no != '0') {
          _hvueAlert({ mes: data.error_info });
          return;
        }
        this.faceRecognitionStat = 2;
        this.$refs.faceImg.src =
          'data:image/jpeg;base64,' + filterBase64Pre(data.base64);
        this.upfaceImage = filterBase64Pre(data.base64);
        try {
          submitLivingLog(
            {
              livingImageData: filterBase64Pre(
                data.actionBase64 || data.base64
              ),
              ...this.livingLogParam
            },
            { loading: false }
          )
            .then((res) => {
              if (res.code == 0) {
                let param = {
                  flowNo: this.regResult.regFlowNo,
                  faceImageData: filterBase64Pre(data.base64),
                  livingImagePath: this.faceImage
                };
                return faceRecognition(param, { loading: false }).then(
                  (data) => {
                    if (data.code == '0') {
                      if (data.data.pass) {
                        this.faceRecognitionStat = 3;
                      } else {
                        return Promise.reject(new Error('人脸对比不通过'));
                      }
                    } else {
                      return Promise.reject(new Error(data.msg));
                    }
                  }
                );
              } else {
                return Promise.reject(new Error(res.msg));
              }
            })
            .catch((e) => {
              this.faceRecognitionStat = 4;
            });
        } catch (err) {
          console.log(err);
        }
        // faceCompare({
        // 	fullFacePhoto: this.faceImage,
        // 	bigHeadPhoto: filterBase64Pre(data.base64)
        // }).then(res => {
        // 	if (res.data.flag) {
        // 		// 认证成功
        // 		this.faceRecognitionStat = 3;
        // 	} else {
        // 		//  认证失败
        // 		this.faceRecognitionStat = 4;
        // 	}
        // });
      }
    },

    nextClick() {
      uploadFile(
        $hvue.customConfig.serverUrl + '/media/imageUpload',
        // this.dataURLtoFile('data:image/jpeg;base64,' + imgInfo.base64),
        this.upfaceImage,
        {
          success: (data) => {
            if (data.code === 0) {
              let contact_bareheaded_pic = '';
              let instrepr_bareheaded_pic = '';
              if (this.flowOutputInfo.inProperty.contactIdCardPortrait) {
                contact_bareheaded_pic = data.data;
              } else {
                instrepr_bareheaded_pic = data.data;
              }
              this.nextFlow({
                id_auth_flag: '1',
                contact_bareheaded_pic, //经办人大头照
                instrepr_bareheaded_pic //法人大头照
              });
            }
          },
          progress: (count) => {
            console.log(count);
          },
          error: (e) => {
            _hvueLoading.close();
            console.log(e);
          }
        },
        {}
      );
    }
  }
};
</script>
