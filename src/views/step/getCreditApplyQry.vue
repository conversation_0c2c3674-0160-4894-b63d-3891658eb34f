<!--  -->
<template>
  <section class="main fixed">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="com_title">
        <h5>评级授信</h5>
      </div>
      <div class="com_box">
        <div class="input_form sx_form">
          <div class="input_text text">
            <span class="tit active">账户净资产(万)</span>
            <input
              class="t1 disabled"
              type="text"
              placeholder="请输入"
              :value="inputData.accountNetAsset"
              disabled="disabled"
            />
          </div>
          <div class="input_text text">
            <span class="tit active">授信额度上限(万)</span>
            <input
              class="t1 disabled"
              type="text"
              placeholder="请输入"
              :value="inputData.creditMoneyMax"
              disabled="disabled"
            />
          </div>
          <div class="input_text text">
            <span class="tit active">拟融资额度(万)</span>
            <input
              v-model="quotaFin"
              class="t1"
              type="text"
              placeholder="请输入"
            />
          </div>
          <div class="input_text text">
            <span class="tit active">拟融券额度(万)</span>
            <input
              v-model="quotaSlo"
              class="t1"
              type="text"
              placeholder="请输入"
            />
          </div>
        </div>
        <div v-if="showTip" class="warn_cm_tips">
          <p>您输入的拟融资+拟融券额度总和不能超过授信额度上限，请重新输入</p>
        </div>
      </div>
      <div class="tips_box">
        <p>1、您申请的拟授信总额度=拟融资额度+拟融券额度；</p>
        <p>2、若需修改总额度值，需修改拟融资或拟融券额度；</p>
        <p>
          3、拟融券额度系统默认计算展示，支持修改；计算规则为拟融券额度=授信额度上限-拟融资额度；
        </p>
        <p>
          4、账户资产净值是指客户账户中出售了投资所购股票、偿清保证金后的资金余额，亦指
          减去投资所购买股票的市值后，投资者账户中的自有资金余额。
        </p>
        <p>
          5、该融资融券授信总额度上限仅根据您当前情况初步评定，仅供参考。最终以您临柜办理时我司审批的额度为准
        </p>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" :class="{ disabled: isCheck }" @click="toNext"
          >下一步</a
        >
      </div>
    </footer>
  </section>
</template>

<script>
import BigNumber from 'bignumber.js';
import { getCreditApplyQry } from '@/service/service';
import flowMixin from '@/common/flowMixin';
export default {
  components: {},

  mixins: [flowMixin],
  data() {
    return {
      inputData: {},
      quotaFin: '',
      quotaSlo: ''
    };
  },

  computed: {
    showTip() {
      let show = '';
      if (
        this.inputData.creditMoneyMax &&
        this.quotaFin != 0 &&
        this.quotaSlo != 0
      ) {
        show = BigNumber(this.quotaFin)
          .plus(this.quotaSlo)
          .comparedTo(this.inputData.creditMoneyMax)
          .toString();
      }
      return show == 1;
    },
    isCheck() {
      return this.showTip || this.quotaSlo === '' || this.quotaFin === '';
    }
  },
  watch: {
    quotaFin() {
      this.calculateAmount();
    }
  },

  mounted() {
    this.initData();
  },

  methods: {
    back() {
      this.prevFlow();
    },
    initData() {
      _hvueLoading.open();
      getCreditApplyQry()
        .then((res) => {
          _hvueLoading.close();
          if (res.code == 0) {
            this.inputData = res.data;
            let { creditMoneyMax } = res.data;
            this.quotaFin = BigNumber(creditMoneyMax)
              .div(2)
              .toFixed(2)
              .toString();
          } else {
            _hvueAlert({ mes: data.msg });
          }
        })
        .catch((e) => {
          _hvueLoading.close();
          console.log(e);
        });
    },
    calculateAmount() {
      if (this.quotaFin) {
        if (
          BigNumber(this.quotaFin)
            .comparedTo(this.inputData.creditMoneyMax)
            .toString() == -1
        ) {
          this.quotaSlo = BigNumber(this.inputData.creditMoneyMax)
            .minus(this.quotaFin)
            .toFixed(2)
            .toString();
        } else {
          this.quotaFin = this.inputData.creditMoneyMax;
          this.quotaSlo = 0;
        }
      }
    },
    toNext() {
      if (this.isCheck) return;
      this.nextFlow({
        fin_quota: Number(this.quotaFin),
        slo_quota: Number(this.quotaSlo),
        cs_total_max_quota: Number(this.inputData.creditMoneyMax),
        net_asset: Number(this.inputData.accountNetAsset),
        total_quota: BigNumber(this.quotaFin).plus(this.quotaSlo).toString()
      });
    }
  }
};
</script>
<style lang="scss" scoped></style>
