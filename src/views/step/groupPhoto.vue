<template>
  <section class="main fixed h5_white_bg">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="com_title">
        <h5>面见人和客户的合照</h5>
      </div>
      <div class="upload_com_box">
        <div class="upload_wrap">
          <div class="upload_item" @click="selImgClick">
            <div class="pic"><img :src="showPic.src" /></div>
            <a v-if="showPic.uploaded" class="reset_btn">重拍</a>
            <a v-else class="btn">拍摄合照</a>
          </div>
          <div class="upload_item">
            <!-- <div class="upload_link">
							<a href="#">查看示例</a>
						</div> -->
          </div>
        </div>
        <div class="photo_tips">
          <h5 class="title">拍摄规范</h5>
          <ul class="list">
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img04_1.png" />
              </div>
              <span class="ok">正确拍摄</span>
            </li>
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img04_2.png" />
              </div>
              <span class="error">人脸出框</span>
            </li>
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img04_3.png" />
              </div>
              <span class="error">光线过暗</span>
            </li>
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img04_4.png" />
              </div>
              <span class="error">其他人出现</span>
            </li>
          </ul>
          <p>拍摄时请正对摄像头，手机横向拍摄</p>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="toNext">下一步</a>
      </div>
    </footer>

    <getImgGrounPhoto
      ref="getImgGrounPhoto"
      :scan="true"
      @getImgCallBack="getImgCallBack"
    ></getImgGrounPhoto>
    <getImgBoxBrowser
      ref="getImgBoxBrowser"
      :scan="true"
      @getImgCallBack="getImgCallBack"
    ></getImgBoxBrowser>
  </section>
</template>

<script>
import getImgBoxBrowser from '@/components/getImg_browser';
import getImgGrounPhoto from '@/components/getImgGrounPhoto';
import { uploadFile } from '@/common/util.js';
import flowMixin from '@/common/flowMixin';

export default {
  components: {
    getImgBoxBrowser,
    getImgGrounPhoto
  },
  mixins: [flowMixin],
  data() {
    return {
      showPic: {
        scr: require('@/assets/images/sl_img04.png'),
        uploaded: false
      },
      group_pic: ''
    };
  },
  methods: {
    back() {
      this.prevFlow();
    },

    renderingView() {
      _hvueLoading.close();
    },

    selImgClick() {
      this.$refs.getImgBoxBrowser.getImg();
      // if ($hvue.platform == 0) {
      // 	this.$refs.getImgBoxBrowser.getImg();
      // } else {
      //     this.$refs.getImgGrounPhoto.getImg(1);
      // }
    },

    getImgCallBack(imgInfo) {
      this.showPic = {
        scr: require('@/assets/images/sl_img04.png'),
        uploaded: false
      };
      uploadFile(
        $hvue.customConfig.serverUrl + '/media/imageUpload',
        // this.dataURLtoFile('data:image/jpeg;base64,' + imgInfo.base64),
        imgInfo.base64,
        {
          success: (data) => {
            console.log(data);
            if (data.code === 0) {
              this.group_pic = data.data;
              this.showPic.src = 'data:image/jpeg;base64,' + imgInfo.base64;
              console.log(this.showPic.src);
              this.showPic.uploaded = true;
            }
          },
          progress: (count) => {
            console.log(count);
          },
          error: (e) => {
            _hvueLoading.close();
            console.log(e);
          }
        },
        {}
      );
    },

    toNext() {
      if (this.group_pic) {
        this.nextFlow({
          group_pic: this.group_pic
        });
      } else {
        _hvueToast({
          mes: '请先拍摄合照'
        });
        return;
      }
    }
  }
};
</script>

<style></style>
