<template>
  <section class="main fixed">
    <t-header :title="agreementName" @back="back" />
    <article
      id="dody"
      class="content"
      style="background: #ffffff"
      @scroll="scrollView"
    >
      <div class="protocol_cont" v-html="agreementContent"></div>
    </article>
    <footer class="footer" style="background: #ffffff">
      <div class="ce_btn">
        <a v-if="countdown" class="p_button disabled">请阅读{{ countdown }}</a>
        <a v-else class="p_button" @click="toNext">我已阅读</a>
      </div>
    </footer>
  </section>
</template>

<script>
import flowMixin from '@/common/flowMixin';
import { getJwtToken, queryAgreement } from '@/service/service';
let timer;
export default {
  mixins: [flowMixin],
  data() {
    return {
      agreementName: '',
      agreementContent: '',
      count: 0,
      hasBottom: false
    };
  },
  computed: {
    countdown() {
      if (this.count <= 0) {
        return false;
      } else {
        return `（${this.count} 秒）`;
      }
    }
  },
  methods: {
    scrollView() {
      let scrollDiv = document.getElementById('dody');
      let scrollDistance =
        scrollDiv.scrollHeight - scrollDiv.scrollTop - scrollDiv.clientHeight;
      if (scrollDistance <= 10) {
        this.hasBottom = true;
      }
    },
    async renderingView() {
      const tokenRes = await getJwtToken({
        flowNo: this.flowOutputInfo.flowNodeNo,
        businessType: this.flowOutputInfo.inProperty.bizType
      });
      $h.setSession('jwtToken', tokenRes.data);
      let params = {
        flowToken: sessionStorage.getItem('TKFlowToken'),
        bizType: this.flowOutputInfo.inProperty.bizType,
        groupId: this.flowOutputInfo.privProperty.groupid,
        contractType: this.flowOutputInfo.privProperty.contracttype
      };
      const agreeRes = await queryAgreement(params);
      _hvueLoading.close();
      this.agreementName = agreeRes.data[0].agreementName;
      this.agreementContent = agreeRes.data[0].agreementContent;
      this.count = $hvue.customConfig.protocolReadSeconds || 0;
      timer = setInterval(() => {
        if (this.count <= 0) {
          clearInterval(timer);
        } else {
          this.count--;
        }
      }, 1000);
      this.$once('hook:deactivated', () => {
        clearInterval(timer);
      });
    },

    back() {
      clearInterval(timer);
      timer = null;
      this.prevFlow();
    },

    toNext() {
      if (this.hasBottom) {
        this.nextFlow({});
      } else {
        _hvueToast({ mes: '请完整阅读投资者教育!' });
      }
    }
  }
};
</script>

<style>
.protocol_cont {
  padding: 0.15rem;
  font-size: 0.16rem;
  line-height: 0.26rem;
}
.protocol_cont p {
  padding: 0.05rem 0;
}
.protocol_cont h1 {
  font-size: 0.18rem;
}
</style>
