<template>
  <section class="main fixed">
    <t-header></t-header>
    <article v-if="pageStep === 0" class="content">
      <div class="business_page">
        <div class="add-upload_page">
          <h5 class="add-com_title">拍摄并上传本人二代身份证</h5>
          <div class="add-upload_cont">
            <div class="upload_pic" @click="selImgClick('idfrontimg')">
              <div class="pic" :class="{ finish: positive.uploaded }">
                <i v-if="positive.uploaded" class="watermark"></i
                ><img :src="positive.src" />
              </div>
              <a v-if="positive.uploaded" class="reset_btn"
                >重新拍摄/上传人像面</a
              >
              <a v-else class="btn">点击拍摄/上传人像面</a>
            </div>
          </div>
          <div class="add-upload_cont">
            <div class="upload_pic" @click="selImgClick('idbackimg')">
              <div class="pic" :class="{ finish: negative.uploaded }">
                <i v-if="negative.uploaded" class="watermark"></i
                ><img :src="negative.src" />
              </div>
              <a v-if="negative.uploaded" class="reset_btn"
                >重新拍摄/上传国徽面</a
              >
              <a v-else class="btn">点击拍摄/上传国徽面</a>
            </div>
          </div>
          <div class="photo_tips">
            <h5 class="title">拍摄规范</h5>
            <ul class="list">
              <li>
                <div class="pic">
                  <img src="@/assets/images/sl_img02_1.png" />
                </div>
                <span class="ok">标准拍摄</span>
              </li>
              <li>
                <div class="pic">
                  <img src="@/assets/images/sl_img02_2.png" />
                </div>
                <span class="error">边角缺失</span>
              </li>
              <li>
                <div class="pic">
                  <img src="@/assets/images/sl_img02_3.png" />
                </div>
                <span class="error">照片模糊</span>
              </li>
              <li>
                <div class="pic">
                  <img src="@/assets/images/sl_img02_4.png" />
                </div>
                <span class="error">反光强烈</span>
              </li>
            </ul>
            <p>1. 拍摄时请将证件平放，手机横向拍摄</p>
            <p>
              2. 确保证明材料 <span class="imp">边框完整、文字清晰可见</span>
            </p>
          </div>
        </div>
      </div>
    </article>
    <article v-if="pageStep === 1" class="content">
      <div class="business_page">
        <div class="add-upload_page">
          <h5 class="add-com_title">请核对您的个人信息，若有误请手动修改</h5>
          <ul class="add-link_list">
            <li class="border_bottom">
              <p class="name">姓名</p>
              <div class="input_box">
                <input
                  v-model="exportParam.client_name"
                  type="text"
                  maxlength="21"
                />
              </div>
            </li>
            <li class="border_bottom">
              <p class="name">身份证号</p>
              <div class="input_box">
                <input v-model="exportParam.id_no" type="text" maxlength="18" />
              </div>
            </li>
            <li class="border_bottom">
              <p class="name">住址</p>
              <div class="input_box">
                <multLineInput
                  v-model="exportParam.id_address"
                  class="tarea01"
                  :maxlength="64"
                  :update-flag="positive.uploaded"
                ></multLineInput>
              </div>
            </li>
            <li class="border_bottom">
              <p class="name">发证机关</p>
              <div class="input_box">
                <input
                  v-model="exportParam.issued_depart"
                  type="text"
                  maxlength="32"
                />
              </div>
            </li>
            <li class="border_bottom">
              <p class="name">起始日期</p>
              <div class="input_box">
                <!-- <input type="text" placeholder="请选择" readonly="readonly" value="2011-03-10" /> -->
                <h-datetime
                  ref="date1"
                  v-model="exportParam.id_begindate"
                  class="t1"
                  title="起始日期"
                  type="date"
                  start-year="1900"
                  end-year="2100"
                ></h-datetime>
              </div>
            </li>
            <li class="border_bottom">
              <p class="name">结束日期</p>
              <div class="input_box">
                <!-- <input type="text" placeholder="请选择" readonly="readonly" value="2021-03-10" /> -->
                <h-datetime
                  v-show="!longTimeChecked"
                  ref="date2"
                  v-model="id_end_date"
                  class="t1"
                  title="结束日期"
                  type="date"
                  start-year="2020"
                  end-year="2100"
                ></h-datetime>
                <input
                  v-show="longTimeChecked"
                  class="t1"
                  type="text"
                  maxlength="32"
                  disabled
                  value="3000-12-31"
                />
              </div>
              <span class="p_icon_check long_span">长期</span>
            </li>
          </ul>
        </div>
      </div>
    </article>
    <footer>
      <div class="add-btn_box">
        <a @click="toNext">下一步</a>
      </div>
    </footer>
    <getImgBoxApp
      ref="getImgBoxThinkive"
      :scan="true"
      @getImgCallBack="getImgCallBack"
    ></getImgBoxApp>
    <getImgBoxBrowser
      ref="getImgBoxBrowser"
      :scan="true"
      @getImgCallBack="getImgCallBack"
    ></getImgBoxBrowser>
  </section>
</template>

<script>
import getImgBoxApp from '@/components/getImg_app';
import getImgBoxBrowser from '@/components/getImg_browser';
import multLineInput from '@/components/multLineInput';
import {
  uploadFile,
  idCardToSex,
  idCardToBirthday,
  getAge
} from '@/common/util.js';
export default {
  components: {
    multLineInput,
    getImgBoxBrowser,
    getImgBoxApp
  },
  data() {
    return {
      pageStep: 0,
      imgType: 'idfrontimg', // 点击上传的图片类型 idfrontimg正面 idbackimg反面
      positive: {
        src: require('@/assets/images/sl_img02.png'),
        uploaded: false
      },
      negative: {
        src: require('@/assets/images/sl_img03.png'),
        uploaded: false
      },
      exportParam: {
        id_begindate: '',
        id_kind: '1',
        nation_id: '1',
        nationality: '1'
      },
      longTimeChecked: false,
      id_end_date: '' //因为日期控件最大期限为3000-12-31时资源过多会卡死，单独处理
    };
  },
  created() {
    // console.log($hvue.platform)
  },
  methods: {
    renderingView() {
      _hvueLoading.close();
    },

    check() {
      if (!this.exportParam.client_name) {
        _hvueToast({
          mes: '姓名不能为空'
        });
        return false;
      }
      if (
        !/^([\u2E80-\uFE4F](?![\u3000-\u303F])){2,4}([·•]?[\u2E80-\uFE4F](?![\u3000-\u303F]))*$/.test(
          this.exportParam.client_name
        )
      ) {
        _hvueToast({
          mes: '姓名格式不正确'
        });
        return false;
      }
      if (!this.exportParam.id_no) {
        _hvueToast({
          mes: '身份证号不能为空'
        });
        return false;
      }
      if (!/^([\d]{17}[\dXx]|[\d]{15})$/.test(this.exportParam.id_no)) {
        _hvueToast({
          mes: '身份证号格式不正确'
        });
        return false;
      }
      // 判断是否小于18或大于70
      let userAge = getAge(this.exportParam.id_no);
      if (userAge && userAge < 18) {
        _hvueToast({
          mes: '开户年龄未满18岁'
        });
        return false;
      }

      if (userAge && userAge >= 70) {
        _hvueToast({
          mes: '开户年龄超过70岁'
        });
        return false;
      }
      if (!this.exportParam.id_address) {
        _hvueToast({
          mes: '住址不能为空'
        });
        return false;
      }
      if (
        !/^[\u4E00-\u9FA5\w\d\-\s/(),，（）#]{8,32}$/.test(
          this.exportParam.id_address
        )
      ) {
        _hvueToast({
          mes: '住址格式不正确'
        });
        return false;
      }
      if (!this.exportParam.issued_depart) {
        _hvueToast({
          mes: '发证机关不能为空'
        });
        return false;
      }
      if (
        !/^[\u4E00-\u9FA5\w\d\-\s/]{4,32}$/.test(this.exportParam.issued_depart)
      ) {
        _hvueToast({
          mes: '发证机关格式不正确'
        });
        return false;
      }
      let beginDate = this.exportParam.id_begindate;
      let endDate = this.longTimeChecked ? '3000-12-31' : this.id_end_date;
      let dateExp = /\d{4}-\d{2}-\d{2}/;
      // 判断有效期格式是否正确
      if (
        !dateExp.test(beginDate) ||
        !dateExp.test(endDate) ||
        Date.parse(beginDate.replace(/\./g, '-')) > Date.now() ||
        beginDate === endDate
      ) {
        _hvueToast({
          mes: '请设置正确的身份证有效期限'
        });
        return false;
      }
      // 判断身份证是否过期
      if (
        !this.longTimeChecked &&
        Date.parse(endDate.replace(/\./g, '-')) < Date.now()
      ) {
        _hvueToast({
          mes: '您的身份证已过期'
        });
        return false;
      }
      return true;
    },
    longTimeClick() {
      this.longTimeChecked = !this.longTimeChecked;
    },
    toNext() {
      if (this.pageStep === 0) {
        if (!this.positive.uploaded) {
          _hvueToast({ mes: '请上传人像面' });
        } else if (!this.negative.uploaded) {
          _hvueToast({ mes: '请上传国徽面' });
        } else {
          this.pageStep = 1;
        }
      } else {
        if (this.check()) {
          this.nextFlow(
            Object.assign({}, this.exportParam, {
              id_begindate: this.exportParam.id_begindate.replace(/-/g, ''),
              id_enddate: this.exportParam.id_enddate.replace(/-/g, '')
            })
          );
        }
      }
    },

    dataURLtoFile(dataurl) {
      var arr = dataurl.split(','),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      let blob = new Blob([u8arr], { type: mime });
      blob.name = new Date().getTime() + '.' + mime.split('/')[1];
      return blob;
    },

    selImgClick(imgType) {
      this.imgType = imgType;
      this.$refs.getImgBoxBrowser.getImg();
    },

    getImgCallBack(imgInfo) {
      uploadFile(
        $hvue.customConfig.serverUrl + '/client/ocrParseIDCard',
        // this.dataURLtoFile('data:image/jpeg;base64,' + imgInfo.base64),
        imgInfo.base64,
        {
          success: (data) => {
            _hvueLoading.close();
            if (data.code == 0) {
              let ocrInfo = Object.assign({}, data.data.ocrMsgInfo, {
                path: data.data.path
              });
              if (
                (this.imgType === 'idfrontimg' && !ocrInfo.idNo) ||
                (this.imgType === 'idbackimg' && !ocrInfo.policeorg)
              ) {
                _hvueAlert({ mes: '身份证识别失败' });
                return;
              }
              this.echoOrcInfo(imgInfo.base64, ocrInfo);
            } else {
              _hvueAlert({ mes: data.msg });
            }
          },
          progress: (count) => {
            console.log(count);
          },
          error: (e) => {
            _hvueLoading.close();
            console.log(e);
          }
        },
        {}
      );
    },

    echoOrcInfo(base64, result) {
      if (result.idNo) {
        this.exportParam.id_no = result.idNo;
        this.idno_ocr = result.idNo;
        this.exportParam.birthday = idCardToBirthday(result.idNo);
        this.$set(
          this.exportParam,
          'client_gender',
          idCardToSex(result.idNo) + ''
        );
        this.$set(
          this.exportParam,
          'client_gender_name',
          this.exportParam.client_gender === '0' ? '男' : '女'
        );
        this.exportParam.client_name = result.custName;
        this.exportParam.id_address = result.idAddress;
        this.exportParam.id_card_portrait = result.path;
        this.positive.uploaded = true;
        this.positive.src = 'data:image/jpeg;base64,' + base64;
      } else if (result.policeorg || result.idenddate) {
        this.exportParam.id_card_national = result.path;
        this.negative.uploaded = true;
        this.negative.src = 'data:image/jpeg;base64,' + base64;
      } else {
        _hvueToast({ mes: '未识别的身份证图片' });
      }
    }
  }
};
</script>

<style></style>
