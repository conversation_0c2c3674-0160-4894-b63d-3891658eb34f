<template>
  <section class="main fixed">
    <t-header @back="modalShow = true"></t-header>
    <article class="content">
      <div class="com_title">
        <h5>基本信息</h5>
      </div>
      <div class="com_box">
        <div class="input_form form_tit_right">
          <div
            class="input_text text required"
            @click="showCitySelect = !showCitySelect"
          >
            <span ref="spanRef1" :class="idProCity ? 'tit active' : 'tit'"
              >经常居住地<em class="imp">*</em></span
            >
            <div class="dropdown" placeholder="请选择">{{ idProCity }}</div>
          </div>
          <div
            class="input_text text required"
            :class="{ error: errors.first('address') }"
          >
            <label for="idAddress" :class="idAddress ? 'tit active' : 'tit'"
              >详细地址<em class="imp">*</em></label
            >
            <multLineInput
              id="idAddress"
              v-model="idAddress"
              v-validate="'address'"
              class="tarea1 needsclick"
              :maxlength="64"
              placeholder="请输入"
              name="address"
              autocomplete="off"
            />
            <p class="error_tips">{{ errors.first('address') }}</p>
          </div>
          <div
            class="input_text text required"
            :class="{ error: errors.first('email') }"
          >
            <label
              ref="spanRef3"
              for="emailNo"
              :class="email ? 'tit active' : 'tit'"
              >邮箱<em class="imp">*</em></label
            >
            <input
              id="emailNo"
              v-model="email"
              v-validate="'email'"
              class="t1"
              type="text"
              placeholder="请输入"
              name="email"
              autocomplete="off"
            />
            <p class="error_tips">{{ errors.first('email') }}</p>
          </div>
          <div
            class="input_text text required"
            @click="degreeCode.show = !degreeCode.show"
          >
            <span
              ref="spanRef4"
              :class="degreeCode.value ? 'tit active' : 'tit'"
              >学历<em class="imp">*</em></span
            >
            <div
              v-if="!degreeCode.value"
              class="dropdown"
              placeholder="请选择"
            ></div>
            <div v-else class="dropdown">{{ degreeCode.label }}</div>
          </div>
          <div
            class="input_text text required"
            @click="professionCode.show = !professionCode.show"
          >
            <span
              ref="spanRef5"
              :class="professionCode.value ? 'tit active' : 'tit'"
              >职业<em class="imp">*</em></span
            >
            <div
              v-if="!professionCode.value"
              class="dropdown"
              placeholder="请选择"
            ></div>
            <div v-else class="dropdown">{{ professionCode.label }}</div>
          </div>
          <div
            class="input_text text required"
            @click="income.show = !income.show"
          >
            <span ref="spanRef6" :class="income.value ? 'tit active' : 'tit'"
              >年收入<em class="imp">*</em></span
            >
            <div
              v-if="!income.value"
              class="dropdown"
              placeholder="请选择"
            ></div>
            <div v-else class="dropdown">{{ income.label }}</div>
          </div>
        </div>
      </div>
      <div class="com_box mt10">
        <div class="input_form form_tit_right">
          <div
            class="input_text text required"
            @click="controlPerson.show = !controlPerson.show"
          >
            <span
              ref="spanRef7"
              :class="controlPerson.value ? 'tit active' : 'tit'"
              >账户实际控制人<em class="imp">*</em></span
            >
            <div
              v-if="!controlPerson.value"
              class="dropdown"
              placeholder="请选择"
            ></div>
            <div v-else class="dropdown">{{ controlPerson.label }}</div>
          </div>
          <div
            class="input_text text required"
            @click="benefitPerson.show = !benefitPerson.show"
          >
            <span
              ref="spanRef8"
              :class="benefitPerson.value ? 'tit active' : 'tit'"
              >账户实际受益人<em class="imp">*</em></span
            >
            <div
              v-if="!benefitPerson.value"
              class="dropdown"
              placeholder="请选择"
            ></div>
            <div v-else class="dropdown">{{ benefitPerson.label }}</div>
          </div>
          <div
            class="input_text text required"
            @click="creditRecord.show = !creditRecord.show"
          >
            <span
              ref="spanRef9"
              :class="creditRecord.value ? 'tit active' : 'tit'"
              >不良诚信记录<em class="imp">*</em></span
            >
            <div
              v-if="!creditRecord.value"
              class="dropdown"
              placeholder="请选择"
            ></div>
            <div v-else class="dropdown">{{ creditRecord.label }}</div>
          </div>
          <div
            class="input_text text required"
            @click="taxResidentCountry.show = !taxResidentCountry.show"
          >
            <span
              ref="spanRef10"
              :class="taxResidentCountry.value ? 'tit active' : 'tit'"
              >涉税信息<em class="imp">*</em></span
            >
            <div
              v-if="!taxResidentCountry.value"
              class="dropdown"
              placeholder="请选择"
            ></div>
            <div v-else class="dropdown">{{ taxResidentCountry.label }}</div>
          </div>
        </div>
      </div>
      <div class="com_box mt10">
        <div class="input_form form_tit_right">
          <div class="input_text text">
            <label for="workUnit" :class="workUnit ? 'tit active' : 'tit'"
              >工作单位</label
            >
            <input
              id="workUnit"
              v-model="workUnit"
              class="t1"
              type="text"
              placeholder="请输入"
              autocomplete="off"
            />
          </div>
          <div class="input_text text" @click="job.show = !job.show">
            <span :class="job.value ? 'tit active' : 'tit'">职务</span>
            <div v-if="!job.value" class="dropdown" placeholder="请选择"></div>
            <div v-else class="dropdown">{{ job.label }}</div>
          </div>
          <div class="input_text text">
            <label for="homeTel" :class="homeTel ? 'tit active' : 'tit'"
              >固定电话</label
            >
            <input
              id="homeTel"
              v-model="homeTel"
              class="t1"
              type="text"
              placeholder="请输入"
              autocomplete="off"
            />
          </div>
          <div class="input_text text">
            <label for="zipCode" :class="zipCode ? 'tit active' : 'tit'"
              >邮编</label
            >
            <input
              id="zipCode"
              v-model="zipCode"
              class="t1"
              type="text"
              placeholder="请输入"
              autocomplete="off"
            />
          </div>
          <div class="input_text text">
            <label
              for="emergencyName"
              :class="emergencyName ? 'tit active' : 'tit'"
              >紧急联系人姓名</label
            >
            <input
              id="emergencyName"
              v-model="emergencyName"
              class="t1"
              type="text"
              placeholder="请输入"
              autocomplete="off"
            />
          </div>
          <div class="input_text text">
            <label
              for="emergencyPhone"
              :class="emergencyPhone ? 'tit active' : 'tit'"
              >紧急联系人电话</label
            >
            <input
              id="emergencyPhone"
              v-model="emergencyPhone"
              class="t1"
              type="text"
              placeholder="请输入"
              autocomplete="off"
            />
          </div>
          <div
            class="input_text text"
            @click="emergency.show = !emergency.show"
          >
            <span :class="emergency.value ? 'tit active' : 'tit'"
              >紧急联系人关系</span
            >
            <div
              v-if="!emergency.value"
              class="dropdown"
              placeholder="请选择"
            ></div>
            <div v-else class="dropdown">{{ emergency.label }}</div>
          </div>
        </div>
      </div>
      <div class="com_box mt10">
        <div class="input_form form_tit_right">
          <div
            class="input_text text required"
            :class="{ error: errors.first('mobileTel') }"
          >
            <label
              ref="spanRef11"
              for="mobileTel"
              :class="mobileTel ? 'tit active' : 'tit'"
              >手机号码<em class="imp">*</em></label
            >
            <input
              id="mobileTel"
              v-model="mobileTel"
              v-validate="'mobileTel'"
              class="t1 disabled"
              type="text"
              placeholder="请输入"
              name="mobileTel"
              autocomplete="off"
            />
            <p class="error_tips">{{ errors.first('mobileTel') }}</p>
            <!-- <a class="code_btn2" href="#">去修改</a> -->
          </div>
          <div class="input_text text code required">
            <label
              ref="spanRef12"
              for="smsCode"
              :class="smsCode ? 'tit active' : 'tit'"
              >短信验证码<em class="imp">*</em></label
            >
            <input
              id="smsCode"
              v-model="smsCode"
              class="t1 disabled"
              type="text"
              placeholder="请输入"
              autocomplete="off"
            />
            <a class="code_btn2" href="#">获取验证码</a>
            <sms-code-btn
              v-if="showSmsCode"
              v-model="uuid"
              :mobile-no="mobileTel"
              :biz-type="flowOutputInfo.inProperty.bizType"
              @send-result="SMSCodeCallback"
            />
          </div>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="toNext">下一步</a>
      </div>
    </footer>
    <selBox
      v-model="degreeCode.show"
      title="请选择学历"
      :category="degreeCode.dict"
      @selCallback="degreeCodeCallBack"
    ></selBox>
    <selBox
      v-model="professionCode.show"
      title="请选择职业"
      :category="professionCode.dict"
      @selCallback="professionCallBack"
    ></selBox>
    <selBox
      v-model="income.show"
      title="请选择年收入"
      :category="income.dict"
      @selCallback="incomeCallBack"
    ></selBox>
    <selBox
      v-model="controlPerson.show"
      title="账户实际控制人"
      :init-data="personDict"
      @selCallback="controlPersonCallBack"
    ></selBox>
    <selBox
      v-model="benefitPerson.show"
      title="账户实际受益人"
      :init-data="personDict"
      @selCallback="benefitPersonCallBack"
    ></selBox>
    <selBox
      v-model="creditRecord.show"
      title="诚信记录"
      :category="creditRecord.dict"
      @selCallback="creditRecordCallBack"
    ></selBox>
    <selBox
      v-model="taxResidentCountry.show"
      title="纳税身份"
      :category="taxResidentCountry.dict"
      @selCallback="taxResidentCountryCallBack"
    ></selBox>
    <selBox
      v-model="job.show"
      title="请选择职务"
      :category="job.dict"
      @selCallback="jobCallBack"
    ></selBox>
    <selBox
      v-model="emergency.show"
      title="请选择关系"
      :category="emergency.dict"
      @selCallback="emergencyCallBack"
    ></selBox>
    <citySelect
      v-model="showCitySelect"
      :is-show-xzqy="true"
      @selCallBack="cityCallBack"
    ></citySelect>
    <div v-show="modalShow" class="back_tip">
      <div class="dialog_overlay"></div>
      <div class="dialog_box">
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>温馨提示</h3>
          <div>
            <p>您还有修改的信息未提交，是否确认返回，未提交则信息不会保存。</p>
          </div>
        </div>
        <div class="dialog_btn fx-row">
          <a class="cancel" @click="goBack">确定</a>
          <a @click="modalShow = false">取消</a>
        </div>
      </div>
    </div>
    <div v-show="modalTips.show" class="back_tip">
      <div class="dialog_overlay"></div>
      <div class="dialog_box">
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>温馨提示</h3>
          <div>
            <p>{{ modalTips.tips }}</p>
          </div>
        </div>
        <div class="dialog_btn fx-row">
          <a @click="modalTips.show = false">确定</a>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import selBox from '@/components/selBox';
import multLineInput from '@/components/multLineInput';
import citySelect from '@/components/citySelect';
import { DICT_TYPE } from '@/common/enumeration';
import flowMixin from '@/common/flowMixin';
import SmsCodeBtn from '@/components/SmsCodeBtn.vue';
import { smsCheck, addressParse } from '@/service/service';
import { getDictData } from '@/service/commonService';
export default {
  components: {
    selBox,
    citySelect,
    multLineInput,
    SmsCodeBtn
  },
  mixins: [flowMixin],
  data() {
    return {
      mobileTel: '',
      idAddress: '',
      email: '',
      zipCode: '',
      idProCity: '',
      workUnit: '',
      emergencyName: '',
      emergencyPhone: '',
      modalTips: {
        show: false,
        tips: ''
      },
      degreeCode: {
        // 学历
        label: '',
        value: '',
        dict: DICT_TYPE.DEGREE_CODE,
        show: false
      },
      professionCode: {
        // 职业
        label: '',
        value: '',
        dict: DICT_TYPE.PROFESSION_CODE,
        show: false
      },
      controlPerson: {
        // 实际控制人
        label: '',
        value: '',
        dict: '',
        show: false
      },
      benefitPerson: {
        // 实际受益人
        label: '',
        value: '',
        dict: '',
        show: false
      },
      creditRecord: {
        // 诚信记录
        label: '',
        value: '',
        dict: DICT_TYPE.INTEGRITYREC,
        show: false
      },
      taxResidentCountry: {
        // 纳税身份
        label: '',
        value: '',
        dict: DICT_TYPE.TAX_RESIDENT_PERSON,
        show: false
      },
      income: {
        // 年收入
        label: '',
        value: '',
        dict: DICT_TYPE.INCOME,
        show: false
      },
      job: {
        // 职务下拉信息
        label: '',
        value: '',
        dict: DICT_TYPE.POST_CODE,
        show: false,
        includeList: []
      },
      emergency: {
        // 紧急联系人
        label: '',
        value: '',
        dict: DICT_TYPE.SOCIALREL,
        show: false
      },
      personDict: [
        {
          label: '本人',
          value: '1'
        },
        {
          label: '非本人',
          value: '0'
        }
      ],
      showCitySelect: false,
      smsCode: '',
      homeTel: '',
      uuid: '',
      modalShow: false,
      showSmsCode: false
    };
  },
  deactivated() {
    this.smsCode = '';
    this.showSmsCode = false;
    this.modalShow = false;
  },
  methods: {
    goBack() {
      this.modalShow = false;
      this.prevFlow();
    },
    SMSCodeCallback(flag) {
      this.sendFlag = flag;
      if (!flag) {
        this.imgCode = '';
        this.captchaToken = '';
        this.uuid = '';
      }
    },
    renderingView() {
      this.showSmsCode = true;
      let _this = this;
      let info = _this.flowOutputInfo.inProperty;
      let privProperty = _this.flowOutputInfo.privProperty;
      let outProperty = _this.flowOutputInfo.outProperty;
      addressParse({
        address: outProperty.address
      }).then((res) => {
        _hvueLoading.close();
        if (res.data) {
          this.idProCity = `${res.data.proRegionName} ${res.data.cityRegionName} ${res.data.areaRegionName}`;
          this.idAddress = res.data.regionDetail;
        }
        this.workUnit = outProperty.workUnit;
        this.homeTel = outProperty.homeTel;
        this.email = outProperty.email;
        this.mobileTel = outProperty.mobileTel;
        this.zipCode = outProperty.zipcode;
        this.emergencyName = outProperty.emergencyContactName;
        this.emergencyPhone = outProperty.emergencyContactPhone;
        this.benefitPerson.value = outProperty.benefitPerson;
        this.benefitPerson.label =
          outProperty.benefitPerson === '1' ? '本人' : '非本人';
        // this.controlPerson.label = outProperty.controlPerson === '1' ? '本人' : '非本人';
        if (
          outProperty.controlPerson ===
            this.$store.state.user.userInfo.clientName ||
          outProperty.controlPerson === '1'
        ) {
          this.controlPerson.label = '本人';
          this.controlPerson.value = '1';
        }
        setTimeout(() => {
          this.echoInfo(outProperty, 'degreeCode');
          this.echoInfo(outProperty, 'professionCode');
          this.echoInfo(outProperty, 'income');
          this.echoInfo(outProperty, 'taxResidentCountry');
          this.echoInfo(outProperty, 'creditRecord');
        }, 300);
      });
      // this.job.value = outProperty.degreeCode?outProperty.degreeCode:''
      // this.emergency.value = outProperty.degreeCode?outProperty.degreeCode:''
    },
    echoInfo(data, type) {
      if (data[type]) {
        this[type].value = data[type];
        getDictData({ dictType: this[type].dict }).then((data) => {
          data.data[this[type].dict].forEach((item) => {
            if (item.dictValue === this[type].value) {
              this[type].label = item.dictLabel;
            }
          });
        });
      }
    },
    degreeCodeCallBack(info) {
      this.degreeCode.label = info.data.label;
      this.degreeCode.value = info.data.value;
    },
    professionCallBack(info) {
      this.professionCode.label = info.data.label;
      this.professionCode.value = info.data.value;
    },
    controlPersonCallBack(info) {
      this.controlPerson.label = info.data.label;
      this.controlPerson.value = info.data.value;
    },
    benefitPersonCallBack(info) {
      this.benefitPerson.label = info.data.label;
      this.benefitPerson.value = info.data.value;
    },
    creditRecordCallBack(info) {
      this.creditRecord.label = info.data.label;
      this.creditRecord.value = info.data.value;
    },
    taxResidentCountryCallBack(info) {
      this.taxResidentCountry.label = info.data.label;
      this.taxResidentCountry.value = info.data.value;
    },
    politiciansPersonRelationCallBack(info) {
      this.politiciansPersonRelation.label = info.data.label;
      this.politiciansPersonRelation.value = info.data.value;
    },
    incomeCallBack(info) {
      this.income.label = info.data.label;
      this.income.value = info.data.value;
    },
    jobCallBack(info) {
      this.job.label = info.data.label;
      this.job.value = info.data.value;
    },
    emergencyCallBack(info) {
      this.emergency.label = info.data.label;
      this.emergency.value = info.data.value;
    },

    cityCallBack(item) {
      this.idProCity = `${item.province.provinceName} ${item.city.cityName} ${item.xzqy.xzqyName}`;
    },

    check() {
      let requiredKey = [
        { key: 'idProCity', tips: '经常居住地' },
        { key: 'idAddress', tips: '详细地址' },
        { key: 'email', tips: '邮箱' },
        { key: 'degreeCode', tips: '学历', type: 'select' },
        { key: 'professionCode', tips: '职业', type: 'select' },
        { key: 'income', tips: '年收入', type: 'select' },
        { key: 'controlPerson', tips: '账户实际控制人', type: 'select' },
        { key: 'benefitPerson', tips: '账户实际受益人', type: 'select' },
        { key: 'creditRecord', tips: '不良诚信记录', type: 'select' },
        { key: 'taxResidentCountry', tips: '涉税信息', type: 'select' },
        { key: 'mobileTel', tips: '手机号码' },
        { key: 'smsCode', tips: '短信验证码' }
      ];
      let requiredTips = '';
      try {
        requiredKey.forEach((item) => {
          if (item.type === 'select') {
            if (!this[item.key].value) {
              requiredTips = item.tips + '不能为空!';
              throw requiredTips;
            }
          } else {
            if (!this[item.key]) {
              requiredTips = item.tips + '不能为空!';
              throw requiredTips;
            }
          }
        });
      } catch (e) {
        console.log(e);
      }
      if (requiredTips) {
        _hvueToast({ mes: requiredTips });
        return false;
      }
      if (
        !/^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/.test(this.email)
      ) {
        _hvueToast({ mes: '邮箱格式不正确!' });
        return false;
      }
      if (this.controlPerson.value !== '1') {
        this.modalTips = {
          show: true,
          tips: '实际控制人非本人，不允许办理此业务'
        };
        return false;
      }
      if (this.benefitPerson.value !== '1') {
        this.modalTips = {
          show: true,
          tips: '实际受益人非本人，不允许办理此业务'
        };
        return false;
      }
      if (this.creditRecord.value !== '0') {
        this.modalTips = {
          show: true,
          tips: '根据监管要求，若存在不良诚信记录，不允许办理此业务'
        };
        // _hvueToast({ mes: '根据监管要求，若存在不良诚信记录，不允许办理此业务' });
        return false;
      }
      return true;
    },

    toNext() {
      // console.log(this.errors.first('mobileTel'));
      this.$validate(async (res) => {
        if (res) {
          const requiredArr = Object.values(this.$refs);
          const flag = requiredArr.some(
            (item) => !item.className.includes('active')
          );
          // if (flag) return _hvueToast({ mes: '请完成必填项的填写！' });
          if (!this.check()) return; //格式校验
          if (!this.uuid)
            return _hvueToast({ mes: '验证码不正确，请重新发送验证码验证!' });
          smsCheck({
            mobileNo: this.mobileTel,
            code: this.smsCode,
            uuid: this.uuid
          })
            .then((res) => {
              if (!res.data.flag) {
                _hvueToast({ mes: '验证码错误，请重新输入' });
                this.smsCode = '';
                return;
              }
              let address = this.idProCity.replace(/\s/g, '') + this.idAddress;
              let param = {
                address: address,
                address_context: address,
                benefit_person: this.benefitPerson.value,
                control_person: this.controlPerson.value,
                credit_record:
                  this.creditRecord.label === '无不良诚信记录' ? '0' : '1',
                credit_record_type: this.creditRecord.value,
                degree_code: this.degreeCode.value,
                duties: this.job.value,
                email: this.email,
                emergency_contact_name: this.emergencyName,
                emergency_contact_phone: this.emergencyPhone,
                emergency_contact_type: this.emergency.value,
                home_tel: this.homeTel,
                income: this.income.value,
                mobile_tel: this.mobileTel,
                profession_code: this.professionCode.value,
                tax_resident_person: this.taxResidentCountry.value,
                work_unit: this.workUnit,
                zipcode: this.zipCode
              };
              this.nextFlow(param);
            })
            .catch((error) => {
              _hvueToast({ mes: error });
            });
        }
      });
    }
  }
};
</script>

<style></style>
