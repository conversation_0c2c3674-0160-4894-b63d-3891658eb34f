<template>
  <section class="main fixed">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="com_title">
        <h5>追保信息填写</h5>
      </div>
      <h5 class="mid_title">
        追保人信息申报<em>请确认追保手机号，以便接收追保通知</em>
      </h5>
      <div class="com_box">
        <div class="input_form">
          <div
            class="input_text text"
            :class="{ error: errors.first('mobileTel') }"
          >
            <label for="appendPhone" :class="mobileTel ? 'tit active' : 'tit'"
              >手机号码</label
            >
            <multLineInput
              id="appendPhone"
              v-model="mobileTel"
              v-validate="'mobileTel'"
              class="t1"
              type="text"
              name="mobileTel"
              :maxlength="11"
              placeholder="请输入（必填）"
              autocomplete="off"
            />
            <p class="error_tips">{{ errors.first('mobileTel') }}</p>
          </div>
          <div v-if="showSmsBtn" class="input_text text code">
            <label for="captcha" :class="captcha ? 'tit active' : 'tit'"
              >图形验证码</label
            >
            <multLineInput
              id="captcha"
              v-model="captcha"
              class="t1"
              type="text"
              :maxlength="4"
              placeholder="请输入（必填）"
              autocomplete="off"
            />
            <a class="code_img" @click="imgClick"><img :src="imgSrc" /></a>
          </div>
          <div v-if="showSmsBtn" class="input_text text code">
            <label
              ref="spanRef12"
              for="smsCode"
              :class="smsCode ? 'tit active' : 'tit'"
              >短信验证码</label
            >
            <input
              id="smsCode"
              v-model="smsCode"
              class="t1 disabled"
              type="text"
              placeholder="请输入"
              autocomplete="off"
            />
            <sms-code-btn
              v-if="showSmsCode"
              v-model="uuid"
              :need-img-code="true"
              :mobile-no="mobileTel"
              :captcha="captcha"
              :captcha-token="captchaToken"
              :biz-type="flowOutputInfo.inProperty.bizType"
              @send-result="SMSCodeCallback"
            />
          </div>
        </div>
      </div>
      <h5 class="mid_title">
        第三方邮箱信息<em
          >根据两融业务合规要求，您融资融券业务审核通过后会自动分配一个第三方专用邮箱，用于接收融资融券业务信息。邮箱账户及密码会以短信方式发送，请注意查收。</em
        >
      </h5>
      <div class="com_box">
        <div class="input_form">
          <div class="input_text text">
            <span class="tit active">邮箱</span>
            <input
              class="t1 disabled"
              disabled="disabled"
              type="text"
              placeholder="请输入"
              :value="appendEmail"
            />
          </div>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="toNext">下一步</a>
      </div>
    </footer>
  </section>
</template>

<script>
import flowMixin from '@/common/flowMixin';
import { generateRecoveryEmail, getImgCode } from '@/service/service';
import multLineInput from '@/components/multLineInput';
import { smsCheck } from '@/service/service';
import SmsCodeBtn from '@/components/SmsCodeBtn.vue';

export default {
  components: {
    multLineInput,
    SmsCodeBtn
  },
  mixins: [flowMixin],
  data() {
    return {
      // appendName: '',
      smsCode: '',
      mobileTel: '',
      appendEmail: '',
      defmobileTel: '',
      uuid: '',
      showSmsCode: false,
      captcha: '',
      captchaToken: '',
      imgSrc: ''
    };
  },
  computed: {
    showSmsBtn() {
      return this.defmobileTel === this.mobileTel ? false : true;
    }
  },
  deactivated() {
    this.smsCode = '';
    this.showSmsCode = false;
  },
  methods: {
    renderingView() {
      this.showSmsCode = true;
      this.defmobileTel = this.flowOutputInfo.inProperty.mobileTel;
      this.mobileTel = this.flowOutputInfo.inProperty.mobileTel;
      this.imgClick();
      generateRecoveryEmail().then((data) => {
        _hvueLoading.close();
        if (data.code === 0) {
          this.appendEmail = data.data;
        }
      });
    },

    SMSCodeCallback(flag) {
      this.sendFlag = flag;
      if (!flag) {
        this.uuid = '';
      }
    },

    back() {
      this.prevFlow();
    },

    imgClick() {
      getImgCode({}, { loading: false }).then((data) => {
        if (data.code === 0) {
          let results = data.data;
          this.imgSrc = results.image;
          this.captchaToken = results.token;
        }
      });
    },

    toNext() {
      if (!this.showSmsBtn) {
        this.nextFlow({
          // appendName: this.appendName,
          appendEmail: this.appendEmail,
          appendTel: this.mobileTel
        });
        return;
      }
      if (!this.uuid)
        return _hvueToast({ mes: '验证码不正确，请重新发送验证码验证!' });
      smsCheck({
        mobileNo: this.mobileTel,
        code: this.smsCode,
        uuid: this.uuid
      })
        .then((res) => {
          if (!res.data.flag) {
            _hvueToast({ mes: '验证码错误，请重新输入' });
            this.smsCode = '';
            return;
          }
          this.nextFlow({
            // appendName: this.appendName,
            appendEmail: this.appendEmail,
            appendTel: this.mobileTel
          });
        })
        .catch((error) => {
          _hvueToast({ mes: error });
        });
    }
  }
};
</script>

<style></style>
