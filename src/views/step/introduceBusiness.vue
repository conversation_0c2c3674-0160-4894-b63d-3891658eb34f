<template>
  <section class="main fixed white_bg" data-page="home">
    <t-header @back="backEvent" />
    <article class="content">
      <div class="h5_home_page">
        <div class="h5_banner_box">
          <img src="@/assets/images/banner.png" />
        </div>
        <div class="bus_home_must">
          <h5 class="title">
            <span><em>办理条件</em></span>
          </h5>
          <ul class="list">
            <li>已开通深A沪A账户，且账户状态正常</li>
            <li>20日日均资产大于等于50万元</li>
            <li>交易经验已满6个月</li>
            <li>客户风险承受能力需为积极型及以上</li>
            <li>未在我司或其他券商开通过信用账户</li>
          </ul>
        </div>
        <div class="bus_home_tips">
          <h5>风险提示</h5>
          <p>
            投资者在开通两融交易前，应当认真阅读有关法律法规和交易所业务规则等相关规定，对其他可能存在的风险因素也应当有所了解和掌握，并确信自己已做好足够的风险评估与财务安排，避免因参与两融交易遭受难以承受的损失
          </p>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn bome_btn">
        <a
          :class="!nosupport ? 'p_button' : 'p_button disabled'"
          href="javascript:void(0);"
          @click="toNext"
          >{{ nosupport ? tipMsg : '下一步' }}</a
        >
      </div>
    </footer>
    <div v-show="showModal">
      <div class="dialog_overlay"></div>
      <div class="dialog_box">
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>{{ tipTitle }}</h3>
          <div>
            <p>{{ tipMsg }}</p>
          </div>
        </div>
        <div class="dialog_btn">
          <a href="javascript:void(0);" @click="showModal = false">我知道了</a>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import {
  checkOpenAccount,
  getJwtToken,
  queryAgreement
} from '@/service/service.js';
import flowMixin from '@/common/flowMixin';
export default {
  mixins: [flowMixin],
  data() {
    return {
      nosupport: false, // 是否不支持跳转下一步
      showModal: false, // 是否展示拦截弹窗
      tipTitle: '', // 提示标题
      tipMsg: '' // 具体提示内容
    };
  },

  methods: {
    backEvent() {
      this.prevFlow();
    },
    // 渲染页面请求数据
    renderingView() {
      checkOpenAccount()
        .then((res) => {
          _hvueLoading.close();
          if (res.code !== 0) {
            this.nosupport = true;
            _hvueToast({ mes: res.msg });
            return;
          }
          if (res.data && res.data.name && res.data.message) {
            this.nosupport = true;
            this.showModal = true;
            this.tipTitle = res.data.name;
            this.tipMsg = res.data.message;
          }
        })
        .catch((err) => {
          _hvueLoading.close();
          this.nosupport = true;
          _hvueToast({ mes: err });
        });
    },
    // 下一步
    async toNext() {
      if (this.nosupport) return;
      let { agreePrintJson } = this.flowOutputInfo.privProperty;
      const tokenRes = await getJwtToken({
        flowNo: this.flowOutputInfo.flowNodeNo,
        businessType: this.flowOutputInfo.inProperty.bizType
      });
      $h.setSession('jwtToken', tokenRes.data);
      let PromiseParams = JSON.parse(agreePrintJson)
        ? JSON.parse(agreePrintJson).map((item) => {
            let params = {
              flowToken: sessionStorage.getItem('TKFlowToken'),
              bizType: this.flowOutputInfo.inProperty.bizType,
              groupId: item.groupId,
              contractType: item.contractType
            };
            return new Promise((resolve) => {
              queryAgreement(params).then((res) => {
                resolve(
                  res.data.map((item) => {
                    return {
                      agreementId: item.agreementId,
                      groupId: item.groupId,
                      contractType: item.contractType,
                      agreementName: item.agreementName,
                      agreementPath: item.agreementPath || '',
                      agreementSubtype: ''
                    };
                  })
                );
              });
            });
          })
        : [];
      Promise.all(PromiseParams).then((res) => {
        let arr = [];
        res.forEach((item) => {
          item.forEach((it) => {
            arr.push(it);
          });
        });
        this.nextFlow({
          agree_print_json: JSON.stringify(arr)
        });
      });
      // this.nextFlow({});
    }
  }
};
</script>
<style lang="scss" scoped></style>
