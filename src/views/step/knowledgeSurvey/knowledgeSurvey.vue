<template>
  <section class="main fixed">
    <t-header :title="title" @back="back"></t-header>
    <article
      v-if="pageStep === 0"
      class="content"
      style="overflow-y: hidden; display: flex; flex-direction: column"
    >
      <div class="com_title" style="overflow: initial">
        <h5>知识测评</h5>
      </div>
      <div class="test_numbox" style="overflow: initial">
        <span
          v-for="(item, index) in questionList"
          :key="index"
          :class="{ off: item.some((a) => a.checked) }"
          @click="toJump(index)"
          >{{ index + 1 }}</span
        >
      </div>
      <div
        v-if="pageStep === 0"
        ref="questionList"
        class="test_list"
        style="overflow-y: auto; margin: 0; height: 100%; position: relative"
      >
        <div
          v-for="(item, index) in questionList"
          :ref="`question${index}`"
          :key="index"
          class="test_list"
        >
          <div class="test_box">
            <h5 class="title">
              {{ index + 1 }}、{{ item[0].questionContent }}
            </h5>
            <div class="radio_list">
              <span
                v-for="(m, i) in item"
                :key="'m' + i"
                :class="{
                  icon_check: m.questionKind === QUESTION_KIND.MULTI,
                  icon_radio: m.questionKind !== QUESTION_KIND.MULTI,
                  checked: m.checked,
                  disabled: m.degreeCode && m.degreeCode !== ''
                }"
                @click="selectItem(m, i, index)"
                >{{ m.answerContent }}</span
              >
            </div>
          </div>
        </div>
      </div>
    </article>
    <article v-if="pageStep === 1" class="content">
      <div class="com_title">
        <h5>{{ title }}结果</h5>
      </div>
      <div class="com_box">
        <div class="test_result_box">
          <div class="icon">
            <img src="@/assets/images/test_result_ok.png" />
          </div>
          <h5>评测等级：<span class="ok">准入</span></h5>
          <p>
            客户评测结果{{
              testResult.paperScore
            }}分，评测等级为准入，测评结果一经提交，不可更改，请确认填写无误后提交。
          </p>
        </div>
      </div>
    </article>
    <footer v-if="pageStep === 0" class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="nextStep">提交</a>
      </div>
    </footer>
    <footer v-if="pageStep === 1" class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="toNext">下一步</a>
        <a class="p_button border" @click="riskAfresh">重新评测</a>
      </div>
    </footer>
    <div v-if="tipInfo.show">
      <div class="dialog_overlay"></div>
      <div class="dialog_box">
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>{{ tipInfo.title }}</h3>
          <div>
            <p>{{ tipInfo.desc }}</p>
          </div>
        </div>
        <div class="dialog_btn fx-row">
          <a v-if="!tipInfo.onlyBtn" class="cancel" @click="goBack">{{
            tipInfo.cancelText
          }}</a>
          <a @click="closeTip">{{ tipInfo.confirmText }}</a>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import flowMixin from '@/common/flowMixin';
import {
  questionQry,
  questionSubmit,
  getQuestionNumber
} from '@/service/service';
import { QUESTION_KIND } from '@/common/enumeration';

export default {
  mixins: [flowMixin],
  data() {
    return {
      title: '',
      pageStep: 0,
      paperType: '',
      QUESTION_KIND: QUESTION_KIND,
      questionList: [],
      answerList: [],
      isObtainLoc: '',
      subjectNo: '',
      testResult: {},
      tipInfo: {
        show: false,
        title: '',
        desc: '',
        onlyBtn: false,
        cancelText: '',
        confirmText: ''
      }
    };
  },
  computed: {
    totalQuestionLength() {
      return this.questionList.length;
    }
  },
  deactivated() {
    this.pageStep = 0;
    // this.questionList = [];
    // this.answerList = []
  },
  methods: {
    back() {
      if (this.pageStep === 1) {
        this.pageStep -= 1;
      } else {
        this.tipInfo = {
          show: true,
          title: '知识测评还未完成',
          desc: '是否确认返回上一步',
          cancelText: '确认',
          confirmText: '取消'
        };
      }
    },

    closeTip() {
      this.tipInfo.show = false;
    },
    goBack() {
      this.tipInfo.show = false;
      this.prevFlow();
    },

    renderingView() {
      this.title = this.flowOutputInfo.stepName;
      document.title = this.title;
      if (this.questionList.length === 0) {
        this.getQuestionList();
      }
    },

    checkScore() {
      if (this.testResult.paperScore < 80) {
        return false;
      }
      return true;
    },

    toNext() {
      // if (!checkScore()) {
      // 	return;
      // }
      this.nextFlow({
        paper_no: this.testResult.subjectId,
        paper_answer: this.testResult.riskQuestion,
        knowledge_score: this.testResult.paperScore
      });
    },

    nextStep() {
      this.submitQuestion();
    },

    toJump(index) {
      this.$refs['questionList'].scrollTop =
        this.$refs[`question${index}`][0].offsetTop;
    },

    jump(index) {
      this.$refs['questionList'].scrollTop =
        this.$refs[`question${index}`][0].offsetTop;
    },

    getQuestionList() {
      let _this = this;
      let { clientId } = _this.flowOutputInfo.inProperty;
      let { paperType, userType, isObtainLoc } =
        _this.flowOutputInfo.privProperty;
      this.paperType = paperType;
      // const info = _this.flowOutputInfo.inProperty;
      questionQry({
        clientId,
        paperType,
        userType,
        isObtainLoc,
        flowToken: sessionStorage.getItem('TKFlowToken')
      })
        .then((data) => {
          _hvueLoading.close();
          let _resArr = data.data;
          let _queId = '';
          let _queNum = -1;
          let i = 0;
          // 取柜台数据时paperType用接口返回的，取本地数据时paperType用私有属性配置的
          _this.pagePaperType =
            isObtainLoc === '1' ? paperType : _resArr[0].paperType;
          _resArr.forEach((item) => {
            if (item.questionNo !== _queId) {
              _queId = item.questionNo;
              _queNum++;
              i = 0;
              item.extName =
                item.questionKind === QUESTION_KIND.MULTI ? '（多选）' : '';
              _this.$set(_this.questionList, _queNum, []);
            }
            // 增加checked属性来判定是否选中当前选项
            if (item.degreeCode !== '' && item.isAlter) {
              item.checked = true;
              _this.$set(
                _this.answerList,
                _queNum,
                `${item.questionNo}_${item.answerNo}`
              );
            } else {
              item.checked = false;
            }
            _this.$set(_this.questionList[_queNum], i++, item);
          });
        })
        .catch((error) => {
          _hvueLoading.close();
          _hvueToast({
            mes: error
          });
        });
    },

    selectItem(item, aIndex, quesIndex) {
      const _this = this;
      let ansAtr = [];
      let quesNo = '';
      if (this.questionList[quesIndex].find((item) => item.degreeCode)) {
        return;
      }
      _this.questionList[quesIndex].forEach((a) => {
        if (a.questionKind === QUESTION_KIND.MULTI) {
          if (item.answerNo === a.answerNo) {
            a.checked = !a.checked;
          }
          if (a.checked) {
            quesNo = a.questionNo;
            ansAtr.push(a.answerNo);
          }
        } else {
          if (a.answerNo === item.answerNo) {
            a.checked = true;
            quesNo = a.questionNo;
            ansAtr.push(a.answerNo);
          } else {
            a.checked = false;
          }
        }
      });
      // 选择后更改答案列表
      let a = '';
      if (ansAtr.length > 0) {
        a = `${quesNo}_${ansAtr.join('&')}`;
      }
      this.$set(this.answerList, quesIndex, a);
      if (
        this.questionList.every((question) => question.some((a) => a.checked))
      ) {
        // this.submitQuestion();
      } else if (
        this.questionList[quesIndex][0].questionKind != QUESTION_KIND.MULTI &&
        quesIndex < this.questionList.length - 1
      ) {
        setTimeout(() => {
          this.jump(quesIndex + 1);
        }, 300);
      }
    },

    submitQuestion() {
      const _this = this;
      const info = _this.flowOutputInfo.inProperty;
      //校验答案是否选择完成
      if (!_this.verifyAnswer()) return;
      let { isObtainLoc } = _this.flowOutputInfo.privProperty;
      const flowToken = sessionStorage.getItem('TKFlowToken');
      if (_this.isObtainLoc === '1' && this.testResult.ifCorpRiskTimes <= 0) {
        // 本地问卷提交前看根据剩余次数能否提交
        _hvueToast({
          msg: '尊敬的投资者，您今天的测评次数已用完，请充分学习业务知识，下个交易日再进行测评，谢谢'
        });
        return;
      }
      questionSubmit({
        flowToken,
        clientId: info.clientId,
        fundAccount: info.fundAccount,
        paperType: this.paperType,
        paperAnswer: this.answerList.join('|'), // 试卷答案对象数据
        isObtainLoc
      })
        .then((data) => {
          Object.assign(_this.testResult, data.data);
          // let subjectId = data.data.subjectId;
          if (isObtainLoc === '1') {
            return getQuestionNumber({
              flowToken,
              clientId: info.clientId,
              subjectNo: this.paperType
            });
          } else {
            if (this.testResult.ifCorpRiskTimes === '0') {
              _hvueToast({
                mes: '尊敬的投资者，您今天的测评次数已用完，请充分学习业务知识，下个交易日再进行测评，谢谢'
              });
            } else {
              _this.pageStep = 1;
            }
          }
        })
        .then((data) => {
          this.testResult.ifCorpRiskTimes = data.data;
          if (data.data > 0) {
            _this.pageStep = 1;
          } else {
            _hvueToast({
              mes: '尊敬的投资者，您今天的测评次数已用完，请充分学习业务知识，下个交易日再进行测评，谢谢'
            });
          }
        })
        .catch((err) => {
          if (err) {
            _hvueToast({
              msg: err
            });
          }
        });
    },

    verifyAnswer() {
      let arr = [];
      for (let quesIndex of this.questionList.keys()) {
        if (!this.answerList[quesIndex]) {
          arr.push(quesIndex + 1);
        }
      }
      if (arr.length > 0) {
        _hvueToast({
          mes: `第${arr.join(',')}题尚未选择，请确认后重新提交`
        });
        return false;
      } else {
        return true;
      }
    },

    riskAfresh() {
      if (
        this.testResult.ifCorpRiskTimes === '0' ||
        this.testResult.ifCorpRiskTimes <= 0
      ) {
        _hvueToast({
          mes: '尊敬的投资者，您今天的测评次数已用完，请充分学习业务知识，下个交易日再进行测评，谢谢'
        });
      } else {
        // 重新测评
        this.pageStep = 0;
        this.$nextTick(() => {
          this.jump(0);
          this.answerList = [];
          this.testResult = this.$options.data().testResult;
          this.getQuestionList();
        });
      }
    }
  }
};
</script>
