<template>
  <div>
    <section class="main fixed">
      <t-header @back="back"></t-header>
      <article class="content">
        <h5 class="com_title">纸质协议采集</h5>
        <h5 class="mid_title">营业部提供</h5>
        <div class="com_box">
          <ul class="file_cmlist">
            <li
              v-for="(item, index) in holderList"
              :key="index"
              @click="toUpload(item, index)"
            >
              <a>{{ item.agreementName }}</a
              ><span v-if="!item.uploaded" class="state error">未完成</span>
            </li>
          </ul>
        </div>
        <!-- <h5 class="mid_title">其他材料</h5>
				<div class="com_box">
					<ul class="file_cmlist">
						<li><a>其他材料</a><span class="state error">未完成（非必填)</span></li>
					</ul>
				</div> -->
      </article>
      <footer class="footer">
        <div class="ce_btn">
          <a class="p_button" @click="toNext">下一步</a>
        </div>
      </footer>
    </section>
    <template>
      <section
        v-for="(item, index) in holderList"
        v-show="item.show"
        :key="index"
        class="main fixed"
        style="z-index: 1499"
      >
        <t-header title="上传资料" @back="uploadBack(item, index)"></t-header>
        <article class="content" style="background: #f2f2f4">
          <h5 class="mid_title">请上传材料：</h5>
          <div class="com_box">
            <div class="file_upload_item" @click="selImgClick(index)">
              <div class="pic"><img :src="item.src" /></div>
              <a class="btn">点击拍摄/上传</a>
            </div>
          </div>
          <p class="imp_tiptxt"><i></i>请上传 jpg / png / jpeg 格式的图片</p>
        </article>
        <footer class="footer">
          <div class="ce_btn">
            <a class="p_button" @click="uploadBack(item, index)">确定</a>
          </div>
        </footer>
      </section>
    </template>
    <getImgBoxBrowser
      ref="getImgBoxBrowser"
      :scan="true"
      @getImgCallBack="getImgCallBack"
    ></getImgBoxBrowser>
  </div>
</template>

<script>
import flowMixin from '@/common/flowMixin';
import {
  getJwtToken,
  queryAgreement,
  businessCanQry
} from '@/service/service.js';
import { uploadFile } from '@/common/util.js';
import getImgBoxBrowser from '@/components/getImg_browser';

export default {
  components: {
    getImgBoxBrowser
  },
  mixins: [flowMixin],
  data() {
    return {
      holderList: [],
      nowUploadIndex: 0
    };
  },
  methods: {
    back() {
      this.prevFlow();
    },

    toNext() {
      if (this.holderList.findIndex((item) => item.uploaded === false) >= 0) {
        _hvueToast({
          mes: '请上传全部资料'
        });
        return;
      }
      let parms = this.holderList.map((item) => ({
        groupId: item.groupId,
        contractType: item.contractType,
        picUrl: item.picUrl,
        agreeName: item.agreementName
      }));
      let Json = JSON.stringify(parms);
      this.nextFlow({
        paper_pic_json: Json,
        audit_biz_type: this.flowOutputInfo.privProperty.auditBizType
      });
    },

    getImgCallBack(imgInfo) {
      uploadFile(
        $hvue.customConfig.serverUrl + '/media/imageUpload',
        // this.dataURLtoFile('data:image/jpeg;base64,' + imgInfo.base64),
        imgInfo.base64,
        {
          success: (data) => {
            console.log(data);
            if (data.code === 0) {
              this.holderList[this.nowUploadIndex].picUrl = data.data;
              this.holderList[this.nowUploadIndex].uploaded = true;
              this.holderList[this.nowUploadIndex].src =
                'data:image/jpeg;base64,' + imgInfo.base64;
            }
          },
          progress: (count) => {
            console.log(count);
          },
          error: (e) => {
            _hvueLoading.close();
            console.log(e);
          }
        },
        {}
      );
    },

    selImgClick(index) {
      this.nowUploadIndex = index;
      this.$refs.getImgBoxBrowser.getImg();
    },

    uploadBack(item, index) {
      this.holderList[index].show = false;
    },

    toUpload(item, index) {
      this.holderList[index].show = true;
    },

    async renderingView() {
      // let holderList = JSON.parse(this.flowOutputInfo.privProperty.chooseHolderRightsJson);
      businessCanQry({ bizType: this.flowOutputInfo.inProperty.bizType }).then(
        async (res) => {
          _hvueLoading.close();
          let holderList = [];
          res.data.forEach((item) => {
            if (item.businessId !== this.flowOutputInfo.inProperty.bizType)
              holderList.push({
                ...JSON.parse(item.propertyConf),
                name: item.businessName
              });
          });
          holderList = holderList.map((item) => {
            return {
              ...item,
              checked: false,
              disabled: false
            };
          });
          let hasCheckedHolderList = this.flowOutputInfo.inProperty
            .chooseHolderRights
            ? this.flowOutputInfo.inProperty.chooseHolderRights
                .split(',')
                .map((item) => {
                  return holderList.find((it) => it.holderRight === item);
                })
            : [];
          const tokenRes = await getJwtToken({
            flowNo: this.flowOutputInfo.flowNodeNo,
            businessType: this.flowOutputInfo.inProperty.bizType
          });
          $h.setSession('jwtToken', tokenRes.data);
          let PromiseParams = hasCheckedHolderList.map((item) => {
            let params = {
              flowToken: sessionStorage.getItem('TKFlowToken'),
              bizType: this.flowOutputInfo.inProperty.bizType,
              groupId: item.groupId,
              contractType: item.contractType
            };
            return new Promise((resolve) => {
              queryAgreement(params).then((res) => {
                resolve(res.data);
              });
              // queryAgreement(params).then(res => {
              // 	let it = res.data[0].agreementName;
              // 	resolve({
              // 		groupId: item.groupId,
              // 		contractType: item.contractType,
              // 		agreementName: it,
              // 		picUrl: '',
              // 		uploaded: false,
              // 		src: require('@/assets/images/file_sl_img01.png'),
              // 		show: false
              // 	});
              // });
            });
          });
          let agreePrintJson = this.flowOutputInfo.inProperty.agreePrintJson
            ? JSON.parse(this.flowOutputInfo.inProperty.agreePrintJson)
            : [];
          let printPromiseParams = agreePrintJson.map((item) => {
            let params = {
              agreementId: item.agreementId,
              flowToken: sessionStorage.getItem('TKFlowToken'),
              bizType: this.flowOutputInfo.inProperty.bizType,
              groupId: item.groupId,
              contractType: item.contractType,
              agreementSubtype: item.agreementSubtype
                ? item.agreementSubtype
                : ''
            };
            return new Promise((resolve, reject) => {
              queryAgreement(params).then((res) => {
                resolve(res.data);
              });
            });
          });
          PromiseParams = PromiseParams.concat(printPromiseParams);
          Promise.all(PromiseParams).then((res) => {
            // this.holderList = res;
            let arr = [];
            res.forEach((item, index) => {
              item.forEach((it) => {
                arr.push({
                  groupId: it.groupId,
                  contractType: it.contractType,
                  agreementName: it.agreementName,
                  picUrl: '',
                  uploaded: false,
                  src: require('@/assets/images/file_sl_img01.png'),
                  show: false
                });
              });
            });
            this.holderList = arr;
          });
        }
      );
    }
  }
};
</script>

<style></style>
