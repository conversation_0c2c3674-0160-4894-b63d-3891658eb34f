<template>
  <section class="main fixed" data-page="home">
    <t-header @back="back"></t-header>
    <article v-if="pageStep === 0" class="content">
      <h5 class="mid_title">
        请跳转至微信小程序“电子营业执照”获取认证信息，认证成功后请点击返回
      </h5>
      <div class="com_box">
        <div class="file_upload_item">
          <div class="pic spel">
            <img src="@/assets/images/file_sl_img02.png" />
          </div>
          <a class="btn" @click="toRegist">认证</a>
          <div v-if="up_loading" class="up_loading">
            <div class="wrap">
              <i class="icon"></i>
              <p>正在查询中，请稍后...</p>
              <p>如果系统长时间无响应，请点击“认证”重新授权</p>
            </div>
          </div>
        </div>
      </div>
      <div class="com_tips">
        <p>没有电子营业执照？<a class="link_right_arrow">点击这里</a></p>
      </div>
    </article>
    <article v-if="pageStep === 1" class="content">
      <h5 class="mid_title">
        请跳转至微信小程序“电子营业执照”获取认证信息，认证成功后请点击返回
      </h5>
      <div class="com_box">
        <div class="file_upload_item">
          <div class="pic spel"><img :src="showOrgInfo.image" /></div>
          <a class="btn" @click="toRegist">重新认证</a>
          <div v-if="up_loading" class="up_loading">
            <div class="wrap">
              <i class="icon"></i>
              <p>正在查询中，请稍后...</p>
              <p>如果系统长时间无响应，请点击“认证”重新授权</p>
            </div>
          </div>
        </div>
      </div>
      <div class="com_box">
        <div class="input_form form_tit_right">
          <div class="input_text text required">
            <span class="tit active">企业名称</span>
            <multLineInput
              v-model="showOrgInfo.organName"
              class="t1"
              type="text"
              placeholder="请输入"
              disabled
            />
            <!-- <p class="error_tips">该信息与柜台不一致</p> -->
          </div>
          <div class="input_text text required">
            <span class="tit active">统一社会信用代码</span>
            <multLineInput
              v-model="showOrgInfo.businessLicence"
              class="t1"
              type="text"
              placeholder="请输入"
              disabled
            />
            <!-- <p class="error_tips">该信息与柜台不一致</p> -->
          </div>
        </div>
      </div>
      <div class="com_box">
        <div class="input_form form_tit_right">
          <div class="input_text text required">
            <span class="tit active">持照人姓名</span>
            <multLineInput
              v-model="showOrgInfo.contactName"
              class="t1"
              type="text"
              placeholder="请输入"
              disabled
            />
          </div>
          <div class="input_text text required">
            <span class="tit active">持照人身份</span>
            <multLineInput
              v-model="showOrgInfo.contactTypeName"
              class="t1"
              type="text"
              placeholder="请输入"
              disabled
            />
          </div>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn"><a class="p_button" @click="toNext">下一步</a></div>
    </footer>
    <div v-if="showTimeOut">
      <div class="dialog_overlay"></div>
      <div class="dialog_box">
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>请求超时</h3>
          <div>
            <p>因系统长时间未查询到数据，请点击重试重新认证</p>
          </div>
        </div>
        <div class="dialog_btn fx-row">
          <a class="cancel" @click="cancelRegist">取消</a>
          <a @click="toRegist">重新认证</a>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
let timer;
import flowMixin from '@/common/flowMixin';
import { getJwtToken, createToken, getSignResult } from '@/service/service.js';
import multLineInput from '@/components/multLineInput';
export default {
  components: { multLineInput },
  mixins: [flowMixin],
  data() {
    return {
      showTimeOut: false,
      up_loading: false,

      pageStep: 0,
      flowId: '',

      showOrgInfo: {},

      business_licence: '', //营业执照
      full_name: '', //账户全称
      organ_name: '' //机构名称
    };
  },
  methods: {
    back() {
      clearInterval(timer);
      this.up_loading = false;
      timer = null;
      if (this.pageStep === 0) {
        this.prevFlow();
      } else {
        this.pageStep = 0;
      }
    },

    renderingView() {
      clearInterval(timer);
      _hvueLoading.close();
    },

    async toRegist() {
      clearInterval(timer);
      this.showTimeOut = false;
      this.up_loading = true;
      const tokenRes = await getJwtToken({
        flowNo: this.flowOutputInfo.flowNodeNo,
        businessType: this.flowOutputInfo.inProperty.bizType
      });
      $h.setSession('jwtToken', tokenRes.data);
      await createToken({
        bizType: this.flowOutputInfo.inProperty.bizType
      }).then((res) => {
        this.flowId = res.data.flowId;
        //todo 对接原生跳转，回调返回token
        var userName = 'gh_81fea41c2854'; //商兆小程序原始ID
        var qrinfo = res.data.qrInfo; //调后端createToken接口获取
        var miniprogramType = '0'; //小程序版本 0：正式版RELEASE； 1：测试版TEST； 2：预览版PREVIEW；默认0
        var param = {
          funcNo: '60076',
          userName: userName,
          path: 'pages/main-company/company/company?parm=' + qrinfo,
          miniprogramType: miniprogramType
        };
        //调原生插件
        $h.callMessageNative(param);
      });
      let times = 0;
      timer = setInterval(async () => {
        await getSignResult({
          flowToken: sessionStorage.getItem('TKFlowToken'),
          flowId: this.flowId
        })
          .then((res) => {
            if (res.data.data) {
              this.showOrgInfo = JSON.parse(res.data.data);
              this.showOrgInfo.image =
                'data:image/jpeg;base64,' + this.showOrgInfo.image;
              this.pageStep = 1;
              this.up_loading = false;
              clearInterval(timer);
              timer = null;
            } else {
              times += 1;
            }
            if (times === 100) {
              // 轮询超过5分钟，唤起弹窗
              this.up_loading = false;
              clearInterval(timer);
              this.showTimeOut = true;
            }
          })
          .catch(() => {
            times += 1;
            if (times === 100) {
              // 轮询超过5分钟，唤起弹窗
              this.up_loading = false;
              clearInterval(timer);
              this.showTimeOut = true;
            }
          });
      }, 3000);
      this.$once('hook:deactivated', () => {
        this.up_loading = false;
        clearInterval(timer);
      });
    },

    cancelRegist() {
      clearInterval(timer);
      this.showTimeOut = false;
    },

    toNext() {
      if (this.pageStep === 0) {
        // todo 校验是否获取到了商照小程序回调token，获取成功才能下一步
        if (this.flowId && this.showOrgInfo.image) {
          this.pageStep = 1;
        } else {
          _hvueToast({
            mes: '请先认证'
          });
          return;
        }
      } else {
        // 校验商照信息和柜台是否一致
        this.nextFlow();
        // confirmBusinessLicense({
        // 	enterpriseName: showOrgInfo.organName,
        // 	unifySocialCreditCodes: showOrgInfo.businessLicence,
        // 	flowToken: sessionStorage.getItem('TKFlowToken')
        // }).then(res => {
        // 	this.nextFlow();
        // });
      }
    }
  }
};
</script>
