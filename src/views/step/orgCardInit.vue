<template>
  <section class="main fixed h5_white_bg" data-page="home">
    <t-header @back="back"></t-header>
    <article v-if="pageStep === 0" class="content">
      <div class="com_title">
        <h5>法定代表人身份证验证</h5>
      </div>
      <div class="upload_com_box">
        <div class="upload_wrap">
          <div class="upload_item" @click="selImgClick('idfrontimg')">
            <div class="pic">
              <img :src="positive.src" />
            </div>
            <a v-if="positive.uploaded" class="reset_btn">重拍</a>
            <a v-else class="btn">拍摄人像面</a>
          </div>
          <div class="upload_item" @click="selImgClick('idbackimg')">
            <div class="pic">
              <img :src="negative.src" />
            </div>
            <a v-if="negative.uploaded" class="reset_btn">重拍</a>
            <a v-else class="btn">拍摄国徽照</a>
          </div>
        </div>
        <div v-if="!showInfo" class="photo_tips">
          <h5 class="title">拍摄规范</h5>
          <ul class="list">
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img02_1.png" />
              </div>
              <span class="ok">标准拍摄</span>
            </li>
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img02_2.png" />
              </div>
              <span class="error">边角缺失</span>
            </li>
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img02_3.png" />
              </div>
              <span class="error">照片模糊</span>
            </li>
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img02_4.png" />
              </div>
              <span class="error">反光强烈</span>
            </li>
          </ul>
          <p class="desc">1. 拍摄时请将证件平放，手机横向拍摄</p>
          <p class="desc">
            2. 确保证明材料 <span class="imp">边框完整、文字清晰可见</span>
          </p>
        </div>
      </div>
      <div v-if="showInfo" class="upload_infobox">
        <div class="input_form">
          <div class="input_text text">
            <div class="tit active">姓名</div>
            <multLineInput
              v-model="exportParam.client_name"
              class="t1"
              type="text"
              :maxlength="21"
            />
          </div>
          <div class="input_text text">
            <div class="tit active">身份证号</div>
            <multLineInput
              v-model="exportParam.id_no"
              class="t1"
              type="text"
              :maxlength="18"
            />
          </div>
          <div v-if="showIssuedDepart" class="input_text text">
            <div class="tit active">发证机关</div>
            <input
              v-model="exportParam.issued_depart"
              class="t1"
              type="text"
              maxlength="32"
            />
          </div>
          <div v-if="showIdAddress" class="input_text text">
            <div class="tit active">证件地址</div>
            <multLineInput
              v-model="exportParam.id_address"
              class="t1"
              :maxlength="64"
              :update-flag="positive.uploaded"
            ></multLineInput>
          </div>
          <div v-if="showIdDate" class="input_text text">
            <div class="tit active">有效期</div>
            <div class="h_date fx-row fx-c-center">
              <div class="fx-1">
                <h-datetime
                  ref="date1"
                  v-model="exportParam.id_begindate"
                  class="t1"
                  title="起始日期"
                  type="date"
                  start-year="1900"
                  end-year="2100"
                ></h-datetime>
              </div>
              <span class="connector">~</span>
              <div class="fx-1">
                <h-datetime
                  v-show="!exportLongTimeChecked"
                  ref="date2"
                  v-model="exportParam.id_enddate"
                  class="t1"
                  title="结束日期"
                  type="date"
                  start-year="2020"
                  end-year="2100"
                ></h-datetime>
                <input
                  v-show="exportLongTimeChecked"
                  class="t1"
                  type="text"
                  maxlength="32"
                  disabled
                  value="3000-12-31"
                />
              </div>
              <span
                class="long_span icon_check"
                :class="{ checked: exportLongTimeChecked }"
                @click="selectExportLongTime"
                >长期</span
              >
            </div>
          </div>
        </div>
      </div>
    </article>
    <article v-if="pageStep === 1" class="content">
      <div class="com_title">
        <h5>经办人身份证验证</h5>
      </div>
      <div class="upload_com_box">
        <div class="upload_wrap">
          <div class="upload_item" @click="selImgClick('idfrontimg')">
            <div class="pic">
              <img :src="contactPositive.src" />
            </div>
            <a v-if="contactPositive.uploaded" class="reset_btn">重拍</a>
            <a v-else class="btn">拍摄人像面</a>
          </div>
          <div class="upload_item" @click="selImgClick('idbackimg')">
            <div class="pic">
              <img :src="contactNegative.src" />
            </div>
            <a v-if="contactNegative.uploaded" class="reset_btn">重拍</a>
            <a v-else class="btn">拍摄国徽照</a>
          </div>
        </div>
        <div v-if="!showInfo" class="photo_tips">
          <h5 class="title">拍摄规范</h5>
          <ul class="list">
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img02_1.png" />
              </div>
              <span class="ok">标准拍摄</span>
            </li>
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img02_2.png" />
              </div>
              <span class="error">边角缺失</span>
            </li>
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img02_3.png" />
              </div>
              <span class="error">照片模糊</span>
            </li>
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img02_4.png" />
              </div>
              <span class="error">反光强烈</span>
            </li>
          </ul>
          <p class="desc">1. 拍摄时请将证件平放，手机横向拍摄</p>
          <p class="desc">
            2. 确保证明材料 <span class="imp">边框完整、文字清晰可见</span>
          </p>
        </div>
      </div>
      <div v-if="showContactInfo" class="upload_infobox">
        <div class="input_form">
          <div class="input_text text">
            <div class="tit active">姓名</div>
            <multLineInput
              v-model="contactExportParam.client_name"
              class="t1"
              type="text"
              :maxlength="21"
            />
          </div>
          <div class="input_text text">
            <div class="tit active">身份证号</div>
            <multLineInput
              v-model="contactExportParam.id_no"
              class="t1"
              type="text"
              :maxlength="18"
            />
          </div>
          <div v-if="showIssuedDepart" class="input_text text">
            <div class="tit active">发证机关</div>
            <input
              v-model="contactExportParam.issued_depart"
              class="t1"
              type="text"
              maxlength="32"
            />
          </div>
          <div v-if="showIdAddress" class="input_text text">
            <div class="tit active">证件地址</div>
            <multLineInput
              v-model="contactExportParam.id_address"
              class="t1"
              :maxlength="64"
              :update-flag="positive.uploaded"
            ></multLineInput>
          </div>
          <div v-if="showIdDate" class="input_text text">
            <div class="tit active">有效期</div>
            <div class="h_date fx-row fx-c-center">
              <div class="fx-1">
                <h-datetime
                  ref="date1"
                  v-model="contactExportParam.id_begindate"
                  class="t1"
                  title="起始日期"
                  type="date"
                  start-year="1900"
                  end-year="2100"
                ></h-datetime>
              </div>
              <span class="connector">~</span>
              <div class="fx-1">
                <h-datetime
                  v-show="!contactLongTimeChecked"
                  ref="date2"
                  v-model="contactExportParam.id_enddate"
                  class="t1"
                  title="结束日期"
                  type="date"
                  start-year="2020"
                  end-year="2100"
                ></h-datetime>
                <input
                  v-show="contactLongTimeChecked"
                  class="t1"
                  type="text"
                  maxlength="32"
                  disabled
                  value="3000-12-31"
                />
              </div>
              <span
                class="long_span icon_check"
                :class="{ checked: contactLongTimeChecked }"
                @click="selectContLongTime"
                >长期</span
              >
            </div>
          </div>
        </div>
      </div>
    </article>

    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="toNext">下一步</a>
      </div>
    </footer>
    <getImgBoxApp
      ref="getImgBoxThinkive"
      :scan="true"
      @getImgCallBack="getImgCallBack"
    ></getImgBoxApp>
    <getImgBoxBrowser
      ref="getImgBoxBrowser"
      :scan="true"
      @getImgCallBack="getImgCallBack"
    ></getImgBoxBrowser>
  </section>
</template>

<script>
import flowMixin from '@/common/flowMixin';
import getImgBoxApp from '@/components/getImg_app';
import getImgBoxBrowser from '@/components/getImg_browser';
import { uploadFile, idCardToSex, idCardToBirthday } from '@/common/util';
import multLineInput from '@/components/multLineInput';
export default {
  components: {
    getImgBoxApp,
    getImgBoxBrowser,
    multLineInput
  },
  mixins: [flowMixin],
  data() {
    return {
      imgType: 'idfrontimg',
      pageStep: 0,
      contactRole: 1, //经办人角色，0代表法人，无需上传经办人身份证
      showIssuedDepart: false,
      showIdAddress: false,
      showIdDate: false,
      exportLongTimeChecked: false,
      contactLongTimeChecked: false,
      positive: {
        // 客户身份证正面
        src: require('@/assets/images/sl_img02.png'),
        uploaded: false
      },
      negative: {
        //  客户身份证反面
        src: require('@/assets/images/sl_img03.png'),
        uploaded: false
      },
      contactPositive: {
        // 经办人身份证正面
        src: require('@/assets/images/sl_img02.png'),
        uploaded: false
      },
      contactNegative: {
        //  经办人身份证反面
        src: require('@/assets/images/sl_img03.png'),
        uploaded: false
      },
      exportParam: {
        id_begindate: '',
        id_enddate: '',
        id_kind: '0'
      },
      contactExportParam: {
        id_begindate: '',
        id_enddate: '',
        id_kind: '0'
      }
    };
  },
  computed: {
    showInfo() {
      return this.positive.uploaded && this.negative.uploaded;
    },

    showContactInfo() {
      return this.contactPositive.uploaded && this.contactNegative.uploaded;
    }
  },
  methods: {
    back() {
      if (this.pageStep === 0) {
        this.prevFlow();
      } else {
        this.pageStep = 0;
      }
    },

    renderingView() {
      _hvueLoading.close();
      this.contactRole = Number(this.flowOutputInfo.inProperty.contactRole);
      let isShow = this.flowOutputInfo.privProperty.isShow
        ? this.flowOutputInfo.privProperty.isShow.split(',')
        : [];
      if (isShow.length === 1) {
        this.showIdAddress = true;
        this.showIssuedDepart = false;
        this.showIdDate = false;
      } else if (isShow.length === 2) {
        this.showIdAddress = true;
        this.showIssuedDepart = true;
        this.showIdDate = false;
      } else if (isShow.length >= 3) {
        this.showIdAddress = true;
        this.showIssuedDepart = true;
        this.showIdDate = true;
      } else if (isShow.length === 0) {
        this.showIdAddress = false;
        this.showIssuedDepart = false;
        this.showIdDate = false;
      }
    },

    toNext() {
      if (this.pageStep === 0) {
        // todo 判断是否需要上传经办人
        // 检测法人,是否能下一步
        // if (!this.instreprCheck()) {
        // 	return;
        // }
        if (this.contactRole) {
          // 如果需要上传经办人
          this.pageStep = 1;
        } else {
          this.nextFlow({
            instrepr_id_card_portrait: this.exportParam.id_card_portrait,
            instrepr_id_card_national: this.exportParam.id_card_national,
            instrepr_id_no: this.exportParam.id_no,
            instrepr_name: this.exportParam.client_name,
            instrepr_id_kind: this.exportParam.id_kind,
            instrepr_id_begindate: this.exportParam.id_begindate.replace(
              /-/g,
              ''
            ),
            instrepr_id_enddate: this.exportParam.id_enddate.replace(/-/g, '')
          });
        }
      } else {
        // 流程引擎下一步
        // 检测经办人
        if (!this.contactCheck()) {
          return;
        }
        this.nextFlow({
          instrepr_id_card_portrait: this.exportParam.id_card_portrait,
          instrepr_id_card_national: this.exportParam.id_card_national,
          instrepr_id_no: this.exportParam.id_no,
          instrepr_name: this.exportParam.client_name,
          instrepr_id_kind: this.exportParam.id_kind,
          instrepr_id_begindate: this.exportParam.id_begindate.replace(
            /-/g,
            ''
          ),
          instrepr_id_enddate: this.exportParam.id_enddate.replace(/-/g, ''),

          contact_id_card_portrait: this.contactExportParam.id_card_portrait,
          contact_id_card_national: this.contactExportParam.id_card_national,
          contact_id_no: this.contactExportParam.id_no,
          contact_name: this.contactExportParam.client_name,
          contact_id_kind: this.contactExportParam.id_kind,
          contact_id_begindate: this.contactExportParam.id_begindate.replace(
            /-/g,
            ''
          ),
          contact_id_enddate: this.contactExportParam.id_enddate.replace(
            /-/g,
            ''
          )
        });
      }
    },

    instreprCheck() {
      if (!this.positive.uploaded) {
        _hvueToast({ mes: '请上传人像面' });
        return false;
      } else if (!this.negative.uploaded) {
        _hvueToast({ mes: '请上传国徽面' });
        return false;
      }
      return true;
    },

    contactCheck() {
      if (!this.contactPositive.uploaded) {
        _hvueToast({ mes: '请上传人像面' });
        return false;
      } else if (!this.contactNegative.uploaded) {
        _hvueToast({ mes: '请上传国徽面' });
        return false;
      }
      return true;
    },

    selectExportLongTime() {
      this.exportLongTimeChecked = !this.exportLongTimeChecked;
    },

    selectContLongTime() {
      this.contactLongTimeChecked = !this.contactLongTimeChecked;
    },

    echoOrcInfo(base64, result) {
      if (this.pageStep === 0) {
        if (result.idNo) {
          this.exportParam.id_no = result.idNo;
          this.exportParam.birthday = idCardToBirthday(result.idNo);
          this.$set(
            this.exportParam,
            'client_gender',
            idCardToSex(result.idNo) + ''
          );
          this.$set(
            this.exportParam,
            'client_gender_name',
            this.exportParam.client_gender === '0' ? '男' : '女'
          );
          this.exportParam.client_name = result.custName;
          this.exportParam.id_address = result.idAddress;
          this.exportParam.id_card_portrait = result.path;
          this.positive.uploaded = true;
          this.positive.src = 'data:image/jpeg;base64,' + base64;
        } else if (result.policeorg || result.idenddate) {
          this.exportParam.id_card_national = result.path;
          this.$set(this.exportParam, 'issued_depart', result.policeorg);
          this.$set(this.exportParam, 'id_begindate', result.idbegindate);
          if (result.idenddate == '3000-12-31') {
            this.exportLongTimeChecked = true;
          } else {
            this.$set(this.exportParam, 'id_enddate', result.idenddate);
          }
          this.negative.uploaded = true;
          this.negative.src = 'data:image/jpeg;base64,' + base64;
        } else {
          _hvueToast({ mes: '未识别的身份证图片' });
        }
      } else if (this.pageStep === 1) {
        if (result.idNo) {
          this.contactExportParam.id_no = result.idNo;
          this.contactExportParam.birthday = idCardToBirthday(result.idNo);
          this.$set(
            this.contactExportParam,
            'client_gender',
            idCardToSex(result.idNo) + ''
          );
          this.$set(
            this.contactExportParam,
            'client_gender_name',
            this.contactExportParam.client_gender === '0' ? '男' : '女'
          );
          this.contactExportParam.client_name = result.custName;
          this.contactExportParam.id_address = result.idAddress;
          this.contactExportParam.id_card_portrait = result.path;
          this.contactPositive.uploaded = true;
          this.contactPositive.src = 'data:image/jpeg;base64,' + base64;
        } else if (result.policeorg || result.idenddate) {
          this.contactExportParam.id_card_national = result.path;
          this.$set(this.contactExportParam, 'issued_depart', result.policeorg);
          this.$set(
            this.contactExportParam,
            'id_begindate',
            result.idbegindate
          );
          if (result.idenddate == '3000-12-31') {
            this.contactLongTimeChecked = true;
          } else {
            this.$set(this.contactExportParam, 'id_enddate', result.idenddate);
          }
          this.contactNegative.uploaded = true;
          this.contactNegative.src = 'data:image/jpeg;base64,' + base64;
        } else {
          _hvueToast({ mes: '未识别的身份证图片' });
        }
      }
    },

    selImgClick(type) {
      this.imgType = type;
      if ($hvue.platform == 0) {
        this.$refs.getImgBoxBrowser.getImg();
      } else {
        this.$refs.getImgBoxThinkive.getImg(type);
      }
    },

    getImgCallBack(imgInfo) {
      _hvueLoading.open();
      uploadFile(
        $hvue.customConfig.serverUrl + '/client/ocrParseIDCard',
        imgInfo.base64,
        {
          success: (data) => {
            _hvueLoading.close();
            if (data.code == 0) {
              let ocrInfo = Object.assign({}, data.data.ocrMsgInfo, {
                path: data.data.path
              });
              if (
                (this.imgType === 'idfrontimg' && !ocrInfo.idNo) ||
                (this.imgType === 'idbackimg' && !ocrInfo.policeorg)
              ) {
                _hvueAlert({ mes: '身份证识别失败' });
                return;
              }
              this.echoOrcInfo(imgInfo.base64, ocrInfo);
            } else {
              _hvueAlert({ mes: data.msg });
            }
          },
          progress: (count) => {
            console.log(count);
          },
          error: (e) => {
            _hvueLoading.close();
            console.log(e);
          }
        },
        {}
      );
    }
  }
};
</script>
