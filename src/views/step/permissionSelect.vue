<!--  -->
<template>
  <section class="main fixed">
    <t-header v-if="!showAgreeDetail" @back="back"></t-header>
    <article v-if="!showAgreeDetail" class="content">
      <div class="com_title">
        <h5>信用权限预选择</h5>
      </div>
      <div class="com_box">
        <ul class="bus_qxlist">
          <li
            v-for="(item, index) in agreeList"
            :key="index"
            @click="checkAgree(index)"
          >
            <span class="icon_check" :class="{ checked: item.checked }">{{
              item.name
            }}</span>
          </li>
        </ul>
      </div>
      <template v-if="agreementShow">
        <h5 class="mid_title">请认真阅读以下相关协议书内容：</h5>
        <div class="com_box">
          <ul class="protocol_list">
            <li
              v-for="(item, index) in agreeList"
              v-show="item.argeeShow"
              :key="index"
              :class="[item.isRead ? 'readed' : 'unread']"
              @click="openAgreeDetail(item, index)"
            >
              <a href="#">{{ `《${item.argeeData.agreementName}》` }}</a>
              <span v-if="item.isRead" class="state">已读</span>
            </li>
          </ul>
        </div>
        <div class="rule_check">
          <span
            class="icon_check"
            :class="{ checked: checkEpaper }"
            @click="signAgree"
          ></span
          >本人已详细阅读并完全理解以上合同及协议，同意签署。
        </div>
      </template>
    </article>
    <footer v-if="!showAgreeDetail" class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="toNext">下一步</a>
      </div>
    </footer>

    <agreementDetail
      :show="showAgreeDetail"
      :info="agreeDetail"
      :is-count="!isCount"
      @callback="agreeCallBack"
    />
  </section>
</template>

<script>
import flowMixin from '@/common/flowMixin';
import agreementDetail from '@/components/agreementDetail';
import {
  queryAgreement,
  getJwtToken,
  doAgreementRecord
} from '@/service/service';

export default {
  components: {
    agreementDetail
  },
  mixins: [flowMixin],
  data() {
    return {
      showAgreeDetail: false,
      agreeDetail: {},
      agreeList: [],
      checkEpaper: false, // 是否勾选同意协议
      selectIndex: 0,
      epaper_sign_json: ''
    };
  },

  computed: {
    agreementShow() {
      let len = this.agreeList.filter((item) => !!item.argeeShow).length;
      return !!len;
    },
    //是否可勾选签署协议
    canCheckEpaper() {
      let len = this.agreeList.filter(
        (item) => item.argeeShow && item.isRead && item.checked
      ).length;
      let len2 = this.agreeList.filter((item) => item.argeeShow).length;
      return len == len2;
    },
    isCount() {
      return (
        this.agreeList &&
        this.agreeList[this.selectIndex] &&
        this.agreeList[this.selectIndex].isRead
      );
    }
  },

  watch: {
    agreementShow() {
      if (!this.agreementShow) {
        this.checkEpaper = false;
      }
    }
  },

  mounted() {},

  methods: {
    checkAgree(index) {
      this.agreeList[index].checked = !this.agreeList[index].checked;
      this.agreeList[index].argeeShow = !this.agreeList[index].argeeShow;
      this.agreeList[index].isRead = false;
      this.checkEpaper = false;
    },
    openAgreeDetail(item, index) {
      this.agreeDetail = item.argeeData;
      this.selectIndex = index;
      this.showAgreeDetail = true;
    },

    agreeCallBack(flag) {
      this.showAgreeDetail = false;
      if (flag) {
        this.agreeList[this.selectIndex].isRead = true;
      }
    },

    async renderingView() {
      const { holderRightsReplace } = this.flowOutputInfo.privProperty;
      const tokenRes = await getJwtToken({
        flowNo: this.flowOutputInfo.flowNodeNo,
        businessType: this.flowOutputInfo.inProperty.bizType
      });
      $h.setSession('jwtToken', tokenRes.data);
      let list = JSON.parse(holderRightsReplace);
      (list || []).map((item) => {
        item.checked = false;
        item.argeeShow = false;
        item.isRead = false;
        let params = {
          flowToken: sessionStorage.getItem('TKFlowToken'),
          bizType: this.flowOutputInfo.inProperty.bizType,
          contractType: item.contractType,
          groupId: item.groupId
        };
        queryAgreement(params).then((res) => {
          _hvueLoading.close();
          if (res.code === 0) {
            item.argeeData = res.data[0];
          } else {
            _hvueAlert({ mes: res.msg });
          }
        });
      });
      this.agreeList = list;
    },
    signAgree() {
      if (!this.canCheckEpaper) {
        _hvueToast({
          mes: '请先阅读协议'
        });
        return;
      } else {
        this.checkEpaper = !this.checkEpaper;
        if (this.checkEpaper) {
          let list = this.agreeList;
          let signList = (list || []).filter((item) => item.isRead);
          let params = {
            flowToken: sessionStorage.getItem('TKFlowToken'),
            bizType: this.flowOutputInfo.inProperty.bizType,
            contractType: signList[0].argeeData.contractType,
            agreementId: signList[0].argeeData.agreementId,
            agreementVersion: signList[0].argeeData.agreementVersion,
            groupId: signList[0].argeeData.groupId
          };
          doAgreementRecord(params)
            .then((data) => {
              return data.data.signBatchno;
            })
            .then((signBatchno) => {
              if (signList.length == 1) {
                let batchNo = signBatchno;
                let nodeId = this.flowOutputInfo.flowNodeNo;
                if (this.flowOutputInfo.inProperty.epaperSignJson) {
                  let defEpaper = JSON.parse(
                    this.flowOutputInfo.inProperty.epaperSignJson
                  );
                  defEpaper.forEach((item) => {
                    if (item.nodeId === nodeId) {
                      item.batchNo = batchNo;
                    } else {
                      defEpaper.push({ nodeId, batchNo });
                    }
                  });
                  this.epaper_sign_json = JSON.stringify(defEpaper);
                } else {
                  let arr = [];
                  arr.push({ nodeId, batchNo });
                  this.epaper_sign_json = JSON.stringify(arr);
                }
                let defEpaper = JSON.parse(this.epaper_sign_json);
                this.epaper_sign_json = JSON.stringify(
                  this.handleRepet(defEpaper)
                );
              } else {
                let signListCopy = signList.slice(1);
                signListCopy.map((item) => {
                  let paramsCopy = {
                    flowToken: sessionStorage.getItem('TKFlowToken'),
                    bizType: this.flowOutputInfo.inProperty.bizType,
                    signBatchno,
                    contractType: item.argeeData.contractType,
                    agreementId: item.argeeData.agreementId,
                    agreementVersion: item.argeeData.agreementVersion,
                    groupId: item.argeeData.groupId
                  };
                  doAgreementRecord(paramsCopy).then((res) => {
                    let batchNo = res.data.signBatchno;
                    let nodeId = this.flowOutputInfo.flowNodeNo;
                    if (this.flowOutputInfo.inProperty.epaperSignJson) {
                      let defEpaper = JSON.parse(
                        this.flowOutputInfo.inProperty.epaperSignJson
                      );
                      defEpaper.forEach((item) => {
                        if (item.nodeId === nodeId) {
                          item.batchNo = batchNo;
                        } else {
                          defEpaper.push({ nodeId, batchNo });
                        }
                      });
                      this.epaper_sign_json = JSON.stringify(defEpaper);
                    } else {
                      let arr = [];
                      arr.push({ nodeId, batchNo });
                      this.epaper_sign_json = JSON.stringify(arr);
                    }
                    let defEpaper = JSON.parse(this.epaper_sign_json);
                    this.epaper_sign_json = JSON.stringify(
                      this.handleRepet(defEpaper)
                    );
                  });
                });
              }
            });
        }
      }
    },
    back() {
      this.prevFlow();
    },
    handleRepet(arr) {
      let resList = [];
      for (var i = 0; i < arr.length; i++) {
        var flag = true;
        for (var j = 0; j < resList.length; j++) {
          if (arr[i].nodeId === resList[j].nodeId) {
            flag = false;
          }
        }
        if (flag) {
          resList.push(arr[i]);
        }
      }
      return resList;
    },
    toNext() {
      if (!this.agreementShow) {
        this.nextFlow();
      } else {
        if (!this.checkEpaper) {
          _hvueToast({
            mes: '请勾选同意协议'
          });
          return;
        } else {
          let holderList = this.agreeList.filter((item) => item.isRead);
          let holderRights = holderList.map((item) => {
            return item.holderRight;
          });
          holderRights = holderRights.filter((i) => i && i.trim()).join(',');
          this.nextFlow({
            epaperSignJson: this.epaper_sign_json,
            epaper_sign_json: this.epaper_sign_json,
            choose_holder_rights: holderRights
          });
        }
      }
    }
  }
};
</script>
<style lang="scss" scoped></style>
