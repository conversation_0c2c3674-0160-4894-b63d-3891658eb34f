<template>
  <section class="main fixed" data-page="home">
    <t-header :show-back="false"></t-header>
    <article class="content">
      <div class="result_page">
        <div class="result_tips">
          <div class="icon ing"></div>
          <h5>两融开户申请提交成功</h5>
          <p>
            预计
            <span class="ared">1</span> 个工作日通过审核，请注意查收手机短信
          </p>
        </div>
        <div class="result_step">
          <ul>
            <li class="s1 off"><i></i><span>申请提交</span></li>
            <li class="s2" :class="{ on: showStep2, off: showStep3 }">
              <i></i><span>开户审核</span>
            </li>
            <li class="s4" :class="{ on: showStep3 }">
              <i></i><span>电话回访</span>
            </li>
            <li class="s3"><i></i><span>开通成功</span></li>
          </ul>
        </div>
        <div class="result_info">
          <ul>
            <li>
              <span class="tit">开户营业部</span>
              <p>{{ branchName }}</p>
            </li>
            <li>
              <span class="tit">三方存管</span>
              <p>
                {{ bankName }}
                <span v-if="isShowBankNo">{{
                  bankNo.replace(/^(\d{4})\d+(\d{4})$/, '$1 **** **** $2')
                }}</span>
              </p>
            </li>
          </ul>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="toIndex">返回首页</a>
        <!-- <a class="p_button border" @click="flowReset">重新办理</a> -->
      </div>
    </footer>
  </section>
</template>

<script>
import flowMixin from '@/common/flowMixin';
import { getSysBranchInfo, queryIns } from '@/service/service';

export default {
  mixins: [flowMixin],
  data() {
    return {
      branchName: '',
      bankNo: '',
      bankName: '',
      isShowBankNo: false,
      status: '0', // 任务状态 0：待办，1：办理中，2：通过，3：驳回，4：取消
      pageShow: false,
      taskType: '0' // 任务类型 1: 预审任务 2：终审任务 3：办理任务 4：见证任务 5：电话回访
    };
  },

  computed: {
    showStep2() {
      return this.taskType === '2' ? true : false;
    },

    showStep3() {
      return this.taskType === '5' ? true : false;
    }
  },

  methods: {
    initData() {
      let _this = this;
      let { preBranchNo, bankNo, bankName } = _this.flowOutputInfo.inProperty;
      this.bankNo = bankNo;
      this.bankName = bankName;
      getSysBranchInfo({ branchNo: preBranchNo }).then((res) => {
        if (res.code == 0) {
          this.branchName = res.data[0] && res.data[0].branchName;
          this.pageShow = true;
        } else {
          this.pageShow = true;
          // _hvueAlert({ mes: data.msg });
        }
      });
    },

    renderingView() {
      this.initData();
      const { bizType } = this.flowOutputInfo.inProperty;
      this.isShowBankNo =
        this.flowOutputInfo.privProperty.isShowBankNo === '0' ? false : true;
      queryIns({
        bizType
      }).then((res) => {
        this.taskType = res.data.taskType;
        _hvueLoading.close();
      });
    },

    flowReset() {
      // 重新新开流程
      this.$store.commit('flow/setFlowBack', true);
      import('@/common/flowMixin.js').then((a) => {
        a.initFlow.call(this, this.flowOutputInfo.inProperty.bizType, '', true);
      });
    }
  }
};
</script>
