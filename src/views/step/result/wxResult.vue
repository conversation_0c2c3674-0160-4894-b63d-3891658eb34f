<template>
  <section class="main fixed white_bg" data-page="home">
    <t-header :show-back="false"></t-header>
    <article v-if="isSubmitCounter !== '0'" class="content">
      <div class="result_page">
        <div class="result_tips">
          <div class="icon" :class="resultClass"></div>
          <h5>{{ resultTips }}</h5>
          <p v-if="dataResult.bandleState !== BANDLE_STATE.SUCCESS">
            {{ dataResult.bandleMsg }}
          </p>
          <p v-if="dataResult.bandleState">提交时间：{{ nowDate }}</p>
        </div>
      </div>
    </article>
    <article v-else class="content">
      <div class="result_page">
        <div class="result_tips">
          <div class="icon ing"></div>
          <h5>成功提交，处理中</h5>
          <p>
            {{ resultDesc }}
          </p>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn black">
        <a class="p_button" @click="back">返回首页</a>
      </div>
    </footer>
  </section>
</template>

<script>
import flowMixin from '@/common/flowMixin';
import {
  BANDLE_STATE,
  PROCESS_STATUS,
  TASK_TYPE,
  TASK_STATUS
} from '@/common/enumeration';
import { flowSubmit, flowQueryIns, flowEnd } from '@/service/service';
import { wakeLoginApp } from '@/common/util';

export default {
  mixins: [flowMixin],
  data() {
    return {
      BANDLE_STATE: BANDLE_STATE,
      TASK_TYPE,
      TASK_STATUS,
      resultDesc: '',
      isSubmitCounter: '0',
      needBackApp: '0',
      dataResult: () => {},
      nowDate: ''
    };
  },

  computed: {
    isApp() {
      return $hvue.platform !== '0';
    },
    resultClass() {
      if (this.dataResult.bandleState === BANDLE_STATE.SUCCESS) {
        return 'ok';
      } else if (this.dataResult.bandleState === BANDLE_STATE.FAIL) {
        return 'fail';
      } else {
        return 'ing';
      }
    },
    resultTips() {
      if (this.dataResult.bandleState === BANDLE_STATE.SUCCESS) {
        return '办理成功';
      } else if (this.dataResult.bandleState === BANDLE_STATE.FAIL) {
        return '办理失败';
      } else {
        return '办理中';
      }
    }
  },

  methods: {
    back() {
      if (this.isApp && this.needBackApp === '1') {
        this.$store.commit('user/setUserInfo', null);
        localStorage.removeItem('vuex');
        sessionStorage.clear();
        wakeLoginApp();
      } else {
        this.toIndex();
      }
    },

    renderingView() {
      const { resultDesc, isSubmitCounter, needBackApp } =
        this.flowOutputInfo.privProperty; // 配置的文字提示
      this.isSubmitCounter = isSubmitCounter;
      this.needBackApp = needBackApp;
      this.resultDesc = resultDesc;
      const flowToken = sessionStorage.getItem('TKFlowToken');
      const accArr = [PROCESS_STATUS.ACCEPT_COMPLETED]; // 配置需要提交受理结果的状态
      flowQueryIns({ flowToken }).then((res) => {
        if (accArr.includes(res.data.status)) {
          if (isSubmitCounter === '0') {
            // 办理完成
            if (
              res.data.taskType === TASK_TYPE.DO_TASK &&
              res.data.taskStatus !== TASK_STATUS.TO_DO &&
              res.data.taskStatus !== TASK_STATUS.DOING
            ) {
              // 完结流程
              flowEnd({ flowToken });
            }
          } else {
            flowSubmit({ flowToken }, { loading: false }).then((data) => {
              this.dataResult = data.data[0];
              this.nowDate = new Date().format('yyyy-MM-dd hh:mm:ss');
            });
          }
        } else {
          this.nextFlow();
          if (isSubmitCounter !== '0') {
            flowSubmit({ flowToken }, { loading: false }).then((data) => {
              this.dataResult = data.data[0];
              this.nowDate = new Date().format('yyyy-MM-dd hh:mm:ss');
            });
          }
        }
      });
    }
  }
};
</script>
