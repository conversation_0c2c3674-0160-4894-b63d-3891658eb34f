<template>
  <section class="main fixed">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="business_page">
        <div class="add-check_account">
          <p class="tit">请选择需要开通北交所定向可转债权限的账户</p>
          <ul class="add-account_list">
            <li
              v-for="(item, index) in viewList"
              :key="index"
              :class="{
                border_bottom: item.isOpenRights === IS_OPEN_RIGHTS.possess,
                icon_check: openFlag(item),
                checked: item.checked,
                radio: item.isOpenRights !== IS_OPEN_RIGHTS.possess
              }"
              @click="selectAcc(item, index)"
            >
              <p>{{ item.exchangeTypeName }}：{{ item.stockAccount }}</p>
              <!-- <span v-if="item.isOpenRights === IS_OPEN_RIGHTS.possess">冻结</span> -->
            </li>
            <!-- <li class="radio checked border_bottom">特转A *********</li>
						<li class="radio checked border_bottom">深A *********</li>
						<li class="radio checked border_bottom">沪A *********</li> -->
            <!-- <li class="border_bottom">特转A *********<span>冻结</span></li> -->
          </ul>
        </div>
      </div>
    </article>
    <footer>
      <div class="add-btn_box">
        <a href="#" @click="submitForm">下一步</a>
      </div>
    </footer>
  </section>
</template>

<script>
import flowMixin from '@/common/flowMixin';
import { DICT_TYPE, HOLDER_STATUS, IS_OPEN_RIGHTS } from '@/common/enumeration';
import { stockAccountQry } from '@/service/service';
import { getDictData } from '@/service/commonService';

export default {
  mixins: [flowMixin],
  data() {
    return {
      viewList: [],
      IS_OPEN_RIGHTS
    };
  },
  computed: {
    openAccList() {
      return this.viewList.filter((item) => {
        return item.checked;
      });
    }
  },
  methods: {
    back() {
      this.prevFlow();
    },

    async renderingView() {
      _hvueLoading.close();
      let _this = this;
      const { clientId, bizType, branchNo } = _this.flowOutputInfo.inProperty;
      const { accountState, capitalAccountKind, dicExchangeType, mainFlag } =
        _this.flowOutputInfo.privProperty;
      const dictData = await getDictData({
        dictType: DICT_TYPE.DIC_EXCHANGE_TYPE
      });
      const dictDataList = dictData.data[DICT_TYPE.DIC_EXCHANGE_TYPE];
      stockAccountQry({
        clientId,
        bizType,
        branchNo,
        accountState,
        capitalAccountKind,
        dicExchangeType,
        mainFlag
      })
        .then((data) => {
          const holderList = data.data.holderList;
          for (let item of holderList) {
            for (let d of dictDataList) {
              if (d.dictValue === item.exchangeType) {
                item.exchangeTypeName = d.dictLabel;
                break;
              }
            }
          }
          _this.viewList = holderList;
        })
        .catch((err) => {
          _hvueToast({ mes: err });
        });
    },

    selectAcc(item, index) {
      if (!this.openFlag(item)) return;
      if (item.checked) {
        this.$set(item, 'checked', false);
      } else {
        this.$set(item, 'checked', true);
      }
      this.viewList[index] = item;
    },

    openFlag(item) {
      return (
        item.holderStatus === HOLDER_STATUS.NORMAL &&
        item.isOpenRights !== IS_OPEN_RIGHTS.possess
      );
    },

    submitForm() {
      if (this.openAccList.length === 0) {
        _hvueToast({
          mes: '请选择需要开通的账户'
        });
        return;
      }
      this.nextFlow({
        qryAccountParam: JSON.stringify(this.openAccList)
      });
    }
  }
};
</script>

<style></style>
