<template>
  <section class="main fixed">
    <t-header></t-header>
    <article class="content">
      <div class="business_page">
        <div class="add-check_account">
          <p class="tit">请选择需要开通北交所定向可转债权限的账户</p>
          <ul class="add-account_list">
            <li class="radio checked border_bottom">特转A *********</li>
            <li class="radio checked border_bottom">深A *********</li>
            <li class="radio checked border_bottom">沪A *********</li>
          </ul>
        </div>
      </div>
    </article>
    <footer>
      <div class="add-btn_box">
        <a @click="submitForm">下一步</a>
      </div>
    </footer>
  </section>
</template>

<script>
// import { HOLDER_STATUS, IS_OPEN_RIGHTS } from '@/common/enumeration';
// import { getDictData } from '@/service/commonService';

export default {
  data() {
    return {
      viewList: [],
      // IS_OPEN_RIGHTS
    };
  },
  computed: {
    openAccList() {
      return this.viewList.filter((item) => {
        return item.checked;
      });
    }
  },
  methods: {
    // async renderingView() {
    //   _hvueLoading.close();
    //   let _this = this;
    //   const { clientId, bizType, branchNo } = _this.flowOutputInfo.inProperty;
    //   // const {
    //   // 	accountState,
    //   // 	capitalAccountKind,
    //   // 	dicExchangeType,
    //   // 	mainFlag
    //   // } = _this.flowOutputInfo.privProperty;
    //   // const dictData = await getDictData({
    //   // 	dictType: DICT_TYPE.DIC_EXCHANGE_TYPE
    //   // });
    //   // const dictDataList = dictData.data[DICT_TYPE.DIC_EXCHANGE_TYPE];
    //   stockAccountQry({
    //     clientId,
    //     bizType,
    //     branchNo
    //     // accountState,
    //     // capitalAccountKind,
    //     // dicExchangeType,
    //     // mainFlag
    //   })
    //     .then((data) => {
    //       const holderList = data.data.holderList;
    //       for (let item of holderList) {
    //         for (let d of dictDataList) {
    //           if (d.dictValue === item.exchangeType) {
    //             item.exchangeTypeName = d.dictLabel;
    //             break;
    //           }
    //         }
    //       }
    //       _this.viewList = holderList;
    //     })
    //     .catch((err) => {
    //       _hvueToast({ mes: err });
    //     });
    // },

    // selectAcc(item, index) {
    //   if (!this.openFlag(item)) return;
    //   if (item.checked) {
    //     this.$set(item, 'checked', false);
    //   } else {
    //     this.$set(item, 'checked', true);
    //   }
    //   this.viewList[index] = item;
    // },

    // openFlag(item) {
    //   return (
    //     item.holderStatus === HOLDER_STATUS.NORMAL &&
    //     item.isOpenRights !== IS_OPEN_RIGHTS.possess
    //   );
    // },

    // submitForm() {
    //   if (this.openAccList.length === 0) {
    //     _hvueToast({
    //       mes: '请选择需要开通的账户'
    //     });
    //     return;
    //   }
    //   this.nextFlow({
    //     qryAccountParam: JSON.stringify(this.openAccList)
    //   });
    // }
  }
};
</script>

<style></style>
