<template>
  <section class="main fixed" data-page="home">
    <t-header @back="backEvent" />
    <article class="content">
      <div class="com_title">
        <h5>市场及密码设置</h5>
      </div>
      <div>
        <h5 class="mid_title">请设置拟开通的信用账户</h5>
        <div class="com_box">
          <ul class="type_selectlist">
            <li>
              <h5>深圳市场信用账户</h5>
              <p v-if="!szTips">开通深圳市场可进行该市场的融资融券交易</p>
              <p v-else>{{ szTips }}</p>
              <span
                class="icon_check"
                :class="{ checked: openSz, disabled: !canOpenSz }"
                @click="changeType('openSz', canOpenSz)"
              ></span>
            </li>
            <li>
              <h5>上海市场信用账户</h5>
              <p v-if="!shTips">开通上海市场可进行该市场的融资融券交易</p>
              <p v-else>{{ shTips }}</p>
              <span
                class="icon_check"
                :class="{ checked: openSh, disabled: !canOpenSh }"
                @click="changeType('openSh', canOpenSh)"
              ></span>
            </li>
          </ul>
        </div>
      </div>
      <div>
        <h5 class="mid_title">信用权限预选择</h5>
        <div class="com_box">
          <ul class="bus_qxlist">
            <li
              v-for="(item, index) in holderList"
              :key="index"
              @click="checkHolder(index, item)"
            >
              <span
                class="icon_check"
                :class="{ checked: item.checked, disabled: item.disabled }"
                >{{ item.name }}</span
              >
            </li>
          </ul>
        </div>
      </div>
      <div class="pwors_select">
        <p>资金密码与交易密码一致</p>
        <div class="switch">
          <input type="checkbox" :checked="moneySomeTrade" @click="sameClick" />
          <div class="switch-inner">
            <div class="switch-arrow"></div>
          </div>
        </div>
      </div>
      <div v-if="!moneySomeTrade">
        <h5 class="mid_title">请设置资金密码</h5>
        <div class="com_box">
          <div class="input_form">
            <div class="input_text pword">
              <!-- <span :class="moneyPwd1 ? 'tit active' : 'tit'">请输入6位数字资金密码</span> -->
              <input
                v-model="moneyPwd1"
                class="t1"
                :type="moneyPwdShow ? 'text' : 'password'"
                placeholder="请输入"
                maxlength="6"
              />
              <a
                class="icon_eye"
                :class="{ show: moneyPwdShow }"
                @click.stop="pwdShowClick('moneyPwd')"
              ></a>
            </div>
            <div class="input_text pword">
              <!-- <span :class="moneyPwd2 ? 'tit active' : 'tit'">请重复输入6位数字资金密码</span> -->
              <input
                v-model="moneyPwd2"
                class="t1"
                :type="moneyPwdShow ? 'text' : 'password'"
                placeholder="请输入"
                maxlength="6"
              />
            </div>
          </div>
        </div>
      </div>
      <div>
        <h5 class="mid_title">
          {{ moneySomeTrade ? '请设置密码' : '请设置交易密码' }}
        </h5>
        <div class="com_box">
          <div class="input_form">
            <div class="input_text pword">
              <!-- <span :class="tradePwd1 ? 'tit active' : 'tit'">{{
								moneySomeTrade ? '请输入6位数字密码' : '请输入6位数字交易密码'
							}}</span> -->
              <input
                v-model="tradePwd1"
                class="t1"
                :type="tradePwdShow ? 'text' : 'password'"
                placeholder="请输入"
                maxlength="6"
              />
              <a
                class="icon_eye"
                :class="{ show: tradePwdShow }"
                @click.stop="pwdShowClick('tradePwd')"
              ></a>
            </div>
            <div class="input_text pword">
              <!-- <span :class="tradePwd2 ? 'tit active' : 'tit'">{{
								moneySomeTrade ? '请重复输入6位数字密码' : '请重复输入6位数字交易密码'
							}}</span> -->
              <input
                v-model="tradePwd2"
                class="t1"
                :type="tradePwdShow ? 'text' : 'password'"
                placeholder="请输入"
                maxlength="6"
              />
            </div>
          </div>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="toNext">下一步</a>
      </div>
    </footer>
  </section>
</template>

<script>
import {
  // checkOpenAccount,
  stockAccountQry,
  // getJwtToken,
  // queryAgreement,
  // doAgreementRecord,
  businessCanQry
} from '@/service/service.js';
// import {
//   DICT_TYPE,
//   EXCHANGE_TYPE,
//   HOLDER_STATUS,
//   IS_OPEN_RIGHTS
// } from '@/common/enumeration';
// import { getDictData } from '@/service/commonService';
import flowMixin from '@/common/flowMixin';
import { getPwdEncryption } from '@/common/util.js';

export default {
  name: 'SetMarketAndPwd',
  mixins: [flowMixin],
  data() {
    return {
      holderList: [],
      openSh: null, // 是否开通上海
      openSz: null, // 是否开通深圳
      canOpenSh: true, // 可以开通上海
      canOpenSz: true, // 可以开通深圳
      shTips: '',
      szTips: '',
      tradePwd1: '', //交易密码1
      tradePwd2: '', //交易密码2
      tradePwdShow: false, //交易密码是否加密，false则加密
      moneyPwd1: '', //资金密码1
      moneyPwd2: '', //资金密码2
      moneyPwdShow: false, //资金密码是否加密，false则加密
      moneySomeTrade: true, //交易密码和资金密码是否一致，默认一致true,若false,则显示资金密码设置模块
      errTxt: '请输入六位数字密码' //错误提示
    };
  },
  watch: {
    openSh(val) {
      if (val && this.canOpenSh) {
        this.holderList[2].disabled = false;
      } else {
        this.holderList[2].disabled = true;
        this.holderList[2].checked = false;
      }
    },
    openSz(val) {
      if (val && this.canOpenSz) {
        this.holderList[1].disabled = false;
      } else {
        this.holderList[1].disabled = true;
        this.holderList[1].checked = false;
      }
    }
  },
  methods: {
    backEvent() {
      this.prevFlow();
    },
    checkHolder(index, item) {
      if (item.disabled) {
        return;
      }
      this.holderList[index].checked = !this.holderList[index].checked;
    },
    changeType(type, canChange) {
      if (!canChange) {
        return;
      }
      this[type] = !this[type];
    },
    confirm() {
      let holderRights = this.holderList
        .filter((item) => item.checked)
        .map((item) => {
          return item.holderRight;
        })
        .filter((i) => i && i.trim())
        .join(',');
      let holderRightsName = this.holderList
        .filter((item) => item.checked)
        .map((item) => {
          return item.name;
        })
        .join('，');
      this.nextFlow({
        choose_biz_type: holderRightsName,
        exchange_type:
          this.openSh && this.openSz
            ? '1,2'
            : this.openSh
            ? '1'
            : this.openSz
            ? '2'
            : '',
        fund_password: this.moneySomeTrade
          ? ''
          : 'encrypt:' + getPwdEncryption(this.moneyPwd1),
        fundPassword: this.moneySomeTrade
          ? ''
          : 'encrypt:' + getPwdEncryption(this.moneyPwd1),
        is_tradepwd_fundpwd_same: this.moneySomeTrade ? '1' : '0',
        trade_password: 'encrypt:' + getPwdEncryption(this.tradePwd1),
        tradePassword: 'encrypt:' + getPwdEncryption(this.tradePwd1),
        // pr_expect_profit: '1',
        // pr_invest_kind: '1',
        // pr_invest_term: '1',
        choose_holder_rights: holderRights
      });
      this.showModal = false;
    },
    // 下一步
    async toNext() {
      // let holderRights = this.holderList
      //   .filter((item) => item.checked)
      //   .map((item) => {
      //     return item.holderRight;
      //   })
      //   .filter((i) => i && i.trim())
      //   .join(',');
      if (!this.openSh && !this.openSz)
        return _hvueToast({ mes: '请选择需要开通的市场' });
      if (this.moneySomeTrade) {
        this.moneyPwd1 = this.tradePwd1;
        this.moneyPwd2 = this.tradePwd2;
      }
      // if (!holderRights) {
      // 	_hvueToast({
      // 		timeout: 1500,
      // 		mes: '请先选择信用权限'
      // 	});
      // 	return;
      // }
      if (
        !this.verifyYes(this.moneyPwd1, 'money') ||
        !this.verifyYes(this.moneyPwd2, 'money') ||
        !this.verifyYes(this.tradePwd1, 'trade') ||
        !this.verifyYes(this.tradePwd2, 'trade') ||
        !this.verifySame('trade') ||
        !this.verifySame('money')
      ) {
        return;
      }
      this.confirm();
    },
    async renderingView() {
      // let dholderList = JSON.parse(this.flowOutputInfo.privProperty.chooseHolderRightsJson);
      businessCanQry({ bizType: this.flowOutputInfo.inProperty.bizType }).then(
        async (res) => {
          _hvueLoading.close();
          let dholderList = [];
          res.data.forEach((item) => {
            if (item.businessId !== this.flowOutputInfo.inProperty.bizType)
              dholderList.push({
                ...JSON.parse(item.propertyConf),
                name: item.businessName
              });
            // if (item.businessId !== this.flowOutputInfo.inProperty.bizType)
            // 	dholderList.push({
            // 		...JSON.parse(item.propertyConf),
            // 		name: item.businessName
            // 	});
          });
          dholderList = dholderList.map((item) => {
            return {
              ...item,
              checked: false,
              disabled: false
            };
          });
          // const dictData = await getDictData();
          // const dictDataList = dictData.data[DICT_TYPE.DIC_EXCHANGE_TYPE];
          stockAccountQry({
            capitalAccountKind: '0',
            bizType: '010174'
          }).then((data) => {
            const holderList = data.data.holderList;
            let shDetail = holderList.find((item) => item.exchangeType === '1');
            let szDetail = holderList.find((item) => item.exchangeType === '2');
            this.openSh = shDetail.isOpenRights === '0' ? true : false;
            if (!this.openSh) {
              this.shTips = '抱歉，您在我司已开通沪市信用账户，不可重复开通';
            }
            if (shDetail.holderStatus !== '0') {
              this.canOpenSh = false;
              this.shTips = '抱歉，您的账户状态异常，请先调整至正常状态';
            }
            this.openSz = szDetail.isOpenRights === '0' ? true : false;
            if (!this.openSz) {
              this.szTips = '抱歉，您在我司已开通深市信用账户，不可重复开通';
            }
            if (szDetail.holderStatus !== '0') {
              this.canOpenSz = false;
              this.szTips = '抱歉，您的账户状态异常，请先调整至正常状态';
            }
            this.holderList = dholderList;
          });
        }
      );
    },
    //密码是否可见设置
    pwdShowClick(type) {
      if (type === 'moneyPwd') {
        //资金密码设置
        this.moneyPwdShow = !this.moneyPwdShow;
        if (this.moneyPwdShow) {
          let moneyPwd1 = this.moneyPwd1;
          let moneyPwd2 = this.moneyPwd2;
          this.moneyPwd1 = '';
          this.moneyPwd2 = '';
          this.$nextTick(function () {
            this.moneyPwd1 = moneyPwd1;
            this.moneyPwd2 = moneyPwd2;
          });
        }
      } else if (type === 'tradePwd') {
        //交易密码设置
        this.tradePwdShow = !this.tradePwdShow;
        if (this.tradePwdShow) {
          let tradePwd1 = this.tradePwd1;
          let tradePwd2 = this.tradePwd2;
          this.tradePwd1 = '';
          this.tradePwd2 = '';
          this.$nextTick(function () {
            this.tradePwd1 = tradePwd1;
            this.tradePwd2 = tradePwd2;
          });
        }
      }
    },
    verifyYes(pwd) {
      if (pwd.trim() === '') {
        this.errTxt = '请输入六位数字密码';
      } else if (!/^\d{6}$/.test(pwd)) {
        this.errTxt = '请输入六位数字密码';
      } else if (/(\d)\d*\1\d*\1/.test(pwd)) {
        this.errTxt = '有数字重复出现三次';
      } else if (/(\d{2,})\1|(\d)\2{2,}/.test(pwd)) {
        this.errTxt = '连续出现两组相同数字';
      } else if (
        /(?:1234|2345|3456|4567|5678|6789|7890|9876|8765|7654|6543|5432|4321)/.test(
          pwd
        )
      ) {
        this.errTxt = '出现四位及以上连续数字';
      }
      // else if (idCardNumber.substr(-6) === pwd) {
      //   this.errTxt = '密码不能为身份证后6位数字';
      // } else if (idCardNumber.indexOf(pwd) > -1) {
      //   this.errTxt = '密码不能为身份证中的连续6位数字';
      // }
      else {
        this.errTxt = 'pass';
      }

      if (this.errTxt !== 'pass') {
        _hvueToast({
          timeout: 1500,
          mes: this.errTxt
        });
        return false;
      }
      return true;
    },
    //交易密码和资金密码是否一致设置
    sameClick() {
      this.moneySomeTrade = !this.moneySomeTrade;
      if (this.moneySomeTrade) {
        this.tradePwd1 = '';
        this.tradePwd2 = '';
      }
    },
    //判断两次密码设置是否一致
    verifySame(type) {
      let txt = '';
      let pwd1;
      let pwd2;
      if (type === 'trade') {
        txt = '交易';
        pwd1 = this.tradePwd1;
        pwd2 = this.tradePwd2;
      } else if (type === 'money') {
        txt = '资金';
        pwd1 = this.moneyPwd1;
        pwd2 = this.moneyPwd2;
      }
      if (pwd1 !== pwd2) {
        _hvueToast({
          timeout: 1200,
          mes: '两次' + txt + '密码不一致'
        });
        return false;
      }
      return true;
    }
  }
};
</script>
<style lang="scss" scoped></style>
