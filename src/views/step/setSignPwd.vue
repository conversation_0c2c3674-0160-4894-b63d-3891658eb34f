<!--  -->
<template>
  <section class="main fixed">
    <t-header @back="back"></t-header>
    <article class="content">
      <h5 class="com_title">设置签署密码</h5>
      <div class="com_box">
        <div class="input_form">
          <div class="input_text text">
            <span :class="['tit', signPwd1 ? 'active' : '']">签署密码</span>
            <input
              v-model="signPwd1"
              class="t1"
              :type="signPwdShow ? 'text' : 'password'"
              maxlength="16"
              placeholder="请输入签署密码"
            />
            <a
              class="icon_eye"
              :class="{ show: signPwdShow }"
              @click.stop="signPwdShow = !signPwdShow"
            ></a>
          </div>
          <div class="input_text text">
            <span :class="['tit', signPwd2 ? 'active' : '']">确认密码</span>
            <input
              v-model="signPwd2"
              class="t1"
              :type="signPwdShow ? 'text' : 'password'"
              maxlength="16"
              placeholder="确认密码"
            />
          </div>
        </div>
      </div>
      <div class="wx_tips">
        <p>
          温馨提示：<br />签署密码将被用于电子协议签署，请妥善保管;密码设置规则如下：<br />·
          1-16个字符； <br />· 至少包含数字、字母、特殊字符任意两种组合；
        </p>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="toNext">下一步</a>
      </div>
    </footer>
  </section>
</template>

<script>
import flowMixin from '@/common/flowMixin';
import {
  getJwtToken,
  checkElectronAgreePwd,
  saveElectronAgreePwd
} from '@/service/service';
import { getPwdCbcEncryption } from '@/common/util.js';
export default {
  mixins: [flowMixin],
  data() {
    return {
      signPwd1: '', // 签署密码1
      signPwd2: '', // 签署密码2
      signPwdShow: false // 签署是否加密，false则加密
      // errTxt: '请输入六位数字密码'
    };
  },

  mounted() {
    _hvueLoading.open();
    checkElectronAgreePwd().then((res) => {
      if (res.code == 0) {
        if (res.data.agreeSignPwd) {
          _hvueLoading.close();
          this.nextFlow({ sign_pwd: res.data.agreeSignPwd });
        } else {
          _hvueLoading.close();
          this.pageShow = true;
        }
      }
    });
  },

  methods: {
    back() {
      this.prevFlow();
    },
    renderingView() {
      _hvueLoading.close();
      // TODO 从上下文拿到身份证号
      // this.idNo = '440681198610152044';
      // this.idNo = this.flowOutputInfo.inProperty.idNo;
    },
    verifyYes(pwd) {
      if (pwd.trim() === '') {
        this.errTxt = '签署密码不能为空，请重新输入';
      }
      // else if (!/^\d{6}$/.test(pwd)) {
      // 	this.errTxt = '签署密码格式不正确，请重新输入';
      // } else if (/(\d)\d*\1\d*\1/.test(pwd)) {
      // 	this.errTxt = '签署密码有数字重复出现三次，请重新输入';
      // } else if (/(\d{2,})\1|(\d)\2{2,}/.test(pwd)) {
      // 	this.errTxt = '签署密码连续出现两组相同数字，请重新输入';
      // } else if (/(?:1234|2345|3456|4567|5678|6789|7890|9876|8765|7654|6543|5432|4321)/.test(pwd)) {
      // 	this.errTxt = '签署密码出现四位及以上连续数字，请重新输入';
      // }
      // else if (this.idNo.substr(-6) === pwd) {
      // 	this.errTxt = '签署密码不能为身份证后6位数字，请重新输入';
      // } else if (this.idNo.indexOf(pwd) > -1) {
      // 	this.errTxt = '签署密码不能为身份证中的连续6位数字，请重新输入';
      // }
      else {
        this.errTxt = 'pass';
      }

      if (this.errTxt !== 'pass') {
        _hvueToast({
          timeout: 1200,
          mes: this.errTxt
        });
        return false;
      }
      return true;
    },
    verifySame() {
      if (this.signPwd2.trim() === '') {
        _hvueToast({
          timeout: 1200,
          mes: '确认签署密码不能为空，请重新输入'
        });
      }
      if (this.signPwd1 !== this.signPwd2) {
        _hvueToast({
          timeout: 1200,
          mes: '两次输入的签署密码不一致，请重新输入'
        });
        return false;
      }
      return true;
    },
    async toNext() {
      if (!this.verifyYes(this.signPwd1, 'pwd1') || !this.verifySame()) {
        return;
      } else {
        const tokenRes = await getJwtToken({
          flowNo: this.flowOutputInfo.flowNodeNo,
          businessType: this.flowOutputInfo.inProperty.bizType
        });
        $h.setSession('jwtToken', tokenRes.data);
        saveElectronAgreePwd({
          flowToken: sessionStorage.getItem('TKFlowToken'),
          agreeSignPwd: getPwdCbcEncryption(this.signPwd1)
          // agreeSignPwd: this.signPwd1
        })
          .then((res) => {
            // this.nextFlow({ sign_pwd: 'encrypt:' + getPwdEncryption(this.signPwd1) });
            this.nextFlow({ sign_pwd: getPwdCbcEncryption(this.signPwd1) });
          })
          .catch((err) => {
            _hvueToast({ mes: err });
          });
      }
    }
  }
};
</script>
<style lang="scss" scoped></style>
