<template>
  <section class="main fixed" data-page="home">
    <t-header @back="back"></t-header>
    <article class="content">
      <h5 class="com_title">签署协议</h5>
      <h5 class="mid_title">请认真阅读以下相关协议书内容：</h5>
      <div class="com_box">
        <ul class="protocol_list">
          <li
            v-for="(item, index) in holderList"
            :key="index"
            :class="{ unread: !item.hasRead, readed: item.hasRead }"
            @click="toAgreeDetail(item, index)"
          >
            <a>{{ item.agreementName }}</a>
            <span class="state">{{ item.hasRead ? '已读' : '未读' }}</span>
          </li>
        </ul>
      </div>
      <div class="rule_check">
        <span
          class="icon_check"
          :class="{ checked: checked }"
          @click="toSign"
        ></span
        >本人已详细阅读并完全理解以上合同及协议，同意签署
      </div>
    </article>
    <agreementDetail
      :show="showAgreeDetail"
      :info="agreeDetail"
      :is-count="true"
      @callback="agreeCallBack"
    />
    <footer class="footer">
      <div class="ce_btn"><a class="p_button" @click="toNext">下一步</a></div>
    </footer>

    <!-- 弹出层 -->
    <div v-if="showPassword">
      <div class="dialog_overlay" style="z-index: 1999"></div>
      <div class="layer_box spel">
        <div class="layer_tit">
          <a class="ly_opea_cancel" @click="showPassword = false">取消</a>
          <h3>请输入签署密码</h3>
          <a class="ly_opea_sure" @click="toConfirmPassword">确认</a>
        </div>
        <div class="layer_cont">
          <div class="com_box">
            <div class="input_form am_loginform">
              <div class="input_text">
                <input
                  id="password"
                  v-model="password"
                  class="t1"
                  type="password"
                  :maxlength="16"
                  placeholder="请输入签署密码"
                  autocomplete="off"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import flowMixin from '@/common/flowMixin';
// import multLineInput from '@/components/multLineInput';
import {
  investProInfoQry,
  getJwtToken,
  queryAgreement,
  doAgreementRecord,
  businessCanQry,
  validationElectronAgreePwd
} from '@/service/service';
import agreementDetail from '@/components/agreementDetail';
import { getPwdEncryption } from '@/common/util';
import { getPwdCbcEncryption } from '@/common/util.js';
export default {
  components: { agreementDetail },
  mixins: [flowMixin],
  data() {
    return {
      showAgreeDetail: false,
      showPassword: false,
      agreeDetail: {},
      nowAgreeIndex: 0,
      holderList: [],
      checked: false,
      password: ''
    };
  },
  methods: {
    back() {
      this.prevFlow();
    },
    toAgreeDetail(item, index) {
      (this.showAgreeDetail = true), (this.nowAgreeIndex = index);
      this.agreeDetail = item;
    },

    async renderingView() {
      businessCanQry({ bizType: this.flowOutputInfo.inProperty.bizType }).then(
        async (res) => {
          let holderList = [];
          res.data.forEach((item) => {
            if (item.businessId !== this.flowOutputInfo.inProperty.bizType)
              holderList.push({
                ...JSON.parse(item.propertyConf),
                name: item.businessName
              });
          });
          holderList = holderList.map((item) => {
            return {
              ...item,
              checked: false,
              disabled: false
            };
          });
          let hasCheckedHolderList = this.flowOutputInfo.inProperty
            .chooseHolderRights
            ? this.flowOutputInfo.inProperty.chooseHolderRights
                .split(',')
                .map((item) => {
                  return holderList.find((it) => it.holderRight === item);
                })
            : [];
          const tokenRes = await getJwtToken({
            flowNo: this.flowOutputInfo.flowNodeNo,
            businessType: this.flowOutputInfo.inProperty.bizType
          });
          $h.setSession('jwtToken', tokenRes.data);
          let PromiseParams = hasCheckedHolderList.map((item) => {
            let params = {
              flowToken: sessionStorage.getItem('TKFlowToken'),
              bizType: this.flowOutputInfo.inProperty.bizType,
              groupId: item.groupId,
              contractType: item.contractType,
              agreementSubtype: item.agreementSubtype
                ? item.agreementSubtype
                : ''
            };
            return new Promise((resolve) => {
              queryAgreement(params).then((res) => {
                resolve(res.data);
              });
            });
          });
          let agreePrintJson = this.flowOutputInfo.inProperty.agreePrintJson
            ? JSON.parse(this.flowOutputInfo.inProperty.agreePrintJson)
            : [];
          let printPromiseParams = agreePrintJson.map((item) => {
            let params = {
              agreementId: item.agreementId,
              flowToken: sessionStorage.getItem('TKFlowToken'),
              bizType: this.flowOutputInfo.inProperty.bizType,
              groupId: item.groupId,
              contractType: item.contractType,
              agreementSubtype: item.agreementSubtype
                ? item.agreementSubtype
                : ''
            };
            return new Promise((resolve, reject) => {
              queryAgreement(params)
                .then((res) => {
                  resolve(res.data);
                })
                .catch((err) => {
                  reject(err);
                });
            });
          });
          PromiseParams = PromiseParams.concat(printPromiseParams);
          Promise.all(PromiseParams)
            .then((res) => {
              let arr = [];
              res.forEach((item, index) => {
                item.forEach((it) => {
                  arr.push({
                    agreementName: it.agreementName,
                    groupId: it.groupId,
                    contractType: it.contractType,
                    agreementId: it.agreementId,
                    agreementContent: it.agreementContent,
                    agreementVersion: it.agreementVersion,
                    agreementPath: it.agreementPath || '',
                    agreementSubtype: '',
                    show: false,
                    hasRead: false
                  });
                });
              });
              this.holderList = arr;
              _hvueLoading.close();
            })
            .catch((err) => {
              _hvueLoading.close();
            });
        }
      );
    },

    agreeCallBack(res) {
      this.showAgreeDetail = false;
      if (res) {
        this.holderList[this.nowAgreeIndex].hasRead = true;
      }
    },

    toSign() {
      if (this.checked) {
        this.checked = false;
      } else {
        let allPass =
          this.holderList.findIndex((item) => item.hasRead === false) >= 0
            ? false
            : true;
        if (!allPass) {
          _hvueToast({
            mes: '请阅读完所有协议'
          });
          return;
        }
        this.showPassword = true;
      }
    },

    toConfirmPassword() {
      validationElectronAgreePwd({
        agreeSignPwd: getPwdCbcEncryption(this.password)
      })
        .then((res) => {
          let doParams = this.holderList.map((item) => {
            let params = {
              flowToken: sessionStorage.getItem('TKFlowToken'),
              bizType: this.flowOutputInfo.inProperty.bizType,
              contractType: item.contractType,
              agreementId: item.agreementId,
              agreementVersion: item.agreementVersion,
              groupId: item.groupId
            };
            return new Promise((resolve, reject) => {
              doAgreementRecord(params)
                .then((res) => {
                  resolve(res.data);
                })
                .catch((err) => {
                  reject();
                });
            });
          });
          Promise.all(doParams).then((res) => {
            let nodeId = this.flowOutputInfo.flowNodeNo;
            let signAgree = {
              nodeId,
              batchNo: res.map((item) => item.signBatchno).join(',')
            };
            let epaper_sign_json = null;
            if (
              this.flowOutputInfo.outProperty.epaperSignJson &&
              JSON.parse(this.flowOutputInfo.outProperty.epaperSignJson)
                .length > 0
            ) {
              let defEpaper = JSON.parse(
                this.flowOutputInfo.outProperty.epaperSignJson
              );
              let nowNodeIndex = defEpaper.findIndex(
                (item) => item.nodeId === nodeId
              );
              if (nowNodeIndex >= 0) {
                defEpaper[nowNodeIndex].batchNo = signAgree.batchNo;
              } else {
                defEpaper.push({ nodeId, batchNo: signAgree.batchNo });
              }
              epaper_sign_json = JSON.stringify(defEpaper);
            } else {
              let arr = [];
              arr.push({ nodeId, batchNo: signAgree.batchNo });
              epaper_sign_json = JSON.stringify(arr);
            }
            this.showPassword = false;
            this.password = '';
            this.nextFlow({
              epaper_sign_json
            });
          });
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    },

    toNext() {}
  }
};
</script>
