<template>
  <section class="main fixed">
    <t-header @back="back"></t-header>
    <article v-if="pageStep === 0" class="content">
      <div class="business_page">
        <div class="add-protocol_box">
          <p class="tit">请认真阅读以下相关协议书内容：</p>
          <ul class="add-protocol_list">
            <li
              v-for="(item, index) in dataList"
              :key="index"
              :class="{ read: item.readFlag }"
              @click="openPdf(item, index)"
            >
              <a href="#">《{{ item.agreementName }}》</a>
            </li>
          </ul>
          <div class="add-xy_box" @click="readAllArgement">
            <p class="txt">
              <span :class="{ checked: readAllFlag }"></span
              >本人已详细阅读并完全理解以上合同及协议，同意签署。
            </p>
          </div>
        </div>
      </div>
    </article>
    <article v-if="pageStep === 1" class="content">
      <div class="business_page">
        <div class="add-protocol_text">
          <h2>《证券账户业务申请表》</h2>
          <pdf
            v-for="i in numPages"
            :key="i"
            class="protocol_pdf"
            :src="pdfSrc"
            :page="i"
          ></pdf>
        </div>
      </div>
    </article>
    <div v-if="pageStep === 1" class="add-btn_box">
      <a v-if="count > 0" href="#" class="disabled">请阅读（{{ count }}s）</a>
      <a v-if="count <= 0" href="#" class="" @click="argementRead"
        >我已知悉并同意</a
      >
    </div>
    <footer>
      <div v-if="pageStep === 0" class="add-btn_box">
        <a href="#" @click="submit">下一步</a>
      </div>
    </footer>
  </section>
</template>

<script>
import flowMixin from '@/common/flowMixin';
import pdf from 'vue-pdf';

export default {
  components: {
    pdf
  },
  mixins: [flowMixin],
  data() {
    return {
      pageStep: 0,
      pdfSrc: '',
      numPages: null,
      nowPdfIndex: '',
      count: 0,
      timer: null,
      readAllFlag: false,
      dataList: [
        {
          agreementName: '科创板股票交易风险揭示书',
          pdfOnlineBrowsingAddress: '/bc-h5-view/views/pdf1.pdf',
          readFlag: false
        },
        {
          agreementName: '科创板股票融资融券交易风险揭示书',
          pdfOnlineBrowsingAddress: '/bc-h5-view/views//pdf2.pdf',
          readFlag: false
        }
      ]
    };
  },
  computed: {
    countdown() {
      if (this.count <= 0) {
        return false;
      } else {
        return `（${this.count} 秒）`;
      }
    }
  },
  methods: {
    back() {
      if (this.pageStep === 1) {
        this.pageStep = 0;
      } else {
        this.prevFlow();
      }
    },

    renderingView() {
      _hvueLoading.close();
    },

    openPdf(item, index) {
      this.pageStep = 1;
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.nowPdfIndex = index;
      const _this = this;
      const pdfOnlineBrowsingAddress = item.pdfOnlineBrowsingAddress;
      const loadingTask = pdf.createLoadingTask(pdfOnlineBrowsingAddress);
      this.pdfSrc = loadingTask;
      loadingTask.promise.then((pdf) => {
        this.numPages = pdf.numPages;
        /** 设置协议阅读倒计时 start */
        _this.count = 5;
        _this.timer = setInterval(() => {
          if (_this.count <= 0) {
            clearInterval(this.timer);
          } else {
            _this.count--;
          }
        }, 1000);
        _this.$once('hook:deactivated', () => {
          clearInterval(this.timer);
        });
        /** 设置协议阅读倒计时 end */
      });
    },

    argementRead() {
      this.dataList[this.nowPdfIndex].readFlag = true;
      this.nowPdfIndex = '';
      this.pageStep = 0;
    },

    readAllArgement() {
      const checkFlag = this.dataList.every((item) => {
        return item.readFlag === true;
      });
      if (!checkFlag) {
        _hvueToast({
          mes: '请先点开协议完成阅读，再进行勾选'
        });
      } else {
        this.readAllFlag = !this.readAllFlag;
      }
    },

    submit() {
      if (this.readAllFlag) {
        this.nextFlow({});
      } else {
        _hvueToast({
          mes: '请阅读并勾选协议'
        });
      }
    }
  }
};
</script>

<style>
.protocol_pdf {
  width: 100%;
}
</style>
