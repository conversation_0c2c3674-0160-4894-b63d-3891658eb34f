<template>
  <section class="main fixed">
    <t-header @back="back"></t-header>
    <article class="content">
      <div>
        <!-- <div class="com_title">
					<h5>特殊信息申报</h5>
				</div> -->
        <div class="test_numbox">
          <span
            v-for="(item, index) in stepForm"
            :key="index"
            :class="{ off: item.hasCheck }"
            @click="jump(index)"
            >{{ index + 1 }}</span
          >
        </div>
        <div
          v-for="(item, index) in stepForm"
          v-show="step === index + 1"
          :key="index"
          class="spel_infobox"
        >
          <h5 class="title">{{ item.title }}</h5>
          <div class="radio_list">
            <span
              class="icon_radio"
              :class="{ checked: item.checked === 0 }"
              @click="changeStep(0, index)"
              >否</span
            >
            <span
              class="icon_radio"
              :class="{ checked: item.checked === 1 }"
              @click="changeStep(1, index)"
              >是</span
            >
          </div>
        </div>
      </div>
      <!-- 1是否有投资者关联人信息申报 -->
      <div
        v-if="step === 1 && stepForm[0].checked === 1"
        class="spel_info_supp"
      >
        <h5 class="mid_title">请补充关联人信息</h5>
        <template v-for="(item, index) in relation">
          <formItemView
            ref="relation"
            :key="index"
            v-model="item.detail"
            :form-index="index"
            :form-item="formItemConfig.step1View"
            :show-delete="relation.length > 1"
            @add="addInfo('relation')"
            @delete="deleteInfo('relation', index)"
          />
        </template>
      </div>
      <!-- 2是否为我公司股东或关联人申报 -->
      <!-- 3是否为持有上市公司股份5%（含）以上 -->
      <div
        v-if="step === 3 && stepForm[2].checked === 1"
        class="spel_info_supp"
      >
        <h5 class="mid_title">请补充持股信息</h5>
        <template v-for="(item, index) in market_holder">
          <formItemView
            ref="market_holder"
            :key="index"
            v-model="item.detail"
            :form-index="index"
            :form-item="formItemConfig.step3View"
            :show-delete="market_holder.length > 1"
            @add="addInfo('market_holder')"
            @delete="deleteInfo('market_holder', index)"
          />
        </template>
      </div>
      <!-- 4是否持有上市公司限售股份 -->
      <div
        v-if="step === 4 && stepForm[3].checked === 1"
        class="spel_info_supp"
      >
        <h5 class="mid_title">请补充持股信息</h5>
        <template v-for="(item, index) in company">
          <formItemView
            ref="company"
            :key="index"
            v-model="item.detail"
            :form-index="index"
            :form-item="formItemConfig.step4View"
            :show-delete="company.length > 1"
            @add="addInfo('company')"
            @delete="deleteInfo('company', index)"
          />
        </template>
      </div>
      <!-- 5是否为上市公司董事、监事、高级管理人员 -->
      <div
        v-if="step === 5 && stepForm[4].checked === 1"
        class="spel_info_supp"
      >
        <h5 class="mid_title">请补充高管信息</h5>
        <template v-for="(item, index) in market_execv">
          <formItemView
            ref="market_execv"
            :key="index"
            v-model="item.detail"
            :form-index="index"
            :form-item="formItemConfig.step5View"
            :show-delete="market_execv.length > 1"
            @add="addInfo('market_execv')"
            @delete="deleteInfo('market_execv', index)"
          />
        </template>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn" @click="nextStep">
        <a class="p_button">下一步</a>
      </div>
    </footer>

    <div v-if="tipInfo.show">
      <div class="dialog_overlay"></div>
      <div class="dialog_box">
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>{{ tipInfo.title }}</h3>
          <div>
            <p>{{ tipInfo.desc }}</p>
          </div>
        </div>
        <div class="dialog_btn" @click="tipInfo.show = false">
          <a>我知道了</a>
        </div>
      </div>
    </div>

    <div v-show="modalShow" class="back_tip">
      <div class="dialog_overlay"></div>
      <div class="dialog_box">
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>温馨提示</h3>
          <div>
            <p>特殊信息申报尚未完成，是否确认返回上一步。</p>
          </div>
        </div>
        <div class="dialog_btn fx-row">
          <a @click="goBack">确定</a>
          <a class="cancel" @click="modalShow = false">取消</a>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import formItemView from '@/components/formItemView';
import flowMixin from '@/common/flowMixin';

export default {
  components: {
    formItemView
  },
  mixins: [flowMixin],
  data() {
    return {
      step: 1,
      modalShow: false,
      tipInfo: {
        show: false,
        title: '',
        desc: ''
      },
      stepForm: [
        { title: '1、投资者关联人信息申报', checked: -1, hasCheck: false },
        {
          title:
            '2、是否为我公司股东或关联人申报（此处所称股东，不包括我公司上市后仅持有5%以下上市流通股份的股东）',
          checked: -1,
          hasCheck: false
        },
        {
          title: '3、是否持有上市公司股份5%（含）以上',
          checked: -1,
          hasCheck: false
        },
        { title: '4、是否持有上市公司限售股份', checked: -1, hasCheck: false },
        {
          title: '5、是否为上市公司董事、监事、高级管理人员',
          checked: -1,
          hasCheck: false
        }
      ],
      formItemConfig: {
        step1View: [
          {
            key: 'relation_name',
            label: '关联人姓名',
            type: '',
            placeholder: '请输入关联人姓名',
            maxLength: 10
          },
          {
            key: 'relation_id_no',
            label: '证件号码',
            type: '',
            placeholder: '请输入证件号码',
            maxLength: 18
          },
          {
            key: 'relation_holder',
            label: '股东账号',
            type: '',
            placeholder: '请输入股东账号',
            maxLength: 10
          },
          {
            key: 'relation_open_company',
            label: '开户证券公司',
            type: '',
            placeholder: '请输入开户证券公司'
          }
        ],
        step3View: [
          {
            key: 'holder_share_id',
            label: '持股代码',
            type: '',
            placeholder: '请输入持股代码',
            maxLength: 15
          },
          {
            key: 'holder_share_name',
            label: '持股名称',
            type: '',
            placeholder: '请输入持股名称',
            maxLength: 15
          },
          {
            key: 'holder_share_no',
            label: '持股数量',
            type: '',
            placeholder: '请输入持股数量',
            unit: '股'
          },
          {
            key: 'holder_share_ratio',
            label: '持股比例',
            type: '',
            placeholder: '请输入持股比例',
            unit: '%'
          }
        ],
        step4View: [
          {
            key: 'limitsell_share_id',
            label: '持股代码',
            type: '',
            placeholder: '请输入持股代码',
            maxLength: 15
          },
          {
            key: 'limitsell_share_name',
            label: '持股名称',
            type: '',
            placeholder: '请输入持股名称',
            maxLength: 15
          },
          {
            key: 'limitsell_unlock_no',
            label: '解禁数量',
            type: '',
            placeholder: '请输入解禁数量',
            unit: '股'
          },
          {
            key: 'limitsell_limit_no',
            label: '未解禁数量',
            type: '',
            placeholder: '请输入未解禁数量',
            unit: '股'
          }
        ],
        step5View: [
          {
            key: 'execv_company_name',
            label: '上市公司名称',
            type: '',
            placeholder: '请输入上市公司名称'
          },
          {
            key: 'execv_post',
            label: '职位',
            type: '',
            placeholder: '请输入职位'
          }
        ]
      },
      relation_context: '', //关联人信息
      market_company_holder_json: '', //上市公司股东信息
      company_limitsell_holder_json: '', //上市公司限售股份持股信息
      market_company_execv_json: '', //上市公司董事、监事、高级管理人员信息
      relation: [{ detail: {} }], // 1
      market_holder: [{ detail: {} }], //3
      company: [{ detail: {} }], //4
      market_execv: [{ detail: {} }] //5
    };
  },
  deactivated() {
    this.modalShow = false;
  },
  methods: {
    goBack() {
      this.modalShow = false;
      this.prevFlow();
    },

    renderingView() {
      _hvueLoading.close();
    },

    jump(index) {
      if (this.stepForm[1].checked === 1) {
        this.tipInfo = {
          show: true,
          title: '不符合申请要求',
          desc: '我公司关联股东不符合融资融券申请人要求'
        };
        return;
      }
      if (this.stepForm[index].hasCheck) {
        this.step = index + 1;
      }
    },

    // 判断增删表单是否填写完整
    checkFormView(key, formKey) {
      let valArr = [];
      this[key].forEach((item) => {
        let ObjKey = this.formItemConfig[formKey].map((item) => item.key);
        ObjKey.forEach((it) => {
          if (item.detail[it] === undefined) {
            valArr.push('');
          } else {
            valArr.push(item.detail[it]);
          }
        });
      });
      let flag = true;
      if (valArr.includes('')) {
        flag = false;
      }
      return flag;
    },

    back() {
      if (this.step > 1) {
        this.step -= 1;
      } else {
        this.modalShow = true;
      }
    },

    changeStep(val, index) {
      this.stepForm[index].checked = val;
      if (val === 0) {
        this.stepForm[index].hasCheck = true;
      }
      if (index < 4) {
        if (val === 0) {
          setTimeout(() => {
            this.nextStep();
          }, 200);
        }
      }
    },

    nextStep() {
      if (this.step <= 5) {
        let canNext = true;
        if (this.step === 1) {
          if (this.stepForm[0].checked === 0) {
            this.stepForm[0].hasCheck = true;
          } else {
            this.$refs.relation.forEach((item) => {
              item.verify(() => {});
            });
            let errors = this.$refs.relation[0].errors.items;
            if (
              !this.checkFormView('relation', 'step1View') ||
              errors.length > 0
            ) {
              this.tipInfo = {
                show: true,
                title: '请完善关联人信息',
                desc: '请继续完成填写'
              };
              canNext = false;
            }
          }
        }
        if (this.step === 2) {
          if (this.stepForm[1].checked === 1) {
            this.tipInfo = {
              show: true,
              title: '不符合申请要求',
              desc: '我公司关联股东不符合融资融券申请人要求'
            };
            canNext = false;
          } else {
            this.stepForm[1].hasCheck = true;
          }
        }
        if (this.step === 3) {
          if (this.stepForm[2].checked === 0) {
            this.stepForm[2].hasCheck = true;
          } else {
            this.$refs.market_holder.forEach((item) => {
              item.verify(() => {});
            });
            let errors = this.$refs.market_holder[0].errors.items;
            if (
              !this.checkFormView('market_holder', 'step3View') ||
              errors.length > 0
            ) {
              this.tipInfo = {
                show: true,
                title: '请完善持股信息',
                desc: '请继续完成填写'
              };
              canNext = false;
            }
          }
        }
        if (this.step === 4) {
          if (this.stepForm[3].checked === 0) {
            this.stepForm[3].hasCheck = true;
          } else {
            this.$refs.company.forEach((item) => {
              item.verify(() => {});
            });
            let errors = this.$refs.company[0].errors.items;
            if (
              !this.checkFormView('company', 'step4View') ||
              errors.length > 0
            ) {
              this.tipInfo = {
                show: true,
                title: '限售股份信息未完成',
                desc: '请继续完成填写'
              };
              canNext = false;
            }
          }
        }
        if (this.step === 5) {
          if (this.stepForm[4].checked === 0) {
            this.stepForm[4].hasCheck = true;
          } else {
            this.$refs.market_execv.forEach((item) => {
              item.verify(() => {});
            });
            let errors = this.$refs.market_execv[0].errors.items;
            if (
              !this.checkFormView('market_execv', 'step5View') ||
              errors.length > 0
            ) {
              this.tipInfo = {
                show: true,
                title: '请完善高管信息',
                desc: '请继续完成填写'
              };
              canNext = false;
            }
          }
        }
        if (canNext && this.step <= 4) {
          this.stepForm[this.step - 1].hasCheck = true;
          this.step += 1;
        } else if (canNext && this.step == 5) {
          let is_personrelated = this.stepForm[0].checked;
          let is_securities_company_holder = this.stepForm[1].checked;
          let is_market_company_holder = this.stepForm[2].checked;
          let is_company_limitsell_holder = this.stepForm[3].checked;
          let is_market_company_execv = this.stepForm[4].checked;
          this.relation_context = this.stepForm[0].checked
            ? JSON.stringify(
                this.relation.map((item) => {
                  let arr = [];
                  let keys = Object.keys(item.detail);
                  arr = keys.map((it) => {
                    let label = this.formItemConfig.step1View.find(
                      (t) => t.key === it
                    ).label;
                    return {
                      key: it,
                      label,
                      value: item.detail[it]
                    };
                  });
                  return arr;
                })
              )
            : '';
          this.market_company_holder_json = this.stepForm[2].checked
            ? JSON.stringify(
                this.market_holder.map((item) => {
                  let arr = [];
                  let keys = Object.keys(item.detail);
                  arr = keys.map((it) => {
                    let label = this.formItemConfig.step3View.find(
                      (t) => t.key === it
                    ).label;
                    return {
                      key: it,
                      label,
                      value: item.detail[it]
                    };
                  });
                  return arr;
                })
              )
            : '';
          this.company_limitsell_holder_json = this.stepForm[3].checked
            ? JSON.stringify(
                this.company.map((item) => {
                  let arr = [];
                  let keys = Object.keys(item.detail);
                  arr = keys.map((it) => {
                    let label = this.formItemConfig.step4View.find(
                      (t) => t.key === it
                    ).label;
                    return {
                      key: it,
                      label,
                      value: item.detail[it]
                    };
                  });
                  return arr;
                })
              )
            : '';
          this.market_company_execv_json = this.stepForm[4].checked
            ? JSON.stringify(
                this.market_execv.map((item) => {
                  let arr = [];
                  let keys = Object.keys(item.detail);
                  arr = keys.map((it) => {
                    let label = this.formItemConfig.step5View.find(
                      (t) => t.key === it
                    ).label;
                    return {
                      key: it,
                      label,
                      value: item.detail[it]
                    };
                  });
                  return arr;
                })
              )
            : '';
          this.nextFlow({
            is_company_limitsell_holder,
            is_market_company_execv,
            is_market_company_holder,
            is_personrelated,
            is_securities_company_holder,
            company_limitsell_holder_json: this.company_limitsell_holder_json,
            market_company_execv_json: this.market_company_execv_json,
            market_company_holder_json: this.market_company_holder_json,
            relation_context: this.relation_context
          });
        }
      }
    },

    addInfo(type) {
      this[type].push({ detail: {} });
    },

    deleteInfo(type, index) {
      if (this[type].length > 1) {
        this[type].splice(index, 1);
      }
    }
  }
};
</script>

<style></style>
