<template>
  <section class="main fixed">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="business_page">
        <div class="add-aptness_box">
          <div class="add-aptness_text">
            <h2 :class="isMatch ? 'succ' : 'error'">
              您的适当性评估{{ isMatch ? '匹配' : '不匹配' }}
            </h2>
            <div class="text_box border_top">
              <p class="tit">北交所定向可转债权限匹配结果</p>
              <ul class="list">
                <li>
                  <p class="p1">
                    您的拟投资期限为: {{ matchResult.investmentTerm }}
                  </p>
                  <p class="p2">北交所定向可转债权限匹配结果</p>
                </li>
                <li>
                  <p class="p1">
                    您的拟投资品种为: {{ matchResult.investmentVarieties }}
                  </p>
                  <p class="p2">
                    北交所定向可转债权限所属投资品种为:{{
                      matchResult.businessInvestmentVarieties
                    }}，与您确认的投资品种一致。
                  </p>
                </li>
                <li>
                  <p class="p1">
                    您的风险承受能力等级: {{ matchResult.corpRiskLevel }}
                  </p>
                  <p class="p2">
                    北交所定向可转债权限风险等级: {{ matchResult.bizRiskLevel }}
                  </p>
                </li>
              </ul>
            </div>
            <div class="tips_box border_top">
              <p>确认声明</p>
              <p>
                我司就上述情況向您做出提示，并建议您应当审慎考察该金融服务的特征及风险，自行做出充分风险评估。但上述匹配结果，并不能取代您的投资判断，也不会降低该金融服务的固有风险，与该金融服务相关的投资风险、履约责任以及费用等将由您自行承担。若您经审慎考虑后，仍坚持接受该金融服务，请签署下附投资确认书。如后期您的适当性信息发生变化，导致上述匹配结果与本确认书中载明的不一致后，我司有权暂停或终止提供该项金融服务。
              </p>
            </div>
          </div>
          <div class="add-xy_box">
            <p class="txt">
              <span
                :class="{ checked: ruleChecked }"
                @click="ruleChecked = !ruleChecked"
              ></span
              >本人已详细阅读并同意签署以下协议<a href="#"
                >《投资者风险承受能力评估结果告知函》</a
              >
            </p>
          </div>
        </div>
      </div>
    </article>
    <footer>
      <div class="add-btn_box">
        <a href="#" @click="submitForm">我已确认</a>
        <a href="#" class="cancel" @click="toIndex">放弃办理</a>
      </div>
    </footer>
  </section>
</template>

<script>
import flowMixin from '@/common/flowMixin';
import { investProInfoQry } from '@/service/service';
import { ELIG_RISK_FLAG } from '@/common/enumeration';

export default {
  mixins: [flowMixin],
  data() {
    return {
      ruleChecked: false,
      matchResult: ''
    };
  },
  computed: {
    isMatch() {
      return this.matchResult.eligRiskFlag === ELIG_RISK_FLAG.SUCCESS;
    }
  },
  methods: {
    back() {
      this.prevFlow();
    },

    renderingView() {
      let _this = this;
      const { clientId, fundAccount, bizType } =
        _this.flowOutputInfo.inProperty;
      investProInfoQry({
        clientId,
        fundAccount,
        bizType
      })
        .then((data) => {
          _hvueLoading.close();
          _this.matchResult = Object.assign({}, data.data);
          // TODO 暂时实现协议弹窗功能,具体获取适当性协议方式待定
          if (_this.isMatch) {
            _this.agreeDetail = {
              agreeId: '6',
              version: '19',
              title: '适当性匹配意见及投资者确认书'
            };
          } else {
            _this.agreeDetail = {
              agreeId: '7',
              version: '19',
              title: '产品或服务不适当警示及投资者确认书'
            };
          }
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    },

    submitForm() {
      if (!this.ruleChecked) {
        _hvueToast({
          mes: '请阅读并勾选协议'
        });
        return;
      }
      this.nextFlow();
    }
  }
};
</script>

<style></style>
