<template>
  <section class="main fixed">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="com_title">
        <h5>
          您的适当性评估{{ suitObject.eligRiskFlag != '1' ? '不' : '' }}匹配<i
            :class="
              suitObject.eligRiskFlag != '1' ? 'appro_ic_error' : 'appro_ic_ok'
            "
          ></i>
        </h5>
      </div>
      <div class="com_box">
        <div class="appro_info">
          <div class="item">
            <span class="tit">您的风险承受能力等级</span>
            <div class="ct">
              <h5>{{ suitObject.corpRiskLevel }}</h5>
            </div>
          </div>
          <div class="item">
            <span class="tit">您的拟投资品种</span>
            <div class="ct">
              <h5>{{ suitObject.investmentVarieties }}</h5>
            </div>
          </div>
          <div class="item">
            <span class="tit">您的拟投资期限</span>
            <div class="ct">
              <h5>{{ suitObject.investmentTerm }}</h5>
            </div>
          </div>
          <!-- <div class="item">
						<span class="tit">您的拟接受服务</span>
						<div class="ct">
							<h5>两融开户</h5>
						</div>
					</div> -->
          <!-- <div class="item">
						<span class="tit">您的拟接受服务风险等级</span>
						<div class="ct">
							<h5>{{ suitObject.bizRiskLevel }}</h5>
						</div>
					</div> -->
        </div>
      </div>
      <div>
        <h5 class="mid_title">拟接受服务的风险等级</h5>
        <div class="com_box">
          <div class="risk_info_wrap">
            <table
              class="risk_info_table"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
            >
              <tr>
                <th>业务名称</th>
                <th>业务等级</th>
                <th>是否匹配</th>
              </tr>
              <tr v-for="(item, index) in holdList" :key="index">
                <td>{{ item.bizName }}</td>
                <td>
                  <span class="state_span_ok">{{ item.bizRiskLevel }}</span>
                </td>
                <td>
                  <span
                    :class="
                      item.eligRiskFlag ? 'state_span_ok' : 'state_span_error'
                    "
                    >{{ item.eligRiskFlag ? '匹配' : '不匹配' }}</span
                  >
                </td>
              </tr>
            </table>
          </div>
        </div>
      </div>
      <div class="appro_tips">
        <h5>本公司在此郑重提醒</h5>
        <p>
          本单位已经向您充分揭示了该金融产品或服务的风险。您的风险承受能力等级，投资品种，投资期限，可承受的最大亏损与该金融产品或服务的风险等级，所属品种、投资期限。产生亏损的程度相匹配。
        </p>
        <p class="t_ind">
          本单位就上述适当性评估结果与您进行确认，并建议您审慎考察该产品或服务的特征及风险，进行充分风险评估，自行做出投资决定。
        </p>
      </div>
      <div class="rule_check spel">
        <span
          class="icon_check"
          :class="{ checked: checkEpaper }"
          @click="clickSign"
        ></span
        >已知晓并确认签署<a
          v-for="(item, index) in epaperList"
          :key="index"
          @click="openAgreeDetail(item)"
          >《{{ item.agreementName }}》</a
        >
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="submitForm">下一步</a>
      </div>
    </footer>
    <agreementDetail
      :show="showAgreeDetail"
      :info="agreeDetail"
      :is-count="true"
      @callback="agreeCallBack"
    />
  </section>
</template>

<script>
import flowMixin from '@/common/flowMixin';
import {
  investProInfoQry,
  getJwtToken,
  queryAgreement,
  doAgreementRecord,
  businessCanQry
} from '@/service/service';
import agreementDetail from '@/components/agreementDetail';

export default {
  components: { agreementDetail },
  mixins: [flowMixin],
  data() {
    return {
      epaperList: [],
      suitObject: {}, // 适当性匹配数据
      ruleChecked: false,
      checkEpaper: false,
      showAgreeDetail: false,
      agreeDetail: {},
      canCheckEpaper: false,
      epaper_sign_json: '',
      holdList: []
    };
  },
  methods: {
    back() {
      this.prevFlow();
    },

    async renderingView() {
      let chooseHolderRights = this.flowOutputInfo.inProperty.chooseHolderRights
        ? this.flowOutputInfo.inProperty.chooseHolderRights.split(',')
        : [];
      let bizType = this.flowOutputInfo.inProperty.bizType;
      const suitRes = await investProInfoQry({ bizType });
      this.suitObject = suitRes.data;
      const tokenRes = await getJwtToken({
        flowNo: this.flowOutputInfo.flowNodeNo,
        businessType: this.flowOutputInfo.inProperty.bizType
      });
      $h.setSession('jwtToken', tokenRes.data);
      let params = {
        flowToken: sessionStorage.getItem('TKFlowToken'),
        bizType: this.flowOutputInfo.inProperty.bizType,
        groupId: 'thinkive-lrkh',
        contractType: this.suitObject.eligRiskFlag == '1' ? '05' : '04'
      };
      const agreeRes = await queryAgreement(params);
      this.epaperList = agreeRes.data;
      businessCanQry({ bizType: this.flowOutputInfo.inProperty.bizType }).then(
        (res) => {
          _hvueLoading.close();
          let holdArr = chooseHolderRights.map((item) => {
            let t = res.data.filter(
              (it) => JSON.parse(it.propertyConf).holderRight === item
            )[0];
            return {
              ...t,
              holderRight: item
            };
          });
          let speBiz = res.data.filter(
            (choseitem) =>
              choseitem.businessId === this.flowOutputInfo.inProperty.bizType
          )[0];
          holdArr.unshift({
            ...speBiz
          });
          console.log(holdArr);
          let PromiseParams = holdArr.map((item) => {
            return new Promise((resolve) => {
              console.log(item);
              investProInfoQry({
                bizType: item.businessId
              }).then((res) => {
                resolve({
                  ...res.data,
                  bizName: item.businessName
                });
              });
            });
          });
          Promise.all(PromiseParams).then((res) => {
            this.holdList = res;
          });
        }
      );
    },

    openAgreeDetail(item) {
      this.agreeDetail = item;
      this.showAgreeDetail = true;
    },

    agreeCallBack(flag) {
      this.showAgreeDetail = false;
      if (flag) {
        this.canCheckEpaper = true;
        this.checkEpaper = true;
        this.signAgree();
      }
    },

    clickSign() {
      if (!this.canCheckEpaper) {
        _hvueToast({
          mes: '请先阅读协议'
        });
        return;
      }
      this.checkEpaper = !this.checkEpaper;
      if (this.checkEpaper) {
        this.signAgree();
      }
    },

    async signAgree() {
      if (!this.canCheckEpaper) {
        _hvueToast({
          mes: '请先阅读协议'
        });
        return;
      } else {
        if (this.checkEpaper) {
          const tokenRes = await getJwtToken({
            flowNo: this.flowOutputInfo.flowNodeNo,
            businessType: this.flowOutputInfo.inProperty.bizType
          });
          $h.setSession('jwtToken', tokenRes.data);
          doAgreementRecord({
            flowToken: sessionStorage.getItem('TKFlowToken'),
            bizType: this.flowOutputInfo.inProperty.bizType,
            signBatchno: this.flowOutputInfo.inProperty.epaperSignJson
              ? JSON.parse(this.flowOutputInfo.inProperty.epaperSignJson)
                  .epaperSignJson
              : '',
            contractType: this.agreeDetail.contractType,
            agreementId: this.agreeDetail.agreementId,
            agreementVersion: this.agreeDetail.agreementVersion,
            groupId: 'thinkive-lrkh'
          }).then((data) => {
            let batchNo = data.data.signBatchno;
            let nodeId = this.flowOutputInfo.flowNodeNo;
            if (
              this.flowOutputInfo.inProperty.epaperSignJson &&
              JSON.parse(this.flowOutputInfo.inProperty.epaperSignJson).length >
                0
            ) {
              let defEpaper = JSON.parse(
                this.flowOutputInfo.inProperty.epaperSignJson
              );
              let nowNodeIndex = defEpaper.findIndex(
                (item) => item.nodeId === nodeId
              );
              if (nowNodeIndex >= 0) {
                defEpaper[nowNodeIndex].batchNo = batchNo;
              } else {
                defEpaper.push({ nodeId, batchNo });
              }
              this.epaper_sign_json = JSON.stringify(defEpaper);
            } else {
              let arr = [];
              arr.push({ nodeId, batchNo });
              this.epaper_sign_json = JSON.stringify(arr);
            }
          });
          this.epaper_sign_json = JSON.stringify(
            this.handleRepet(this.epaper_sign_json)
          );
          this.ruleChecked = true;
        } else {
          this.ruleChecked = false;
        }
      }
    },

    handleRepet(arr) {
      let resList = [];
      for (var i = 0; i < arr.length; i++) {
        var flag = true;
        for (var j = 0; j < resList.length; j++) {
          if (arr[i].nodeId === resList[j].nodeId) {
            flag = false;
          }
        }
        if (flag) {
          resList.push(arr[i]);
        }
      }
      return resList;
    },

    submitForm() {
      // if (!this.ruleChecked) {
      // 	_hvueToast({
      // 		mes: '请阅读并勾选协议'
      // 	});
      // 	return;
      // }
      let en_corp_risk_level_json = JSON.stringify(
        this.holdList.map((item) => ({
          holderRight: item.bizName,
          level: item.bizRiskLevel,
          isMatch: item.eligRiskFlag ? '匹配' : '不匹配',
          term: item.businessInvestmentTerm
            ? item.businessInvestmentTerm
            : '1到5年',
          trMatch: '匹配',
          variety: item.businessInvestmentVarieties
            ? item.businessInvestmentVarieties
            : '债券、货币市场基金、债券基金等固定收益类',
          vrMatch: '匹配'
        }))
      );
      this.nextFlow({
        ...this.flowOutputInfo.outProperty,
        pr_expect_profit: '1',
        pr_invest_kind: '1',
        pr_invest_term: '1',
        matching_result: this.suitObject.eligRiskFlag,
        en_corp_risk_level_json: en_corp_risk_level_json,
        epaper_sign_json: this.epaper_sign_json,
        en_invest_term: '1',
        en_invest_kind: '1',
        corp_risk_level: '1'
      });
    }
  }
};
</script>

<style></style>
