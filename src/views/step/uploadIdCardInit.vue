<!--  -->
<template>
  <section class="main fixed h5_white_bg">
    <t-header
      :title="pageStep === 0 ? '上传身份证' : '拍摄免冠照'"
      @back="backTip"
    ></t-header>
    <article v-if="pageStep === 0" class="content">
      <div class="com_title">
        <h5>二代身份证验证</h5>
      </div>
      <div class="upload_com_box">
        <div class="upload_wrap">
          <div class="upload_item" @click="selImgClick('idfrontimg')">
            <div class="pic">
              <img :src="positive.src" />
            </div>
            <a v-if="positive.uploaded" class="reset_btn">重拍</a>
            <a v-else class="btn">拍摄人像面</a>
          </div>
          <div class="upload_item" @click="selImgClick('idbackimg')">
            <div class="pic">
              <img :src="negative.src" />
            </div>
            <a v-if="negative.uploaded" class="reset_btn">重拍</a>
            <a v-else class="btn">拍摄国徽照</a>
          </div>
        </div>

        <div v-if="!showInfo" class="photo_tips">
          <h5 class="title">拍摄规范</h5>
          <ul class="list">
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img02_1.png" />
              </div>
              <span class="ok">标准拍摄</span>
            </li>
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img02_2.png" />
              </div>
              <span class="error">边角缺失</span>
            </li>
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img02_3.png" />
              </div>
              <span class="error">照片模糊</span>
            </li>
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img02_4.png" />
              </div>
              <span class="error">反光强烈</span>
            </li>
          </ul>
          <p class="desc">1. 拍摄时请将证件平放，手机横向拍摄</p>
          <p class="desc">
            2. 确保证明材料 <span class="imp">边框完整、文字清晰可见</span>
          </p>
        </div>
      </div>
      <div v-if="showInfo" class="upload_infobox">
        <div class="com_title">
          <h5>请核对您的个人信息，若有误请手动修改</h5>
        </div>
        <div class="input_form">
          <div class="input_text text">
            <div class="tit active">姓名</div>
            <input
              v-model="exportParam.client_name"
              class="t1"
              type="text"
              maxlength="21"
            />
          </div>
          <div class="input_text text">
            <div class="tit active">身份证号</div>
            <input
              v-model="exportParam.id_no"
              class="t1"
              type="text"
              maxlength="18"
            />
          </div>
          <div class="input_text text">
            <div class="tit active">发证机关</div>
            <input
              v-model="exportParam.issued_depart"
              class="t1"
              type="text"
              maxlength="32"
            />
          </div>
          <div class="input_text text">
            <div class="tit active">证件地址</div>
            <multLineInput
              v-model="exportParam.id_address"
              class="tarea1 needsclick"
              :maxlength="64"
              :update-flag="positive.uploaded"
            ></multLineInput>
          </div>
          <div class="input_text text">
            <span class="tit active">起始日期</span>
            <h-datetime
              ref="date1"
              v-model="exportParam.id_begindate"
              class="t1"
              title="起始日期"
              type="date"
              start-year="1900"
              end-year="2100"
            ></h-datetime>
          </div>
          <div class="input_text text">
            <span class="tit active">结束日期</span>
            <h-datetime
              v-show="!longTimeChecked"
              ref="date2"
              v-model="exportParam.id_enddate"
              class="t1"
              title="结束日期"
              type="date"
              start-year="2020"
              end-year="2100"
            ></h-datetime>
            <input
              v-show="longTimeChecked"
              class="t1"
              type="text"
              maxlength="32"
              disabled
              value="3000-12-31"
            />
            <span
              class="long_span icon_check"
              :class="{ checked: longTimeChecked }"
              @click="selectLongTime"
              >长期</span
            >
          </div>
        </div>
      </div>
    </article>

    <article v-if="pageStep === 1" class="content">
      <div class="com_title">
        <h5>拍摄免冠照</h5>
      </div>
      <div class="upload_com_box">
        <div class="upload_wrap">
          <div class="upload_item" @click="selImgClick('photoimg')">
            <div class="pic">
              <img :src="photo.src" />
            </div>
            <a v-if="photo.uploaded" class="reset_btn">重拍</a>
            <a v-else class="btn">拍摄照片</a>
          </div>

          <div class="upload_item">
            <div class="upload_link">
              <!-- <a>查看示例</a> -->
            </div>
          </div>
        </div>

        <div class="photo_tips">
          <h5 class="title">拍摄规范</h5>
          <ul class="list">
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img01_1.png" />
              </div>
              <span class="ok">正确拍摄</span>
            </li>
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img01_2.png" />
              </div>
              <span class="error">人脸出框</span>
            </li>
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img01_3.png" />
              </div>
              <span class="error">光线过暗</span>
            </li>
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_img01_4.png" />
              </div>
              <span class="error">其他人出现</span>
            </li>
          </ul>
          <p class="desc">拍摄时请正对摄像头，手机竖向拍摄</p>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="toNext">下一步</a>
      </div>
    </footer>

    <div v-if="tipInfo.show">
      <div class="dialog_overlay"></div>
      <div class="dialog_box">
        <div class="dialog_cont">
          <div class="dialog_tip_icon"></div>
          <h3>{{ tipInfo.title }}</h3>
          <div>
            <p>{{ tipInfo.desc }}</p>
          </div>
        </div>
        <div class="dialog_btn fx-row">
          <a v-if="!tipInfo.onlyBtn" class="cancel" @click="goBack">{{
            tipInfo.cancelText
          }}</a>
          <a @click="closeTip">{{ tipInfo.confirmText }}</a>
        </div>
      </div>
    </div>

    <getImgBoxApp
      ref="getImgBoxThinkive"
      :scan="true"
      @getImgCallBack="getImgCallBack"
    ></getImgBoxApp>
    <getImgBoxBrowser
      ref="getImgBoxBrowser"
      :scan="true"
      @getImgCallBack="getImgCallBack"
    ></getImgBoxBrowser>
  </section>
</template>

<script>
import getImgBoxApp from '@/components/getImg_app';
import getImgBoxBrowser from '@/components/getImg_browser';
import {
  uploadFile,
  idCardToSex,
  idCardToBirthday,
  getAge
} from '@/common/util';
import flowMixin from '@/common/flowMixin';
import { confirmClientInfo, faceCompare } from '@/service/service';
import multLineInput from '@/components/multLineInput';
export default {
  components: {
    getImgBoxApp,
    getImgBoxBrowser,
    multLineInput
  },

  mixins: [flowMixin],
  data() {
    return {
      imgType: 'idfrontimg',
      positive: {
        src: require('@/assets/images/sl_img02.png'),
        uploaded: false
      },
      negative: {
        src: require('@/assets/images/sl_img03.png'),
        uploaded: false
      },
      photo: {
        src: require('@/assets/images/sl_img01.png'),
        uploaded: false
      },
      exportParam: {
        id_begindate: '',
        id_enddate: '',
        id_kind: '0'
      },
      longTimeChecked: false,
      tipInfo: {
        show: false,
        title: '',
        desc: '',
        onlyBtn: false,
        cancelText: '',
        confirmText: ''
      },
      pageStep: 0
    };
  },

  computed: {
    showInfo() {
      return this.positive.uploaded && this.negative.uploaded;
    }
  },

  mounted() {},

  deactivated() {
    this.pageStep = 0;
  },

  methods: {
    renderingView() {
      _hvueLoading.close();
    },

    backTip() {
      if (this.pageStep == 0) {
        this.tipInfo = {
          show: true,
          title: '身份认证还未完成',
          desc: '是否确认返回上一步',
          cancelText: '确认',
          confirmText: '取消'
        };
      } else {
        this.pageStep = 0;
      }
    },
    closeTip() {
      this.tipInfo.show = false;
    },
    goBack() {
      this.tipInfo.show = false;
      this.prevFlow();
    },

    selectLongTime() {
      this.longTimeChecked = !this.longTimeChecked;
    },
    check() {
      if (!this.exportParam.client_name) {
        _hvueToast({
          mes: '姓名不能为空'
        });
        return false;
      }
      if (
        !/^([\u2E80-\uFE4F](?![\u3000-\u303F])){2,4}([·•]?[\u2E80-\uFE4F](?![\u3000-\u303F]))*$/.test(
          this.exportParam.client_name
        )
      ) {
        _hvueToast({
          mes: '姓名格式不正确'
        });
        return false;
      }
      if (!this.exportParam.id_no) {
        _hvueToast({
          mes: '身份证号不能为空'
        });
        return false;
      }
      if (!/^([\d]{17}[\dXx]|[\d]{15})$/.test(this.exportParam.id_no)) {
        _hvueToast({
          mes: '身份证号格式不正确'
        });
        return false;
      }
      // 判断是否小于18或大于70
      let userAge = getAge(this.exportParam.id_no);
      if (userAge && userAge < 18) {
        _hvueToast({
          mes: '开户年龄未满18岁'
        });
        return false;
      }

      if (userAge && userAge >= 70) {
        _hvueToast({
          mes: '开户年龄超过70岁'
        });
        return false;
      }
      if (!this.exportParam.id_address) {
        _hvueToast({
          mes: '住址不能为空'
        });
        return false;
      }
      if (
        !/^[\u4E00-\u9FA5\w\d\-\s/(),，（）#]{8,32}$/.test(
          this.exportParam.id_address
        )
      ) {
        _hvueToast({
          mes: '住址格式不正确'
        });
        return false;
      }
      if (!this.exportParam.issued_depart) {
        _hvueToast({
          mes: '发证机关不能为空'
        });
        return false;
      }
      if (
        !/^[\u4E00-\u9FA5\w\d\-\s/]{4,32}$/.test(this.exportParam.issued_depart)
      ) {
        _hvueToast({
          mes: '发证机关格式不正确'
        });
        return false;
      }
      let beginDate = this.exportParam.id_begindate;
      let endDate = this.longTimeChecked
        ? '3000-12-31'
        : this.exportParam.id_enddate;
      let dateExp = /\d{4}-\d{2}-\d{2}/;
      console.log('beginDate', beginDate, 'endDate', endDate);
      // 判断有效期格式是否正确
      if (
        !dateExp.test(beginDate) ||
        !dateExp.test(endDate) ||
        Date.parse(beginDate.replace(/\./g, '-')) > Date.now() ||
        beginDate === endDate
      ) {
        _hvueToast({
          mes: '请设置正确的身份证有效期限'
        });
        return false;
      }
      // 判断身份证是否过期
      if (
        !this.longTimeChecked &&
        Date.parse(endDate.replace(/\./g, '-')) < Date.now()
      ) {
        _hvueToast({
          mes: '您的身份证已过期'
        });
        return false;
      }
      return true;
    },
    selImgClick(type) {
      this.imgType = type;
      if ($hvue.platform == 0) {
        this.$refs.getImgBoxBrowser.getImg();
      } else {
        if (type == 'photoimg') {
          this.takePhotos();
        } else {
          this.$refs.getImgBoxThinkive.getImg(type);
        }
      }
    },
    getImgCallBack(imgInfo) {
      _hvueLoading.open();
      if (this.imgType == 'photoimg') {
        let base64 = this.filterBase64Pre(imgInfo.base64);
        uploadFile(
          $hvue.customConfig.serverUrl + '/media/imageUpload',
          base64,
          {
            success: (data) => {
              _hvueLoading.close();
              if (data.code == 0) {
                this.$set(this.exportParam, 'bareheaded_pic', data.data);
                this.photo.src = 'data:image/jpeg;base64,' + base64;
                this.photo.uploaded = true;
              } else {
                _hvueAlert({ mes: data.msg });
              }
            },
            progress: (count) => {
              console.log(count);
            },
            error: (e) => {
              _hvueLoading.close();
              console.log(e);
            }
          },
          {}
        );
      } else {
        uploadFile(
          $hvue.customConfig.serverUrl + '/client/ocrParseIDCard',
          imgInfo.base64,
          {
            success: (data) => {
              _hvueLoading.close();
              if (data.code == 0) {
                let ocrInfo = Object.assign({}, data.data.ocrMsgInfo, {
                  path: data.data.path
                });
                if (
                  (this.imgType === 'idfrontimg' && !ocrInfo.idNo) ||
                  (this.imgType === 'idbackimg' && !ocrInfo.policeorg)
                ) {
                  _hvueAlert({ mes: '身份证识别失败' });
                  return;
                }
                this.echoOrcInfo(imgInfo.base64, ocrInfo);
              } else {
                _hvueAlert({ mes: data.msg });
              }
            },
            progress: (count) => {
              console.log(count);
            },
            error: (e) => {
              _hvueLoading.close();
              console.log(e);
            }
          },
          {}
        );
      }
    },
    filterBase64Pre(ndata) {
      let arr = ndata.split('base64,');
      return arr[arr.length - 1];
    },
    echoOrcInfo(base64, result) {
      if (result.idNo) {
        this.exportParam.id_no = result.idNo;
        this.idno_ocr = result.idNo;
        this.exportParam.birthday = idCardToBirthday(result.idNo);
        this.$set(
          this.exportParam,
          'client_gender',
          idCardToSex(result.idNo) + ''
        );
        this.$set(
          this.exportParam,
          'client_gender_name',
          this.exportParam.client_gender === '0' ? '男' : '女'
        );
        this.exportParam.client_name = result.custName;
        this.exportParam.id_address = result.idAddress;
        this.exportParam.id_card_portrait = result.path;
        this.positive.uploaded = true;
        this.positive.src = 'data:image/jpeg;base64,' + base64;
      } else if (result.policeorg || result.idenddate) {
        this.exportParam.id_card_national = result.path;
        this.$set(this.exportParam, 'issued_depart', result.policeorg);
        this.$set(this.exportParam, 'id_begindate', result.idbegindate);
        if (result.idenddate == '3000-12-31') {
          this.longTimeChecked = true;
        } else {
          this.$set(this.exportParam, 'id_enddate', result.idenddate);
        }
        this.negative.uploaded = true;
        this.negative.src = 'data:image/jpeg;base64,' + base64;
      } else {
        _hvueToast({ mes: '未识别的身份证图片' });
      }
    },
    takePhotos() {
      window.imgCallBack = this.getImgCallBack;
      var phoneConfig = {
        requestParam: 'test', // h5上传不需要 但原生判断了是否为空
        imgType: '3', // 需上传图片类型 3大头照 4 身份证正面 5 反面
        url: $hvue.customConfig.serverUrl + '/servlet/json?',
        funcNo: 60013,
        action: 'pai', // 60013  phone相册 pai拍照
        userId: '1', // h5上传不需要 但原生判断了是否为空
        isUpload: '0',
        isAlbum: '0', //是否显示相册 1表示显示相册，0或其他表示不显示 默认隐藏
        isTake: '1', //是否显示拍照按钮
        compressSize: 200, //原生压缩大小 不传默认200k
        moduleName: 'open' // 必须为open
      };
      let result = $h.callMessageNative(phoneConfig);
      if (result.error_no !== '0') {
        console.log({ mes: result.error_info });
      }
    },
    toNext() {
      if (this.pageStep == 0) {
        if (!this.showInfo) {
          if (!this.positive.uploaded) {
            _hvueToast({ mes: '请上传人像面' });
          } else if (!this.negative.uploaded) {
            _hvueToast({ mes: '请上传国徽面' });
          }
        } else {
          if (this.check()) {
            let params = {
              clientName: this.exportParam.client_name,
              idNo: this.exportParam.id_no,
              idenddate: this.longTimeChecked
                ? '3000-12-31'
                : this.exportParam.id_enddate
            };
            confirmClientInfo(params)
              .then(() => {
                this.pageStep = 1;
              })
              .catch((e) => {
                this.tipInfo = {
                  show: true,
                  title: '身份信息不匹配',
                  onlyBtn: true,
                  desc: e,
                  confirmText: '我知道了'
                };
              });
          }
        }
      } else {
        if (!this.photo.uploaded) {
          _hvueToast({ mes: '请上传免冠照' });
        } else {
          faceCompare({
            fullFacePhoto: this.exportParam.id_card_portrait,
            bigHeadPhoto: this.photo.src.split('data:image/jpeg;base64,')[1]
          })
            .then((res) => {
              if (res.data.flag) {
                let params = {
                  birthday: this.exportParam.birthday.replace(/-/g, ''),
                  bareheaded_pic: this.exportParam.bareheaded_pic,
                  id_address: this.exportParam.id_address,
                  id_begindate: this.exportParam.id_begindate.replace(/-/g, ''),
                  id_card_national: this.exportParam.id_card_national,
                  id_card_portrait: this.exportParam.id_card_portrait,
                  id_enddate: this.longTimeChecked
                    ? '30001231'
                    : this.exportParam.id_enddate.replace(/-/g, ''),
                  id_kind: this.exportParam.id_kind,
                  id_no: this.exportParam.id_no,
                  issued_depart: this.exportParam.issued_depart
                };
                this.nextFlow(params);
              } else {
                // _hvueToast({ mes: '免冠照与证件照不符' });
                this.tipInfo = {
                  show: true,
                  title: '免冠照与证件照不符',
                  onlyBtn: true,
                  // desc: e,
                  confirmText: '我知道了'
                };
              }
            })
            .catch((res) => {
              this.tipInfo = {
                show: true,
                title: res,
                onlyBtn: true,
                // desc: e,
                confirmText: '我知道了'
              };
            });
        }
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.h_date {
  position: relative;
  .connector {
    position: absolute;
    left: 37%;
  }
}
.tarea01 {
  display: block;
  width: 100%;
  padding: 0.14rem 0.3rem 0.14rem 0;
  min-height: 0.24rem;
  font-size: 0.16rem;
  color: #020324;
  line-height: 0.24rem;
  outline: none;
  border: none;
  resize: none;
  -webkit-user-select: auto;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
</style>
