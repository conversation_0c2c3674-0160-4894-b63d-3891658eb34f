<template>
  <div class="main fixed">
    <t-header :show-back="pageState == 1" @back="back"></t-header>
    <article v-show="pageState == 1" class="content">
      <div class="video_tippage">
        <div class="title spel">
          <h3>将由<b>见证人员</b>与<b>您</b>进行视频连线</h3>
          <p>请做好以下准备</p>
        </div>
        <!-- <ul class="witness_list">
					<li>
						<i class="icon"><img src="@/asstes/images/vd_icon01.png"/></i>
						<p>光线充足的位置</p>
					</li>
					<li>
						<i class="icon"><img src="@/asstes/images/vd_icon02.png"/></i>
						<p>推荐使用WIFI网络</p>
					</li>
					<li>
						<i class="icon"><img src="@/asstes/images/vd_icon03.png"/></i>
						<p>周遭环境安静</p>
					</li>
					<li>
						<i class="icon"><img src="@/asstes/images/vd_icon04.png"/></i>
						<p>视频认证时间<em class="imp">周一至周五 9:00 ~ 22:00</em></p>
					</li>
				</ul> -->
      </div>
    </article>
    <article v-show="pageState == 3" class="content">
      <div class="notice_box">
        <!-- <div class="pic"><img src="@/assets/images/notice_ic01.png" /></div> -->
        <h5>恭喜! 视频见证已通过</h5>
        <p>您可以继续下一步</p>
      </div>
    </article>
    <footer v-if="pageState != 2" class="footer">
      <div class="ce_btn">
        <a v-if="pageState == 1" class="p_button" @click="toRegist">开始认证</a>
        <a v-else-if="pageState == 3" class="p_button" @click="nextClick"
          >下一步</a
        >
      </div>
    </footer>
    <witnessStatus
      v-if="pageState == 2"
      :user-info="regResult"
      @videoCallBack="videoCallBack"
      @cancel="pageState = 1"
    ></witnessStatus>
  </div>
</template>

<script>
import { videoRegist, getVideoResult } from '@/service/service.js';
import { toVideoFormatDate } from '@/common/util.js';
import flowMixin from '@/common/flowMixin';
// import witnessStatus from '@/components/witnessStatus';
import witnessStatus from '@/components/witnessAuth/two/witnessStatus';
import { getDictData } from '@/service/commonService';
export default {
  name: 'WitnessTwo',
  components: {
    witnessStatus
  },
  mixins: [flowMixin],
  data() {
    return {
      pageState: 1, // 1准备页面 2排队（h5）  3成功页面
      regResult: {},
      dictData: {}
    };
  },
  created() {
    getDictData().then((data) => {
      if (data.code == 0) {
        this.dictData = data.data;
      } else {
        _hvueAlert({ mes: data.msg });
      }
    });
  },
  deactivated() {
    this.pageState = 1;
  },
  methods: {
    back() {
      if (this.pageState == 2) {
        this.pageState = 1;
      } else {
        this.$router.replace({ name: 'videoApprove' });
      }
    },
    startTwoVideo() {
      if ($hvue.env != 'thinkive') {
        this.pageState = 2;
        return;
      }
      window.videoCallBack = this.videoCallBack;
      let biz_type = this.flowOutputInfo.inProperty.bizType;
      var param = {
        userId: this.regResult.regFlowNo, // 用户编号
        userName: this.flowOutputInfo.inProperty.clientName, // 用户名称
        orgId: this.flowOutputInfo.inProperty.preBranchNo, // 营业部编号
        netWorkStatus: 'WIFI', // 网络状态 (可不填
        url:
          $hvue.customConfig.video.videoServer +
          '/wa-queue-server/servlet/json?', // 连接排队BUS的服务器地址 (ios必须要在url加上?不然无法识别
        moduleName: 'open', // 必须为open
        funcNo: '60005', // 双向视频见证
        isRejectToH5: '1', //原生是否处理透明通道信息 默认为0
        isNewView: '0', // 是否使用vue版本界面
        isShowHeadRect: '0', // 是否视频见证中显示头像取景框方便客户对准	N	1：显示；其他都隐藏（目前4.0视频界面使用）
        version: '4.0', // 排队bus版本
        // mainColor: $hvue.customConfig.themeColor, //视频见证主题颜色
        // 统一视频兼容
        user_id: this.regResult.regFlowNo,
        branch_id: this.flowOutputInfo.inProperty.preBranchNo,
        branchno: this.flowOutputInfo.inProperty.preBranchNo,
        user_name: this.flowOutputInfo.inProperty.clientName,
        client_id: this.regResult.regFlowNo,
        client_name: this.flowOutputInfo.inProperty.clientName,
        id_no: this.flowOutputInfo.inProperty.idNo,
        mobile_no: this.flowOutputInfo.inProperty.mobileTel,
        appId: $hvue.customConfig.video.anychat.appId,
        origin: $hvue.platform,
        videoType: $hvue.customConfig.video.videoType,
        requestParam: `videoType=${$hvue.customConfig.video.videoType}`,
        biz_type: biz_type,
        bizType: biz_type,
        business_type: biz_type,
        business_code: biz_type,
        useTsyp: $hvue.customConfig.video.useTysp,
        requestHeaders: {
          'tk-token-authorization': this.regResult.jwtToken
        }
      };
      console.log(param);
      let result = $h.callMessageNative(param);
      if (result.error_no !== '0') {
        console.log(result.error_info);
      }
    },

    toRegist() {
      // let regData = {
      // 	client_name: this.flowOutputInfo.inProperty['clientName'],
      // 	client_gender: this.flowOutputInfo.inProperty['clientGender'],
      // 	id_kind: '身份证',
      // 	id_no: this.flowOutputInfo.inProperty['idNo'],
      // 	nation_id: this.flowOutputInfo.inProperty['nation_id'],
      // 	birthday: toVideoFormatDate(this.flowOutputInfo.inProperty['birthday']),
      // 	issued_depart: this.flowOutputInfo.inProperty['issuedDepart'],
      // 	id_address: this.flowOutputInfo.inProperty['idAddress'],
      // 	id_begindate: toVideoFormatDate(this.flowOutputInfo.inProperty['idBegindate']),
      // 	id_enddate: toVideoFormatDate(this.flowOutputInfo.inProperty['idEnddate']),
      // 	corp_risk_level: this.flowOutputInfo.inProperty['corpRiskLevel'],
      // 	en_invest_kind: this.flowOutputInfo.inProperty['enInvestKind'],
      // 	en_invest_term: this.flowOutputInfo.inProperty['enInvestTerm'],
      // 	bus_suitability_grade: this.flowOutputInfo.inProperty['busSuitabilityGrade'],
      // 	matching_result: this.flowOutputInfo.inProperty['matchingResult'] != '1' ? '不匹配' : '匹配',
      // 	id_card_portrait: JSON.stringify({ mode: '1', src: this.flowOutputInfo.inProperty['idCardPortrait'] }),
      // 	id_card_national: JSON.stringify({ mode: '1', src: this.flowOutputInfo.inProperty['idCardNational'] }),
      // 	bareheaded_pic: JSON.stringify({ mode: '1', src: this.flowOutputInfo.inProperty['bareheadedPic'] })
      // };
      let param = {
        flowToken: sessionStorage.getItem('TKFlowToken'),
        custId: $h.getSession('authorization'),
        businessType: this.flowOutputInfo.inProperty.bizType,
        custName: this.flowOutputInfo.inProperty.clientName,
        branchNo: this.flowOutputInfo.inProperty.preBranchNo,
        preBranchNo: this.flowOutputInfo.inProperty.preBranchNo,
        operationType: '1' // 操作类别，1、见证；2、审核
        // regData: JSON.stringify(this.translateDict(regData))
      };
      videoRegist(param)
        .then((data) => {
          if (data.code == '0') {
            if (!data.data) {
              _hvueAlert({ mes: '注册失败' });
              return;
            }
            this.regResult = Object.assign(
              this.flowOutputInfo.inProperty,
              data.data
            );
            this.regResult.branchNo = this.regResult.preBranchNo;
            $h.setSession('videoToken', this.regResult.jwtToken);
            this.startTwoVideo();
          } else {
            _hvueAlert({ mes: data.msg });
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    translateDict(param) {
      console.log(param);
      let dictMap = {
        client_gender: 'bc.common.Gender',
        corp_risk_level: 'bc.common.corpRiskLevel',
        en_invest_kind: 'bc.common.prInvestKind',
        en_invest_term: 'bc.common.prInvestTerm',
        bus_suitability_grade: 'bc.common.prodriskLevel'
      };
      Object.keys(dictMap).forEach((a) => {
        let arr = [];
        this.dictData[dictMap[a]].forEach((b) => {
          try {
            if (param[a].split(',').includes(b.dictValue)) {
              arr.push(b.dictLabel);
            }
          } catch (e) {
            console.log(a + ',' + param[a]);
          }
        });
        param[a] = arr.join(';');
      });
      return param;
    },
    videoCallBack(msg = {}) {
      this.pageState = 1;
      console.log(msg);
      let message = JSON.parse(msg.message || '');
      if (message.msgNo == 0 || message.msgNo == 1) {
        this.getVideoResult();
      }

      // if (!msg.videoFlag) return;
      // try {
      //   msg = JSON.parse(msg.videoFlag);
      // } catch (e) {
      //   console.log(e);
      // }
      // if (msg.msgNo == 0) {
      //   this.pageState = 2;
      // } else {
      //   let rejectReason = '';
      //   msg.msgInfo.forEach((a, index) => {
      //     rejectReason += `${index + 1}、${a.reasons.join(';')}<br>`;
      //   });
      //   _hvueAlert({
      //     mes: `<div style="text-align:left;">驳回：<br>${rejectReason}</div>`,
      //     callback: () => {
      //       this.getVideoResult();
      //     }
      //   });
      // }
    },
    nextClick() {
      this.nextFlow({}); // 接口处理数据
    },
    rejectHandler(msgArr, notNext) {
      let rejectReason = '';
      msgArr.forEach((a, index) => {
        rejectReason += `${index + 1}、${a}<br>`;
      });
      _hvueAlert({
        mes: `<div style="text-align:left;">驳回：<br>${rejectReason}</div>`,
        callback: () => {
          if (!notNext) {
            this.nextClick();
          }
        }
      });
    },
    getVideoResult() {
      getVideoResult({
        regFlowNo: this.regResult.regFlowNo,
        flowToken: sessionStorage.getItem('TKFlowToken'),
        source: $hvue.platform === '0' ? '3' : $hvue.platform
      }).then((data) => {
        if (data.code == 0) {
          let result = data.data;
          if (result.opFlowStatus == 2) {
            // 1待处理 2通过 3驳回
            this.pageState = 3;
          } else if (result.opFlowStatus == 3) {
            this.rejectHandler(result.witnessMsg);
          }
        } else {
          if (data.code == 2002) {
            //仅仅驳回了视频，算组件内错误，不需要调用流程引擎下一步接口
            this.rejectHandler(data.msg.split(';'), true);
          } else {
            _hvueAlert({ mes: data.msg });
          }
        }
      });
    },
    renderingView() {
      _hvueLoading.close();
      console.log(this.flowOutputInfo.inProperty);
    }
  }
};
</script>

<style>
.fc_basebox {
  background: #fff;
  padding: 0.35rem 0.15rem 0.1rem;
  text-align: center;
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #999999;
}
.fc_basebox h5 {
  font-size: 0.24rem;
  line-height: 0.28rem;
  font-weight: normal;
  margin-bottom: 0.1rem;
  color: #000000;
}
.fc_basebox .pic {
  margin-top: 0.3rem;
}
.fc_basebox .pic img {
  display: block;
  height: 2.5rem;
  margin: 0 auto;
}
.lz_tipbox {
  background: #fff;
  padding: 0.15rem 0.05rem;
  margin-bottom: 0.1rem;
}
.lz_tipbox .title {
  margin: 0 0.2rem 0.14rem;
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #999999;
  font-weight: normal;
}
.lz_tipbox ul {
  width: 100%;
  display: table;
  table-layout: fixed;
}
.lz_tipbox ul li {
  display: table-cell;
  text-align: center;
}
.lz_tipbox ul li i {
  display: block;
  width: 0.36rem;
  height: 0.36rem;
  margin: 0 auto 0.05rem;
}
.lz_tipbox ul li i img {
  display: block;
  width: 100%;
  height: 100%;
}
.lz_tipbox ul li span {
  display: block;
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #999;
}
</style>
