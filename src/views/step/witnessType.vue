<!--  -->
<template>
  <section class="main fixed">
    <t-header @back="back"></t-header>
    <article class="content">
      <div class="com_title">
        <h5>选择见证方式</h5>
      </div>
      <div class="com_box">
        <ul class="p_video_typelist">
          <li
            :class="[witnessPlace === 0 ? 'active' : 'disable']"
            @click="selectWitness(0)"
          >
            <span
              class="icon_radio"
              :class="{ checked: witnessPlace === 0 }"
            ></span>
            <h5>临柜见证</h5>
            <p>您需要携带身份证、外部资产证明、征信加分材料、亲临营业部办理</p>
          </li>
          <li :class="[witnessPlace === 1 ? 'active' : 'disable']">
            <span
              class="icon_radio"
              :class="{ checked: witnessPlace === 1 }"
            ></span>
            <h5>见证人员办理</h5>
            <p>
              您已预约好见证人员在您身边，同时您已准备好身份证、外部资产证明、征信加分材料
            </p>
          </li>
        </ul>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="toNext">下一步</a>
      </div>
    </footer>
  </section>
</template>

<script>
import flowMixin from '@/common/flowMixin';
export default {
  mixins: [flowMixin],
  data() {
    return {
      witnessPlace: 0
    };
  },

  methods: {
    renderingView() {
      _hvueLoading.close();
    },

    selectWitness(type) {
      this.witnessPlace = type;
    },
    back() {
      this.prevFlow();
    },
    toNext() {
      this.nextFlow({
        witness_place: this.witnessPlace
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.disable {
  opacity: 0.6;
  background: #eee;
}
</style>
