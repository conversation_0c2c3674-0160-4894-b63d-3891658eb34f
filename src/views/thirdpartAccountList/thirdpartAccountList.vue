<template>
  <section class="main fixed backWhite_box" data-page="home">
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click.stop="back" />
        <h1 class="title">三方存管</h1>
      </div>
    </header>
    <article>
      <div
        v-if="status === 'thirdpartList' && thirdpartList.length > 0"
        class="account_top"
      >
        <div>普通账户</div>
        <div>{{ fundAccount }}</div>
      </div>
      <div
        v-if="status === 'thirdpartList' && thirdpartList.length > 0"
        class="thirdpart"
      >
        <div class="bank_card">
          <bankCard
            v-for="(item, idx) in thirdpartList"
            :key="idx"
            :bankItem="item"
            :bindBank="bindBank"
            :changeBank="changeBank"
          />
        </div>
        <div class="warm_tips">
          <p>温馨提示：</p>
          <p>因办理三方存管银行变更需满足“当日无资金流水”、“当日无可取资金”的条件，在此提示您：</p>
          <p>
            1、若申请当日账户发生了资金流水，需等待至无资金流水的交易日方可发起三方存管银行变更；
          </p>
          <p>
            2、若申请当日账户有可取资金，您可以自助将资金转出，如果您开通了现金理财功能，也可以将保留金额设置为0；对未自助转出的，我司会自动将资金转出到您原三方存管银行。资金转出后的第二个交易日方可发起三方存管银行变更；
          </p>
          <p>3、线上仅支持异行换卡，同行换卡请到银行柜台办理。</p>
        </div>
      </div>
      <div v-if="status === 'noThirdpart'" class="no_data_content">
        <img src="@/assets/images/no_data.png" class="no_data_icon" />
        <div class="tips_top">暂未找到您的主资金账户三方存管账户</div>
        <div class="tips_bottom">
          您的账户目前无法进行资金存取，请尽快开通三方存管账户，如有疑问，请联系在线客服或拨打热线：<span
            class="phone_num"
            >95310</span
          >
        </div>
      </div>
    </article>
    <footer class="footer" v-if="status === 'noThirdpart'">
      <div class="ce_btn">
        <a class="p_button" @click="toNext(status)">马上开通</a>
      </div>
    </footer>
  </section>
</template>
<script>
import { indexquery, getBank, businessStrategyCheck } from '@/service/service';
import { exitApp, getOpStation } from '@/common/util';
import bankCard from '../../components/bankCard.vue';

export default {
  components: {
    bankCard
  },
  data() {
    return {
      fundAccount: '',
      status: '',
      thirdpartList: []
    };
  },
  computed: {
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo;
    }
  },
  watch: {
    ssoLoginFlag: {
      handler: function (userInfo) {
        console.log('userInfo', userInfo);
        if (userInfo) {
          this.renderingView();
        }
      },
      immediate: true
    }
  },
  created() {
    $h.setSession('introduceNeedBack', true);
    window.viewShowCallBack = this.viewShowCallBack;
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  mounted() {},
  methods: {
    back() {
      $h.clearSession('introduceNeedBack');
      if ($hvue.platform === '0') {
        this.$router.back();
      } else {
        exitApp();
      }
    },
    viewShowCallBack() {
      this.renderingView();
    },
    renderingView() {
      businessStrategyCheck({
        strategyNo: 'wt_sfcgbg_init_check',
        bizType: '010030'
      })
        .then((res) => {
          if (res.data.strategyResult === '1') {
            return getBank({});
          } else {
            let tip = JSON.parse(res.data.result[0].ruleResultDesc);
            this.$TAlert({
              title: tip.title,
              tips: tip.tips,
              confirm: () => {
                this.back();
              }
            });
          }
        })
        .then((res) => {
          const { code, data } = res;
          if (code == 0) {
            const { clientId, fundAccount, branchNo } =
              this.$store.state.user?.userInfo;
            this.fundAccount = fundAccount;
            let opStation = getOpStation();
            indexquery({
              clientId: clientId,
              fundAccount: fundAccount,
              opStation: opStation,
              opEntrustWay: '7',
              branchNo: branchNo
            })
              .then((res) => {
                if (res.code == 0) {
                  this.status = 'thirdpartList';
                  this.thirdpartList = res.data.diList.map((item) => {
                    data.forEach((element) => {
                      if (element.bankNo === item.bankNo) {
                        item.bankLogo = element.bankLogo;
                      }
                    });
                    return item;
                  });
                  let mainAccountItem = this.thirdpartList.filter((item) => {
                    if (item.mainFlag == '1') {
                      return item;
                    }
                  });
                  console.log('mainAccountItem', mainAccountItem);
                  if (!mainAccountItem[0]?.bankNo) {
                    // 主资金账号无三方存管场景
                    this.status = 'noThirdpart';
                  }
                }
              })
              .catch((err) => {
                // 客户当前三方存管状态异常
                this.$TAlert({
                  tips: '您的三方存管账户状态异常，请联系客服95310',
                  confirm: () => {
                    this.back();
                  }
                });
              });
          }
        });
    },
    toNext(type) {
      if (type === 'noThirdpart') {
        console.log('走新开流程');
        $h.setSession('bizType', '010027');
        import('@/common/flowMixinV2.js').then((a) => {
          a.initFlow.call(this, {
            bizType: '010027',
            flowNo: '0-010027',
            isReset: 0,
            contextParam: JSON.stringify({ extInitParams: { bindStatus: 1 } }),
            initJumpMode: '0'
          });
        });
      }
    },
    bindBank({ bankLogo, ...bankItem }) {
      console.log('绑定银行卡======', bankItem);
      $h.setSession('bizType', '010027');
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, {
          bizType: '010027',
          flowNo: '0-010027',
          isReset: 0,
          contextParam: JSON.stringify({
            extInitParams: { oldBankInfo: bankItem, bindStatus: 3 }
          }),
          initJumpMode: '0'
        });
      });
    },
    changeBank({ bankLogo, ...bankItem }) {
      console.log('变更银行卡=====', bankItem);
      $h.setSession('bizType', '010030');
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, {
          bizType: '010030',
          flowNo: '0-010030',
          isReset: 0,
          contextParam: JSON.stringify({
            extInitParams: { oldBankInfo: bankItem, bindStatus: 2 },
            bankNo: bankItem.bankNo
          }),
          initJumpMode: '0'
        });
      });
    }
  }
};
</script>
<style scoped lang="less">
.account_top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  padding: 0 16px;
  background-color: #fff;
}
.thirdpart {
  padding: 16px 16px 0;
  background: var(--bg-line-backgroud-f-5, #f2f4f8);
  min-height: 100vh;
  .warm_tips {
    color: var(--typography-666, #55555e);
    font-family: PingFang SC;
    font-size: 14px;
  }
}
.tips_top {
  color: var(--typography-333, #0f0f1b);
  text-align: center;
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  margin-bottom: 20px;
}
.tips_bottom {
  color: var(--typography-666, #55555e);
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
}
.phone_num {
  color: var(--fixed-link, #338aff);
}
.footer {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
}

.no_data_content {
  text-align: center;
  color: var(--typography-333, #333);
  font-size: 0.16rem;
  line-height: 0.24rem;
  padding: 0 0.1rem;
  .no_data_icon {
    width: 1.8rem;
    height: 1.54rem;
    margin-top: 0.5rem;
    margin-bottom: 0.2rem;
  }
}
</style>
