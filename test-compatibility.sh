#!/bin/bash

# Vue2工程兼容性测试脚本
# 用于验证打包后的代码是否正确转译了ES6+语法

echo "🔍 Vue2工程兼容性测试"
echo "===================="

# 检查dist目录是否存在
if [ ! -d "dist/bc-h5-view/views/js" ]; then
    echo "❌ 错误: dist目录不存在，请先运行 npm run build"
    exit 1
fi

echo "📁 检查构建文件..."

# 查找主要的JS文件
APP_JS=$(find dist/bc-h5-view/views/js -name "app.*.js" | head -1)
VENDOR_JS=$(find dist/bc-h5-view/views/js -name "vendor*.js" | head -1)

if [ -z "$APP_JS" ]; then
    echo "❌ 错误: 找不到app.js文件"
    exit 1
fi

echo "✅ 找到主要文件: $(basename $APP_JS)"

echo ""
echo "🔍 ES6+语法检查"
echo "==============="

# 检查箭头函数
ARROW_COUNT=$(grep -o "=>" "$APP_JS" | wc -l | tr -d ' ')
echo "箭头函数 (=>) 数量: $ARROW_COUNT"

# 检查const声明
CONST_COUNT=$(grep -o "const " "$APP_JS" | wc -l | tr -d ' ')
echo "const声明数量: $CONST_COUNT"

# 检查let声明
LET_COUNT=$(grep -o "let " "$APP_JS" | wc -l | tr -d ' ')
echo "let声明数量: $LET_COUNT"

# 检查模板字符串
TEMPLATE_COUNT=$(grep -o "\`" "$APP_JS" | wc -l | tr -d ' ')
echo "模板字符串 (\`) 数量: $TEMPLATE_COUNT"

# 检查class关键字
CLASS_COUNT=$(grep -o "class " "$APP_JS" | wc -l | tr -d ' ')
echo "class声明数量: $CLASS_COUNT"

echo ""
echo "📊 兼容性评估"
echo "============"

TOTAL_ES6=$((ARROW_COUNT + CONST_COUNT + LET_COUNT + TEMPLATE_COUNT + CLASS_COUNT))

if [ $TOTAL_ES6 -eq 0 ]; then
    echo "✅ 完美! 没有发现ES6+语法，代码已完全转译为ES5"
    COMPATIBILITY="完美兼容"
elif [ $TOTAL_ES6 -lt 50 ]; then
    echo "⚠️  良好: 发现少量ES6+语法($TOTAL_ES6个)，大部分代码已转译"
    COMPATIBILITY="良好兼容"
elif [ $TOTAL_ES6 -lt 200 ]; then
    echo "⚠️  一般: 发现较多ES6+语法($TOTAL_ES6个)，需要进一步优化"
    COMPATIBILITY="一般兼容"
else
    echo "❌ 警告: 发现大量ES6+语法($TOTAL_ES6个)，兼容性可能有问题"
    COMPATIBILITY="兼容性差"
fi

echo ""
echo "📱 目标设备兼容性"
echo "================"
echo "iOS Safari 9+:     $([ $ARROW_COUNT -eq 0 ] && echo '✅ 兼容' || echo '⚠️  可能有问题')"
echo "Android 4.4+:      $([ $CONST_COUNT -eq 0 ] && echo '✅ 兼容' || echo '⚠️  可能有问题')"
echo "微信内置浏览器:     $([ $TEMPLATE_COUNT -eq 0 ] && echo '✅ 兼容' || echo '⚠️  可能有问题')"

echo ""
echo "📋 文件大小信息"
echo "=============="
if [ -f "$APP_JS" ]; then
    APP_SIZE=$(du -h "$APP_JS" | cut -f1)
    echo "主应用文件: $APP_SIZE"
fi

if [ -f "$VENDOR_JS" ]; then
    VENDOR_SIZE=$(du -h "$VENDOR_JS" | cut -f1)
    echo "第三方库文件: $VENDOR_SIZE"
fi

# 检查gzip文件
APP_GZ="${APP_JS}.gz"
if [ -f "$APP_GZ" ]; then
    APP_GZ_SIZE=$(du -h "$APP_GZ" | cut -f1)
    echo "主应用文件(gzip): $APP_GZ_SIZE"
fi

echo ""
echo "🎯 总结"
echo "======"
echo "兼容性状态: $COMPATIBILITY"
echo "ES6+语法总数: $TOTAL_ES6"
echo ""

if [ $TOTAL_ES6 -gt 0 ]; then
    echo "💡 建议:"
    echo "1. 检查babel.config.js配置"
    echo "2. 确认.browserslistrc目标浏览器设置"
    echo "3. 验证forceAllTransforms是否为true"
    echo "4. 在目标设备上进行实际测试"
else
    echo "🎉 恭喜! 代码已完全兼容低版本移动设备"
fi

echo ""
echo "测试完成!"
