const path = require('path');
let pkg = require('./package.json');
const webpack = require('webpack');

const generateZip = false; // 打包时是否生成zip包

let env = 'local'; // 默认本地环境
if (process.env.NODE_ENV === 'production') {
  for (let key in process.env) {
    if (key.indexOf('npm_config_sit') > -1 || process.env.npm_config_develop) {
      env = 'test';
      break;
    } else if (/^npm_config_release_v\d+(\.\d+){2}$/.test(key)) {
      env = 'uat';
      break;
    } else {
      env = 'prod';
    }
  }
  // if (process.env.npm_config_dev) {
  //   env = 'dev';
  // } else if (process.env.npm_config_sit) {
  //   env = 'test';
  // } else if (process.env.npm_config_develop) {
  //   env = 'uat';
  // } else {
  //   env = 'prod';
  // }
}
console.log('-----------  ' + env + '  ------------');
function resolve(dir) {
  return path.join(__dirname, '.', dir);
}

let _config = {
  entry: {
    app: './src/main.js'
  }
};
//开发生产通用基本项
_config.base = {
  //路由前缀
  publicPath: '/' + pkg.name + '/views/',
  //打包输出路径
  outputDir: 'dist/' + pkg.name + '/views',
  //主页输出路径,相对于outputDir
  indexPath: 'index.html',
  //使用vue的运行编译版本
  runtimeCompiler: false,
  //文件名加哈希值
  filenameHashing: true,
  //开启eslint代码规范检测
  lintOnSave: true,
  //打包生成生产环境sourceMap文件
  productionSourceMap: env !== 'prod',
  //需要bable编译的npm模块
  transpileDependencies: ['thinkive-hvue', '_thinkive-hvue']
};
//设置别名
_config.alias = {
  //thinkive-hvue start
  config: 'src/config.js',
  store: 'src/store/index.js',
  router: 'src/router/index.js',
  netIntercept: 'src/service/netIntercept.js',
  nativeCallH5: 'src/nativeShell/nativeCallH5.js',
  h5CallNative: 'src/nativeShell/h5CallNative.js'
  //thinkive-hvue end
};
//开发环境配置
_config.dev = {
  devServer: {
    //浏览器控制台输出
    clientLogLevel: 'warning',
    //开启gzip压缩
    compress: false,
    hot: true,
    // host: '0.0.0.0',
    // port: 8080,
    useLocalIp: true,
    //自动打开浏览器
    open: false,
    overlay: {
      warnings: false,
      errors: true
    },
    watchOptions: {
      poll: false
    },
    headers: {
      'X-Frame-Options': 'ALLOW-FROM http://loaclhost:8080'
    },
    //跨域请求代理
    proxy: {
      '/auth-living-view': {
        target: 'https://fzsdbusiness.yjbtest.com',
        changeOrigin: true
      },
      /* '/bc-bff-server': {
        target: 'http://*************:8066',
        changeOrigin: true
      },
      '/file-common-server/file': {
        target: 'http://*************:8066',
        changeOrigin: true
      },
      '/work-common-server': {
        target: 'http://*************:8066',
        changeOrigin: true
      },*/
      'wa-queue-server': {
        target: 'https://fzsdbusiness.yjbtest.com',
        changeOrigin: true
      },
      '/bc-bff-server': {
        target: 'https://fzsdbusiness.yjbtest.com',
        changeOrigin: true
      },
      '/bf-engine-server': {
        target: 'https://fzsdbusiness.yjbtest.com',
        changeOrigin: true
      },
      '/work-common-server': {
        target: 'http://**************:8888',
        changeOrigin: true
      }
    },
    https: false
  },
  css: {
    //提取组件css为单独文件
    //MiniCssExtractPlugin暂不支持热更新, 开发模式不提取
    extract: false,
    sourceMap: false,
    modules: false,
    loaderOptions: {
      less: {
        // 若 less-loader 版本小于 6.0，请移除 lessOptions 这一级，直接配置选项。
        lessOptions: {
          modifyVars: {
            // 直接覆盖变量
            // 或者可以通过 less 文件覆盖（文件路径为绝对路径）
            hack: `true; @import "${path.resolve(
              __dirname,
              './src/theme/style.less'
            )}";`
          }
        }
      }
    }
  },
  //webpack环境变量
  webpackEnv: {
    SERVER_URL: JSON.stringify('') //框架底层会读取这个变量，但实际没用，请求地址放到public/configuration.js
  }
};
//编译环境配置
_config.build = {
  css: {
    //提取组件css为单独文件
    extract: {
      filename: 'css/[name].[contenthash:10].css',
      chunkFilename: 'css/[name].[contenthash:10].css'
    },
    sourceMap: env !== 'prod',
    modules: false,
    loaderOptions: {
      less: {
        // 若 less-loader 版本小于 6.0，请移除 lessOptions 这一级，直接配置选项。
        lessOptions: {
          modifyVars: {
            // 或者可以通过 less 文件覆盖（文件路径为绝对路径）
            hack: `true; @import "${path.resolve(
              __dirname,
              './src/theme/style.less'
            )}";`
          }
        }
      }
    }
  },
  //压缩gzip
  isGzip: env === 'prod',
  //生产打包删除console.log
  delConsoleLog: false,
  //webpack环境变量
  webpackEnv: {
    SERVER_URL: JSON.stringify('') //框架底层会读取这个变量，但实际没用，请求地址放到public/configuration.js
  }
};

module.exports = Object.assign(
  {
    pluginOptions: {
      'style-resources-loader': {
        preProcessor: 'less',
        patterns: [
          // 这个是加上自己的路径,不能使用(如下:alias)中配置的别名路径
          path.resolve(__dirname, './src/theme/style.less')
        ]
      }
    },
    devServer: _config.dev.devServer,
    css:
      process.env.NODE_ENV === 'production'
        ? _config.build.css
        : _config.dev.css,
    configureWebpack: (config) => {
      //入口文件
      config.entry = _config.entry;
      //项目模块名-webpack环境变量
      config.plugins.push(
        new webpack.DefinePlugin({
          MODULE_NAME: JSON.stringify(pkg.name),
          PACK_ENV: JSON.stringify(env)
        })
      );
      //本地开发环境
      if (env === 'local') {
        config.plugins.push(new webpack.DefinePlugin(_config.dev.webpackEnv));
      }
      //编译环境
      else {
        config.plugins.push(new webpack.DefinePlugin(_config.build.webpackEnv));
        //把webpack runtime 插入index.html中，减少请求
        const ScriptExtHtmlWebpackPlugin = require('script-ext-html-webpack-plugin');
        config.plugins.push(
          new ScriptExtHtmlWebpackPlugin({
            inline: /(runtime|styles)\..*\.js$/
          })
        );
        //js压缩-minimizer选项
        //webpack4后已改为使用terser-webpack-plugin插件
        //vuecli3默认的压缩配置在依赖中：@vue/cli-service/lib/config/terserOptions
        Object.assign(
          config.optimization.minimizer[0].options.terserOptions.compress,
          {
            warnings: false,
            drop_debugger: true,
            drop_console: _config.build.delConsoleLog
            // pure_funcs: ['console.log']
            // exclude: /\/plugins/,
          }
        );

        Object.assign(config.optimization, {
          //把webpack runtime单独提取出来
          runtimeChunk: 'single',
          splitChunks: {
            automaticNameDelimiter: '-',
            chunks: 'all',
            //拆分文件大小下限（未压缩前的js文件大小）
            minSize: 20000,
            maxSize: 250000,
            cacheGroups: {
              default: false,
              // UI组件库单独分包
              vant: {
                test: /[\\/]node_modules[\\/]vant[\\/]/,
                name: 'vant',
                chunks: 'all',
                priority: 60
              },
              'element-ui': {
                test: /[\\/]node_modules[\\/]element-ui[\\/]/,
                name: 'element-ui',
                chunks: 'all',
                priority: 60
              },
              // 表单相关库
              formily: {
                test: /[\\/]node_modules[\\/](@formily|@common\/formily-parser-h5)[\\/]/,
                name: 'formily',
                chunks: 'all',
                priority: 55
              },
              // 自定义UI库
              'vendor-hui': {
                test: /[\\/]node_modules[\\/](thinkive-hui|thinkive-hvue)[\\/]/,
                name: 'vendor-hui',
                priority: 55,
                chunks: 'all'
              },
              // 工具库
              utils: {
                test: /[\\/]node_modules[\\/](lodash|moment|dayjs|crypto-js|bignumber\.js|urijs)[\\/]/,
                name: 'utils',
                chunks: 'all',
                priority: 50
              },
              // Vue相关
              vue: {
                test: /[\\/]node_modules[\\/](vue|vue-router|vuex|vue-fragment)[\\/]/,
                name: 'vue',
                chunks: 'all',
                priority: 50
              },
              // 其他第三方库
              vendor: {
                test: /[\\/]node_modules[\\/]/,
                name: 'vendor',
                priority: 30,
                chunks: 'all',
                reuseExistingChunk: true
              },
              // 公共代码
              common: {
                name: 'common',
                minChunks: 2,
                priority: 10,
                chunks: 'all',
                reuseExistingChunk: true
              }
            }
          }
        });

        //gzip压缩
        if (_config.build.isGzip) {
          const CompressionWebpackPlugin = require('compression-webpack-plugin');
          config.plugins.push(
            new CompressionWebpackPlugin({
              filename: '[path].gz[query]',
              algorithm: 'gzip',
              test: new RegExp('\\.(' + ['js', 'css', 'html'].join('|') + ')$'),
              threshold: 2048,
              minRatio: 0.8,
              cache: false,
              deleteOriginalAssets: false
            })
          );
        }
        // npm run build --report 构建加上参数 --report生成包文件分析页面
        if (process.env.npm_config_report) {
          const BundleAnalyzerPlugin =
            require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
          config.plugins.push(new BundleAnalyzerPlugin());
          return;
        }
        //config.plugins.push(new MiniCssExtractPluginCleanup(/styles.*\.js$/))
      }

      if (env === 'local') {
        console.log('local environment');
      } else {
        // 打包后自动生成 压缩部署文件
        const FileManagerPlugin = require('filemanager-webpack-plugin');
        const nowTime = require('dayjs')().format('YYYYMMDDHHmm');
        const outPutPath = path.join(__dirname, 'dist') + '/' + pkg.name;
        const zipOutPutPath = `${outPutPath}_${env}_${nowTime}`;
        console.log('zip path:' + zipOutPutPath);
        // 将文件夹打包成压缩文件
        config.plugins.push(
          new FileManagerPlugin({
            onEnd: [
              {
                move: [
                  //将当前打包环境的配置覆盖configuration.js
                  {
                    source: `${outPutPath}/views/configuration.js`,
                    destination: `${outPutPath}/views/configuration.js`
                  }
                ]
              },
              {
                // 删除其他环境的配置文件
                delete: [`${outPutPath}/views/configuration_*.js`]
              },
              !generateZip // 如果需要打压缩包
                ? {}
                : {
                    copy: [
                      {
                        source: `${outPutPath}`,
                        destination: `${zipOutPutPath}/${pkg.name}`
                      }
                    ],
                    archive: [
                      {
                        source: zipOutPutPath,
                        destination: `${zipOutPutPath}.zip`
                      }
                    ],
                    delete: [zipOutPutPath]
                  }
            ]
          })
        );
      }
      console.log(`environment:${env}`);
    },
    chainWebpack: (config) => {
      //移除 prefetch 插件
      //如果有需要 prefetch, 可以在路由引入时，用内联注释方式注明
      //import(/* webpackPrefetch: true */ './someAsyncComponent.vue')
      config.plugins.delete('prefetch');
      config.plugin('html').tap((options) => {
        options.excludeChunks = ['runtime', 'styles'];
        return options;
      });
      //webpack runtime插入index.html后，删除预加载runtime文件
      config.plugin('preload').tap((options) => {
        Object.assign(options[0], {
          fileBlacklist: [
            /\.map$/,
            /hot-update\.js$/,
            /runtime\S*\.js$/,
            /styles\S*\.js$/
          ]
        });
        return options;
      });
      //设置别名
      for (let key in _config.alias) {
        config.resolve.alias.set(key, resolve(_config.alias[key]));
      }

      //设置转换成base64的图片大小上限(bit)
      config.module
        .rule('images')
        .use('url-loader')
        .loader('url-loader')
        .tap((options) => {
          options.limit = 500;
          return options;
        });
      config.module
        .rule('media')
        .use('url-loader')
        .loader('url-loader')
        .tap((options) => {
          options.limit = 50;
          return options;
        });
      config.module
        .rule('fonts')
        .use('url-loader')
        .loader('url-loader')
        .tap((options) => {
          options.limit = 50;
          return options;
        });
      config.module
        .rule('svg')
        .use('file-loader')
        .loader('file-loader')
        .tap((options) => {
          options.limit = 50;
          return options;
        });

      // 处理v-html导致的xss安全问题
      config.module
        .rule('vue')
        .use('vue-loader')
        .loader('vue-loader')
        .tap((options) => {
          options.compilerOptions.directives = {
            html(node, directiveMeta) {
              (node.props || (node.props = [])).push({
                name: 'innerHTML',
                value: `xss(_s(${directiveMeta.value}))`
              });
            }
          };
          return options;
        });

      //image(jpg,gif,png)压缩配置项，各参数已经测试校验过，保证压缩与质量比例最优.
      //如需更改，请先行熟悉各参数项的意义
      // 注意：如果构建时出现图片相关错误，可以注释掉这段配置
      if (env !== 'local') {
        try {
          config.module
            .rule('images')
            .use('image-webpack-loader')
            .loader('image-webpack-loader')
            .options({
              mozjpeg: {
                progressive: true,
                quality: 85
              },
              gifsicle: {
                interlaced: true,
                optimizationLevel: 3
              },
              optipng: {
                enabled: false
              },
              pngquant: {
                quality: [0.65, 0.85],
                speed: 4
              }
            })
            .end();
        } catch (e) {
          console.warn('图片压缩配置失败，跳过图片压缩:', e.message);
        }
      }
    }
  },
  _config.base
);
